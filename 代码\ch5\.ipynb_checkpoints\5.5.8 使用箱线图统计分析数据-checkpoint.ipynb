{"cells": [{"cell_type": "code", "execution_count": 28, "id": "196d2488", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "\n", "plt.rcParams['font.family'] = ['SimHei'] # 设置中文字体\n", "plt.rcParams['axes.unicode_minus'] = False # 设置负号显示\n", "\n", "# 生成100个随机数,其中含10个离群值\n", "data = np.random.normal(0, 1, 100)  \n", "outliers = np.random.normal(3, 1, 10)\n", "data = np.concatenate((data, outliers))\n", "\n", "# 绘制箱线图,并标出离群值  \n", "plt.boxplot(data, showfliers=True)\n", "\n", "# 添加标题和轴标签\n", "plt.title('带离群值的数据箱线图')\n", "plt.xlabel('数值')\n", "plt.ylabel('频率')\n", "\n", "# 显示图形\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "70b232d9", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}