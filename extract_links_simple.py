#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版HTML解码链接提取器
快速从HTML文件中提取解码链接
"""

import re
import os
import glob

def extract_decode_links(file_path):
    """从HTML文件中提取解码链接及其上下文"""
    
    # 定义链接模式
    patterns = [
        r'showfilesbot_\d+[VP]_[A-Za-z0-9]+',           # showfilesbot_1V_D1t7H5U2y0i7z6p8W1h6
        r'showfilesbot_\d+[VP]_\d+[VP]_[A-Za-z0-9]+',   # showfilesbot_9P_1V_y1D764N772A9e4I3j0s5
        r'[A-Za-z0-9]+=_grp',                           # VT6AjWM77I5v_Xl62uqZV0tU=_grp
        r'@filepan_bot:_\d+[VP]_\d+[VP]_[A-Za-z0-9]+', # @filepan_bot:_78P_115V_eaeT1VzuCB3x
    ]
    
    # 编译正则表达式
    compiled_patterns = [re.compile(pattern, re.IGNORECASE) for pattern in patterns]
    
    results = []
    
    try:
        # 尝试多种编码读取文件
        encodings = ['utf-8', 'gbk', 'gb2312', 'ansi', 'latin-1']
        content = None
        
        for encoding in encodings:
            try:
                with open(file_path, 'r', encoding=encoding) as f:
                    content = f.read()
                    break
            except UnicodeDecodeError:
                continue
        
        if content is None:
            print(f"无法读取文件: {file_path}")
            return results
        
        lines = content.split('\n')
        
        for i, line in enumerate(lines):
            # 检查每一行是否包含目标链接
            found_links = []
            for pattern in compiled_patterns:
                matches = pattern.findall(line)
                found_links.extend(matches)
            
            if found_links:
                # 收集上下文信息（上一行、当前行、下一行）
                context_lines = []

                # 上一行
                if i > 0:
                    context_lines.append(lines[i-1].strip())
                else:
                    context_lines.append("")

                # 当前行（包含链接）
                context_lines.append(line.strip())

                # 下一行
                if i < len(lines) - 1:
                    context_lines.append(lines[i+1].strip())
                else:
                    context_lines.append("")

                context = {
                    'file': os.path.basename(file_path),
                    'line_num': i + 1,
                    'links': found_links,
                    'context_lines': context_lines
                }
                results.append(context)
    
    except Exception as e:
        print(f"处理文件 {file_path} 时出错: {e}")
    
    return results

def main():
    """主函数"""
    print("HTML解码链接提取器")
    print("=" * 40)
    
    # 查找当前目录下的所有HTML文件
    html_files = glob.glob("*.html") + glob.glob("*.htm")
    
    if not html_files:
        print("当前目录下没有找到HTML文件")
        return
    
    print(f"找到 {len(html_files)} 个HTML文件")
    
    all_results = []
    
    # 处理每个HTML文件
    for html_file in html_files:
        print(f"\n正在处理: {html_file}")
        results = extract_decode_links(html_file)
        all_results.extend(results)
        
        if results:
            print(f"  找到 {len(results)} 个匹配项")
    
    # 输出结果
    if all_results:
        print(f"\n总共找到 {len(all_results)} 个解码链接")
        print("=" * 60)
        
        # 保存到文件
        with open('解码链接提取结果.txt', 'w', encoding='utf-8') as f:
            f.write("HTML解码链接提取结果\n")
            f.write("=" * 60 + "\n\n")
            
            for i, result in enumerate(all_results, 1):
                output = f"匹配项 {i}:\n"
                output += f"文件: {result['file']}\n"
                output += f"行号: {result['line_num']}\n"
                output += f"链接: {', '.join(result['links'])}\n"
                output += "-" * 40 + "\n"
                output += "上下文内容（共三行）:\n"

                context_lines = result['context_lines']
                if context_lines[0]:  # 上一行
                    output += f"上一行: {context_lines[0]}\n"

                output += f"当前行: {context_lines[1]} ← 【包含链接】\n"

                if context_lines[2]:  # 下一行
                    output += f"下一行: {context_lines[2]}\n"

                output += "\n" + "=" * 60 + "\n\n"

                # 同时输出到控制台和文件
                print(output)
                f.write(output)
        
        print(f"结果已保存到: 解码链接提取结果.txt")
        
        # 统计信息
        link_count = sum(len(result['links']) for result in all_results)
        file_count = len(set(result['file'] for result in all_results))
        
        print(f"\n统计信息:")
        print(f"总链接数: {link_count}")
        print(f"涉及文件数: {file_count}")
        
    else:
        print("\n未找到任何匹配的解码链接")

if __name__ == "__main__":
    main()
