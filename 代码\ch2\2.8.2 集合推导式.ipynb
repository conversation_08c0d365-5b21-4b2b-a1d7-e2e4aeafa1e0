{"cells": [{"cell_type": "code", "execution_count": 1, "id": "ce9a28f3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{0, 70, 40, 10, 80, 50, 20, 90, 60, 30}\n"]}], "source": ["# coding=utf-8\n", "n_set = {x for x in range(100) if x % 2 == 0 if x % 5 == 0}\n", "\n", "print(n_set)"]}, {"cell_type": "code", "execution_count": null, "id": "44046ebf", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}