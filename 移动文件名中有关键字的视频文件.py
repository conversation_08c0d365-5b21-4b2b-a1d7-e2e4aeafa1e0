import os
import shutil
import tkinter as tk
from tkinter import filedialog


def move_videos_to_current_directory(start_directory, keyword):
    # 将关键词转换为小写
    keyword = keyword.lower()

    for root, dirs, files in os.walk(start_directory):
        for filename in files:
            # 如果关键词为空，或者文件名中包含关键词（不区分大小写）
            if is_video_file(filename) and (keyword == "" or keyword in filename.lower()):
                file_path = os.path.join(root, filename)
                # 如果文件在子目录中且文件名与子目录名相同，添加后缀
                if os.path.basename(root) == os.path.splitext(filename)[0]:
                    new_filename = add_suffix_to_filename(filename, "-no")
                    new_file_path = os.path.join(root, new_filename)
                    os.rename(file_path, new_file_path)
                    file_path = new_file_path  # 更新文件路径
                    print(f"给视频文件 {filename} 添加后缀：{new_filename}")
                destination_path = os.path.join(start_directory, os.path.basename(file_path))
                shutil.move(file_path, destination_path)
                print(f"移动文件: {file_path} 到 {destination_path}")


def is_video_file(filename):
    video_extensions = [".mp4", ".avi", ".mkv", ".mov", ".wmv", ".mpg", ".ts"]
    return any(filename.lower().endswith(ext) for ext in video_extensions)


def add_suffix_to_filename(filename, suffix):
    base, extension = os.path.splitext(filename)
    return f"{base}{suffix}{extension}"


def select_directory_and_move_videos():
    start_directory = filedialog.askdirectory(title="选择开始的目录")
    if start_directory:
        keyword = keyword_entry.get().strip()
        move_videos_to_current_directory(start_directory, keyword)
        print("处理完成！")


# 创建主窗口
root = tk.Tk()
root.title("移动视频文件")

# 创建关键词输入框和标签
tk.Label(root, text="输入关键词（可选）:").pack()
keyword_entry = tk.Entry(root)
keyword_entry.pack()

# 创建按钮来选择目录并移动视频文件
button = tk.Button(root, text="选择开始的目录并移动视频文件", command=select_directory_and_move_videos)
button.pack()

# 运行程序
root.mainloop()
