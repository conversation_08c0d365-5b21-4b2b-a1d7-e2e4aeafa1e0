import os
import shutil
import tkinter as tk
from tkinter import filedialog

def move_subdirectories_up(source_directory):
    for root, dirs, files in os.walk(source_directory, topdown=False):
        for subdirectory in dirs:
            subdirectory_path = os.path.join(root, subdirectory)
            if os.path.dirname(subdirectory_path) != source_directory:
                new_path = os.path.join(source_directory, subdirectory)
                if os.path.exists(new_path):
                    i = 1
                    while True:
                        new_path = os.path.join(source_directory, f"{subdirectory}-{i}")
                        if not os.path.exists(new_path):
                            break
                        i += 1
                shutil.move(subdirectory_path, new_path)
                print(f"Moved subdirectory: {subdirectory_path} to {new_path}")

def move_all_subdirectories_up():
    source_directory = filedialog.askdirectory(title="选择源文件夹")
    if source_directory:
        move_subdirectories_up(source_directory)
        print("向上移动所有子文件夹完成！")

root = tk.Tk()
button = tk.Button(root, text="选择源文件夹并将所有子文件夹向上移动到根目录", command=move_all_subdirectories_up)
button.pack()
root.mainloop()
