import tkinter as tk
from tkinter import ttk
import win32api
import win32con
import win32gui
import keyboard
import threading
import time
from datetime import datetime
import ctypes
from ctypes import wintypes, create_unicode_buffer, byref, sizeof, c_wchar_p
import pyperclip  # 添加pyperclip库用于剪贴板操作
import json
import os

class AutoCopyApp:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("文本选择自动复制工具")
        self.root.geometry("800x400")
        
        # 设置窗口样式
        self.root.configure(bg='#f0f0f0')

        # 前缀配置文件路径
        self.prefix_config_file = "prefix_config.json"

        # 创建主框架
        self.main_frame = ttk.Frame(self.root, padding="10")
        self.main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 状态标签
        self.status_var = tk.StringVar(value="未启动")
        self.status_label = ttk.Label(
            self.main_frame, 
            text="状态：",
            font=('Arial', 10)
        )
        self.status_label.grid(row=0, column=0, sticky=tk.W, pady=5)
        
        self.status_value = ttk.Label(
            self.main_frame,
            textvariable=self.status_var,
            font=('Arial', 10)
        )
        self.status_value.grid(row=0, column=1, sticky=tk.W, pady=5)
        
        # 功能按钮框架
        self.button_frame = ttk.Frame(self.main_frame)
        self.button_frame.grid(row=1, column=0, columnspan=2, pady=10)
        
        # 开关按钮
        self.toggle_button = ttk.Button(
            self.button_frame,
            text="开启自动复制",
            command=self.toggle_copy_mode
        )
        self.toggle_button.grid(row=0, column=0, padx=5)
        
        # 自动粘贴按钮
        self.paste_button = ttk.Button(
            self.button_frame,
            text="开启自动粘贴",
            command=self.toggle_paste_mode
        )
        self.paste_button.grid(row=0, column=1, padx=5)
        
        # 自动保存按钮
        self.save_button = ttk.Button(
            self.button_frame,
            text="开启自动保存",
            command=self.toggle_save_mode
        )
        self.save_button.grid(row=0, column=2, padx=5)
        
        # 补全文件名按钮
        self.filename_button = ttk.Button(
            self.button_frame,
            text="开启补全文件名",
            command=self.toggle_filename_mode
        )
        self.filename_button.grid(row=0, column=3, padx=5)
        
        # 添加文件名前缀下拉框
        self.filename_prefix_label = ttk.Label(
            self.button_frame,
            text="文件名前缀:",
            font=('Arial', 9)
        )
        self.filename_prefix_label.grid(row=0, column=4, padx=(10, 0))

        # 初始化前缀列表
        self.prefix_list = self.load_prefix_list()

        self.filename_prefix_var = tk.StringVar(value="")
        self.filename_prefix_combo = ttk.Combobox(
            self.button_frame,
            textvariable=self.filename_prefix_var,
            values=self.prefix_list,
            width=15,
            state="normal"  # 允许输入和选择
        )
        self.filename_prefix_combo.grid(row=0, column=5, padx=5)

        # 绑定点击事件
        self.filename_prefix_combo.bind('<Button-1>', self.on_filename_prefix_click)

        # 添加保存按钮
        self.save_prefix_button = ttk.Button(
            self.button_frame,
            text="保存",
            command=self.save_prefix,
            width=6
        )
        self.save_prefix_button.grid(row=0, column=6, padx=2)

        # 添加删除按钮
        self.delete_prefix_button = ttk.Button(
            self.button_frame,
            text="删除",
            command=self.delete_prefix,
            width=6
        )
        self.delete_prefix_button.grid(row=0, column=7, padx=2)
        
        # 复制历史显示区域
        self.history_frame = ttk.LabelFrame(self.main_frame, text="复制历史", padding="5")
        self.history_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=5)
        
        # 创建文本框和滚动条
        self.history_text = tk.Text(self.history_frame, height=15, width=60)
        self.scrollbar = ttk.Scrollbar(self.history_frame, orient=tk.VERTICAL, command=self.history_text.yview)
        self.history_text.configure(yscrollcommand=self.scrollbar.set)
        
        # 绑定点击事件
        self.history_text.bind('<Button-1>', self.on_history_click)
        
        self.history_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        self.scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # 创建按钮框架
        self.button_frame_bottom = ttk.Frame(self.history_frame)
        self.button_frame_bottom.grid(row=1, column=0, columnspan=2, pady=5)
        
        # 添加清空按钮
        self.clear_button = ttk.Button(
            self.button_frame_bottom,
            text="清空历史",
            command=self.clear_history
        )
        self.clear_button.grid(row=0, column=0, padx=5)
        
        # 添加清空剪贴板按钮
        self.clear_clipboard_button = ttk.Button(
            self.button_frame_bottom,
            text="清空剪贴板",
            command=self.clear_clipboard
        )
        self.clear_clipboard_button.grid(row=0, column=1, padx=5)
        
        # 添加粘贴后清空剪贴板选项
        self.clear_after_paste_var = tk.BooleanVar(value=False)
        self.clear_after_paste_check = ttk.Checkbutton(
            self.button_frame_bottom,
            text="粘贴后清空剪贴板",
            variable=self.clear_after_paste_var,
            command=self.toggle_clear_after_paste
        )
        self.clear_after_paste_check.grid(row=0, column=2, padx=5)
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        self.main_frame.columnconfigure(1, weight=1)
        self.main_frame.rowconfigure(2, weight=1)
        self.history_frame.columnconfigure(0, weight=1)
        self.history_frame.rowconfigure(0, weight=1)
        
        # 初始化变量
        self.copy_mode = False
        self.paste_mode = False
        self.save_mode = False
        self.filename_mode = False
        self.clear_after_paste = False  # 添加新变量，控制是否在粘贴后清空剪贴板
        self.monitor_thread = None
        self.paste_thread = None
        self.save_thread = None
        self.filename_thread = None
        self.last_selection = ""
        self.last_clipboard = ""
        self.last_click_time = 0
        
        # 添加剪贴板锁，防止多线程同时访问剪贴板
        self.clipboard_lock = threading.Lock()
        # 添加剪贴板缓存，记录上一次复制的内容
        self.clipboard_cache = ""
        
        # 设置关闭窗口事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        
        # 初始化Windows API
        self.user32 = ctypes.WinDLL('user32', use_last_error=True)
        self.kernel32 = ctypes.WinDLL('kernel32', use_last_error=True)

    def load_prefix_list(self):
        """从配置文件加载前缀列表"""
        try:
            if os.path.exists(self.prefix_config_file):
                with open(self.prefix_config_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    return data.get('prefixes', [])
            else:
                # 如果文件不存在，返回默认的空列表
                return []
        except Exception as e:
            print(f"加载前缀列表时出错: {e}")
            return []

    def save_prefix_list(self, prefix_list):
        """保存前缀列表到配置文件"""
        try:
            data = {'prefixes': prefix_list}
            with open(self.prefix_config_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存前缀列表时出错: {e}")

    def save_prefix(self):
        """保存当前输入的前缀到列表中"""
        try:
            current_prefix = self.filename_prefix_var.get().strip()
            if current_prefix:
                # 如果前缀不在列表中，则添加
                if current_prefix not in self.prefix_list:
                    self.prefix_list.append(current_prefix)
                    # 更新下拉框的值
                    self.filename_prefix_combo['values'] = self.prefix_list
                    # 保存到文件
                    self.save_prefix_list(self.prefix_list)

                    # 添加时间戳到历史记录
                    timestamp = datetime.now().strftime("%H:%M:%S")
                    self.add_to_history(f"{timestamp}: [前缀已保存] {current_prefix}")
                    self.status_var.set("前缀已保存")
                else:
                    self.status_var.set("前缀已存在")
            else:
                self.status_var.set("请输入前缀内容")
        except Exception as e:
            print(f"保存前缀时出错: {e}")
            self.status_var.set("保存前缀失败")

    def delete_prefix(self):
        """删除当前选中的前缀"""
        try:
            current_prefix = self.filename_prefix_var.get().strip()
            if current_prefix and current_prefix in self.prefix_list:
                # 从列表中移除
                self.prefix_list.remove(current_prefix)
                # 更新下拉框的值
                self.filename_prefix_combo['values'] = self.prefix_list
                # 清空当前输入
                self.filename_prefix_var.set("")
                # 保存到文件
                self.save_prefix_list(self.prefix_list)

                # 添加时间戳到历史记录
                timestamp = datetime.now().strftime("%H:%M:%S")
                self.add_to_history(f"{timestamp}: [前缀已删除] {current_prefix}")
                self.status_var.set("前缀已删除")
            else:
                self.status_var.set("未找到要删除的前缀")
        except Exception as e:
            print(f"删除前缀时出错: {e}")
            self.status_var.set("删除前缀失败")

    def get_selected_text(self):
        try:
            # 保存当前剪贴板内容
            old_clipboard = pyperclip.paste()
            
            # 模拟Ctrl+C
            keyboard.send('ctrl+c')
            time.sleep(0.2)  # 等待复制操作完成
            
            # 获取新的剪贴板内容
            new_clipboard = pyperclip.paste()
            
            # 如果剪贴板内容变化了，说明有新的选中文本
            if new_clipboard != old_clipboard and new_clipboard != self.last_clipboard:
                self.last_clipboard = new_clipboard
                return new_clipboard
            
            return ""
        except Exception as e:
            print(f"获取选中文本时出错: {e}")
            return ""
    
    def copy_to_clipboard(self, text):
        # 复制文本到剪贴板
        if text:
            try:
                # 确保文本是字符串类型
                if not isinstance(text, str):
                    text = str(text)
                
                # 尝试处理可能的编码问题
                try:
                    # 如果文本包含非ASCII字符，尝试使用UTF-8编码
                    if any(ord(c) > 127 for c in text):
                        # 尝试将文本转换为UTF-8编码
                        text = text.encode('utf-8', errors='ignore').decode('utf-8')
                except Exception as e:
                    print(f"处理文本编码时出错: {e}")
                
                # 使用锁确保剪贴板操作的线程安全
                with self.clipboard_lock:
                    # 使用pyperclip确保跨平台兼容性
                    pyperclip.copy(text)
                    
                    # 同时使用Tkinter的剪贴板功能作为备份
                    self.root.clipboard_clear()
                    self.root.clipboard_append(text)
                    self.root.update()
                    
                    # 更新剪贴板缓存
                    self.clipboard_cache = text
                    
                    # 添加时间戳到历史记录
                    timestamp = datetime.now().strftime("%H:%M:%S")
                    self.add_to_history(f"{timestamp}: {text}")
            except Exception as e:
                print(f"复制到剪贴板时出错: {e}")
                pass
    
    def toggle_copy_mode(self):
        self.copy_mode = not self.copy_mode
        if self.copy_mode:
            self.toggle_button.configure(text="关闭自动复制")
            self.status_var.set("已启动")
            self.start_monitoring()
        else:
            self.toggle_button.configure(text="开启自动复制")
            self.status_var.set("未启动")
            self.stop_monitoring()
    
    def toggle_paste_mode(self):
        self.paste_mode = not self.paste_mode
        if self.paste_mode:
            self.paste_button.configure(text="关闭自动粘贴")
            self.status_var.set("自动粘贴已启动")
            self.start_paste_monitoring()
        else:
            self.paste_button.configure(text="开启自动粘贴")
            self.status_var.set("未启动")
            self.stop_paste_monitoring()
    
    def toggle_save_mode(self):
        self.save_mode = not self.save_mode
        if self.save_mode:
            self.save_button.configure(text="关闭自动保存")
            self.status_var.set("自动保存已启动")
            self.start_save_monitoring()
        else:
            self.save_button.configure(text="开启自动保存")
            self.status_var.set("未启动")
            self.stop_save_monitoring()
    
    def toggle_filename_mode(self):
        self.filename_mode = not self.filename_mode
        if self.filename_mode:
            self.filename_button.configure(text="关闭补全文件名")
            self.status_var.set("补全文件名已启动")
            self.start_filename_monitoring()
        else:
            self.filename_button.configure(text="开启补全文件名")
            self.status_var.set("未启动")
            self.stop_filename_monitoring()
    
    def toggle_clear_after_paste(self):
        """切换是否在粘贴后清空剪贴板"""
        self.clear_after_paste = self.clear_after_paste_var.get()
        if self.clear_after_paste:
            self.status_var.set("已启用粘贴后清空剪贴板")
        else:
            self.status_var.set("未启用粘贴后清空剪贴板")
    
    def start_monitoring(self):
        self.monitor_thread = threading.Thread(target=self.monitor_selection, daemon=True)
        self.monitor_thread.start()
    
    def stop_monitoring(self):
        self.monitor_thread = None
    
    def start_paste_monitoring(self):
        self.paste_thread = threading.Thread(target=self.monitor_paste, daemon=True)
        self.paste_thread.start()
    
    def stop_paste_monitoring(self):
        self.paste_thread = None
    
    def start_save_monitoring(self):
        self.save_thread = threading.Thread(target=self.monitor_save, daemon=True)
        self.save_thread.start()
    
    def stop_save_monitoring(self):
        self.save_thread = None
    
    def start_filename_monitoring(self):
        self.filename_thread = threading.Thread(target=self.monitor_filename, daemon=True)
        self.filename_thread.start()
    
    def stop_filename_monitoring(self):
        self.filename_thread = None
    
    def monitor_selection(self):
        mouse_pressed = False
        while self.copy_mode:
            try:
                # 检查鼠标左键状态
                left_button_state = win32api.GetKeyState(win32con.VK_LBUTTON)
                
                # 鼠标按下
                if left_button_state < 0 and not mouse_pressed:
                    mouse_pressed = True
                
                # 鼠标释放
                elif left_button_state >= 0 and mouse_pressed:
                    mouse_pressed = False
                    # 等待一小段时间让选择完成
                    time.sleep(0.1)
                    # 获取选中的文本
                    selected_text = self.get_selected_text()
                    if selected_text and selected_text != self.last_selection:
                        self.last_selection = selected_text
                        self.copy_to_clipboard(selected_text)
            except Exception:
                pass
            time.sleep(0.05)  # 减少CPU使用率
    
    def monitor_paste(self):
        """监控鼠标点击，自动粘贴剪贴板内容"""
        last_click_time = 0
        mouse_pressed = False  # 添加鼠标按下状态标志
        last_window = None  # 记录上一次的窗口句柄
        
        while self.paste_mode:
            try:
                # 检查鼠标左键状态
                left_button_state = win32api.GetKeyState(win32con.VK_LBUTTON)
                
                # 获取当前窗口句柄
                current_window = win32gui.GetForegroundWindow()
                
                # 鼠标按下
                if left_button_state < 0 and not mouse_pressed:
                    mouse_pressed = True
                    current_time = time.time()
                    # 确保两次点击之间有足够的时间间隔（防止重复触发）
                    if current_time - last_click_time > 0.5:
                        last_click_time = current_time
                        # 等待一小段时间让点击完成
                        time.sleep(0.3)  # 增加等待时间，确保点击已完成
                        
                        # 获取鼠标点击位置的窗口句柄
                        cursor_pos = win32gui.GetCursorPos()
                        hwnd = win32gui.WindowFromPoint(cursor_pos)
                        
                        # 获取窗口类名
                        class_name = win32gui.GetClassName(hwnd)
                        
                        # 打印调试信息（临时）
                        print(f"自动粘贴 - 点击的窗口类名: {class_name}")
                        
                        # 检查是否是顶层窗口切换
                        if last_window is not None and current_window != last_window:
                            print(f"自动粘贴 - 检测到顶层窗口切换，不执行粘贴操作")
                            last_window = current_window
                            continue
                        
                        # 检查是否是输入框（扩展检测范围）
                        is_input_field = ("EDIT" in class_name or 
                                         "ComboBox" in class_name or 
                                         "RichEdit" in class_name or
                                         "TextBox" in class_name or
                                         "Chrome_RenderWidgetHostHWND" in class_name or  # Chrome浏览器的输入框
                                         "Qt51515QWindow" in class_name or  # Qt应用程序窗口
                                         "Qt51515QWindowIcon" in class_name or  # Qt应用程序图标窗口
                                         "Qt51515QWindowPopupSaveBits" in class_name)  # Qt应用程序弹出窗口
                        
                        # 尝试查找子窗口，检查是否有输入框
                        if not is_input_field:
                            # 获取父窗口
                            parent_hwnd = win32gui.GetParent(hwnd)
                            if parent_hwnd:
                                # 获取父窗口类名
                                parent_class = win32gui.GetClassName(parent_hwnd)
                                print(f"自动粘贴 - 父窗口类名: {parent_class}")
                                
                                # 检查父窗口是否是输入框
                                if ("EDIT" in parent_class or 
                                    "ComboBox" in parent_class or 
                                    "RichEdit" in parent_class or
                                    "TextBox" in parent_class or
                                    "Chrome_RenderWidgetHostHWND" in parent_class or
                                    "Qt51515QWindow" in parent_class):
                                    is_input_field = True
                                    print(f"自动粘贴 - 父窗口是输入框")
                        
                        # 如果不是输入框，则不执行粘贴操作
                        if not is_input_field:
                            print(f"自动粘贴 - 点击的不是输入框，不执行粘贴操作")
                            continue
                        
                        # 先清空输入框
                        try:
                            # 全选当前内容 (Ctrl+A)
                            keyboard.send('ctrl+a')
                            time.sleep(0.2)  # 增加等待时间，确保全选完成
                            
                            # 删除选中内容 (Delete)
                            keyboard.send('delete')
                            time.sleep(0.2)  # 增加等待时间，确保删除完成
                            
                            print(f"自动粘贴 - 已清空输入框")
                        except Exception as e:
                            print(f"清空输入框时出错: {e}")
                        
                        # 在粘贴前再次获取最新的剪贴板内容
                        current_clipboard = ""
                        with self.clipboard_lock:
                            # 优先使用缓存的剪贴板内容
                            if self.clipboard_cache:
                                current_clipboard = self.clipboard_cache
                            else:
                                # 如果缓存为空，则从系统剪贴板获取
                                current_clipboard = pyperclip.paste()
                        
                        print(f"自动粘贴 - 准备粘贴的内容: {current_clipboard}")
                        
                        # 确保剪贴板有内容才执行粘贴
                        if current_clipboard:
                            # 模拟Ctrl+V粘贴
                            keyboard.send('ctrl+v')
                            time.sleep(0.3)  # 增加等待时间，确保粘贴完成
                            
                            # 添加时间戳到历史记录
                            timestamp = datetime.now().strftime("%H:%M:%S")
                            self.add_to_history(f"{timestamp}: [自动粘贴] {current_clipboard}")
                            
                            # 如果启用了粘贴后清空剪贴板，则立即清空剪贴板
                            if self.clear_after_paste:
                                # 使用锁确保线程安全
                                with self.clipboard_lock:
                                    try:
                                        # 清空剪贴板
                                        pyperclip.copy('')
                                        self.root.clipboard_clear()
                                        self.root.update()
                                        
                                        # 清空剪贴板缓存
                                        self.clipboard_cache = ""
                                        
                                        # 添加清空记录
                                        self.add_to_history(f"{timestamp}: [剪贴板已清空]")
                                        
                                        # 更新状态提示
                                        self.status_var.set("粘贴完成，剪贴板已清空")
                                        print(f"自动粘贴 - 剪贴板已清空")  # 添加调试信息
                                    except Exception as e:
                                        print(f"清空剪贴板时出错: {e}")
                                        self.status_var.set("粘贴完成，但清空剪贴板失败")
                            else:
                                self.status_var.set("粘贴完成")
                
                # 鼠标释放
                elif left_button_state >= 0 and mouse_pressed:
                    mouse_pressed = False
                
                # 更新上一次的窗口句柄
                last_window = current_window
                    
            except Exception as e:
                # 打印异常信息（临时）
                print(f"自动粘贴时出错: {e}")
                mouse_pressed = False  # 重置鼠标状态
            time.sleep(0.05)  # 减少CPU使用率
    
    def monitor_save(self):
        """监控ALT+鼠标左键点击，自动执行保存操作"""
        last_click_time = 0
        while self.save_mode:
            try:
                # 检查ALT键和鼠标左键状态
                alt_state = win32api.GetKeyState(win32con.VK_MENU)  # ALT键
                left_button_state = win32api.GetKeyState(win32con.VK_LBUTTON)  # 鼠标左键
                
                # ALT键按下且鼠标左键按下
                if alt_state < 0 and left_button_state < 0:
                    current_time = time.time()
                    # 确保两次点击之间有足够的时间间隔（防止重复触发）
                    if current_time - last_click_time > 0.5:
                        last_click_time = current_time
                        # 等待一小段时间让点击完成
                        time.sleep(0.1)
                        # 模拟右键点击
                        keyboard.send('right')
                        time.sleep(0.1)
                        # 模拟方向下键三次
                        for _ in range(3):
                            keyboard.send('down')
                            time.sleep(0.1)
                        # 模拟回车键
                        keyboard.send('enter')
            except Exception:
                pass
            time.sleep(0.05)  # 减少CPU使用率
    
    def monitor_filename(self):
        """监控文件名输入框点击，自动补全文件名"""
        last_click_time = 0
        while self.filename_mode:
            try:
                # 检查鼠标左键状态
                left_button_state = win32api.GetKeyState(win32con.VK_LBUTTON)
                
                # 鼠标按下
                if left_button_state < 0:
                    current_time = time.time()
                    # 确保两次点击之间有足够的时间间隔（防止重复触发）
                    if current_time - last_click_time > 0.5:
                        last_click_time = current_time
                        # 等待一小段时间让点击完成
                        time.sleep(0.3)  # 增加等待时间，确保点击已完成
                        
                        # 获取当前窗口类名
                        hwnd = win32gui.GetForegroundWindow()
                        class_name = win32gui.GetClassName(hwnd)
                        
                        # 打印调试信息（临时）
                        print(f"点击的窗口类名: {class_name}")
                        
                        # 检查是否是文件名输入框（扩展检测范围）
                        # 增加对Qt应用程序窗口类的支持
                        if ("EDIT" in class_name or 
                            "ComboBox" in class_name or 
                            "RichEdit" in class_name or
                            "TextBox" in class_name or
                            "Chrome_RenderWidgetHostHWND" in class_name or  # Chrome浏览器的输入框
                            "Qt51515QWindow" in class_name or  # Qt应用程序窗口
                            "Qt51515QWindowIcon" in class_name or  # Qt应用程序图标窗口
                            "Qt51515QWindowPopupSaveBits" in class_name or  # Qt应用程序弹出窗口
                            "#32770" in class_name):  # 对话框窗口
                            
                            # 获取文件名前缀
                            prefix = self.filename_prefix_var.get()
                            
                            # 保存当前剪贴板内容
                            old_clipboard = ""
                            with self.clipboard_lock:
                                old_clipboard = pyperclip.paste()
                            
                            # 方法1: 使用与自动复制功能相同的方法获取输入框内容
                            # 先全选当前内容 (Ctrl+A)
                            keyboard.send('ctrl+a')
                            time.sleep(0.2)  # 增加等待时间，确保全选完成
                            
                            # 复制选中内容 (Ctrl+C)
                            keyboard.send('ctrl+c')
                            time.sleep(0.3)  # 增加等待时间，确保复制完成
                            
                            # 获取复制的内容
                            current_text = ""
                            with self.clipboard_lock:
                                current_text = pyperclip.paste()
                            
                            # 打印调试信息（临时）
                            print(f"获取到的输入框内容: {current_text}")
                            
                            # 处理文本编码问题
                            try:
                                # 过滤掉代理对字符和其他非法字符
                                current_text = ''.join(char for char in current_text if ord(char) < 0xD800 or ord(char) > 0xDFFF)
                                # 确保文本是有效的UTF-8
                                current_text = current_text.encode('utf-8', errors='ignore').decode('utf-8')
                            except Exception as e:
                                print(f"处理文本编码时出错: {e}")
                                current_text = ""
                            
                            # 如果当前输入框有内容，则拼接前缀和当前内容
                            if current_text and current_text != old_clipboard:
                                new_text = f"{prefix}_{current_text}"
                            else:
                                new_text = prefix
                            
                            # 方法2: 使用模拟按键设置新文本
                            # 先全选当前内容 (Ctrl+A)
                            keyboard.send('ctrl+a')
                            time.sleep(0.2)  # 增加等待时间，确保全选完成
                            # 删除选中内容 (Delete)
                            keyboard.send('delete')
                            time.sleep(0.2)  # 增加等待时间，确保删除完成
                            # 输入新文本
                            keyboard.write(new_text)
                            
                            # 方法3: 使用Windows API设置文本（作为备份）
                            try:
                                # 清空输入框
                                self.user32.SendMessageW(hwnd, win32con.WM_SETTEXT, 0, "")
                                # 设置新文本
                                self.user32.SendMessageW(hwnd, win32con.WM_SETTEXT, 0, new_text)
                            except:
                                pass
                            
                            # 添加时间戳到历史记录
                            timestamp = datetime.now().strftime("%H:%M:%S")
                            self.add_to_history(f"{timestamp}: [补全文件名] {new_text}")
                            
                            # 恢复原始剪贴板内容
                            with self.clipboard_lock:
                                # 如果启用了粘贴后清空剪贴板，则清空剪贴板
                                if self.clear_after_paste:
                                    # 清空剪贴板
                                    pyperclip.copy('')
                                    self.root.clipboard_clear()
                                    self.root.update()
                                    self.clipboard_cache = ""
                                    
                                    # 添加时间戳到历史记录
                                    self.add_to_history(f"{timestamp}: [剪贴板已清空]")
                                else:
                                    # 恢复原始剪贴板内容
                                    pyperclip.copy(old_clipboard)
                                    self.root.clipboard_clear()
                                    self.root.clipboard_append(old_clipboard)
                                    self.root.update()
                                    self.clipboard_cache = old_clipboard
            except Exception as e:
                # 打印异常信息（临时）
                print(f"补全文件名时出错: {e}")
            time.sleep(0.05)  # 减少CPU使用率
    
    def add_to_history(self, text):
        """添加文本到历史记录，处理可能的编码问题"""
        try:
            # 确保文本是字符串类型
            if not isinstance(text, str):
                text = str(text)
            
            # 尝试处理可能的编码问题
            try:
                # 如果文本包含乱码，尝试使用不同的编码方式处理
                if any(ord(c) > 0xFFFF for c in text):
                    # 尝试将文本转换为UTF-8编码
                    text = text.encode('utf-8', errors='ignore').decode('utf-8')
            except Exception as e:
                print(f"处理文本编码时出错: {e}")
            
            # 添加时间戳到历史记录
            self.history_text.insert("1.0", text + "\n")
        except Exception as e:
            print(f"添加历史记录时出错: {e}")
    
    def clear_history(self):
        """清空复制历史记录"""
        self.history_text.delete(1.0, tk.END)
    
    def clear_clipboard(self):
        """清空剪贴板内容"""
        try:
            # 使用锁确保线程安全
            with self.clipboard_lock:
                # 使用pyperclip清空剪贴板
                pyperclip.copy('')
                
                # 同时使用Tkinter的剪贴板功能作为备份
                self.root.clipboard_clear()
                self.root.update()
                
                # 清空剪贴板缓存
                self.clipboard_cache = ""
                
                # 添加时间戳到历史记录
                timestamp = datetime.now().strftime("%H:%M:%S")
                self.add_to_history(f"{timestamp}: [剪贴板已清空]")
        except Exception:
            pass
    
    def on_closing(self):
        self.copy_mode = False
        self.paste_mode = False
        self.save_mode = False
        self.filename_mode = False
        self.root.destroy()
    
    def on_history_click(self, event):
        """处理历史记录点击事件"""
        try:
            # 获取点击位置的行号
            index = self.history_text.index(f"@{event.x},{event.y}")
            line_start = self.history_text.index(f"{index} linestart")
            line_end = self.history_text.index(f"{index} lineend")
            
            # 获取该行的内容
            line_content = self.history_text.get(line_start, line_end)
            
            # 提取时间戳和实际内容
            # 格式通常是 "HH:MM:SS: 实际内容"
            parts = line_content.split(": ", 1)
            if len(parts) > 1:
                actual_content = parts[1]
                # 复制到剪贴板
                self.copy_to_clipboard(actual_content)
                
                # 高亮显示选中的行（可选）
                self.history_text.tag_remove("highlight", "1.0", tk.END)
                self.history_text.tag_add("highlight", line_start, line_end)
                self.history_text.tag_config("highlight", background="lightblue")
                
                # 如果启用了粘贴后清空剪贴板，则提示用户
                if self.clear_after_paste:
                    self.status_var.set("已复制到剪贴板，粘贴后将自动清空")
                else:
                    self.status_var.set("已复制到剪贴板")
        except Exception as e:
            print(f"点击历史记录时出错: {e}")
    
    def on_filename_prefix_click(self, event):
        """处理文件名前缀下拉框点击事件"""
        try:
            # 获取当前剪贴板内容
            with self.clipboard_lock:
                clipboard_content = self.clipboard_cache if self.clipboard_cache else pyperclip.paste()

            if clipboard_content:
                # 设置下拉框内容为剪贴板内容
                self.filename_prefix_var.set(clipboard_content)

                # 添加时间戳到历史记录
                timestamp = datetime.now().strftime("%H:%M:%S")
                self.add_to_history(f"{timestamp}: [文件名前缀已更新] {clipboard_content}")

                # 更新状态提示
                self.status_var.set("文件名前缀已更新")

                # 如果启用了粘贴后清空剪贴板，则清空剪贴板
                if self.clear_after_paste:
                    with self.clipboard_lock:
                        pyperclip.copy('')
                        self.root.clipboard_clear()
                        self.root.update()
                        self.clipboard_cache = ""
                        self.add_to_history(f"{timestamp}: [剪贴板已清空]")
                        self.status_var.set("文件名前缀已更新，剪贴板已清空")
        except Exception as e:
            print(f"更新文件名前缀时出错: {e}")
    
    def run(self):
        self.root.mainloop()

if __name__ == "__main__":
    app = AutoCopyApp()
    app.run() 