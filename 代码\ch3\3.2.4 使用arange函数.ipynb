{"cells": [{"cell_type": "code", "execution_count": 3, "id": "6f8d2806", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[0 1 2 3 4 5 6 7 8 9]\n", "[1 3 5 7 9]\n", "[ 1 -2 -5 -8]\n", "[ 1.  0. -1. -2. -3. -4. -5. -6. -7. -8. -9.]\n", "float64\n"]}], "source": ["import numpy as np\n", "a = np.arange(10)\n", "print(a)\n", "b = np.arange(1, 10, 2)\n", "print(b)\n", "c = np.arange(1, -10, -3)\n", "print(c)\n", "d = np.arange(1, -10, -1 , dtype=float)\n", "print(d)\n", "print(d.dtype)"]}, {"cell_type": "code", "execution_count": 4, "id": "d487557a", "metadata": {}, "outputs": [], "source": ["d = np.arange(1, -10, -1 , dtype=float)"]}, {"cell_type": "code", "execution_count": 5, "id": "5eebdc0b", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([ 1.,  0., -1., -2., -3., -4., -5., -6., -7., -8., -9.])"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["d"]}, {"cell_type": "code", "execution_count": null, "id": "2b4e2efd", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}