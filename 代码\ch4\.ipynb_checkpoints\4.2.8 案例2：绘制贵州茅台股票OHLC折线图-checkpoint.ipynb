{"cells": [{"cell_type": "code", "execution_count": 2, "id": "6f568288", "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'df2' is not defined", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[2], line 16\u001b[0m\n\u001b[0;32m     12\u001b[0m df_sorted \u001b[38;5;241m=\u001b[39m df\u001b[38;5;241m.\u001b[39msort_values(by\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mDate\u001b[39m\u001b[38;5;124m'\u001b[39m)\n\u001b[0;32m     14\u001b[0m plt\u001b[38;5;241m.\u001b[39mtitle(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m贵州茅台股票OHLC折线图\u001b[39m\u001b[38;5;124m'\u001b[39m)  \u001b[38;5;66;03m# 添加图表标题\u001b[39;00m\n\u001b[1;32m---> 16\u001b[0m plt\u001b[38;5;241m.\u001b[39mplot(\u001b[43mdf2\u001b[49m[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mDate\u001b[39m\u001b[38;5;124m'\u001b[39m], df_sorted[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mOpen\u001b[39m\u001b[38;5;124m'\u001b[39m], label\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m开盘价\u001b[39m\u001b[38;5;124m'\u001b[39m) \n\u001b[0;32m     17\u001b[0m plt\u001b[38;5;241m.\u001b[39mplot(df2[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mDate\u001b[39m\u001b[38;5;124m'\u001b[39m], df_sorted[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mHigh\u001b[39m\u001b[38;5;124m'\u001b[39m], label\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m最高价\u001b[39m\u001b[38;5;124m'\u001b[39m)\n\u001b[0;32m     18\u001b[0m plt\u001b[38;5;241m.\u001b[39mplot(df2[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mDate\u001b[39m\u001b[38;5;124m'\u001b[39m], df_sorted[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mLow\u001b[39m\u001b[38;5;124m'\u001b[39m], label\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m最低价\u001b[39m\u001b[38;5;124m'\u001b[39m)\n", "\u001b[1;31mNameError\u001b[0m: name 'df2' is not defined"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1500x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "import pandas as pd\n", "\n", "plt.rcParams['font.family'] = ['SimHei']  # 设置中文字体\n", "plt.rcParams['axes.unicode_minus'] = False  # 设置负号显示\n", "\n", "# 设置图表大小\n", "plt.figure(figsize=(15, 5))\n", "\n", "f = r'data\\贵州茅台股票历史交易数据.csv'\n", "df = pd.read_csv(f, sep=',', encoding='gbk', header=0)\n", "df_sorted = df.sort_values(by='Date')\n", "\n", "plt.title('贵州茅台股票OHLC折线图')  # 添加图表标题\n", "\n", "plt.plot(df_sorted['Date'], df_sorted['Open'], label='开盘价') \n", "plt.plot(df_sorted['Date'], df_sorted['High'], label='最高价')\n", "plt.plot(df_sorted['Date'], df_sorted['Low'], label='最低价')\n", "plt.plot(df_sorted['Date'], df_sorted['Close'], label='收盘价') \n", "\n", "plt.title('贵州茅台股票OHLC折线图')  # 添加图表标题\n", "plt.ylabel('成交量')  # 添加y轴标题\n", "plt.xlabel('交易日期')  # 添加x轴标题\n", "plt.xticks(rotation=40)\n", "plt.legend()  # 设置图例\n", "\n", "# 保存图片\n", "plt.savefig('贵州茅台股票OHLC折线图', dpi=200)\n", "plt.show()  # 显示图形"]}, {"cell_type": "code", "execution_count": null, "id": "20ad8729", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}