# -*- coding: utf-8 -*-
import os
import time
import subprocess
from tkinter import filedialog
import tkinter as tk
import datetime
import json


def log_successful_extraction(file_path, extract_to, password_used=None):
    """记录成功解压的文件信息"""
    log_file = os.path.join(os.path.dirname(extract_to), 'unzip_log.json')
    log_entry = {
        'file_name': os.path.basename(file_path),
        'original_path': file_path,
        'extract_to': extract_to,
        'password_used': password_used,
        'timestamp': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    }
    
    try:
        # 读取现有日志
        if os.path.exists(log_file):
            with open(log_file, 'r', encoding='utf-8') as f:
                log_data = json.load(f)
        else:
            log_data = []
            
        # 添加新记录
        log_data.append(log_entry)
        
        # 保存日志
        with open(log_file, 'w', encoding='utf-8') as f:
            json.dump(log_data, f, ensure_ascii=False, indent=2)
            
        print(f"解压记录已保存到：{log_file}")
    except Exception as e:
        print(f"保存解压记录时出错：{e}")


def unzip_files(folder_path):
    # 获取密码字典文件的路径
    password_dict_path = os.path.join(folder_path, 'passdict.txt')
    has_password_dict = os.path.isfile(password_dict_path)
    if not has_password_dict:
        print(f"未找到密码字典文件：{password_dict_path}，将跳过密码字典尝试")

    # 用于记录本次运行中新解压的文件夹
    newly_extracted_dirs = set()

    # 遍历所有子文件夹和文件
    for root, dirs, files in os.walk(folder_path):
        # 检查当前目录是否是新解压的文件夹的子目录
        is_in_new_dir = False
        for new_dir in newly_extracted_dirs:
            if root.startswith(new_dir + os.sep):
                print(f"跳过新解压文件夹中的内容：{root}")
                dirs.clear()  # 跳过子目录
                is_in_new_dir = True
                break
        
        if is_in_new_dir:
            continue
            
        for file in files:
            file_path = os.path.join(root, file)
            if file.endswith(('.zip', '.ZIP', '.rar', '.RAR', '.tar', '.TAR', '.7z', '.7Z', '.7z.001', '.zip.001', '.z01', '.z02')):
                # 检查是否是分卷压缩包的主文件
                if not is_split_archive_main_file(file_path):
                    print(f"跳过分卷压缩包的非主文件：{file_path}")
                    continue

                print(f"\n正在处理压缩文件：{file_path}")

                # 1. 首先尝试查找解压密码.txt等文件
                extract_password = get_extract_password(root, folder_path)
                if extract_password is not None:  # 找到了解压密码文件
                    if extract_password == '':  # 空字符串表示没找到有效密码
                        pass  # 继续尝试其他方法
                    else:  # 找到了具体密码
                        print(f"使用解压密码文件中的密码尝试解压...")
                        if extract_with_7z(file_path, root, extract_password):
                            print(f"成功解压文件：{file_path}")
                            # 记录新解压的目录
                            newly_extracted_dirs.add(root)
                            continue
                elif extract_password is None:  # 解压密码文件指示无密码
                    print("解压密码文件指示无密码，直接尝试无密码解压...")
                    if extract_without_password(file_path, root):
                        print(f"成功解压文件：{file_path}")
                        # 记录新解压的目录
                        newly_extracted_dirs.add(root)
                        continue

                # 2. 然后尝试查找 password.txt
                password = get_password(root, folder_path)
                if password is not None:  # 找到了密码文件
                    if password == '':  # 空字符串表示没找到有效密码
                        pass  # 继续尝试其他方法
                    else:  # 找到了具体密码
                        print(f"使用 password.txt 中的密码尝试解压...")
                        if extract_with_7z(file_path, root, password):
                            print(f"成功解压文件：{file_path}")
                            # 记录新解压的目录
                            newly_extracted_dirs.add(root)
                            continue
                elif password is None:  # 密码文件指示无密码
                    print("password.txt文件指示无密码，直接尝试无密码解压...")
                    if extract_without_password(file_path, root):
                        print(f"成功解压文件：{file_path}")
                        # 记录新解压的目录
                        newly_extracted_dirs.add(root)
                        continue

                # 3. 尝试使用密码字典（如果存在）
                if has_password_dict:
                    print("尝试使用密码字典解压...")
                    if try_password_dict(file_path, root, password_dict_path):
                        print(f"成功解压文件：{file_path}")
                        # 记录新解压的目录
                        newly_extracted_dirs.add(root)
                        continue

                # 4. 尝试无密码解压
                print("尝试无密码解压...")
                if extract_without_password(file_path, root):
                    print(f"成功解压文件：{file_path}")
                    # 记录新解压的目录
                    newly_extracted_dirs.add(root)
                    continue

                # 如果所有方法都失败，输出提示并继续处理下一个文件
                print(f"警告：文件 {file_path} 所有解压方法都失败，跳过此文件")
                continue


def get_dir_size(path):
    """获取目录总大小"""
    total_size = 0
    for dirpath, dirnames, filenames in os.walk(path):
        for f in filenames:
            fp = os.path.join(dirpath, f)
            try:
                total_size += os.path.getsize(fp)
            except OSError:
                continue
    return total_size


def is_split_archive_main_file(file_path):
    """判断是否是分卷压缩包的主文件（第一个文件）"""
    file_name = os.path.basename(file_path)
    dir_path = os.path.dirname(file_path)

    import re

    # 检查 .part01.rar, .part001.rar 等格式（只有第一个是主文件）
    if re.search(r'\.part0*1\.(rar|zip|7z|tar)$', file_name, re.IGNORECASE):
        return True

    # 检查 .001, .7z.001, .zip.001 等格式
    if re.search(r'\.(rar|zip|7z|tar)\.001$', file_name, re.IGNORECASE):
        return True

    # 检查 .zip 文件（可能是 .z01 系列的主文件）
    if file_name.lower().endswith('.zip'):
        base_name = os.path.splitext(file_name)[0]
        # 检查是否存在对应的 .z01 文件
        z01_file = os.path.join(dir_path, base_name + '.z01')
        if os.path.exists(z01_file):
            return True  # 这是 .z01 系列的主文件
        else:
            # 检查是否存在其他 .zXX 文件
            has_z_files = any(f.startswith(base_name + '.z') and f != file_name
                             for f in os.listdir(dir_path))
            if not has_z_files:
                return True  # 这是单个 zip 文件
            else:
                return False  # 这不是主文件

    # 检查单个文件（非分卷）
    if re.search(r'\.(rar|zip|7z|tar)$', file_name, re.IGNORECASE):
        base_name = os.path.splitext(file_name)[0]
        ext = os.path.splitext(file_name)[1]

        # 检查是否存在分卷文件
        has_split_files = any(
            f.startswith(base_name) and (
                re.search(r'\.part\d+' + re.escape(ext) + r'$', f, re.IGNORECASE) or
                re.search(re.escape(ext) + r'\.\d+$', f, re.IGNORECASE)
            ) for f in os.listdir(dir_path)
        )

        if not has_split_files:
            return True  # 这是单个文件

    return False


def get_all_split_archive_files(file_path):
    """获取分卷压缩包的所有相关文件（更安全的版本）"""
    dir_path = os.path.dirname(file_path)
    file_name = os.path.basename(file_path)
    related_files = []

    import re

    # 处理 .part01.rar 格式
    if re.search(r'\.part\d+\.(rar|zip|7z|tar)$', file_name, re.IGNORECASE):
        base_match = re.match(r'(.+)\.part(\d+)\.(rar|zip|7z|tar)$', file_name, re.IGNORECASE)
        if base_match:
            base_name = base_match.group(1)
            ext = base_match.group(3)

            # 首先找到所有可能的分卷文件
            potential_files = []
            for f in os.listdir(dir_path):
                part_match = re.match(rf'{re.escape(base_name)}\.part(\d+)\.{ext}$', f, re.IGNORECASE)
                if part_match:
                    part_num = int(part_match.group(1))
                    potential_files.append((part_num, f))

            # 排序并检查连续性
            potential_files.sort()
            if potential_files:
                # 找到连续的分卷序列
                consecutive_files = []
                expected_num = 1
                for part_num, filename in potential_files:
                    if part_num == expected_num:
                        consecutive_files.append(filename)
                        expected_num += 1
                    elif part_num > expected_num:
                        # 如果跳过了某个序号，停止添加
                        break

                # 只添加连续的分卷文件
                for filename in consecutive_files:
                    related_files.append(os.path.join(dir_path, filename))

    # 处理 .7z.001 格式
    elif re.search(r'\.(rar|zip|7z|tar)\.\d+$', file_name, re.IGNORECASE):
        base_match = re.match(r'(.+)\.(rar|zip|7z|tar)\.(\d+)$', file_name, re.IGNORECASE)
        if base_match:
            base_name = base_match.group(1)
            ext = base_match.group(2)
            current_num = int(base_match.group(3))

            # 只处理从 001 开始的分卷
            if current_num == 1:
                # 找到所有连续的分卷文件
                num = 1
                while True:
                    expected_file = f"{base_name}.{ext}.{num:03d}"
                    expected_path = os.path.join(dir_path, expected_file)
                    if os.path.exists(expected_path):
                        related_files.append(expected_path)
                        num += 1
                    else:
                        break

    # 处理 .z01, .z02 格式（主文件是 .zip）
    elif file_name.lower().endswith('.zip'):
        base_name = os.path.splitext(file_name)[0]

        # 检查是否存在 .z01 文件，如果存在才认为是分卷
        z01_file = os.path.join(dir_path, f"{base_name}.z01")
        if os.path.exists(z01_file):
            # 包含主 .zip 文件
            related_files.append(file_path)

            # 查找连续的 .z01, .z02, .z03... 文件
            num = 1
            while True:
                z_file = os.path.join(dir_path, f"{base_name}.z{num:02d}")
                if os.path.exists(z_file):
                    related_files.append(z_file)
                    num += 1
                else:
                    break
        else:
            # 单个 zip 文件
            related_files.append(file_path)

    # 如果没有找到相关文件，说明是单个文件
    if not related_files:
        related_files.append(file_path)

    return related_files


def delete_split_archive_files(file_path):
    """删除分卷压缩文件的所有部分"""
    related_files = get_all_split_archive_files(file_path)

    for file_to_delete in related_files:
        try:
            if os.path.exists(file_to_delete):
                os.remove(file_to_delete)
                print(f"已删除分卷文件：{file_to_delete}")
        except Exception as e:
            print(f"删除文件 {file_to_delete} 时出错：{e}")


def extract_with_7z(file_path, extract_to, password):
    """使用给定密码解压文件"""
    print(f"尝试使用密码：{password}")
    
    command = [r'C:\Program Files\7-Zip\7z.exe', 'x', file_path, f'-p{password}', '-aou']
    process = None
    try:
        process = subprocess.Popen(command, cwd=extract_to, stdout=subprocess.PIPE, stderr=subprocess.STDOUT,
                                 universal_newlines=True, creationflags=subprocess.CREATE_NO_WINDOW)
        
        start_time = time.time()
        last_size = get_dir_size(extract_to)
        last_check_time = start_time
        
        while True:
            current_time = time.time()
            # 每10秒检查一次目录大小
            if current_time - last_check_time >= 10:
                current_size = get_dir_size(extract_to)
                if current_size > last_size:
                    # 目录在增长，重置计时器
                    start_time = current_time
                    last_size = current_size
                last_check_time = current_time
            
            # 检查是否超时
            if current_time - start_time > 300:
                print("解压操作超时，终止当前尝试")
                process.kill()
                return False
                
            # 检查进程是否结束
            if process.poll() is not None:
                break
                
            # 读取并显示输出
            while True:
                line = process.stdout.readline()
                if not line:
                    break
                print(line, end='')
            
            time.sleep(0.1)
            
        if process.returncode == 0:
            print("解压成功！")
            # 记录成功解压的文件
            log_successful_extraction(file_path, extract_to, password)

            # 删除压缩包文件（包括所有分卷文件）
            delete_split_archive_files(file_path)

            return True
        print("解压失败")
        return False
    except PermissionError as e:
        print(f"权限错误：{e}，请以管理员身份运行程序")
        return False
    except FileNotFoundError:
        print("未找到7-Zip程序，请确保已正确安装7-Zip")
        return False
    except Exception as e:
        print(f"解压过程出错：{e}")
        return False
    finally:
        if process:
            try:
                process.kill()
            except:
                pass
    return False


def try_password_dict(file_path, extract_to, password_dict_path):
    """尝试使用密码字典中的密码解压"""
    try:
        with open(password_dict_path, 'r') as f:
            for password in f:
                password = password.strip()
                if not password:
                    continue
                if extract_with_7z(file_path, extract_to, password):
                    return True
    except Exception as e:
        print(f"读取密码字典时出错：{e}")
    return False


def extract_without_password(file_path, extract_to):
    """尝试不使用密码进行解压缩"""
    command = [r'C:\Program Files\7-Zip\7z.exe', 'x', file_path, '-aou']
    process = None
    try:
        process = subprocess.Popen(command, cwd=extract_to, stdout=subprocess.PIPE, stderr=subprocess.STDOUT,
                                 universal_newlines=True, creationflags=subprocess.CREATE_NO_WINDOW)
        
        start_time = time.time()
        last_size = get_dir_size(extract_to)
        last_check_time = start_time
        
        while True:
            current_time = time.time()
            # 每10秒检查一次目录大小
            if current_time - last_check_time >= 10:
                current_size = get_dir_size(extract_to)
                if current_size > last_size:
                    # 目录在增长，重置计时器
                    start_time = current_time
                    last_size = current_size
                last_check_time = current_time
            
            # 检查是否超时
            if current_time - start_time > 300:
                print("解压操作超时，终止当前尝试")
                process.kill()
                return False
                
            # 检查进程是否结束
            if process.poll() is not None:
                break
                
            # 读取并显示输出
            while True:
                line = process.stdout.readline()
                if not line:
                    break
                print(line, end='')
            
            time.sleep(0.1)
            
        if process.returncode == 0:
            print("解压成功（无密码）。")
            # 记录成功解压的文件
            log_successful_extraction(file_path, extract_to)
            # 删除压缩包文件（包括所有分卷文件）
            delete_split_archive_files(file_path)
            return True
        print("解压失败（无密码）。")
        return False
    except PermissionError as e:
        print(f"权限错误：{e}，请以管理员身份运行程序")
        return False
    except FileNotFoundError:
        print("未找到7-Zip程序，请确保已正确安装7-Zip")
        return False
    except Exception as e:
        print(f"解压过程出错：{e}")
        return False
    finally:
        if process:
            try:
                process.kill()
            except:
                pass
    return False


def get_password(folder, original_folder):
    """从当前文件夹及其上级文件夹中查找 password.txt 文件，包括原始选择的文件夹，最多尝试 5 次"""
    retry_count = 0  # 重试计数器
    current_folder = folder

    while current_folder and retry_count < 5:
        password_file = os.path.join(current_folder, 'password.txt')
        if os.path.isfile(password_file):
            print(f"找到密码文件：{password_file}")
            return read_password_from_file(password_file)

        # 如果已经到达原始文件夹，停止搜索
        if current_folder == original_folder:
            break

        current_folder = os.path.dirname(current_folder)  # 移动到上一级目录
        retry_count += 1  # 增加重试次数

    return ''


def read_password_from_file(file_path):
    """从 password.txt 文件中解析格式为 '密码：XXXXXX' 的密码"""
    # 尝试多种编码格式，优先使用ANSI编码
    encodings = ['ansi', 'gbk', 'gb2312', 'utf-8', 'latin-1']

    for encoding in encodings:
        try:
            with open(file_path, 'r', encoding=encoding) as f:
                content = f.read().strip()
                print(f"使用 {encoding} 编码成功读取密码文件")
                print(f"密码文件内容：{content}")

                for line in content.split('\n'):
                    line = line.strip()
                    if line.startswith("密码："):
                        password = line.split("密码：", 1)[1].strip()
                        # 检查是否表示无密码
                        if password.lower() in ['无', 'none', 'null', '空', '']:
                            print("密码文件指示该压缩包无密码")
                            return None  # 返回None表示无密码
                        print(f"从文件中解析到密码：{password}")
                        return password
                    # 也尝试其他可能的格式
                    elif line.startswith("密码:"):
                        password = line.split("密码:", 1)[1].strip()
                        # 检查是否表示无密码
                        if password.lower() in ['无', 'none', 'null', '空', '']:
                            print("密码文件指示该压缩包无密码")
                            return None  # 返回None表示无密码
                        print(f"从文件中解析到密码：{password}")
                        return password
                    elif line and not line.startswith("#"):  # 如果是非空行且不是注释，直接作为密码
                        # 检查是否表示无密码
                        if line.lower() in ['无', 'none', 'null', '空']:
                            print("密码文件指示该压缩包无密码")
                            return None  # 返回None表示无密码
                        print(f"将整行作为密码：{line}")
                        return line

                print("未在密码文件中找到有效密码")
                return ''

        except UnicodeDecodeError:
            print(f"使用 {encoding} 编码读取失败，尝试下一种编码...")
            continue
        except Exception as e:
            print(f"读取密码文件时出错：{e}")
            continue

    print("所有编码格式都无法读取密码文件")
    return ''


def get_extract_password(folder, original_folder):
    """从当前文件夹及其上级文件夹中查找解压密码.txt文件，最多尝试5次"""
    retry_count = 0
    current_folder = folder

    while current_folder and retry_count < 5:
        # 查找多种可能的解压密码文件名
        password_files = [
            '解压密码.txt',
            '解压密码.TXT',
            '密码.txt',
            '密码.TXT',
            'password_extract.txt',
            'extract_password.txt',
            'unzip_password.txt'
        ]

        for password_filename in password_files:
            password_file = os.path.join(current_folder, password_filename)
            if os.path.isfile(password_file):
                print(f"找到解压密码文件：{password_file}")
                password = read_extract_password_from_file(password_file)
                if password:
                    return password

        # 如果已经到达原始文件夹，停止搜索
        if current_folder == original_folder:
            break

        current_folder = os.path.dirname(current_folder)
        retry_count += 1

    return ''


def read_extract_password_from_file(file_path):
    """从解压密码文件中读取密码"""
    # 尝试多种编码格式，优先使用GBK编码处理中文
    encodings = ['gbk', 'gb2312', 'utf-8', 'ansi', 'latin-1']

    for encoding in encodings:
        try:
            with open(file_path, 'r', encoding=encoding) as f:
                content = f.read().strip()

                # 检查是否包含乱码字符
                if contains_garbled_text(content):
                    print(f"使用 {encoding} 编码读取到乱码，尝试下一种编码...")
                    continue

                print(f"使用 {encoding} 编码成功读取解压密码文件")
                print(f"解压密码文件内容：{content}")

                for line in content.split('\n'):
                    line = line.strip()

                    # 支持多种密码格式
                    password_patterns = [
                        "密码：", "密码:", "password:", "Password:", "PASSWORD:",
                        "解压密码：", "解压密码:", "extract password:", "Extract Password:",
                        "unzip password:", "Unzip Password:", "解压码：", "解压码:"
                    ]

                    for pattern in password_patterns:
                        if line.startswith(pattern):
                            password = line.split(pattern, 1)[1].strip()
                            # 检查是否表示无密码
                            if password.lower() in ['无', 'none', 'null', '空', '']:
                                print("解压密码文件指示该压缩包无密码")
                                return None
                            print(f"从解压密码文件中解析到密码：{password}")
                            return password

                    # 如果是非空行且不是注释，直接作为密码
                    if line and not line.startswith("#") and not line.startswith("//"):
                        # 检查是否表示无密码
                        if line.lower() in ['无', 'none', 'null', '空']:
                            print("解压密码文件指示该压缩包无密码")
                            return None
                        print(f"将整行作为解压密码：{line}")
                        return line

                print("未在解压密码文件中找到有效密码")
                return ''

        except UnicodeDecodeError:
            print(f"使用 {encoding} 编码读取解压密码文件失败，尝试下一种编码...")
            continue
        except Exception as e:
            print(f"读取解压密码文件时出错：{e}")
            continue

    print("所有编码格式都无法读取解压密码文件")
    return ''


def contains_garbled_text(text):
    """检测文本是否包含乱码"""
    if not text:
        return False

    # 统计各种字符类型
    ascii_count = 0
    chinese_count = 0
    other_count = 0

    for char in text:
        if ord(char) < 128:  # ASCII字符
            ascii_count += 1
        elif '\u4e00' <= char <= '\u9fff':  # 中文字符
            chinese_count += 1
        else:  # 其他字符
            other_count += 1

    total_chars = len(text)
    if total_chars == 0:
        return False

    # 如果其他字符（可能是乱码）占比超过30%，认为是乱码
    other_ratio = other_count / total_chars

    # 特别检查常见的乱码模式
    garbled_patterns = ['瑙ｅ帇', '瀵嗙爜', '锛�', '鈥�', '鈥�', '鈥�', '锛�']
    for pattern in garbled_patterns:
        if pattern in text:
            return True

    # 检查是否包含大量连续的特殊Unicode字符
    special_unicode_count = 0
    for char in text:
        # 检查是否为常见乱码字符范围
        char_code = ord(char)
        if (0x2000 <= char_code <= 0x206F) or (0x2100 <= char_code <= 0x214F) or (0xFF00 <= char_code <= 0xFFEF):
            special_unicode_count += 1

    special_unicode_ratio = special_unicode_count / total_chars if total_chars > 0 else 0

    return other_ratio > 0.3 or special_unicode_ratio > 0.1


def select_folder():
    """用户选择文件夹，默认打开D:/BaiduYunDownload/"""
    try:
        root = tk.Tk()
        root.withdraw()
        folder_path = filedialog.askdirectory(initialdir="D:\\BaiduYunDownload\\")
        if folder_path:
            unzip_files(folder_path)
    except Exception as e:
        print(f"程序执行出错：{e}")


if __name__ == "__main__":
    select_folder()
