{"cells": [{"cell_type": "code", "execution_count": 7, "id": "1d401a59", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["------df1-------\n", "   apples  oranges  bananas\n", "0       3        0        1\n", "1       2        1        2\n", "2       0        2        1\n", "3       1        3        0\n", "------df2-------\n", "        apples  oranges  bananas\n", "June         3        0        1\n", "Robert       2        1        2\n", "Lily         0        2        1\n", "David        1        3        0\n"]}], "source": ["import pandas as pd\n", "\n", "data ={  'apples': [3, 2, 0, 1],\n", "        'oranges': [0, 1, 2, 3],\n", "        'bananas': [1, 2, 1, 0]\n", "       }\n", "\n", "df1 = pd.DataFrame(data)                   # 使用字典创建DataFrame\n", "print(\"------df1-------\")\n", "print(df1)\n", "\n", "df2 = pd.DataFrame(data, index=['June','<PERSON>','<PERSON>','<PERSON>'])    # 指定行标签创建DataFrame\n", "print(\"------df2-------\")\n", "print(df2)"]}, {"cell_type": "code", "execution_count": null, "id": "33164d46", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}