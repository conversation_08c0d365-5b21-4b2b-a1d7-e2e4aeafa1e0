import os
import shutil
import datetime
import tkinter as tk
from tkinter import filedialog, messagebox

def split_files_into_subfolders(folder_path, max_files_per_folder):
    # 获取文件夹中的所有文件和子文件夹
    all_items = os.listdir(folder_path)
    
    # 过滤出文件（排除子文件夹）并获取修改时间
    files_with_time = []
    for item in all_items:
        if os.path.isfile(os.path.join(folder_path, item)):
            file_path = os.path.join(folder_path, item)
            mod_time = os.path.getmtime(file_path)
            files_with_time.append((item, mod_time))
    
    # 按修改时间排序
    files_with_time.sort(key=lambda x: x[1])
    files = [f[0] for f in files_with_time]
    
    total_files = len(files)
    if total_files <= max_files_per_folder:
        messagebox.showinfo("完成", f"文件夹中只有 {total_files} 个文件，无需分割。")
        return
    
    # 创建进度窗口
    progress_window, progress_bar, status_label, detail_label = create_progress_window("分割文件夹", total_files)
    
    # 计算需要的子文件夹数量
    num_subfolders = (total_files + max_files_per_folder - 1) // max_files_per_folder
    base_folder_name = os.path.basename(folder_path)
    
    # 创建子文件夹（使用01, 02格式命名）
    subfolders = []
    for i in range(1, num_subfolders + 1):
        start_idx = (i-1) * max_files_per_folder
        end_idx = min(i * max_files_per_folder, total_files) - 1
        
        # 获取该范围内第一个和最后一个文件的时间
        start_time = datetime.datetime.fromtimestamp(files_with_time[start_idx][1])
        end_time = datetime.datetime.fromtimestamp(files_with_time[end_idx][1])
        
        # 如果开始和结束时间在同一个月
        if start_time.year == end_time.year and start_time.month == end_time.month:
            folder_name = f"{base_folder_name}_{start_time.strftime('%Y%m')}_{i:02d}"
        else:
            folder_name = f"{base_folder_name}_{start_time.strftime('%Y%m')}-{end_time.strftime('%Y%m')}_{i:02d}"
        
        subfolder_path = os.path.join(folder_path, folder_name)
        os.makedirs(subfolder_path, exist_ok=True)
        subfolders.append(subfolder_path)
    
    # 将文件分配到子文件夹
    for i, file in enumerate(files):
        status_label.config(text=f"正在移动: {file}")
        file_time = datetime.datetime.fromtimestamp(files_with_time[i][1])
        current_folder_index = i // max_files_per_folder
        folder_name = os.path.basename(subfolders[current_folder_index])
        detail_label.config(text=f"进度: {i+1}/{total_files}\n"
                                f"时间: {file_time.strftime('%Y-%m-%d %H:%M:%S')}\n"
                                f"目标文件夹: {folder_name}")
        
        subfolder_index = i // max_files_per_folder
        src_path = os.path.join(folder_path, file)
        dst_path = os.path.join(subfolders[subfolder_index], file)
        shutil.move(src_path, dst_path)
        
        progress_bar['value'] = i + 1
        progress_window.update()
    
    # 准备完成信息
    folder_info = "\n".join([f"文件夹{i+1}: {os.path.basename(subfolder)}" 
                            for i, subfolder in enumerate(subfolders)])
    
    progress_window.destroy()
    messagebox.showinfo("完成", 
                      f"已将 {total_files} 个文件按时间顺序分配到 {num_subfolders} 个子文件夹中。\n"
                      f"每个子文件夹最多 {max_files_per_folder} 个文件。\n"
                      f"\n文件夹列表：\n{folder_info}")

def create_progress_window(title, total_files):
    window = tk.Tk()
    window.title(title)
    
    progress_bar = tk.ttk.Progressbar(window, orient="horizontal", length=300, mode="determinate")
    progress_bar.pack(pady=10)
    
    status_label = tk.Label(window, text="准备中...")
    status_label.pack(pady=10)
    
    detail_label = tk.Label(window, text="")
    detail_label.pack(pady=10)
    
    return window, progress_bar, status_label, detail_label 