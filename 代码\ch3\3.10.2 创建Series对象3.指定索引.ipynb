{"cells": [{"cell_type": "code", "execution_count": 1, "id": "224eb344", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["a    3\n", "b    2\n", "c    0\n", "d    1\n", "dtype: int64\n"]}], "source": ["import pandas as pd\n", "apples = pd.Series([3,2,0,1], index=['a','b','c','d'])\n", "print(apples)"]}, {"cell_type": "code", "execution_count": null, "id": "93050363", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}