import os
import json
import shutil
import logging
from datetime import datetime
from tkinter import Tk, filedialog

def setup_logging(work_dir):
    """设置日志记录"""
    # 创建logs目录
    logs_dir = os.path.join(work_dir, 'logs')
    os.makedirs(logs_dir, exist_ok=True)
    
    # 创建日志文件名，包含时间戳
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    log_file = os.path.join(logs_dir, f'move_log_{timestamp}.txt')
    
    # 配置日志记录器
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler()  # 同时输出到控制台
        ]
    )
    return log_file

def select_folder(prompt):
    root = Tk()
    root.withdraw()
    folder_path = filedialog.askdirectory(title=prompt)
    return folder_path

def save_folder_names_to_dict(folder_path, output_path):
    folder_names = {}
    # 定义标点符号，不包括下划线和空格
    punctuation = '.,()[]{}!@#$%^&*+=<>?/\\'
    
    for entry in os.scandir(folder_path):
        if entry.is_dir() and entry.name.lower() not in ('t', 'temp', 't1', 't2'):
            # 将文件夹名称按空格分隔，保持原始格式
            keywords = [k for k in entry.name.split() if not all(c in punctuation for c in k)]
            for keyword in keywords:
                if keyword.lower() not in folder_names:
                    folder_names[keyword.lower()] = []
                folder_names[keyword.lower()].append(entry.name)
    
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(folder_names, f, ensure_ascii=False, indent=4)
    print(f"文件夹名称已保存到 {output_path}")

def move_matching_folders(source_folder, target_folder, dict_path):
    with open(dict_path, 'r', encoding='utf-8') as f:
        folder_names = json.load(f)
    
    # 确保临时目录存在
    temp_dir = os.path.join(source_folder, 'temp')
    if not os.path.exists(temp_dir):
        os.makedirs(temp_dir)
        logging.info(f"创建临时目录: {temp_dir}")
    
    moved_count = 0
    
    # 第一阶段：处理所有文件夹
    logging.info("\n=== 第一阶段：处理文件夹 ===")
    for root, dirs, _ in os.walk(target_folder):
        for dir_name in dirs[:]:
            dir_path = os.path.join(root, dir_name)
            entry_name_lower = dir_name.lower().replace('_', ' ')
            level = dir_path[len(target_folder):].count(os.sep)
            logging.info(f"\n正在处理文件夹 (层级 {level}): {dir_path}")
            
            if process_entry(dir_path, entry_name_lower, folder_names, temp_dir, target_folder):
                moved_count += 1
                dirs.remove(dir_name)
    
    logging.info(f"\n文件夹处理完成。共移动了 {moved_count} 个文件夹")
    
    # 第二阶段：处理所有文件
    logging.info("\n=== 第二阶段：处理文件 ===")
    file_count = 0
    for root, _, files in os.walk(target_folder):
        for file_name in files[:]:
            file_path = os.path.join(root, file_name)
            name_without_ext = os.path.splitext(file_name)[0]
            entry_name_lower = name_without_ext.lower().replace('_', ' ')
            level = file_path[len(target_folder):].count(os.sep)
            logging.info(f"\n正在处理文件 (层级 {level}): {file_path}")
            
            if process_entry(file_path, entry_name_lower, folder_names, temp_dir, target_folder):
                file_count += 1
    
    logging.info(f"\n文件处理完成。共移动了 {file_count} 个文件")
    logging.info(f"\n总计移动了 {moved_count + file_count} 个项目")

def process_entry(entry_path, entry_name_lower, folder_names, temp_dir, target_folder):
    """处理单个文件或文件夹的移动"""
    matched = False
    
    for keyword in folder_names:
        normalized_keyword = keyword.replace('_', ' ')
        if normalized_keyword in entry_name_lower:
            logging.info(f"找到匹配关键字: {keyword}")
            for folder_name in folder_names[keyword]:
                if normalized_keyword in entry_name_lower:
                    rel_path = os.path.relpath(entry_path, target_folder)
                    target_subfolder = os.path.join(temp_dir, rel_path)
                    logging.info(f"准备移动: {entry_path} -> {target_subfolder}")
                    
                    try:
                        os.makedirs(os.path.dirname(target_subfolder), exist_ok=True)
                        if os.path.exists(target_subfolder):
                            logging.warning(f"目标路径已存在: {target_subfolder}")
                        else:
                            shutil.move(entry_path, target_subfolder)
                            logging.info(f"成功移动项目")
                            matched = True
                            break
                    except Exception as e:
                        logging.error(f"移动失败: {str(e)}")
                        logging.error(f"源路径: {entry_path}")
                        logging.error(f"目标路径: {target_subfolder}")
            
            if matched:
                break
    
    return matched

def main():
    work_dir = r'z:\work'
    if not os.path.exists(work_dir):
        os.makedirs(work_dir)
    
    # 设置日志记录
    log_file = setup_logging(work_dir)
    logging.info(f"开始运行程序，日志文件: {log_file}")
    
    source_folder = select_folder("请选择源文件夹")
    logging.info(f"选择的源文件夹: {source_folder}")
    
    source_folder_name = os.path.basename(source_folder)
    dict_path = os.path.join(work_dir, f'{source_folder_name}.json')
    save_folder_names_to_dict(source_folder, dict_path)
    
    target_folder = select_folder("请选择目标文件夹")
    logging.info(f"选择的目标文件夹: {target_folder}")
    
    move_matching_folders(source_folder, target_folder, dict_path)
    logging.info("程序运行完成")

if __name__ == "__main__":
    main()
