import os
import urllib.parse
import tkinter as tk
from tkinter import filedialog
import re


def get_video_files(directory):
    video_files = []
    video_extensions = ('.mp4', '.avi', '.mkv', '.mov')

    for root, _, files in os.walk(directory):
        for file in files:
            if file.endswith(video_extensions):
                full_path = os.path.join(root, file)
                video_files.append(full_path)
                print(f"Found video file: {full_path}")  # 打印搜集到的视频文件完整路径

    return video_files


def create_strm_files(video_files, input_base_folder, output_base_folder):
    #base_url = "http://192.168.2.25:5678/d/"  #这是小雅直连
    base_url = "http://192.168.2.25:5244/d"   #这是Alist套娃小雅

    for full_path in video_files:
        # 替换路径中的反斜杠为正斜杠
        full_path_unix = full_path.replace("\\", "/")
        print(f"Converted path: {full_path_unix}")  # 打印转换后的路径

        # 去掉盘符部分
        path_without_drive = full_path_unix.split(":/", 1)[-1]
        # 去掉指定的路径部分 "d/CloudDrive/"
        path_without_drive = path_without_drive.replace("CloudDrive", "")
        path_without_drive = path_without_drive.replace("WebDAV", "")
        print(path_without_drive)

        # 分割路径并去掉最后一个元素
        folders = path_without_drive.split('/')

        print(f"Original folders: {folders}")

        # 移除最后一个元素，得到新的 folders 列表
        new_folders = folders[:-1]
        print(f"New folders (without last): {new_folders}")

        # 确认是否还有元素
        if not new_folders:
            continue

        # 计算相对于输入基础文件夹的相对路径
        relative_path = "/".join(folders)
        # 对相对路径进行编码以确保URL安全
        encoded_path = urllib.parse.quote(relative_path)
        # 替换编码后的路径中的 `�` 为 `GBP`
        encoded_path_replaced = encoded_path.replace('%EF%BF%BD', 'GBP')
        url = base_url + encoded_path_replaced

        # 解析新的路径，判断新的最后一个文件夹名是否为 "s数字" 形式
        last_folder = new_folders[-1]
        second_last_folder = new_folders[-2] if len(new_folders) > 1 else ""

        # 判断是否为 "s数字" 形式或包含 "Season"
        if re.match(r'^(s\d{1,2}|season\s?\d{1,2})$', last_folder, re.IGNORECASE):
            # 使用新的倒数第二个文件夹名创建一个文件夹
            parent_folder = os.path.join(output_base_folder, second_last_folder)
            if not os.path.exists(parent_folder):
                os.makedirs(parent_folder)
                print(f"Created directory: {parent_folder}")

            # 在上一步的文件夹中创建一个以新的最后一个文件夹名命名的子文件夹
            output_folder = os.path.join(parent_folder, last_folder)
            if not os.path.exists(output_folder):
                os.makedirs(output_folder)
                print(f"Created directory: {output_folder}")

        else:
            # 如果新的最后一个文件夹名不符合 "s数字" 形式，直接创建以新的最后一个文件夹名为名的文件夹
            output_folder = os.path.join(output_base_folder, last_folder)
            if not os.path.exists(output_folder):
                os.makedirs(output_folder)
                print(f"Created directory: {output_folder}")

        # 在指定的文件夹中创建包含编码URL的 .strm 文件
        strm_file_name = os.path.splitext(os.path.basename(full_path_unix))[0] + ".strm"
        strm_file_path = os.path.join(output_folder, strm_file_name)

        with open(strm_file_path, 'w') as f:
            f.write(url)

        print(f"Created .strm file: {strm_file_path} with URL: {url}")


def main():
    root = tk.Tk()
    root.withdraw()

    # 第一次选择文件夹
    folder_selected_1 = filedialog.askdirectory(title="选择包含视频文件的文件夹")

    if folder_selected_1:
        video_files = get_video_files(folder_selected_1)

        # 第二次选择文件夹
        folder_selected_2 = filedialog.askdirectory(title="选择创建strm文件的目标文件夹")

        if folder_selected_2:
            create_strm_files(video_files, folder_selected_1, folder_selected_2)
            print(f".strm files created in {folder_selected_2}")


if __name__ == "__main__":
    main()
