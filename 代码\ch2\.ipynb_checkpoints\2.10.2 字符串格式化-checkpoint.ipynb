{"cells": [{"cell_type": "code", "execution_count": null, "id": "d1591e92", "metadata": {}, "outputs": [], "source": ["name = '<PERSON>'\n", "age = 18\n", "s = '她的年龄是{0}岁。'.format(age)\n", "print(s)\n", "s = '{0}芳龄是{1}岁。'.format(name, age)\n", "print(s)\n", "s = '{1}芳龄是{0}岁。'.format(age, name)\n", "print(s)\n", "s = '{n}芳龄是{a}岁。'.format(n=name, a=age) \n", "print(s)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}