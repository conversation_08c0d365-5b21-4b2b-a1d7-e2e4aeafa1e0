import os
import tkinter as tk
from tkinter import filedialog
from tkinter import messagebox


def truncate_filename(filename, max_length=100):
    """截短文件名，保留扩展名"""
    name, ext = os.path.splitext(filename)
    if len(filename) <= max_length:
        return filename
    
    # 保留扩展名，截短主文件名
    available_length = max_length - len(ext)
    truncated_name = name[:available_length-3] + '...'
    return truncated_name + ext


def rename_files(folder_path):
    renamed_files = []  # 用于存储重命名后的文件列表
    for root, dirs, files in os.walk(folder_path):
        for file in files:
            try:
                file_path = os.path.join(root, file)
                file_size = os.path.getsize(file_path)
                filename, ext = os.path.splitext(file)

                # 处理长文件名
                if len(file) > 100:
                    new_filename = truncate_filename(file)
                    new_file_path = os.path.join(root, new_filename)
                    if new_file_path != file_path:
                        os.rename(file_path, new_file_path)
                        renamed_files.append(new_file_path)
                        file_path = new_file_path  # 更新文件路径以供后续处理
                        filename, ext = os.path.splitext(new_filename)

                # 如果文件没有扩展名且大于100MB，添加.7z扩展名
                if not ext and file_size > 100 * 1024 * 1024:
                    new_file_path = os.path.join(root, filename + '.7z')
                    os.rename(file_path, new_file_path)
                    renamed_files.append(new_file_path)

                # 如果扩展名中有汉字，移除汉字
                elif any('\u4e00' <= char <= '\u9fff' for char in ext):
                    new_ext = ''.join(char for char in ext if not ('\u4e00' <= char <= '\u9fff'))
                    new_file_path = os.path.join(root, filename + new_ext)
                    os.rename(file_path, new_file_path)
                    renamed_files.append(new_file_path)

                # 将大于500MB的PDF文件重命名为.7z
                elif ext.lower() == '.pdf' and file_size > 500 * 1024 * 1024:
                    new_file_path = os.path.join(root, filename + '.7z')
                    os.rename(file_path, new_file_path)
                    renamed_files.append(new_file_path)

                # 将扩展名为.7的文件更改为.7z
                elif ext.lower() == '.7':
                    new_file_path = os.path.join(root, filename + '.7z')
                    os.rename(file_path, new_file_path)
                    renamed_files.append(new_file_path)

                # 将扩展名为.7zz的文件更改为.7z
                elif ext.lower() == '.7zz':
                    new_file_path = os.path.join(root, filename + '.7z')
                    os.rename(file_path, new_file_path)
                    renamed_files.append(new_file_path)


            except Exception as e:
                print(f"重命名文件 {file_path} 时发生错误: {e}")

    return renamed_files


def select_folder():
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口
    # 设置默认文件夹为 D:\BaiduYunDownload\
    default_path = r"D:\BaiduYunDownload"
    folder_path = filedialog.askdirectory(initialdir=default_path)  # 显示文件夹选择对话框
    if folder_path:
        renamed_files = rename_files(folder_path)
        if renamed_files:
            print("重命名结果", "以下文件已被重命名：" + "\n".join(renamed_files))
        else:
            print("重命名结果", "没有符合条件的文件需要重命名。")


select_folder()
