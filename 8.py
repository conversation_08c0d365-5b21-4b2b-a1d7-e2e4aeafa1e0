import os
import ctypes
from tkinter import Tk
from tkinter import filedialog

def is_hidden_or_system(folder_path):
    """
    检查文件夹是否具有隐藏属性或系统属性。
    """
    FILE_ATTRIBUTE_HIDDEN = 0x2  # 隐藏属性
    FILE_ATTRIBUTE_SYSTEM = 0x4  # 系统属性

    # 调用 Windows API 获取文件属性
    attributes = ctypes.windll.kernel32.GetFileAttributesW(folder_path)
    if attributes == -1:  # 获取失败，文件可能不存在
        return False
    # 检查是否具有隐藏或系统属性
    return bool(attributes & (FILE_ATTRIBUTE_HIDDEN | FILE_ATTRIBUTE_SYSTEM))


def find_hidden_or_system_folders(directory):
    """
    列出指定目录中具有系统属性或隐藏属性的子文件夹。
    """
    try:
        hidden_or_system_folders = []

        # 遍历文件夹
        for item in os.listdir(directory):
            item_path = os.path.join(directory, item)
            # 检查是否为文件夹
            if os.path.isdir(item_path) and is_hidden_or_system(item_path):
                hidden_or_system_folders.append(item_path)

        return hidden_or_system_folders
    except Exception as e:
        print(f"发生错误: {e}")
        return []


def select_folder():
    """
    弹出对话框让用户选择文件夹。
    """
    # 隐藏主窗口
    root = Tk()
    root.withdraw()

    # 弹出文件夹选择对话框
    folder_selected = filedialog.askdirectory(title="请选择一个文件夹")
    
    # 如果选择了文件夹，则处理
    if folder_selected:
        print(f"您选择的文件夹是：{folder_selected}")
        result = find_hidden_or_system_folders(folder_selected)
        if result:
            print("以下是具有隐藏或系统属性的子文件夹：")
            for folder in result:
                print(folder)
        else:
            print("未找到具有隐藏或系统属性的子文件夹。")
    else:
        print("您未选择任何文件夹。")


if __name__ == "__main__":
    select_folder()
