{"cells": [{"cell_type": "code", "execution_count": 1, "id": "2918ba88", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2 * 3 =  6\n", "3 / 2 =  1.5\n", "3 % 2 =  1\n", "3 // 2 =  1\n", " -3 // 2 =  -2\n", "a > b =  True\n", "a< b =  False\n", "a>= b =  True\n", "a<= b =  False\n", "1.0 == 1 =  True\n", "1.0 != 1 =  False\n", "或运算为 真\n", "与运算为 假\n", "a + b = 3\n", "a + b + 3 = 8\n", "a - b = 6\n", "a * b = 12\n", "a / b = 6.0\n", "a % b = 0.0\n"]}], "source": ["print('2 * 3 = ', 2 * 3)\n", "print('3 / 2 = ', 3 / 2)\n", "print('3 % 2 = ', 3 % 2)\n", "print('3 // 2 = ', 3 // 2)\n", "print(' -3 // 2 = ', -3 // 2)\n", "\n", "a = 10\n", "b = 9\n", "\n", "print('a > b = ', a > b)\n", "print('a< b = ', a < b)\n", "print('a>= b = ', a >= b)\n", "print('a<= b = ', a <= b)\n", "print('1.0 == 1 = ', 1.0 == 1)\n", "print('1.0 != 1 = ', 1.0 != 1)\n", "\n", "i = 0\n", "a = 10\n", "b = 9\n", "\n", "if a > b or i == 1:\n", "    print(\"或运算为 真\")\n", "else:\n", "    print(\"或运算为 假\")\n", "\n", "if a < b and i == 1:\n", "    print(\"与运算为 真\")\n", "else:\n", "    print(\"与运算为 假\")\n", "\n", "a = 1\n", "b = 2\n", "\n", "a += b  # 相当于a = a + b\n", "\n", "print(\"a + b =\", a)  # 输出结果3\n", "\n", "a += b + 3  # 相当于 a = a + b + 3\n", "\n", "print(\"a + b + 3 =\", a)  # 输出结果8\n", "\n", "a -= b  # 相当于a = a - b\n", "print(\"a - b =\", a)  # 输出结果6\n", "\n", "a *= b  # 相当于a = a * b\n", "print(\"a * b =\", a)  # 输出结果12\n", "\n", "\n", "a /= b  # 相当于a = a / b\n", "print(\"a / b =\", a)  # 输出结果6.0\n", "\n", "a %= b  # 相当于a = a % b\n", "print(\"a % b =\", a)  # 输出结果0.0"]}, {"cell_type": "code", "execution_count": null, "id": "8db9fb09", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}