{"cells": [{"cell_type": "code", "execution_count": 4, "id": "ec2e1a9e", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "plt.rcParams['font.family'] = ['SimHei'] # 设置中文字体\n", "\n", "# 股票投资组合数据\n", "portfolio = {\n", "    'AAPL': 30,  # 苹果公司占比30%\n", "    'GOOGL': 20,  # 谷歌公司占比20%\n", "    'AMZN': 25,  # 亚马逊公司占比25%\n", "    'MSFT': 15,  # 微软公司占比15%\n", "    'FB': 10  # Facebook公司占比10%\n", "}\n", "\n", "# 提取数据和标签\n", "stocks = list(portfolio.keys())\n", "weights = list(portfolio.values())\n", "\n", "# 绘制饼状图\n", "plt.pie(weights, labels=stocks, autopct='%1.1f%%')\n", "\n", "# 设置图表标题\n", "plt.title('股票投资组合')\n", "\n", "# 显示图形\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": null, "id": "5eb0cfba", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}