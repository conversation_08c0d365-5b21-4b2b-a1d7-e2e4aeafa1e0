import os
import shutil
import tkinter as tk
from tkinter import filedialog


def select_folder(title):
    root = tk.Tk()
    root.withdraw()
    folder_path = filedialog.askdirectory(title=title)
    return folder_path


def copy_symlink(src, dst):
    if os.path.islink(src):
        target = os.readlink(src)
        os.symlink(target, dst)
        print(f"Copied symlink: {src} -> {dst}")
    else:
        print(f"Skipping non-symlink: {src}")


def copy_directory(src, dst):
    if not os.path.exists(dst):
        os.makedirs(dst)

    for item in os.listdir(src):
        src_item = os.path.join(src, item)
        dst_item = os.path.join(dst, item)

        if os.path.islink(src_item):
            copy_symlink(src_item, dst_item)
        elif os.path.isdir(src_item):
            copy_directory(src_item, dst_item)
        else:
            shutil.copy2(src_item, dst_item)


def main():
    source_folder = select_folder("Select the source folder")
    if not source_folder:
        print("No source folder selected. Exiting.")
        return

    destination_folder = select_folder("Select the destination folder")
    if not destination_folder:
        print("No destination folder selected. Exiting.")
        return

    copy_directory(source_folder, destination_folder)
    print("Completed copying directory with symlinks.")


if __name__ == "__main__":
    main()
