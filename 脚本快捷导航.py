import tkinter as tk
import subprocess
import os
import json
import sys  # 添加这行
from tkinter import filedialog
from tkinter import simpledialog
from tkinter import messagebox  # 导入 messagebox 用于提示
import threading
import queue

class ScriptNavigator:
    def __init__(self, root):
        self.root = root
        self.root.title("Python 脚本快捷导航")
        
        # 修改配置文件路径到 Z:\work
        self.config_dir = r"Z:\work\configs"
        if not os.path.exists(self.config_dir):
            try:
                os.makedirs(self.config_dir)
            except Exception as e:
                messagebox.showerror("错误", f"无法创建配置目录：{str(e)}\n将使用当前目录作为配置目录。")
                self.config_dir = os.path.join(os.path.dirname(__file__), "configs")
                if not os.path.exists(self.config_dir):
                    os.makedirs(self.config_dir)
        
        self.script_list = []
        self.layout_type = "空白"  # 默认布局类型改为 "空白"
        self.layouts = self.load_layouts()  # 加载可用布局类型
        self.load_scripts()  # 加载已保存的脚本

        self.script_frame = tk.Frame(root)
        self.script_frame.pack(pady=10, fill=tk.BOTH, expand=True)  # 添加一些垂直间距并允许扩展

        self.populate_buttons()  # 创建已保存脚本的按钮

        # 创建下拉框选择布局
        self.layout_var = tk.StringVar(root)
        self.layout_var.set(self.layout_type)
        self.layout_menu = tk.OptionMenu(root, self.layout_var, *self.layouts,
                                       command=self.do_change_layout)
        self.layout_menu.pack(side=tk.TOP, pady=10)

        self.save_button = tk.Button(root, text="保存布局", command=self.save_layout)
        self.save_button.pack(side=tk.TOP, pady=10)  # 在顶部添加保存布局按钮

        self.add_button = tk.Button(root, text="添加脚本", command=self.add_script)
        self.add_button.pack(side=tk.BOTTOM, pady=10)  # 将按钮放在底部并添加一些垂直间距

        self.root.geometry("400x400")  # 设置窗口大小
        self.root.resizable(False, False)  # 固定窗口大小

    def load_layouts(self):
        layouts = ["空白"]
        config_path = os.path.join(self.config_dir)
        for file in os.listdir(config_path):
            if file.endswith('_scripts.json'):
                layouts.append(file.replace('_scripts.json', ''))
        return layouts

    def load_scripts(self):
        self.script_list = []  # 清空当前脚本列表
        
        if self.layout_type == "空白":
            return
        
        try:
            config_file = os.path.join(self.config_dir, f"{self.layout_type}_scripts.json")
            if os.path.exists(config_file):  # 检查文件是否存在
                with open(config_file, "r", encoding='utf-8') as f:
                    self.script_list = json.load(f)
            else:
                messagebox.showinfo("提示", f"布局 '{self.layout_type}' 还没有保存任何脚本")
        except Exception as e:
            messagebox.showerror("错误", f"加载布局时出错：{str(e)}")
            self.script_list = []

    def save_scripts(self):
        # 如果是空白布局，不保存
        if self.layout_type == "空白":
            return
        
        config_file = os.path.join(self.config_dir, f"{self.layout_type}_scripts.json")
        with open(config_file, "w", encoding='utf-8') as f:
            json.dump(self.script_list, f, ensure_ascii=False, indent=4)

    def do_change_layout(self, selected_layout):
        """实际执行布局切换的方法"""
        if selected_layout == self.layout_type:
            return
        
        # 保存当前布局（如果不是空白布局）
        if self.layout_type != "空白" and self.script_list:
            self.save_scripts()
        
        # 切换到新布局
        self.layout_type = selected_layout
        self.load_scripts()
        self.populate_buttons()
        self.layout_var.set(selected_layout)

    def save_layout(self):
        if self.layout_type == "空白" and not self.script_list:
            messagebox.showwarning("警告", "空白布局不能保存，请先添加脚本")
            return
        
        layout_name = simpledialog.askstring("保存布局", "请输入要保存的布局名称:", 
                                           initialvalue=self.layout_type)
        if layout_name:
            if layout_name == "空白":
                messagebox.showerror("错误", "不能使用'空白'作为布局名称")
                return
            
            # 如果是重命名，删除旧的布局文件
            if self.layout_type != "空白":
                old_config_file = os.path.join(self.config_dir, f"{self.layout_type}_scripts.json")
                try:
                    if os.path.exists(old_config_file):
                        os.remove(old_config_file)
                except Exception as e:
                    messagebox.showerror("错误", f"删除旧布局文件失败：{str(e)}")
                    return
            
            self.layout_type = layout_name
            self.save_scripts()
            messagebox.showinfo("成功", f"布局已保存为: {layout_name}")
            
            # 更新布局列表和下拉菜单
            self.layouts = self.load_layouts()
            menu = self.layout_menu['menu']
            menu.delete(0, 'end')
            for layout in self.layouts:
                menu.add_command(label=layout, 
                    command=lambda l=layout: self.do_change_layout(l))
            
            self.layout_var.set(layout_name)
            self.populate_buttons()

    def add_script(self):
        filename = filedialog.askopenfilename(filetypes=[("Python Files", "*.py")])
        if filename:
            name = simpledialog.askstring("输入脚本名称", "请输入脚本名称:")
            if name:
                # 创建一个新的脚本配置字典
                script_config = {
                    "path": filename,
                    "name": name,
                }
                self.script_list.append(script_config)
                self.create_button(filename, name)
                self.save_scripts()  # 保存脚本列表

    def populate_buttons(self):
        # 清空现有按钮
        for widget in self.script_frame.winfo_children():
            widget.destroy()
        for script in self.script_list:
            self.create_button(script["path"], script["name"])

    def create_button(self, path, name):
        # 创建一个框架来容纳按钮
        frame = tk.Frame(self.script_frame)
        frame.pack(fill=tk.X, padx=5, pady=2)
        
        # 创建带样式的按钮
        button = tk.Button(frame, 
                          text=name,
                          command=lambda: self.run_script(path),
                          relief=tk.GROOVE,
                          bg='#f0f0f0',
                          activebackground='#e0e0e0',
                          width=30)
        button.pack(side=tk.LEFT, expand=True)
        
        # 添加删除按钮
        delete_btn = tk.Button(frame,
                             text="×",
                             command=lambda: self.delete_script(path, name),
                             relief=tk.FLAT,
                             bg='#f0f0f0',
                             activebackground='#ff9999')
        delete_btn.pack(side=tk.RIGHT)
        
        # 绑定右键菜单
        button.bind("<Button-3>", lambda event: self.show_context_menu(event, path, name))

    def show_context_menu(self, event, path, name):
        context_menu = tk.Menu(self.script_frame, tearoff=0)
        context_menu.add_command(label="编辑", command=lambda: self.edit_script(path, name))
        context_menu.add_command(label="修改路径", command=lambda: self.edit_script_path(path))
        context_menu.add_command(label="删除", command=lambda: self.delete_script(path, name))
        context_menu.post(event.x_root, event.y_root)  # 在鼠标位置显示菜单

    def edit_script(self, path, name):
        new_name = simpledialog.askstring("编辑脚本名称", "请输入新的脚本名称:", initialvalue=name)
        if new_name:
            for script in self.script_list:
                if script["path"] == path:
                    script["name"] = new_name  # 更新脚本名称
                    break
            self.populate_buttons()  # 更新按钮显示
            self.save_scripts()  # 保存更新后的脚本列表

    def edit_script_path(self, path):
        dialog = PathEditDialog(self.root, path)
        self.root.wait_window(dialog)
        
        if dialog.result:
            new_path = dialog.result
            if not os.path.exists(new_path):
                if not messagebox.askyesno("警告", "输入的路径不存在，是否继续保存？"):
                    return
            
            for script in self.script_list:
                if script["path"] == path:
                    script["path"] = new_path  # 更新脚本路径
                    break
            self.populate_buttons()  # 更新按钮显示
            self.save_scripts()  # 保存更新后的脚本列表

    def delete_script(self, path, name):
        for script in self.script_list:
            if script["path"] == path:
                self.script_list.remove(script)  # 从脚本列表中移除
                break
        self.populate_buttons()  # 更新按钮显示
        self.save_scripts()  # 保存更新后的脚本列表

    def run_script(self, path):
        try:
            if not os.path.exists(path):
                messagebox.showerror("错误", f"脚本文件不存在：{path}")
                return
            
            # 改进 Python 解释器查找逻辑
            python_exe = None
            
            # 1. 首先尝试使用当前运行的 Python
            if hasattr(sys, 'executable') and os.path.exists(sys.executable):
                python_exe = sys.executable
            
            # 2. 如果上面失败，尝试在 PATH 中查找 python.exe
            if not python_exe:
                possible_names = ['python.exe', 'python3.exe', 'python']
                for name in possible_names:
                    try:
                        python_exe = subprocess.check_output(['where', name], 
                                                           text=True, 
                                                           stderr=subprocess.PIPE).strip().split('\n')[0]
                        if os.path.exists(python_exe):
                            break
                    except:
                        continue
            
            if not python_exe or not os.path.exists(python_exe):
                messagebox.showerror("错误", "找不到 Python 解释器，请确保 Python 已正确安装。")
                return
            
            # 直接在终端中运行脚本
            cmd = [python_exe, path]
            subprocess.Popen(
                cmd,
                cwd=os.path.dirname(path),
                shell=False,
                env=os.environ
            )
            
        except Exception as e:
            messagebox.showerror("错误", f"运行脚本时出错：{str(e)}")

class PathEditDialog(tk.Toplevel):
    def __init__(self, parent, current_path):
        super().__init__(parent)
        self.result = None
        self.current_path = current_path
        
        self.title("修改脚本路径")
        self.geometry("500x120")
        self.resizable(False, False)
        
        # 创建路径输入框和浏览按钮的框架
        path_frame = tk.Frame(self)
        path_frame.pack(fill=tk.X, padx=10, pady=5)
        
        tk.Label(path_frame, text="路径:").pack(side=tk.LEFT)
        
        self.path_var = tk.StringVar(value=current_path)
        self.path_entry = tk.Entry(path_frame, textvariable=self.path_var, width=50)
        self.path_entry.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)
        
        browse_btn = tk.Button(path_frame, text="浏览...", command=self.browse_file)
        browse_btn.pack(side=tk.LEFT, padx=5)
        
        # 创建确定和取消按钮的框架
        btn_frame = tk.Frame(self)
        btn_frame.pack(side=tk.BOTTOM, pady=10)
        
        tk.Button(btn_frame, text="确定", command=self.ok_clicked).pack(side=tk.LEFT, padx=5)
        tk.Button(btn_frame, text="取消", command=self.cancel_clicked).pack(side=tk.LEFT, padx=5)
        
        # 设置模态对话框
        self.transient(parent)
        self.grab_set()
        
    def browse_file(self):
        filename = filedialog.askopenfilename(
            filetypes=[("Python Files", "*.py")],
            initialfile=os.path.basename(self.current_path),
            initialdir=os.path.dirname(self.current_path)
        )
        if filename:
            self.path_var.set(filename)
    
    def ok_clicked(self):
        self.result = self.path_var.get()
        self.destroy()
    
    def cancel_clicked(self):
        self.destroy()

if __name__ == "__main__":
    root = tk.Tk()
    app = ScriptNavigator(root)
    root.mainloop()
