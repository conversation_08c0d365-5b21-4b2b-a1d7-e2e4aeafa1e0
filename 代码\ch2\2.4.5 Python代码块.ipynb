{"cells": [{"cell_type": "code", "execution_count": 1, "id": "824f285a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["20\n", "10.0\n", "HelloWorld\n"]}], "source": ["_hello = \"HelloWorld\"\n", "score_for_student = 10.0\n", "y = 20\n", "if y > 10:\n", "    print(y) \n", "    print(score_for_student)\n", "else:\n", "    print(y * 10) \n", "print(_hello)"]}, {"cell_type": "code", "execution_count": null, "id": "00515164", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}