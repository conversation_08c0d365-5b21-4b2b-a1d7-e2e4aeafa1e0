#!/usr/bin/env python3
# -*- coding: utf-8 -*-

# 测试剪贴板功能
import tkinter as tk

def test_clipboard():
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口
    
    # 模拟三个链接
    test_links = [
        "showfilesbot_1V_D1t7H5U2y0i7z6p8W1h6",
        "showfilesbot_2V_Test123456789",
        "VT6AjWM77I5v_Xl62uqZV0tU=_grp"
    ]
    
    # 清空剪贴板
    root.clipboard_clear()
    root.update()
    
    # 添加链接到剪贴板
    clipboard_text = "\n".join(test_links)
    root.clipboard_append(clipboard_text)
    root.update()
    
    print("已将以下内容复制到剪贴板：")
    print("-" * 40)
    print(clipboard_text)
    print("-" * 40)
    print("请检查剪贴板内容是否正确")
    
    # 读取剪贴板内容验证
    try:
        clipboard_content = root.clipboard_get()
        print("\n从剪贴板读取的内容：")
        print("-" * 40)
        print(repr(clipboard_content))
        print("-" * 40)
        
        if clipboard_content == clipboard_text:
            print("✅ 剪贴板内容正确！")
        else:
            print("❌ 剪贴板内容不匹配")
            
    except Exception as e:
        print(f"读取剪贴板失败: {e}")
    
    root.destroy()

if __name__ == "__main__":
    test_clipboard()
