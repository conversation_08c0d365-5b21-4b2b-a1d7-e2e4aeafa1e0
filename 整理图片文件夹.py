import os
import shutil
from tkinter import Tk, filedialog, messagebox, simpledialog
from tkinter import ttk
from PIL import Image
from PIL.ExifTags import TAGS
import datetime
import time

def create_progress_window(title, maximum):
    progress_window = Tk()
    progress_window.title(title)
    progress_window.geometry("400x150")
    
    # 居中显示窗口
    progress_window.eval('tk::PlaceWindow . center')
    
    # 进度标签
    status_label = ttk.Label(progress_window, text="准备开始...")
    status_label.pack(pady=10)
    
    # 进度条
    progress_bar = ttk.Progressbar(progress_window, length=300, mode='determinate', maximum=maximum)
    progress_bar.pack(pady=10)
    
    # 详细信息标签
    detail_label = ttk.Label(progress_window, text="")
    detail_label.pack(pady=5)
    
    progress_window.update()
    return progress_window, progress_bar, status_label, detail_label

def clean_filename(name):
    if not name:
        return ""
    
    # 移除空字符和不可打印字符
    result = "".join(char for char in name if char.isprintable())
    
    # 替换Windows文件系统中不允许的字符
    invalid_chars = ['<', '>', ':', '"', '/', '\\', '|', '?', '*']
    for char in invalid_chars:
        result = result.replace(char, '-')
    
    # 去除首尾空格和控制字符
    result = result.strip()
    
    # 如果清理后为空，返回默认名称
    return result if result else "Unknown"

def get_image_info(image_path):
    try:
        # 打开图片
        image = Image.open(image_path)
        # 获取EXIF数据
        exif = image._getexif()
        if exif is None:
            return None, None
        
        date_taken = None
        camera_model = None
        
        # 查找DateTimeOriginal和相机型号标签
        for tag_id in exif:
            tag = TAGS.get(tag_id, tag_id)
            if tag == 'DateTimeOriginal':
                date_str = exif[tag_id]
                # 转换日期字符串为时间戳
                try:
                    date_time = datetime.datetime.strptime(date_str, '%Y:%m:%d %H:%M:%S')
                    date_taken = time.mktime(date_time.timetuple())
                except:
                    pass
            elif tag == 'Model':
                camera_model = clean_filename(exif[tag_id].strip())
        
        return date_taken, camera_model
    except:
        return None, None

def adjust_image_dates(folder_path):
    # 支持的图片格式
    image_extensions = ('.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff')
    
    # 获取文件夹中的所有文件
    all_items = os.listdir(folder_path)
    image_files = [item for item in all_items 
                  if os.path.isfile(os.path.join(folder_path, item)) 
                  and item.lower().endswith(image_extensions)]
    
    if not image_files:
        messagebox.showinfo("完成", "没有找到图片文件。")
        return
    
    # 创建进度窗口
    progress_window, progress_bar, status_label, detail_label = create_progress_window("处理图片日期和器材", len(image_files))
    
    updated_count = 0
    renamed_count = 0
    skipped_count = 0
    error_files = []
    
    for i, image_file in enumerate(image_files):
        try:
            status_label.config(text=f"正在处理: {image_file}")
            detail_label.config(text=f"进度: {i+1}/{len(image_files)}")
            progress_bar['value'] = i + 1
            progress_window.update()
            
            file_path = os.path.join(folder_path, image_file)
            date_taken, camera_model = get_image_info(file_path)
            
            # 更新文件日期
            if date_taken is not None:
                try:
                    os.utime(file_path, (date_taken, date_taken))
                    updated_count += 1
                except Exception as e:
                    error_files.append(f"{image_file} - 更新日期失败: {str(e)}")
                    skipped_count += 1
            else:
                skipped_count += 1
            
            # 重命名文件（如果有相机型号）
            if camera_model:
                try:
                    file_name, file_ext = os.path.splitext(image_file)
                    camera_tag = f"[{camera_model}]"
                    
                    # 检查文件名中是否已包含相机型号标记
                    has_camera_model = False
                    if "[" in file_name and "]" in file_name:
                        start_idx = file_name.find("[")
                        end_idx = file_name.find("]")
                        if start_idx >= 0 and end_idx > start_idx:
                            existing_model = file_name[start_idx:end_idx+1]
                            if existing_model.lower() == camera_tag.lower():
                                has_camera_model = True
                    
                    # 如果文件名还没有相机型号前缀，则添加
                    if not has_camera_model:
                        new_name = f"{camera_tag}{file_name}{file_ext}"
                        new_path = os.path.join(folder_path, new_name)
                        
                        # 处理文件名冲突
                        counter = 1
                        while os.path.exists(new_path):
                            new_name = f"{camera_tag}{file_name}_{counter}{file_ext}"
                            new_path = os.path.join(folder_path, new_name)
                            counter += 1
                        
                        os.rename(file_path, new_path)
                        renamed_count += 1
                except Exception as e:
                    error_files.append(f"{image_file} - 重命名失败: {str(e)}")
        except Exception as e:
            error_files.append(f"{image_file} - 处理失败: {str(e)}")
            skipped_count += 1
    
    progress_window.destroy()
    
    # 显示处理结果
    result_message = f"处理完成！\n" \
                    f"成功更新日期：{updated_count} 个文件\n" \
                    f"成功重命名：{renamed_count} 个文件\n" \
                    f"跳过/失败：{skipped_count} 个文件"
    
    # 如果有错误，显示详细信息
    if error_files:
        result_message += "\n\n出现以下错误："
        for error in error_files[:10]:  # 只显示前10个错误
            result_message += f"\n{error}"
        if len(error_files) > 10:
            result_message += f"\n...等共 {len(error_files)} 个错误"
    
    messagebox.showinfo("完成", result_message)

def merge_subfolders(folder_path):
    # 获取文件夹中的所有项目
    all_items = os.listdir(folder_path)
    
    # 获取所有子文件夹
    subfolders = [item for item in all_items if os.path.isdir(os.path.join(folder_path, item))]
    
    if not subfolders:
        messagebox.showinfo("完成", "没有找到需要合并的子文件夹。")
        return
    
    # 计算总文件数
    total_files = sum(len([f for f in os.listdir(os.path.join(folder_path, subfolder))
                          if os.path.isfile(os.path.join(folder_path, subfolder, f))])
                      for subfolder in subfolders)
    
    # 创建进度窗口
    progress_window, progress_bar, status_label, detail_label = create_progress_window("合并文件夹", total_files)
    
    processed_files = 0
    for subfolder in subfolders:
        subfolder_path = os.path.join(folder_path, subfolder)
        files = [f for f in os.listdir(subfolder_path) if os.path.isfile(os.path.join(subfolder_path, f))]
        
        for file in files:
            status_label.config(text=f"正在移动: {file}")
            detail_label.config(text=f"从文件夹: {subfolder}")
            
            src_path = os.path.join(subfolder_path, file)
            dst_path = os.path.join(folder_path, file)
            
            if os.path.exists(dst_path):
                base, ext = os.path.splitext(file)
                counter = 1
                while os.path.exists(dst_path):
                    new_name = f"{base}_{counter}{ext}"
                    dst_path = os.path.join(folder_path, new_name)
                    counter += 1
            
            shutil.move(src_path, dst_path)
            processed_files += 1
            progress_bar['value'] = processed_files
            progress_window.update()
        
        # 删除空的子文件夹
        os.rmdir(subfolder_path)
    
    progress_window.destroy()
    messagebox.showinfo("完成", f"已将 {len(subfolders)} 个子文件夹中的 {processed_files} 个文件合并到根目录。")

def split_files_into_subfolders(folder_path, max_files_per_folder):
    # 获取文件夹中的所有文件和子文件夹
    all_items = os.listdir(folder_path)
    
    # 过滤出文件（排除子文件夹）并获取修改时间
    files_with_time = []
    for item in all_items:
        if os.path.isfile(os.path.join(folder_path, item)):
            file_path = os.path.join(folder_path, item)
            mod_time = os.path.getmtime(file_path)
            files_with_time.append((item, mod_time))
    
    # 按修改时间排序
    files_with_time.sort(key=lambda x: x[1])
    files = [f[0] for f in files_with_time]
    
    total_files = len(files)
    if total_files <= max_files_per_folder:
        messagebox.showinfo("完成", f"文件夹中只有 {total_files} 个文件，无需分割。")
        return
    
    # 创建进度窗口
    progress_window, progress_bar, status_label, detail_label = create_progress_window("分割文件夹", total_files)
    
    # 计算需要的子文件夹数量
    num_subfolders = (total_files + max_files_per_folder - 1) // max_files_per_folder
    subfolder_name = os.path.basename(folder_path)
    
    # 创建子文件夹
    subfolders = []
    for i in range(1, num_subfolders + 1):
        subfolder_num = f"{i:02d}"
        subfolder_path = os.path.join(folder_path, f"{subfolder_name}_{subfolder_num}")
        os.makedirs(subfolder_path, exist_ok=True)
        subfolders.append(subfolder_path)
    
    # 将文件分配到子文件夹
    for i, file in enumerate(files):
        status_label.config(text=f"正在移动: {file}")
        detail_label.config(text=f"进度: {i+1}/{total_files}")
        
        # 获取文件的修改时间并格式化为可读字符串
        file_time = datetime.datetime.fromtimestamp(files_with_time[i][1]).strftime('%Y-%m-%d %H:%M:%S')
        detail_label.config(text=f"进度: {i+1}/{total_files}\n修改时间: {file_time}")
        
        subfolder_index = i // max_files_per_folder
        src_path = os.path.join(folder_path, file)
        dst_path = os.path.join(subfolders[subfolder_index], file)
        shutil.move(src_path, dst_path)
        
        progress_bar['value'] = i + 1
        progress_window.update()
    
    progress_window.destroy()
    messagebox.showinfo("完成", 
                      f"已将 {total_files} 个文件按时间顺序分配到 {num_subfolders} 个子文件夹中。\n"
                      f"每个子文件夹最多 {max_files_per_folder} 个文件。\n"
                      f"文件已按修改时间从早到晚排序。")

def merge_by_camera(folder_path):
    # 支持的图片格式
    image_extensions = ('.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff')
    
    # 获取文件夹中的所有文件
    all_items = os.listdir(folder_path)
    image_files = [item for item in all_items 
                  if os.path.isfile(os.path.join(folder_path, item)) 
                  and item.lower().endswith(image_extensions)]
    
    if not image_files:
        messagebox.showinfo("完成", "没有找到图片文件。")
        return
    
    # 创建进度窗口
    progress_window, progress_bar, status_label, detail_label = create_progress_window("按器材合并文件", len(image_files))
    
    # 用于存储每个相机型号对应的文件列表
    camera_files = {}
    unknown_files = []
    error_files = []
    
    # 第一遍扫描：获取所有文件的相机型号
    for i, image_file in enumerate(image_files):
        try:
            status_label.config(text=f"正在扫描: {image_file}")
            detail_label.config(text=f"进度: {i+1}/{len(image_files)}")
            progress_bar['value'] = i + 1
            progress_window.update()
            
            file_path = os.path.join(folder_path, image_file)
            _, camera_model = get_image_info(file_path)
            
            if camera_model:
                if camera_model not in camera_files:
                    camera_files[camera_model] = []
                camera_files[camera_model].append(image_file)
            else:
                unknown_files.append(image_file)
        except Exception as e:
            error_files.append(f"{image_file} - 扫描失败: {str(e)}")
            unknown_files.append(image_file)
    
    if not camera_files and not unknown_files:
        progress_window.destroy()
        messagebox.showinfo("完成", "没有找到可处理的图片文件。")
        return
    
    # 第二遍：移动文件到对应的文件夹
    total_moved = 0
    progress_bar['maximum'] = len(image_files)
    progress_bar['value'] = 0
    
    # 处理有相机型号的文件
    for camera_model, files in camera_files.items():
        try:
            # 创建相机型号文件夹
            camera_folder = os.path.join(folder_path, f"[{camera_model}]")
            os.makedirs(camera_folder, exist_ok=True)
            
            for file in files:
                try:
                    status_label.config(text=f"正在移动: {file}")
                    detail_label.config(text=f"移动到: [{camera_model}]")
                    
                    src_path = os.path.join(folder_path, file)
                    dst_path = os.path.join(camera_folder, file)
                    
                    # 处理文件名冲突
                    if os.path.exists(dst_path):
                        base, ext = os.path.splitext(file)
                        counter = 1
                        while os.path.exists(dst_path):
                            new_name = f"{base}_{counter}{ext}"
                            dst_path = os.path.join(camera_folder, new_name)
                            counter += 1
                    
                    shutil.move(src_path, dst_path)
                    total_moved += 1
                    progress_bar['value'] = total_moved
                    progress_window.update()
                except Exception as e:
                    error_files.append(f"{file} - 移动到 [{camera_model}] 失败: {str(e)}")
        except Exception as e:
            error_files.append(f"创建文件夹 [{camera_model}] 失败: {str(e)}")
            continue
    
    # 处理未知相机型号的文件
    if unknown_files:
        try:
            unknown_folder = os.path.join(folder_path, "[未知器材]")
            os.makedirs(unknown_folder, exist_ok=True)
            
            for file in unknown_files:
                try:
                    status_label.config(text=f"正在移动: {file}")
                    detail_label.config(text="移动到: [未知器材]")
                    
                    src_path = os.path.join(folder_path, file)
                    dst_path = os.path.join(unknown_folder, file)
                    
                    # 处理文件名冲突
                    if os.path.exists(dst_path):
                        base, ext = os.path.splitext(file)
                        counter = 1
                        while os.path.exists(dst_path):
                            new_name = f"{base}_{counter}{ext}"
                            dst_path = os.path.join(unknown_folder, new_name)
                            counter += 1
                    
                    shutil.move(src_path, dst_path)
                    total_moved += 1
                    progress_bar['value'] = total_moved
                    progress_window.update()
                except Exception as e:
                    error_files.append(f"{file} - 移动到 [未知器材] 失败: {str(e)}")
        except Exception as e:
            error_files.append(f"创建 [未知器材] 文件夹失败: {str(e)}")
    
    progress_window.destroy()
    
    # 显示处理结果
    result_message = f"处理完成！\n" \
                    f"共处理 {len(camera_files)} 种器材的图片\n" \
                    f"成功移动：{total_moved} 个文件\n" \
                    f"其中未知器材：{len(unknown_files)} 个文件"
    
    # 如果有错误，显示详细信息
    if error_files:
        result_message += "\n\n出现以下错误："
        for error in error_files[:10]:  # 只显示前10个错误
            result_message += f"\n{error}"
        if len(error_files) > 10:
            result_message += f"\n...等共 {len(error_files)} 个错误"
    
    messagebox.showinfo("完成", result_message)

def select_operation():
    # 创建主窗口
    root = Tk()
    root.title("文件夹操作工具")
    root.geometry("300x250")  # 增加窗口高度以容纳新按钮
    
    # 居中显示窗口
    root.eval('tk::PlaceWindow . center')
    
    def on_split():
        folder_path = filedialog.askdirectory(title="选择要分割的文件夹")
        if folder_path:
            # 询问每个文件夹的最大文件数
            max_files = simpledialog.askinteger("输入", "每个文件夹最多包含多少个文件？", 
                                              initialvalue=300, minvalue=1)
            if max_files:
                split_files_into_subfolders(folder_path, max_files)
    
    def on_merge():
        folder_path = filedialog.askdirectory(title="选择要合并的文件夹")
        if folder_path:
            merge_subfolders(folder_path)
    
    def on_adjust_dates():
        folder_path = filedialog.askdirectory(title="选择包含图片的文件夹")
        if folder_path:
            adjust_image_dates(folder_path)
    
    def on_merge_by_camera():
        folder_path = filedialog.askdirectory(title="选择要按器材合并的文件夹")
        if folder_path:
            merge_by_camera(folder_path)
    
    # 添加说明标签
    label = ttk.Label(root, text="请选择要执行的操作：")
    label.pack(pady=10)
    
    # 添加按钮
    split_btn = ttk.Button(root, text="分割文件夹", command=on_split)
    split_btn.pack(pady=5)
    
    merge_btn = ttk.Button(root, text="合并文件夹", command=on_merge)
    merge_btn.pack(pady=5)
    
    adjust_dates_btn = ttk.Button(root, text="调整图片日期和器材", command=on_adjust_dates)
    adjust_dates_btn.pack(pady=5)
    
    merge_by_camera_btn = ttk.Button(root, text="按器材合并", command=on_merge_by_camera)
    merge_by_camera_btn.pack(pady=5)
    
    root.mainloop()

if __name__ == "__main__":
    select_operation()