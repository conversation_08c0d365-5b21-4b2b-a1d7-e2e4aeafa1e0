{"cells": [{"cell_type": "code", "execution_count": 1, "id": "118d9c9e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[{'Date': '2023-06-30', 'Open': '1700.00', 'Close': '1691.00', 'Low': '1686.48', 'High': '1708.99', 'Volume': '20459'}, {'Date': '2023-06-29', 'Open': '1731.00', 'Close': '1713.71', 'Low': '1713.01', 'High': '1734.99', 'Volume': '14231'}, {'Date': '2023-06-28', 'Open': '1713.18', 'Close': '1728.38', 'Low': '1711.00', 'High': '1734.00', 'Volume': '18574'}, {'Date': '2023-06-27', 'Open': '1709.99', 'Close': '1711.05', 'Low': '1700.09', 'High': '1719.70', 'Volume': '15174'}, {'Date': '2023-06-26', 'Open': '1720.11', 'Close': '1709.00', 'Low': '1695.00', 'High': '1730.00', 'Volume': '23993'}, {'Date': '2023-06-21', 'Open': '1740.00', 'Close': '1735.83', 'Low': '1735.00', 'High': '1756.60', 'Volume': '17721'}, {'Date': '2023-06-20', 'Open': '1740.00', 'Close': '1743.46', 'Low': '1735.00', 'High': '1765.00', 'Volume': '20947'}, {'Date': '2023-06-19', 'Open': '1790.00', 'Close': '1744.00', 'Low': '1738.00', 'High': '1797.95', 'Volume': '31700'}, {'Date': '2023-06-16', 'Open': '1757.00', 'Close': '1797.69', 'Low': '1750.10', 'High': '1800.00', 'Volume': '37918'}, {'Date': '2023-06-15', 'Open': '1730.34', 'Close': '1755.00', 'Low': '1723.00', 'High': '1755.65', 'Volume': '25223'}, {'Date': '2023-06-14', 'Open': '1719.00', 'Close': '1726.88', 'Low': '1706.21', 'High': '1734.24', 'Volume': '31506'}, {'Date': '2023-06-13', 'Open': '1693.88', 'Close': '1699.00', 'Low': '1685.00', 'High': '1704.61', 'Volume': '14663'}, {'Date': '2023-06-12', 'Open': '1666.02', 'Close': '1696.00', 'Low': '1661.52', 'High': '1708.00', 'Volume': '27029'}, {'Date': '2023-06-09', 'Open': '1671.99', 'Close': '1666.00', 'Low': '1666.00', 'High': '1677.97', 'Volume': '17580'}, {'Date': '2023-06-08', 'Open': '1655.83', 'Close': '1668.00', 'Low': '1650.01', 'High': '1675.00', 'Volume': '14703'}, {'Date': '2023-06-07', 'Open': '1672.99', 'Close': '1650.90', 'Low': '1650.00', 'High': '1678.80', 'Volume': '17890'}, {'Date': '2023-06-06', 'Open': '1670.00', 'Close': '1666.99', 'Low': '1651.55', 'High': '1684.00', 'Volume': '19914'}, {'Date': '2023-06-05', 'Open': '1666.16', 'Close': '1665.00', 'Low': '1663.00', 'High': '1682.88', 'Volume': '16200'}, {'Date': '2023-06-02', 'Open': '1645.00', 'Close': '1670.60', 'Low': '1636.11', 'High': '1673.15', 'Volume': '25058'}, {'Date': '2023-06-01', 'Open': '1618.00', 'Close': '1635.92', 'Low': '1618.00', 'High': '1674.99', 'Volume': '28645'}, {'Date': '2023-05-31', 'Open': '1661.01', 'Close': '1628.90', 'Low': '1626.67', 'High': '1665.72', 'Volume': '36957'}, {'Date': '2023-05-30', 'Open': '1689.00', 'Close': '1668.29', 'Low': '1658.56', 'High': '1698.89', 'Volume': '26453'}, {'Date': '2023-05-29', 'Open': '1697.00', 'Close': '1689.00', 'Low': '1678.00', 'High': '1698.35', 'Volume': '17771'}, {'Date': '2023-05-26', 'Open': '1702.00', 'Close': '1690.56', 'Low': '1675.01', 'High': '1705.00', 'Volume': '18291'}, {'Date': '2023-05-25', 'Open': '1705.99', 'Close': '1701.00', 'Low': '1690.00', 'High': '1710.00', 'Volume': '22466'}, {'Date': '2023-05-24', 'Open': '1732.33', 'Close': '1710.90', 'Low': '1710.11', 'High': '1733.55', 'Volume': '18479'}, {'Date': '2023-05-23', 'Open': '1753.99', 'Close': '1724.55', 'Low': '1724.55', 'High': '1754.00', 'Volume': '16894'}, {'Date': '2023-05-22', 'Open': '1690.01', 'Close': '1746.00', 'Low': '1690.00', 'High': '1752.00', 'Volume': '41284'}, {'Date': '2023-05-19', 'Open': '1671.00', 'Close': '1699.50', 'Low': '1655.55', 'High': '1704.33', 'Volume': '26741'}, {'Date': '2023-05-18', 'Open': '1704.99', 'Close': '1691.00', 'Low': '1690.00', 'High': '1705.40', 'Volume': '19219'}, {'Date': '2023-05-17', 'Open': '1708.00', 'Close': '1701.00', 'Low': '1699.01', 'High': '1713.00', 'Volume': '16777'}, {'Date': '2023-05-16', 'Open': '1713.99', 'Close': '1703.29', 'Low': '1697.00', 'High': '1719.99', 'Volume': '22224'}, {'Date': '2023-05-15', 'Open': '1702.00', 'Close': '1716.30', 'Low': '1691.20', 'High': '1717.00', 'Volume': '25740'}, {'Date': '2023-05-12', 'Open': '1715.00', 'Close': '1706.40', 'Low': '1705.01', 'High': '1729.88', 'Volume': '19460'}, {'Date': '2023-05-11', 'Open': '1731.00', 'Close': '1717.07', 'Low': '1715.00', 'High': '1735.00', 'Volume': '15246'}, {'Date': '2023-05-10', 'Open': '1715.19', 'Close': '1725.00', 'Low': '1715.00', 'High': '1736.00', 'Volume': '20773'}, {'Date': '2023-05-09', 'Open': '1715.00', 'Close': '1722.00', 'Low': '1707.07', 'High': '1739.98', 'Volume': '29378'}, {'Date': '2023-05-08', 'Open': '1768.32', 'Close': '1720.52', 'Low': '1717.17', 'High': '1769.00', 'Volume': '33171'}, {'Date': '2023-05-05', 'Open': '1766.00', 'Close': '1750.00', 'Low': '1736.39', 'High': '1766.00', 'Volume': '20714'}, {'Date': '2023-05-04', 'Open': '1769.00', 'Close': '1749.90', 'Low': '1736.00', 'High': '1777.67', 'Volume': '27466'}, {'Date': '2023-04-28', 'Open': '1763.01', 'Close': '1760.52', 'Low': '1753.97', 'High': '1782.77', 'Volume': '26225'}, {'Date': '2023-04-27', 'Open': '1748.00', 'Close': '1757.92', 'Low': '1730.00', 'High': '1770.00', 'Volume': '25605'}, {'Date': '2023-04-26', 'Open': '1744.00', 'Close': '1743.91', 'Low': '1720.00', 'High': '1761.00', 'Volume': '27892'}, {'Date': '2023-04-25', 'Open': '1706.00', 'Close': '1730.38', 'Low': '1702.00', 'High': '1735.98', 'Volume': '20583'}, {'Date': '2023-04-24', 'Open': '1721.00', 'Close': '1705.00', 'Low': '1701.00', 'High': '1722.00', 'Volume': '20231'}, {'Date': '2023-04-21', 'Open': '1746.00', 'Close': '1725.47', 'Low': '1721.28', 'High': '1748.08', 'Volume': '18518'}, {'Date': '2023-04-20', 'Open': '1760.11', 'Close': '1741.88', 'Low': '1729.00', 'High': '1765.00', 'Volume': '19885'}, {'Date': '2023-04-19', 'Open': '1762.00', 'Close': '1759.00', 'Low': '1750.00', 'High': '1765.50', 'Volume': '14601'}, {'Date': '2023-04-18', 'Open': '1753.00', 'Close': '1758.00', 'Low': '1746.02', 'High': '1769.00', 'Volume': '18314'}, {'Date': '2023-04-17', 'Open': '1740.00', 'Close': '1753.00', 'Low': '1728.00', 'High': '1753.00', 'Volume': '30467'}, {'Date': '2023-04-14', 'Open': '1726.00', 'Close': '1713.42', 'Low': '1704.80', 'High': '1733.00', 'Volume': '21232'}, {'Date': '2023-04-13', 'Open': '1690.00', 'Close': '1723.00', 'Low': '1684.01', 'High': '1723.59', 'Volume': '29543'}, {'Date': '2023-04-12', 'Open': '1747.26', 'Close': '1694.10', 'Low': '1692.82', 'High': '1750.00', 'Volume': '51105'}, {'Date': '2023-04-11', 'Open': '1793.00', 'Close': '1745.50', 'Low': '1744.00', 'High': '1793.00', 'Volume': '29209'}, {'Date': '2023-04-10', 'Open': '1790.88', 'Close': '1771.70', 'Low': '1744.00', 'High': '1790.88', 'Volume': '29418'}, {'Date': '2023-04-07', 'Open': '1795.00', 'Close': '1790.99', 'Low': '1788.34', 'High': '1806.01', 'Volume': '13525'}, {'Date': '2023-04-06', 'Open': '1805.00', 'Close': '1796.96', 'Low': '1788.22', 'High': '1815.90', 'Volume': '14874'}, {'Date': '2023-04-04', 'Open': '1812.00', 'Close': '1814.59', 'Low': '1787.00', 'High': '1815.17', 'Volume': '20066'}, {'Date': '2023-04-03', 'Open': '1825.00', 'Close': '1802.07', 'Low': '1800.08', 'High': '1827.77', 'Volume': '21417'}, {'Date': '2023-03-31', 'Open': '1825.00', 'Close': '1820.00', 'Low': '1819.00', 'High': '1848.00', 'Volume': '27446'}, {'Date': '2023-03-30', 'Open': '1793.00', 'Close': '1800.00', 'Low': '1779.00', 'High': '1805.00', 'Volume': '19257'}, {'Date': '2023-03-29', 'Open': '1799.00', 'Close': '1790.00', 'Low': '1785.07', 'High': '1800.00', 'Volume': '15393'}, {'Date': '2023-03-28', 'Open': '1770.00', 'Close': '1781.80', 'Low': '1765.02', 'High': '1790.00', 'Volume': '17261'}, {'Date': '2023-03-27', 'Open': '1778.60', 'Close': '1767.79', 'Low': '1756.00', 'High': '1778.60', 'Volume': '15296'}, {'Date': '2023-03-24', 'Open': '1769.08', 'Close': '1778.62', 'Low': '1766.00', 'High': '1783.60', 'Volume': '12770'}, {'Date': '2023-03-23', 'Open': '1766.00', 'Close': '1774.86', 'Low': '1765.01', 'High': '1791.11', 'Volume': '17356'}, {'Date': '2023-03-22', 'Open': '1780.00', 'Close': '1773.35', 'Low': '1765.55', 'High': '1793.00', 'Volume': '15330'}, {'Date': '2023-03-21', 'Open': '1735.00', 'Close': '1775.00', 'Low': '1723.97', 'High': '1785.85', 'Volume': '31142'}, {'Date': '2023-03-20', 'Open': '1751.00', 'Close': '1729.60', 'Low': '1728.00', 'High': '1755.00', 'Volume': '20491'}, {'Date': '2023-03-17', 'Open': '1770.00', 'Close': '1742.00', 'Low': '1736.00', 'High': '1775.89', 'Volume': '27023'}, {'Date': '2023-03-16', 'Open': '1740.00', 'Close': '1751.99', 'Low': '1739.01', 'High': '1770.00', 'Volume': '17646'}, {'Date': '2023-03-15', 'Open': '1778.37', 'Close': '1750.92', 'Low': '1750.12', 'High': '1784.88', 'Volume': '19213'}, {'Date': '2023-03-14', 'Open': '1763.78', 'Close': '1766.00', 'Low': '1738.50', 'High': '1779.88', 'Volume': '23705'}, {'Date': '2023-03-13', 'Open': '1751.00', 'Close': '1762.00', 'Low': '1749.00', 'High': '1775.00', 'Volume': '20560'}, {'Date': '2023-03-10', 'Open': '1751.57', 'Close': '1750.00', 'Low': '1750.00', 'High': '1781.00', 'Volume': '21161'}, {'Date': '2023-03-09', 'Open': '1768.00', 'Close': '1770.02', 'Low': '1740.00', 'High': '1785.00', 'Volume': '27612'}, {'Date': '2023-03-08', 'Open': '1780.02', 'Close': '1770.42', 'Low': '1761.12', 'High': '1785.94', 'Volume': '22764'}, {'Date': '2023-03-07', 'Open': '1805.98', 'Close': '1788.30', 'Low': '1788.00', 'High': '1816.60', 'Volume': '22785'}, {'Date': '2023-03-06', 'Open': '1818.18', 'Close': '1807.14', 'Low': '1796.77', 'High': '1818.50', 'Volume': '20646'}, {'Date': '2023-03-03', 'Open': '1839.77', 'Close': '1818.04', 'Low': '1802.48', 'High': '1841.61', 'Volume': '16198'}]\n"]}], "source": ["from selenium import webdriver\n", "from selenium.webdriver.common.by import By\n", "\n", "driver = webdriver.Firefox()\n", "driver.get('http://q.stock.sohu.com/cn/600519/lshq.shtml')\n", "table_element = driver.find_element(By.ID, 'BIZ_hq_historySearch')\n", "tbody = table_element.find_element(By.TAG_NAME, \"tbody\")\n", "trlist = tbody.find_elements(By.TAG_NAME, 'tr')\n", "\n", "data = []\n", "\n", "for idx, tr in enumerate(trlist):\n", "    if idx == 0:\n", "        # 跳过table第一行\n", "        continue\n", "\n", "    td_list = tr.find_elements(By.TAG_NAME, \"td\")\n", "    fields = {}\n", "    fields['Date'] = td_list[0].text  # 日期\n", "    fields['Open'] = td_list[1].text  # 开盘\n", "    fields['Close'] = td_list[2].text  # 收盘\n", "    fields['Low'] = td_list[5].text  # 最低\n", "    fields['High'] = td_list[6].text  # 最高\n", "    fields['Volume'] = td_list[7].text  # 成交量\n", "    data.append(fields)\n", "\n", "print(data)\n", "driver.quit()"]}, {"cell_type": "code", "execution_count": 4, "id": "d80358ed", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["CSV文件已生成：data.csv\n"]}], "source": ["import csv\n", "keys = data[0].keys()\n", "\n", "f = 'data/搜狐证券贵州茅台股票数据.csv'\n", "# 将数据写入CSV文件\n", "with open(f, 'w', newline='') as csvfile:\n", "    writer = csv.DictWriter(csvfile, fieldnames=keys)\n", "\n", "    # 写入表头\n", "    writer.writeheader()\n", "\n", "    # 写入数据\n", "    writer.writerows(data)\n", "\n", "print(\"CSV文件已生成：data.csv\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "3c892adc", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}