{"cells": [{"cell_type": "markdown", "id": "b061236e", "metadata": {}, "source": ["### 清洗中国石化股票数据"]}, {"cell_type": "code", "execution_count": 17, "id": "ef7c1cc5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["处理完成。\n"]}], "source": ["# 原始文件\n", "inputfile =  'data/0600028股票历史交易数据.csv'\n", "# 目标文件\n", "outfile =  'data/中石化.csv'\n", "\n", "# 打开原始文件和目标文件\n", "with open(inputfile, 'r') as input_file, open(outfile, 'w') as output_file:\n", "    # 逐行读取原始文件\n", "    for line in input_file:\n", "        # 去除行末的换行符\n", "        line = line.rstrip('\\n')\n", "        # 判断是否为空行\n", "        if line:\n", "            # 写入非空行到目标文件\n", "            output_file.write(line + '\\n')\n", "\n", "print('处理完成。')"]}, {"cell_type": "markdown", "id": "95a0309e", "metadata": {}, "source": ["### 清洗中国石油股票数据"]}, {"cell_type": "code", "execution_count": 36, "id": "8f4fcc21", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["处理完成。\n"]}], "source": ["# 原始文件\n", "inputfile =  'data/0601857股票历史交易数据.csv'\n", "# 目标文件\n", "outfile =  'data/中国石油.csv'\n", "\n", "# 打开原始文件和目标文件\n", "with open(inputfile, 'r') as input_file, open(outfile, 'w') as output_file:\n", "    # 逐行读取原始文件\n", "    for line in input_file:\n", "        # 去除行末的换行符\n", "        line = line.rstrip('\\n')\n", "        # 判断是否为空行\n", "        if line:\n", "            # 写入非空行到目标文件\n", "            output_file.write(line + '\\n')\n", "\n", "print('处理完成。')"]}, {"cell_type": "markdown", "id": "935b5a08", "metadata": {}, "source": ["### 读取中国石化股票数据"]}, {"cell_type": "code", "execution_count": 11, "id": "2ccea902", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Close</th>\n", "      <th>High</th>\n", "      <th>Low</th>\n", "      <th>Open</th>\n", "      <th>前收盘</th>\n", "      <th>涨跌额</th>\n", "      <th>涨跌幅</th>\n", "      <th>换手率</th>\n", "      <th>成交量</th>\n", "      <th>成交金额</th>\n", "      <th>总市值</th>\n", "      <th>流通市值</th>\n", "    </tr>\n", "    <tr>\n", "      <th>日期</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2021-03-23</th>\n", "      <td>4.26</td>\n", "      <td>4.30</td>\n", "      <td>4.22</td>\n", "      <td>4.29</td>\n", "      <td>4.29</td>\n", "      <td>-0.03</td>\n", "      <td>-0.6993</td>\n", "      <td>0.1405</td>\n", "      <td>134253618</td>\n", "      <td>5.722791e+08</td>\n", "      <td>5.157634e+11</td>\n", "      <td>4.070761e+11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-03-22</th>\n", "      <td>4.29</td>\n", "      <td>4.31</td>\n", "      <td>4.27</td>\n", "      <td>4.27</td>\n", "      <td>4.26</td>\n", "      <td>0.03</td>\n", "      <td>0.7042</td>\n", "      <td>0.1586</td>\n", "      <td>151597465</td>\n", "      <td>6.500290e+08</td>\n", "      <td>5.193955e+11</td>\n", "      <td>4.099428e+11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-03-19</th>\n", "      <td>4.26</td>\n", "      <td>4.34</td>\n", "      <td>4.25</td>\n", "      <td>4.34</td>\n", "      <td>4.44</td>\n", "      <td>-0.18</td>\n", "      <td>-4.0541</td>\n", "      <td>0.3223</td>\n", "      <td>307982059</td>\n", "      <td>1.322718e+09</td>\n", "      <td>5.157634e+11</td>\n", "      <td>4.070761e+11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-03-18</th>\n", "      <td>4.44</td>\n", "      <td>4.46</td>\n", "      <td>4.41</td>\n", "      <td>4.45</td>\n", "      <td>4.46</td>\n", "      <td>-0.02</td>\n", "      <td>-0.4484</td>\n", "      <td>0.1447</td>\n", "      <td>138233862</td>\n", "      <td>6.125115e+08</td>\n", "      <td>5.375562e+11</td>\n", "      <td>4.242765e+11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-03-17</th>\n", "      <td>4.46</td>\n", "      <td>4.52</td>\n", "      <td>4.44</td>\n", "      <td>4.52</td>\n", "      <td>4.55</td>\n", "      <td>-0.09</td>\n", "      <td>-1.9780</td>\n", "      <td>0.1572</td>\n", "      <td>150215513</td>\n", "      <td>6.716877e+08</td>\n", "      <td>5.399776e+11</td>\n", "      <td>4.261877e+11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-03-16</th>\n", "      <td>4.55</td>\n", "      <td>4.59</td>\n", "      <td>4.52</td>\n", "      <td>4.58</td>\n", "      <td>4.61</td>\n", "      <td>-0.06</td>\n", "      <td>-1.3015</td>\n", "      <td>0.1216</td>\n", "      <td>116163072</td>\n", "      <td>5.283357e+08</td>\n", "      <td>5.508740e+11</td>\n", "      <td>4.347879e+11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-03-15</th>\n", "      <td>4.61</td>\n", "      <td>4.62</td>\n", "      <td>4.47</td>\n", "      <td>4.47</td>\n", "      <td>4.48</td>\n", "      <td>0.13</td>\n", "      <td>2.9018</td>\n", "      <td>0.2475</td>\n", "      <td>236540480</td>\n", "      <td>1.081757e+09</td>\n", "      <td>5.581383e+11</td>\n", "      <td>4.405213e+11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-03-12</th>\n", "      <td>4.48</td>\n", "      <td>4.52</td>\n", "      <td>4.43</td>\n", "      <td>4.50</td>\n", "      <td>4.48</td>\n", "      <td>0.00</td>\n", "      <td>0.0000</td>\n", "      <td>0.1444</td>\n", "      <td>137980472</td>\n", "      <td>6.170500e+08</td>\n", "      <td>5.423990e+11</td>\n", "      <td>4.280988e+11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-03-11</th>\n", "      <td>4.48</td>\n", "      <td>4.49</td>\n", "      <td>4.44</td>\n", "      <td>4.45</td>\n", "      <td>4.42</td>\n", "      <td>0.06</td>\n", "      <td>1.3575</td>\n", "      <td>0.1254</td>\n", "      <td>119855260</td>\n", "      <td>5.350162e+08</td>\n", "      <td>5.423990e+11</td>\n", "      <td>4.280988e+11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-03-10</th>\n", "      <td>4.42</td>\n", "      <td>4.51</td>\n", "      <td>4.40</td>\n", "      <td>4.49</td>\n", "      <td>4.52</td>\n", "      <td>-0.10</td>\n", "      <td>-2.2124</td>\n", "      <td>0.1560</td>\n", "      <td>149027437</td>\n", "      <td>6.643956e+08</td>\n", "      <td>5.351347e+11</td>\n", "      <td>4.223653e+11</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["            Close  High   Low  Open   前收盘   涨跌额     涨跌幅     换手率        成交量  \\\n", "日期                                                                           \n", "2021-03-23   4.26  4.30  4.22  4.29  4.29 -0.03 -0.6993  0.1405  134253618   \n", "2021-03-22   4.29  4.31  4.27  4.27  4.26  0.03  0.7042  0.1586  151597465   \n", "2021-03-19   4.26  4.34  4.25  4.34  4.44 -0.18 -4.0541  0.3223  307982059   \n", "2021-03-18   4.44  4.46  4.41  4.45  4.46 -0.02 -0.4484  0.1447  138233862   \n", "2021-03-17   4.46  4.52  4.44  4.52  4.55 -0.09 -1.9780  0.1572  150215513   \n", "2021-03-16   4.55  4.59  4.52  4.58  4.61 -0.06 -1.3015  0.1216  116163072   \n", "2021-03-15   4.61  4.62  4.47  4.47  4.48  0.13  2.9018  0.2475  236540480   \n", "2021-03-12   4.48  4.52  4.43  4.50  4.48  0.00  0.0000  0.1444  137980472   \n", "2021-03-11   4.48  4.49  4.44  4.45  4.42  0.06  1.3575  0.1254  119855260   \n", "2021-03-10   4.42  4.51  4.40  4.49  4.52 -0.10 -2.2124  0.1560  149027437   \n", "\n", "                    成交金额           总市值          流通市值  \n", "日期                                                    \n", "2021-03-23  5.722791e+08  5.157634e+11  4.070761e+11  \n", "2021-03-22  6.500290e+08  5.193955e+11  4.099428e+11  \n", "2021-03-19  1.322718e+09  5.157634e+11  4.070761e+11  \n", "2021-03-18  6.125115e+08  5.375562e+11  4.242765e+11  \n", "2021-03-17  6.716877e+08  5.399776e+11  4.261877e+11  \n", "2021-03-16  5.283357e+08  5.508740e+11  4.347879e+11  \n", "2021-03-15  1.081757e+09  5.581383e+11  4.405213e+11  \n", "2021-03-12  6.170500e+08  5.423990e+11  4.280988e+11  \n", "2021-03-11  5.350162e+08  5.423990e+11  4.280988e+11  \n", "2021-03-10  6.643956e+08  5.351347e+11  4.223653e+11  "]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "# 数据文件\n", "f =  'data/中国石化.csv'\n", "# 读取中国石化股票历史交易数据\n", "stock1_data  = pd.read_csv(f, encoding='gbk', index_col='日期', parse_dates=True)\n", "# 移除“股票代码”和“名称”列\n", "stock1_data = stock1_data.drop(['股票代码','名称'], axis=1)\n", "stock1_data = stock1_data.query('日期.dt.year == 2021')\n", "\n", "# 重新命名列名\n", "column_mapping = {\n", "    '日期': 'Date',\n", "    '收盘价': 'Close',\n", "    '最高价': 'High',\n", "    '最低价': 'Low',\n", "    '开盘价': 'Open',\n", "}\n", "stock1_data = stock1_data.rename(columns=column_mapping)\n", "# 打印前10条数据\n", "stock1_data.head(10)"]}, {"cell_type": "markdown", "id": "45b94668", "metadata": {}, "source": ["### 读取中国石油"]}, {"cell_type": "code", "execution_count": 12, "id": "be0bc0d2", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Close</th>\n", "      <th>High</th>\n", "      <th>Low</th>\n", "      <th>Open</th>\n", "      <th>前收盘</th>\n", "      <th>涨跌额</th>\n", "      <th>涨跌幅</th>\n", "      <th>换手率</th>\n", "      <th>成交量</th>\n", "      <th>成交金额</th>\n", "      <th>总市值</th>\n", "      <th>流通市值</th>\n", "    </tr>\n", "    <tr>\n", "      <th>日期</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2021-03-23</th>\n", "      <td>4.32</td>\n", "      <td>4.35</td>\n", "      <td>4.31</td>\n", "      <td>4.35</td>\n", "      <td>4.36</td>\n", "      <td>-0.04</td>\n", "      <td>-0.9174</td>\n", "      <td>0.0394</td>\n", "      <td>63729753</td>\n", "      <td>275666312.0</td>\n", "      <td>7.906506e+11</td>\n", "      <td>6.995034e+11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-03-22</th>\n", "      <td>4.36</td>\n", "      <td>4.36</td>\n", "      <td>4.30</td>\n", "      <td>4.31</td>\n", "      <td>4.32</td>\n", "      <td>0.04</td>\n", "      <td>0.9259</td>\n", "      <td>0.0464</td>\n", "      <td>75187588</td>\n", "      <td>325738439.0</td>\n", "      <td>7.979715e+11</td>\n", "      <td>7.059803e+11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-03-19</th>\n", "      <td>4.32</td>\n", "      <td>4.36</td>\n", "      <td>4.30</td>\n", "      <td>4.32</td>\n", "      <td>4.41</td>\n", "      <td>-0.09</td>\n", "      <td>-2.0408</td>\n", "      <td>0.0902</td>\n", "      <td>146109801</td>\n", "      <td>632201307.0</td>\n", "      <td>7.906506e+11</td>\n", "      <td>6.995034e+11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-03-18</th>\n", "      <td>4.41</td>\n", "      <td>4.44</td>\n", "      <td>4.41</td>\n", "      <td>4.43</td>\n", "      <td>4.44</td>\n", "      <td>-0.03</td>\n", "      <td>-0.6757</td>\n", "      <td>0.0479</td>\n", "      <td>77613380</td>\n", "      <td>343180296.0</td>\n", "      <td>8.071225e+11</td>\n", "      <td>7.140764e+11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-03-17</th>\n", "      <td>4.44</td>\n", "      <td>4.45</td>\n", "      <td>4.39</td>\n", "      <td>4.45</td>\n", "      <td>4.47</td>\n", "      <td>-0.03</td>\n", "      <td>-0.6711</td>\n", "      <td>0.0574</td>\n", "      <td>92878001</td>\n", "      <td>410329613.0</td>\n", "      <td>8.126131e+11</td>\n", "      <td>7.189340e+11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-03-16</th>\n", "      <td>4.47</td>\n", "      <td>4.50</td>\n", "      <td>4.44</td>\n", "      <td>4.49</td>\n", "      <td>4.51</td>\n", "      <td>-0.04</td>\n", "      <td>-0.8869</td>\n", "      <td>0.0574</td>\n", "      <td>92965905</td>\n", "      <td>414855664.0</td>\n", "      <td>8.181038e+11</td>\n", "      <td>7.237917e+11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-03-15</th>\n", "      <td>4.51</td>\n", "      <td>4.52</td>\n", "      <td>4.43</td>\n", "      <td>4.43</td>\n", "      <td>4.45</td>\n", "      <td>0.06</td>\n", "      <td>1.3483</td>\n", "      <td>0.0886</td>\n", "      <td>143431847</td>\n", "      <td>644391276.0</td>\n", "      <td>8.254246e+11</td>\n", "      <td>7.302686e+11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-03-12</th>\n", "      <td>4.45</td>\n", "      <td>4.48</td>\n", "      <td>4.41</td>\n", "      <td>4.46</td>\n", "      <td>4.44</td>\n", "      <td>0.01</td>\n", "      <td>0.2252</td>\n", "      <td>0.0668</td>\n", "      <td>108147674</td>\n", "      <td>480526340.0</td>\n", "      <td>8.144434e+11</td>\n", "      <td>7.205532e+11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-03-11</th>\n", "      <td>4.44</td>\n", "      <td>4.45</td>\n", "      <td>4.39</td>\n", "      <td>4.42</td>\n", "      <td>4.39</td>\n", "      <td>0.05</td>\n", "      <td>1.1390</td>\n", "      <td>0.0658</td>\n", "      <td>106602200</td>\n", "      <td>471419674.0</td>\n", "      <td>8.126131e+11</td>\n", "      <td>7.189340e+11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-03-10</th>\n", "      <td>4.39</td>\n", "      <td>4.47</td>\n", "      <td>4.39</td>\n", "      <td>4.46</td>\n", "      <td>4.47</td>\n", "      <td>-0.08</td>\n", "      <td>-1.7897</td>\n", "      <td>0.0722</td>\n", "      <td>116971016</td>\n", "      <td>518078260.0</td>\n", "      <td>8.034621e+11</td>\n", "      <td>7.108379e+11</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["            Close  High   Low  Open   前收盘   涨跌额     涨跌幅     换手率        成交量  \\\n", "日期                                                                           \n", "2021-03-23   4.32  4.35  4.31  4.35  4.36 -0.04 -0.9174  0.0394   63729753   \n", "2021-03-22   4.36  4.36  4.30  4.31  4.32  0.04  0.9259  0.0464   75187588   \n", "2021-03-19   4.32  4.36  4.30  4.32  4.41 -0.09 -2.0408  0.0902  146109801   \n", "2021-03-18   4.41  4.44  4.41  4.43  4.44 -0.03 -0.6757  0.0479   77613380   \n", "2021-03-17   4.44  4.45  4.39  4.45  4.47 -0.03 -0.6711  0.0574   92878001   \n", "2021-03-16   4.47  4.50  4.44  4.49  4.51 -0.04 -0.8869  0.0574   92965905   \n", "2021-03-15   4.51  4.52  4.43  4.43  4.45  0.06  1.3483  0.0886  143431847   \n", "2021-03-12   4.45  4.48  4.41  4.46  4.44  0.01  0.2252  0.0668  108147674   \n", "2021-03-11   4.44  4.45  4.39  4.42  4.39  0.05  1.1390  0.0658  106602200   \n", "2021-03-10   4.39  4.47  4.39  4.46  4.47 -0.08 -1.7897  0.0722  116971016   \n", "\n", "                   成交金额           总市值          流通市值  \n", "日期                                                   \n", "2021-03-23  275666312.0  7.906506e+11  6.995034e+11  \n", "2021-03-22  325738439.0  7.979715e+11  7.059803e+11  \n", "2021-03-19  632201307.0  7.906506e+11  6.995034e+11  \n", "2021-03-18  343180296.0  8.071225e+11  7.140764e+11  \n", "2021-03-17  410329613.0  8.126131e+11  7.189340e+11  \n", "2021-03-16  414855664.0  8.181038e+11  7.237917e+11  \n", "2021-03-15  644391276.0  8.254246e+11  7.302686e+11  \n", "2021-03-12  480526340.0  8.144434e+11  7.205532e+11  \n", "2021-03-11  471419674.0  8.126131e+11  7.189340e+11  \n", "2021-03-10  518078260.0  8.034621e+11  7.108379e+11  "]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "# 数据文件\n", "f =  'data/中国石油.csv'\n", "# 读取中国石油股票历史交易数据\n", "stock2_data  = pd.read_csv(f, encoding='gbk', index_col='日期', parse_dates=True)\n", "# 移除“股票代码”和“名称”列\n", "stock2_data = stock2_data.drop(['股票代码','名称'], axis=1)\n", "stock2_data = stock2_data.query('日期.dt.year == 2021')\n", "\n", "# 重新命名列名\n", "column_mapping = {\n", "    '日期': 'Date',\n", "    '收盘价': 'Close',\n", "    '最高价': 'High',\n", "    '最低价': 'Low',\n", "    '开盘价': 'Open',\n", "}\n", "stock2_data = stock2_data.rename(columns=column_mapping)\n", "# 打印前10条数据\n", "stock2_data.head(10)"]}, {"cell_type": "markdown", "id": "7c7708d3", "metadata": {}, "source": ["### 两只股票相关性分析"]}, {"cell_type": "code", "execution_count": 13, "id": "43ccdef0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["皮尔逊相关系数： 0.9330312695712456\n"]}], "source": ["import numpy as np\n", "import pandas as pd\n", "\n", "# 计算相关系数\n", "correlation_matrix = np.corrcoef(stock1_data['Close'], stock2_data['Close'])\n", "# 提取相关系数\n", "correlation = correlation_matrix[0, 1]\n", "print('皮尔逊相关系数：', correlation)"]}, {"cell_type": "markdown", "id": "a1d85c89", "metadata": {}, "source": ["### 回测两只股票的历史数据"]}, {"cell_type": "code", "execution_count": 15, "id": "9d72e7ab", "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'stock1_prices' is not defined", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[15], line 2\u001b[0m\n\u001b[0;32m      1\u001b[0m \u001b[38;5;66;03m# 将价格数据转换为DataFrame\u001b[39;00m\n\u001b[1;32m----> 2\u001b[0m data \u001b[38;5;241m=\u001b[39m pd\u001b[38;5;241m.\u001b[39mDataFrame({\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mstock1\u001b[39m\u001b[38;5;124m'\u001b[39m: \u001b[43mstock1_prices\u001b[49m, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mstock2\u001b[39m\u001b[38;5;124m'\u001b[39m: stock2_prices})\n\u001b[0;32m      4\u001b[0m \u001b[38;5;66;03m# 计算两只股票的价格差异（spread）\u001b[39;00m\n\u001b[0;32m      5\u001b[0m spread \u001b[38;5;241m=\u001b[39m data[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mstock1\u001b[39m\u001b[38;5;124m'\u001b[39m] \u001b[38;5;241m-\u001b[39m data[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mstock2\u001b[39m\u001b[38;5;124m'\u001b[39m]\n", "\u001b[1;31mNameError\u001b[0m: name 'stock1_prices' is not defined"]}], "source": ["stock1_prices = stock1_data['Close']\n", "stock2_prices = stock2_data['Close']\n", "# 将价格数据转换为DataFrame\n", "data = pd.DataFrame({'stock1': stock1_prices, 'stock2': stock2_prices})\n", "\n", "# 计算两只股票的价格差异（spread）\n", "spread = data['stock1'] - data['stock2']\n", "\n", "# 计算价格差异的均值和标准差\n", "mean_spread = spread.mean()\n", "std_spread = spread.std()\n", "\n", "# 定义配对交易策略的信号\n", "threshold = 1.5  # 设置价格差异的阈值\n", "entry_zscore = 1.0  # 设置进入交易的z-score阈值\n", "exit_zscore = 0.0  # 设置退出交易的z-score阈值\n", "\n", "# 定义一个函数来执行配对交易策略\n", "def pairs_trading_strategy(data, mean_spread, std_spread, threshold, entry_zscore, exit_zscore):\n", "    # 计算当前价格差异的z-score\n", "    current_spread = data['stock1'] - data['stock2']\n", "    zscore = (current_spread - mean_spread) / std_spread\n", "    \n", "    # 获取最新的zscore值\n", "    latest_zscore = zscore.iloc[-1]\n", "    \n", "    # 判断是否满足进入交易条件\n", "    if latest_zscore > entry_zscore and np.abs(latest_zscore) > threshold:\n", "        # 执行买入stock1、卖空stock2的交易操作\n", "        print(\"进入交易：买入 中国石化，卖空 中国石油\")\n", "        \n", "    # 判断是否满足退出交易条件\n", "    if np.abs(latest_zscore) < exit_zscore:\n", "        # 执行平仓交易操作\n", "        print(\"退出交易：平仓\")\n", "\n", "# 遍历每个时间点，执行配对交易策略\n", "for timestamp, row in data.iterrows():\n", "    pairs_trading_strategy(data.loc[:timestamp], mean_spread, std_spread, threshold, entry_zscore, exit_zscore)\n"]}, {"cell_type": "code", "execution_count": null, "id": "52e7f6db", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}