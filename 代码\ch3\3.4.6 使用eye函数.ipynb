{"cells": [{"cell_type": "code", "execution_count": 3, "id": "40a6c8d7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[[1. 0. 0. 0.]\n", " [0. 1. 0. 0.]\n", " [0. 0. 1. 0.]]\n", "float64\n", "[[0. 1. 0. 0.]\n", " [0. 0. 1. 0.]\n", " [0. 0. 0. 1.]]\n", "float64\n", "[[0. 1. 0. 0.]\n", " [0. 0. 1. 0.]\n", " [0. 0. 0. 1.]]\n", "float64\n"]}], "source": ["import numpy as np\n", "c = np.eye(3,4)\n", "print(c)\n", "print(c.dtype)\n", "\n", "d = np.eye(3, 4, k=1)\n", "print(d)\n", "print(d.dtype)\n", "\n", "e = np.eye(3, 4, k=1, dtype=float)\n", "print(e)\n", "print(e.dtype)"]}, {"cell_type": "code", "execution_count": null, "id": "e7cf2458", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}