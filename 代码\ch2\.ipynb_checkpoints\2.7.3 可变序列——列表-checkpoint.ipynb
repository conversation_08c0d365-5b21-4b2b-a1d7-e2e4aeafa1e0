{"cells": [{"cell_type": "code", "execution_count": 2, "id": "5b47ebf0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["L1： [20, 10, 50, 40, 30]\n", "a1数据类型是： <class 'list'>\n", "a2数据类型是： <class 'list'>\n", "['张三', '李四', '王五']\n", "['张三', '李四', '王五', '董六']\n"]}], "source": ["# 通过元素之间用逗号分隔创建列表\n", "L1 = [20, 10, 50, 40, 30]\n", "print('L1：', L1)\n", "\n", "L2 = ['Hello', 'World', 1, 2, 3] \n", "\n", "# 通过list函数创建列表\n", "L3 = list((20, 10, 50, 40, 30)) \n", "\n", "a1 = [10]\n", "a2 = [10, ]\n", "print('a1数据类型是：', type(a1)) \n", "\n", "print('a2数据类型是：', type(a2)) \n", "\n", "s_list = ['张三', '李四', '王五']\n", "print(s_list)\n", "s_list.append('董六')\n", "print(s_list)\n", "s_list.remove('王五') "]}, {"cell_type": "code", "execution_count": null, "id": "f5930082", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}