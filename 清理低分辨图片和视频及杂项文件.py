import os
import shutil
import cv2
import json
import re
from tkinter import Tk, filedialog, Frame, Button, Text, Scrollbar, StringVar, Label, messagebox
from PIL import Image
from datetime import datetime
import threading
import queue
import sys
import time

def select_folder():
    """打开对话框选择文件夹"""
    default_path = r"V:\wangluo\work"  # 设置默认路径
    folder_path = filedialog.askdirectory(title="选择文件夹", initialdir=default_path)
    return folder_path

def get_video_resolution(video_path):
    """获取视频的分辨率"""
    try:
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            return None
            
        # 直接获取分辨率，不读取帧
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        cap.release()
        
        if width == 0 or height == 0:
            return None
            
        return width, height
    except Exception as e:
        return None

def move_low_resolution_videos(folder_path):
    """移动低分辨率视频到lowre文件夹，同时保留目录结构"""
    lowre_folder = os.path.join(folder_path, 'lowre')
    os.makedirs(lowre_folder, exist_ok=True)  # 创建lowre文件夹

    # 首先统计需要处理的文件数量
    total_files = 0
    for root, dirs, files in os.walk(folder_path):
        for file in files:
            if file.lower().endswith(('.mp4', '.avi', '.mov', '.mkv', '.flv', '.ts')):
                total_files += 1

    if total_files == 0:
        print("没有找到需要处理的视频文件")
        return

    print(f"\n开始处理视频文件，共发现 {total_files} 个视频文件")
    processed_files = 0
    skipped_files = 0
    moved_files = 0
    error_files = 0

    for root, dirs, files in os.walk(folder_path):
        for file in files:
            if file.lower().endswith(('.mp4', '.avi', '.mov', '.mkv', '.flv', '.ts')):
                video_path = os.path.join(root, file)
                processed_files += 1
                
                # 更新进度显示
                progress = f"处理进度: {processed_files}/{total_files} ({min(processed_files/total_files*100, 100):.1f}%)"
                current_file = f"当前文件: {video_path}"
                print(progress)
                print(current_file)
                
                resolution = get_video_resolution(video_path)
                if resolution is None:
                    skipped_files += 1
                    error_files += 1
                    continue

                # 判断条件：高度和宽度都小于800像素
                if resolution[0] < 800 and resolution[1] < 800:
                    # 保留原有文件夹结构
                    relative_path = os.path.relpath(root, folder_path)
                    target_folder = os.path.join(lowre_folder, relative_path)
                    os.makedirs(target_folder, exist_ok=True)  # 创建目标文件夹
                    target_path = os.path.join(target_folder, file)
                    try:
                        shutil.move(video_path, target_path)  # 移动文件
                        moved_files += 1
                        print(f"\n移动视频文件: {video_path} -> {target_path}，分辨率: {resolution}")
                    except Exception as e:
                        print(f"\n移动文件失败 {video_path}: {str(e)}")
                        skipped_files += 1
                        error_files += 1

    print(f"\n\n视频处理完成:")
    print(f"- 总文件数: {total_files}")
    print(f"- 成功移动: {moved_files}")
    print(f"- 跳过文件: {skipped_files}")
    print(f"- 错误文件: {error_files}")

def check_and_delete_files(folder_path, extensions):
    """删除指定扩展名的文件"""
    found_files = False
    # 定义需要保护的文件
    protected_files = {'passdict.txt'}

    # 遍历指定文件夹及其子文件夹
    for root, dirs, files in os.walk(folder_path):
        for filename in files:
            # 如果文件在保护列表中，跳过
            if filename in protected_files:
                continue
                
            filename_lower = filename.lower()
            if filename_lower.endswith(extensions):
                filepath = os.path.join(root, filename)
                try:
                    print(f"删除文件: {filepath}")  # 打印删除信息
                    os.remove(filepath)
                    found_files = True
                except PermissionError:
                    print(f"无法删除文件 (权限错误): {filepath}")  # 打印权限错误信息
                except Exception as e:
                    print(f"删除文件 {filepath} 时出错：{e}")  # 打印其他错误信息

    return found_files

def move_low_resolution_images(folder_path):
    """移动低分辨率图片到lowrespic文件夹，同时保留目录结构"""
    lowres_folder = os.path.join(folder_path, 'lowrespic')  # 创建 lowrespic 文件夹的路径
    os.makedirs(lowres_folder, exist_ok=True)  # 创建lowrespic文件夹（如果不存在）

    # 首先统计需要处理的文件数量
    total_files = 0
    for root, dirs, files in os.walk(folder_path):
        for filename in files:
            if filename.lower().endswith(('.jpg', '.png', '.jpeg')):
                total_files += 1

    if total_files == 0:
        print("没有找到需要处理的图片文件")
        return

    print(f"\n开始处理图片文件，共发现 {total_files} 个图片文件")
    processed_files = 0
    skipped_files = 0
    moved_files = 0
    error_files = 0

    for root, dirs, files in os.walk(folder_path):
        for filename in files:
            filename_lower = filename.lower()
            if filename_lower.endswith(('.jpg', '.png', '.jpeg')):
                filepath = os.path.join(root, filename)
                processed_files += 1
                
                # 更新进度显示
                progress = f"处理进度: {processed_files}/{total_files} ({min(processed_files/total_files*100, 100):.1f}%)"
                current_file = f"当前文件: {filepath}"
                print(progress)
                print(current_file)
                
                try:
                    with Image.open(filepath) as im:
                        width, height = im.size

                    # 检查分辨率是否小于 800x800
                    if width < 800 or height < 800:
                        # 构建保留原始路径信息的目标路径
                        relative_path = os.path.relpath(root, folder_path)
                        target_folder = os.path.join(lowres_folder, relative_path)
                        os.makedirs(target_folder, exist_ok=True)

                        # 移动文件到目标文件夹
                        target_filepath = os.path.join(target_folder, filename)
                        try:
                            shutil.move(filepath, target_filepath)
                            moved_files += 1
                            print(f"\n移动低分辨率文件: {filepath} -> {target_filepath}，分辨率: {width}x{height}")
                        except Exception as e:
                            print(f"\n移动文件失败 {filepath}: {str(e)}")
                            skipped_files += 1
                            error_files += 1
                except Exception as e:
                    print(f"\n处理文件失败 {filepath}: {str(e)}")
                    skipped_files += 1
                    error_files += 1
                    continue

    print(f"\n\n图片处理完成:")
    print(f"- 总文件数: {total_files}")
    print(f"- 成功移动: {moved_files}")
    print(f"- 跳过文件: {skipped_files}")
    print(f"- 错误文件: {error_files}")

def delete_empty_folders(folder_path):
    """删除空文件夹"""
    for root, dirs, files in os.walk(folder_path, topdown=False):
        for dir_name in dirs:
            dir_path = os.path.join(root, dir_name)
            if not os.listdir(dir_path):  # 检查目录是否为空
                print(f"文件夹 {dir_path} 是空的，将被删除。")
                try:
                    os.rmdir(dir_path)
                except PermissionError:
                    print(f"无法删除文件夹 (权限错误): {dir_path}")  # 打印权限错误信息
                except Exception as e:
                    print(f"删除文件夹 {dir_path} 时出错：{e}")  # 打印其他错误信息

def delete_small_files(folder_path):
    """删除大小为零或小于50KB的文件，包括隐藏文件"""
    found_files = False
    # 定义需要保护的文件
    protected_files = {'passdict.txt'}
    
    for root, dirs, files in os.walk(folder_path):
        for filename in files:
            # 如果文件在保护列表中，跳过
            if filename in protected_files:
                continue
                
            filepath = os.path.join(root, filename)
            # 检查文件大小
            if os.path.getsize(filepath) < 50 * 1024:  # 小于50KB
                try:
                    print(f"删除小文件: {filepath}")
                    os.remove(filepath)
                    found_files = True
                except PermissionError:
                    print(f"无法删除文件 (权限错误): {filepath}")  # 打印权限错误信息
                except Exception as e:
                    print(f"删除文件 {filepath} 时出错：{e}")  # 打印其他错误信息
    return found_files

def is_simple_filename(filename):
    """判断是否为简单文件名（仅包含数字和个别字母）"""
    # 去除文件扩展名
    name_without_ext = os.path.splitext(filename)[0]
    
    # 如果文件名长度小于8且主要由数字构成，或者完全由数字构成
    if len(name_without_ext) < 8 and name_without_ext.isdigit():
        return True
        
    # 检查是否主要由数字和少量字母组成
    # 统计数字和字母的数量
    digit_count = sum(c.isdigit() for c in name_without_ext)
    letter_count = sum(c.isalpha() for c in name_without_ext)
    
    # 如果数字占比超过60%且字母少于3个，认为是简单文件名
    total_len = len(name_without_ext)
    if total_len > 0:
        digit_ratio = digit_count / total_len
        if digit_ratio > 0.6 and letter_count < 3:
            return True
    
    return False

def process_single_item_folders(folder_path):
    """处理只有一个子文件夹或文件的文件夹"""
    move_records = []
    
    # 定义需要跳过的特定文件名
    skip_files = {'passdict.txt'}
    
    for root, dirs, files in os.walk(folder_path, topdown=False):
        # 跳过特定文件夹
        if os.path.basename(root) in ['lowre', 'lowrespic']:
            continue
            
        # 获取当前文件夹中的所有项目（文件和文件夹）
        all_items = dirs + files
        
        # 如果文件夹为空，跳过
        if not all_items:
            continue
            
        # 如果只有一个项目
        if len(all_items) == 1:
            single_item = all_items[0]
            
            # 如果文件名在跳过列表中，直接跳过
            if single_item in skip_files:
                print(f"跳过特定文件: {os.path.join(root, single_item)}")
                continue
                
            single_item_path = os.path.join(root, single_item)
            parent_folder = os.path.dirname(root)
            current_folder_name = os.path.basename(root)
            
            # 如果是文件夹
            if os.path.isdir(single_item_path):
                # 移动子文件夹中的所有内容到父文件夹
                for item in os.listdir(single_item_path):
                    src_path = os.path.join(single_item_path, item)
                    dst_path = os.path.join(root, item)
                    
                    # 如果目标路径已存在，添加数字后缀
                    if os.path.exists(dst_path):
                        base, ext = os.path.splitext(dst_path)
                        counter = 1
                        while os.path.exists(f"{base}_{counter}{ext}"):
                            counter += 1
                        dst_path = f"{base}_{counter}{ext}"
                    
                    shutil.move(src_path, dst_path)
                    move_records.append({
                        'type': 'folder_content',
                        'src': src_path,
                        'dst': dst_path,
                        'parent_folder': single_item_path
                    })
                # 删除空文件夹
                os.rmdir(single_item_path)
                print(f"已移动文件夹 {single_item_path} 中的内容到 {root}")
                
            # 如果是文件
            elif os.path.isfile(single_item_path):
                # 检查是否为简单文件名
                if is_simple_filename(single_item):
                    # 使用文件夹名作为新文件名，保留原扩展名
                    ext = os.path.splitext(single_item)[1]
                    new_filename = current_folder_name + ext
                    dst_path = os.path.join(parent_folder, new_filename)
                    
                    # 如果目标路径已存在，添加数字后缀
                    if os.path.exists(dst_path):
                        base = os.path.join(parent_folder, current_folder_name)
                        counter = 1
                        while os.path.exists(f"{base}_{counter}{ext}"):
                            counter += 1
                        dst_path = f"{base}_{counter}{ext}"
                    
                    print(f"重命名并移动文件: {single_item} -> {os.path.basename(dst_path)}")
                else:
                    dst_path = os.path.join(parent_folder, single_item)
                    
                    # 如果目标路径已存在，添加数字后缀
                    if os.path.exists(dst_path):
                        base, ext = os.path.splitext(dst_path)
                        counter = 1
                        while os.path.exists(f"{base}_{counter}{ext}"):
                            counter += 1
                        dst_path = f"{base}_{counter}{ext}"
                
                shutil.move(single_item_path, dst_path)
                move_records.append({
                    'type': 'single_file',
                    'src': single_item_path,
                    'dst': dst_path,
                    'original_name': single_item
                })
                print(f"已移动文件 {single_item_path} 到 {dst_path}")
    
    # 保存移动记录
    if move_records:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        record_file = os.path.join(folder_path, f"move_records_{timestamp}.json")
        with open(record_file, 'w', encoding='utf-8') as f:
            json.dump(move_records, f, ensure_ascii=False, indent=4)
        print(f"移动记录已保存到: {record_file}")
    
    # 移动完成后删除所有空文件夹
    print("\n开始清理空文件夹...")
    empty_folders_count = 0
    for root, dirs, files in os.walk(folder_path, topdown=False):
        for dir_name in dirs:
            dir_path = os.path.join(root, dir_name)
            try:
                # 检查文件夹是否为空
                if not os.listdir(dir_path):
                    os.rmdir(dir_path)
                    empty_folders_count += 1
                    print(f"已删除空文件夹: {dir_path}")
            except Exception as e:
                print(f"删除文件夹 {dir_path} 时出错: {str(e)}")
    
    if empty_folders_count > 0:
        print(f"\n共删除 {empty_folders_count} 个空文件夹")
    else:
        print("\n没有找到需要删除的空文件夹")
    
    return move_records

def restore_moves(record_file):
    """根据记录文件恢复移动操作"""
    try:
        with open(record_file, 'r', encoding='utf-8') as f:
            move_records = json.load(f)
        
        # 反向遍历记录以恢复
        for record in reversed(move_records):
            if record['type'] == 'single_file':
                # 确保目标文件夹存在
                os.makedirs(os.path.dirname(record['src']), exist_ok=True)
                shutil.move(record['dst'], record['src'])
                print(f"已恢复文件: {record['dst']} -> {record['src']}")
            elif record['type'] == 'folder_content':
                # 确保原始文件夹存在
                os.makedirs(record['parent_folder'], exist_ok=True)
                shutil.move(record['dst'], record['src'])
                print(f"已恢复内容: {record['dst']} -> {record['src']}")
        
        print("所有移动操作已恢复完成")
        # 删除记录文件
        os.remove(record_file)
        
    except Exception as e:
        print(f"恢复过程中出错: {str(e)}")

class RedirectText:
    def __init__(self, text_widget):
        self.text_widget = text_widget
        self.queue = queue.Queue()
        self.update_timer = None
        self.original_stdout = sys.stdout
        sys.stdout = self
        self.buffer = []
        self.buffer_size = 100  # 缓冲区大小
        self.last_update = 0
        self.update_interval = 0.1  # 更新间隔（秒）
        self.progress_line = None  # 进度行号
        self.current_file_line = None  # 当前文件行号
        self.last_message_line = None  # 最后一条消息的行号
        self.message_buffer = []  # 消息缓冲区
        self.last_message_time = 0  # 最后一条消息的时间
        self.message_update_interval = 0.5  # 消息更新间隔（秒）

    def write(self, string):
        # 检查是否是进度或当前文件信息
        if string.startswith("处理进度:") or string.startswith("当前文件:"):
            self.text_widget.after(1, self._update_progress, string)
            return

        # 其他输出添加到消息缓冲区
        current_time = time.time()
        if current_time - self.last_message_time >= self.message_update_interval:
            self.flush()
            self.last_message_time = current_time
        
        self.message_buffer.append(string)

    def _update_progress(self, string):
        """在主线程中更新进度信息"""
        self.text_widget.configure(state='normal')
        
        # 如果是进度信息
        if string.startswith("处理进度:"):
            if self.progress_line is None:
                # 第一次显示进度，在末尾添加
                self.text_widget.insert('end', '\n')
                self.progress_line = self.text_widget.index('end-2c')
            else:
                # 更新已有进度行
                self.text_widget.delete(f"{self.progress_line}", f"{self.progress_line} lineend")
            self.text_widget.insert(f"{self.progress_line}", string)
        
        # 如果是当前文件信息
        elif string.startswith("当前文件:"):
            if self.current_file_line is None:
                # 第一次显示文件信息，在进度行后添加
                self.text_widget.insert('end', '\n')
                self.current_file_line = self.text_widget.index('end-2c')
            else:
                # 更新已有文件信息行
                self.text_widget.delete(f"{self.current_file_line}", f"{self.current_file_line} lineend")
            self.text_widget.insert(f"{self.current_file_line}", string)
        
        self.text_widget.configure(state='disabled')

    def flush(self):
        if not self.message_buffer:
            return
            
        text = ''.join(self.message_buffer)
        self.message_buffer = []
        self.last_update = time.time()
        
        # 使用after方法在主线程中更新界面
        self.text_widget.after(1, self._update_text, text)

    def _update_text(self, text):
        self.text_widget.configure(state='normal')
        
        # 如果有最后一条消息行，则更新它
        if self.last_message_line is not None:
            self.text_widget.delete(f"{self.last_message_line}", 'end')
        else:
            # 第一次显示消息，在文件信息后添加
            self.text_widget.insert('end', '\n')
            self.last_message_line = self.text_widget.index('end-2c')
        
        # 插入新消息
        self.text_widget.insert(self.last_message_line, text)
        
        self.text_widget.configure(state='disabled')

    def __del__(self):
        sys.stdout = self.original_stdout

class FileCleanerGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("文件清理工具")
        self.root.geometry("800x400")  # 减小初始高度
        
        # 创建主框架
        self.main_frame = Frame(root)
        self.main_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # 创建按钮框架
        self.button_frame = Frame(self.main_frame)
        self.button_frame.pack(fill='x', pady=(0, 5))  # 减小按钮区域的下边距
        
        # 创建按钮
        self.create_buttons()
        
        # 创建文本显示区域
        self.create_text_area()
        
        # 当前选择的文件夹路径
        self.current_folder = StringVar()
        
        # 处理状态
        self.is_processing = False
        self.should_stop = False
        self.progress_var = StringVar()
        self.current_file_var = StringVar()
        
        # 创建进度显示标签
        self.progress_label = Label(self.main_frame, textvariable=self.progress_var)
        self.progress_label.pack(fill='x', pady=(0, 2))  # 减小标签的下边距
        self.current_file_label = Label(self.main_frame, textvariable=self.current_file_var)
        self.current_file_label.pack(fill='x', pady=(0, 2))  # 减小标签的下边距

    def create_buttons(self):
        # 左侧按钮框架
        self.left_button_frame = Frame(self.button_frame)
        self.left_button_frame.pack(side='left', fill='x', expand=True)
        
        # 选择文件夹按钮
        self.select_folder_btn = Button(self.left_button_frame, text="选择文件夹", command=self.select_folder)
        self.select_folder_btn.pack(side='left', padx=2)  # 减小按钮之间的间距
        
        # 清理低分辨率文件按钮
        self.clean_low_res_btn = Button(self.left_button_frame, text="清理低分辨率文件", command=self.clean_low_resolution)
        self.clean_low_res_btn.pack(side='left', padx=2)  # 减小按钮之间的间距
        
        # 处理单文件/单文件夹按钮
        self.process_single_btn = Button(self.left_button_frame, text="处理单文件/单文件夹", command=self.process_single_items)
        self.process_single_btn.pack(side='left', padx=2)  # 减小按钮之间的间距
        
        # 恢复移动操作按钮
        self.restore_btn = Button(self.left_button_frame, text="恢复移动操作", command=self.show_restore_dialog)
        self.restore_btn.pack(side='left', padx=2)  # 减小按钮之间的间距
        
        # 右侧停止按钮
        self.stop_btn = Button(self.button_frame, text="停止", command=self.stop_processing, state='disabled')
        self.stop_btn.pack(side='right', padx=2)  # 减小按钮之间的间距

    def create_text_area(self):
        # 创建文本框
        self.text_area = Text(self.main_frame, wrap='word', state='disabled', height=15)  # 设置固定高度
        self.text_area.pack(side='left', fill='both', expand=True)
        
        # 重定向标准输出到文本框
        self.redirect = RedirectText(self.text_area)

    def stop_processing(self):
        """停止当前处理"""
        if self.is_processing:
            self.should_stop = True
            print("\n正在停止处理...")
            self.stop_btn.configure(state='disabled')

    def update_progress(self, progress, current_file):
        """更新进度显示"""
        self.progress_var.set(progress)
        self.current_file_var.set(current_file)
        self.root.update_idletasks()

    def select_folder(self):
        if self.is_processing:
            messagebox.showwarning("警告", "请等待当前操作完成！")
            return
            
        folder_path = filedialog.askdirectory(title="选择文件夹", initialdir=r"V:\wangluo\work")
        if folder_path:
            self.current_folder.set(folder_path)
            print(f"已选择文件夹: {folder_path}")

    def clean_low_resolution(self):
        if self.is_processing:
            messagebox.showwarning("警告", "请等待当前操作完成！")
            return
            
        if self.current_folder.get() == "未选择文件夹":
            messagebox.showwarning("警告", "请先选择文件夹！")
            return
            
        self.is_processing = True
        self.should_stop = False
        self.clean_low_res_btn.configure(state='disabled')
        self.process_single_btn.configure(state='disabled')
        self.restore_btn.configure(state='disabled')
        self.stop_btn.configure(state='normal')
        
        def run_clean():
            try:
                # 删除特定扩展名的文件
                if not self.should_stop:
                    found_html_files = check_and_delete_files(
                        self.current_folder.get(),
                        ('.htm', '.html', '.txt', '.url', '.info', '.db', '.mht', '.svg', 'editor.gif', 'signbg1.png', '上村花论坛看小姐姐.jpg', '.torrent', '.chm')
                    )
                    if not found_html_files:
                        print("没有找到杂项文件。")

                # 删除大小为零或小于50KB的文件
                if not self.should_stop:
                    found_small_files = delete_small_files(self.current_folder.get())
                    if not found_small_files:
                        print("没有找到符合条件的小文件。")

                # 移动低分辨率视频
                if not self.should_stop:
                    move_low_resolution_videos(self.current_folder.get())

                # 移动低分辨率图像
                if not self.should_stop:
                    found_images = move_low_resolution_images(self.current_folder.get())
                    if not found_images:
                        print("没有找到低分辨率图片。")

                # 删除空文件夹
                if not self.should_stop:
                    delete_empty_folders(self.current_folder.get())
                
                if self.should_stop:
                    print("\n处理已停止！")
                else:
                    print("\n清理完成！")
            finally:
                self.root.after(0, self.finish_processing)
            
        # 在新线程中运行清理操作
        threading.Thread(target=run_clean, daemon=True).start()

    def process_single_items(self):
        if self.is_processing:
            messagebox.showwarning("警告", "请等待当前操作完成！")
            return
            
        if self.current_folder.get() == "未选择文件夹":
            messagebox.showwarning("警告", "请先选择文件夹！")
            return
            
        self.is_processing = True
        self.should_stop = False
        self.clean_low_res_btn.configure(state='disabled')
        self.process_single_btn.configure(state='disabled')
        self.restore_btn.configure(state='disabled')
        self.stop_btn.configure(state='normal')
        
        def run_process():
            try:
                if not self.should_stop:
                    process_single_item_folders(self.current_folder.get())
                    if self.should_stop:
                        print("\n处理已停止！")
                    else:
                        print("\n处理完成！")
            finally:
                self.root.after(0, self.finish_processing)
            
        # 在新线程中运行处理操作
        threading.Thread(target=run_process, daemon=True).start()

    def finish_processing(self):
        """完成处理后的清理工作"""
        self.is_processing = False
        self.should_stop = False
        self.clean_low_res_btn.configure(state='normal')
        self.process_single_btn.configure(state='normal')
        self.restore_btn.configure(state='normal')
        self.stop_btn.configure(state='disabled')
        self.progress_var.set("")
        self.current_file_var.set("")

    def show_restore_dialog(self):
        if self.is_processing:
            messagebox.showwarning("警告", "请等待当前操作完成！")
            return
            
        if self.current_folder.get() == "未选择文件夹":
            messagebox.showwarning("警告", "请先选择文件夹！")
            return
            
        record_files = [f for f in os.listdir(self.current_folder.get()) 
                       if f.startswith("move_records_") and f.endswith(".json")]
        
        if not record_files:
            messagebox.showinfo("提示", "没有找到移动记录文件")
            return
            
        # 创建记录文件选择对话框
        dialog = Tk()
        dialog.title("选择要恢复的记录文件")
        dialog.geometry("400x300")
        
        Label(dialog, text="请选择要恢复的记录文件：").pack(pady=10)
        
        for file in record_files:
            btn = Button(dialog, text=file, 
                        command=lambda f=file: self.restore_moves(f, dialog))
            btn.pack(fill='x', padx=10, pady=2)
            
        Button(dialog, text="取消", command=dialog.destroy).pack(pady=10)

    def restore_moves(self, record_file, dialog):
        dialog.destroy()
        self.is_processing = True
        self.should_stop = False
        self.clean_low_res_btn.configure(state='disabled')
        self.process_single_btn.configure(state='disabled')
        self.restore_btn.configure(state='disabled')
        self.stop_btn.configure(state='normal')
        
        def run_restore():
            try:
                if not self.should_stop:
                    restore_moves(os.path.join(self.current_folder.get(), record_file))
                    if self.should_stop:
                        print("\n恢复已停止！")
                    else:
                        print("\n恢复完成！")
            finally:
                self.root.after(0, self.finish_processing)
            
        # 在新线程中运行恢复操作
        threading.Thread(target=run_restore, daemon=True).start()

if __name__ == "__main__":
    root = Tk()
    app = FileCleanerGUI(root)
    root.mainloop()
