import uuid
import hashlib
import time
import requests
import logging
import sys
sys.path.append(r'Z:\work')  # 添加配置文件路径
from config import YOUDAO_APP_KEY, YOUDAO_APP_SECRET  # 导入有道API配置

def encrypt(signStr):
    """计算有道API签名"""
    hash_algorithm = hashlib.sha256()
    hash_algorithm.update(signStr.encode('utf-8'))
    return hash_algorithm.hexdigest()

def truncate(q):
    """截取字符串"""
    if q is None:
        return None
    size = len(q)
    return q if size <= 20 else q[0:10] + str(size) + q[size - 10:size]

def translate_text(text):
    """调用有道API翻译文本"""
    try:
        youdao_url = 'https://openapi.youdao.com/api'
        curtime = str(int(time.time()))
        salt = str(uuid.uuid1())
        
        sign_str = YOUDAO_APP_KEY + truncate(text) + salt + curtime + YOUDAO_APP_SECRET
        sign = encrypt(sign_str)
        
        data = {
            'q': text,
            'from': 'ja',
            'to': 'zh-CHS',
            'appKey': YOUDAO_APP_KEY,
            'salt': salt,
            'sign': sign,
            'signType': 'v3',
            'curtime': curtime,
        }
        
        headers = {'Content-Type': 'application/x-www-form-urlencoded'}
        response = requests.post(youdao_url, data=data, headers=headers)
        result = response.json()
        
        if result.get('translation'):
            return result['translation'][0]
        else:
            logging.error(f"翻译失败: {result.get('errorCode')}")
            return text
            
    except Exception as e:
        logging.error(f"翻译出错: {str(e)}")
        return text

def translate_title(text):
    """翻译标题，保留原文的空格格式"""
    try:
        # 找到最后一个空格的位置
        last_space_index = text.rfind(' ')
        
        if last_space_index != -1:
            # 分割文本为两部分
            part1 = text[:last_space_index]
            part2 = text[last_space_index + 1:]
            
            # 去除多余空格后分别翻译
            part1_translated = translate_text(part1)
            part2_translated = translate_text(part2)
            
            # 组合翻译后的文本
            translated_text = f"{part1_translated} {part2_translated}"
        else:
            # 如果没有空格，直接翻译整个文本
            translated_text = translate_text(text)
        
        return translated_text
    except Exception as e:
        logging.error(f"翻译出错: {str(e)}")
        return text 