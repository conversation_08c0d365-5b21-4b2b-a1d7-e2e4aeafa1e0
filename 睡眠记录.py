import tkinter as tk
from tkinter import messagebox, ttk
from tkcalendar import DateEntry
import pandas as pd
from datetime import datetime


# 定义保存数据的函数
def save_data():
    selected_meds = [med_name for med_name, var in meds_vars.items() if var.get()]
    selected_dosages = [dosage.get() for dosage in meds_dosage_entries]
    selected_times = [f"{hour.get()}:{minute.get()}" for hour, minute in zip(meds_hour_entries, meds_minute_entries) if hour.get() and minute.get()]

    data = {
        "日期": date_entry.get(),
        "上床时间": f"{bed_hour_entry.get()}:{bed_minute_entry.get()}",
        "入睡时间": f"{sleep_hour_entry.get()}:{sleep_minute_entry.get()}",
        "睡醒时间": f"{wake_hour_entry.get()}:{wake_minute_entry.get()}",
        "起床时间": f"{get_up_hour_entry.get()}:{get_up_minute_entry.get()}",
        "半夜是否醒过": woke_up_var.get(),
        "是否下床小便": bathroom_var.get(),
        "醒来时长": awake_duration_entry.get(),
        "是否服用药物": meds_var.get(),
        "药物名称": ", ".join(selected_meds) if meds_var.get() else "",
        "服用量": ", ".join(selected_dosages) if meds_var.get() else "",
        "服用时间": ", ".join(selected_times) if meds_var.get() else "",
        "白天服用的药物或保健品名称及数量": ", ".join([f"{med} {daytime_meds_vars[med].get()}" for med in daytime_meds_names]),
        "对睡眠的满意度": satisfaction_entry.get()
    }

    # 将数据保存到Excel文件
    df = pd.DataFrame([data])
    try:
        df_existing = pd.read_excel('sleep_data.xlsx')
        df = pd.concat([df_existing, df], ignore_index=True)
    except FileNotFoundError:
        pass

    df.to_excel('z:\\work\\sleep_data.xlsx', index=False)
    messagebox.showinfo("保存成功", "数据已保存到sleep_data.xlsx")

# 定义勾选事件函数
def on_woke_up_check():
    if woke_up_var.get():
        bathroom_checkbutton.pack()
        awake_duration_label.pack()
        awake_duration_entry.pack()
    else:
        bathroom_checkbutton.pack_forget()
        awake_duration_label.pack_forget()
        awake_duration_entry.pack_forget()

def on_meds_check():
    meds_frame.pack_forget()  # 先隐藏药物输入框
    if meds_var.get():
        meds_frame.pack()  # 然后重新显示药物输入框
    # 重新布局所有控件
    daytime_meds_label.pack_forget()
    daytime_meds_frame.pack_forget()
    satisfaction_label.pack_forget()
    satisfaction_entry.pack_forget()
    save_button.pack_forget()

    daytime_meds_label.pack()
    daytime_meds_frame.pack()
    satisfaction_label.pack()
    satisfaction_entry.pack()
    save_button.pack()

# 创建小时和分钟选择列表
hours_bed_sleep = [f"{h:02d}" for h in range(22, 24)] + [f"{h:02d}" for h in range(0, 22)]
hours_wake_getup = [f"{h:02d}" for h in range(4, 24)] + [f"{h:02d}" for h in range(0, 4)]
minutes = [f"{m:02d}" for m in range(0, 60, 5)]

def create_time_input(parent, start_hour):
    frame = tk.Frame(parent)
    hour_entry = ttk.Combobox(frame, values=start_hour, width=3)
    if start_hour == hours_bed_sleep:
        hour_entry.set("22")  # 默认设置小时为22
    else:
        hour_entry.set("04")  # 默认设置小时为04
    minute_entry = ttk.Combobox(frame, values=minutes, width=3)
    minute_entry.set("00")  # 默认设置分钟为00
    hour_entry.pack(side=tk.LEFT)
    tk.Label(frame, text=":").pack(side=tk.LEFT)
    minute_entry.pack(side=tk.LEFT)
    frame.pack()
    return frame, hour_entry, minute_entry


# 创建主窗口
root = tk.Tk()
root.title("睡眠数据记录")
root.geometry("400x550")  # 设置固定宽度和高度
root.pack_propagate(False)  # 防止窗口大小随内容变化

# 创建并放置标签和输入框
tk.Label(root, text="记录日期").pack()
date_entry = DateEntry(root, width=12, background='darkblue', foreground='white', borderwidth=2)
date_entry.set_date(datetime.now())
date_entry.pack()

labels = ["上床时间", "入睡时间", "睡醒时间", "起床时间"]
time_entries = []

for i, label in enumerate(labels, start=1):
    tk.Label(root, text=label).pack()
    if label in ["上床时间", "入睡时间"]:
        time_frame, hour_entry, minute_entry = create_time_input(root, hours_bed_sleep)
    else:
        time_frame, hour_entry, minute_entry = create_time_input(root, hours_wake_getup)
    time_entries.append((hour_entry, minute_entry))

bed_hour_entry, bed_minute_entry = time_entries[0]
sleep_hour_entry, sleep_minute_entry = time_entries[1]
wake_hour_entry, wake_minute_entry = time_entries[2]
get_up_hour_entry, get_up_minute_entry = time_entries[3]

# 创建并放置复选框
woke_up_var = tk.BooleanVar()
bathroom_var = tk.BooleanVar()
meds_var = tk.BooleanVar()

# 使用一个单独的框架来包含夜间醒来的相关选项，以确保它们可以一起显示和隐藏
night_waking_frame = tk.Frame(root)
tk.Checkbutton(night_waking_frame, text="半夜是否醒过", variable=woke_up_var, command=on_woke_up_check).pack(anchor='w')
bathroom_checkbutton = tk.Checkbutton(night_waking_frame, text="是否下床小便", variable=bathroom_var)
awake_duration_label = tk.Label(night_waking_frame, text="醒来时长")
awake_durations = [f"{i * 10:02d}" for i in range(16)]  # 醒来时长每10分钟一档，最多150分钟
awake_duration_entry = ttk.Combobox(night_waking_frame, values=awake_durations)
awake_duration_entry.pack_forget()

# 初始隐藏半夜醒来后的选项
bathroom_checkbutton.pack_forget()
awake_duration_label.pack_forget()
awake_duration_entry.pack_forget()

night_waking_frame.pack()

# 创建药物输入框
tk.Checkbutton(root, text="是否服用药物", variable=meds_var, command=on_meds_check).pack()

meds_frame = tk.Frame(root)
meds_frame.pack_forget()  # 初始隐藏药物输入框
meds_vars = {}
meds_names = ["佑佐匹克隆", "艾司唑仑"]
meds_dosage_entries = []
meds_hour_entries = []
meds_minute_entries = []

for med in meds_names:
    frame = tk.Frame(meds_frame)
    frame.pack(fill=tk.X, padx=5, pady=2)

    var = tk.BooleanVar()
    meds_vars[med] = var
    chk = tk.Checkbutton(frame, text=med, variable=var, command=lambda m=med: on_med_check(m))
    chk.pack(side=tk.LEFT)

    time_frame, hour_entry, minute_entry = create_time_input(frame, hours_bed_sleep)

    time_frame.pack(side=tk.LEFT, padx=5)
    meds_hour_entries.append(hour_entry)
    meds_minute_entries.append(minute_entry)

    meds_dosage_entry = ttk.Combobox(frame, values=[0.5, 1, 2], width=2)
    meds_dosage_entries.append(meds_dosage_entry)
    meds_dosage_entry.pack(side=tk.LEFT, padx=10)

    time_frame.pack_forget()
    meds_dosage_entry.pack_forget()

# 动态显示药物时间和剂量
def on_med_check(med):
    idx = meds_names.index(med)
    if meds_vars[med].get():
        meds_hour_entries[idx].master.pack(side=tk.LEFT, padx=5)
        meds_minute_entries[idx].master.pack(side=tk.LEFT, padx=5)
        meds_dosage_entries[idx].pack(side=tk.LEFT, padx=5)
    else:
        meds_hour_entries[idx].master.pack_forget()
        meds_minute_entries[idx].master.pack_forget()
        meds_dosage_entries[idx].pack_forget()


# 白天药物和满意度
daytime_meds_label = tk.Label(root, text="白天服用的药物或保健品")
daytime_meds_label.pack()
daytime_meds_frame = tk.Frame(root)
daytime_meds_frame.pack()

daytime_meds_vars = {}
daytime_meds_names = ["曲唑酮", "酸枣仁"]
for med in daytime_meds_names:
    var = tk.BooleanVar()
    chk = tk.Checkbutton(daytime_meds_frame, text=med, variable=var)
    chk.pack(anchor='w')
    daytime_meds_vars[med] = var

tk.Label(daytime_meds_frame, text="其他:").pack(anchor='w')
other_daytime_meds_entry = tk.Entry(daytime_meds_frame)
other_daytime_meds_entry.pack(anchor='w')

satisfaction_label = tk.Label(root, text="对睡眠的满意度")
satisfaction_label.pack()
satisfaction_entry = ttk.Combobox(root, values=list(range(1, 10)) , width=10)
satisfaction_entry.pack()

# 创建保存按钮
save_button = tk.Button(root, text="保存", command=save_data)
save_button.pack()

# 运行主循环
root.mainloop()
