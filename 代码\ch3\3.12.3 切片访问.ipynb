{"cells": [{"cell_type": "code", "execution_count": 1, "id": "62b8aad8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["   apples  oranges  bananas\n", "A       3        2        1\n", "B       2        4        3\n", "C       0        6        5\n", "   apples  oranges  bananas\n", "A       3        2        1\n", "B       2        4        3\n", "C       0        6        5\n", "   apples  oranges\n", "A       3        2\n", "B       2        4\n", "C       0        6\n", "D       1        8\n", "   apples  oranges\n", "A       3        2\n", "B       2        4\n", "C       0        6\n", "D       1        8\n"]}], "source": ["import pandas as pd\n", "\n", "data = {'apples': [3, 2, 0, 1], 'oranges': [2, 4, 6, 8], 'bananas': [1, 3, 5, 7]}\n", "index = ['A', 'B', 'C', 'D']\n", "df = pd.DataFrame(data, index=index)\n", "\n", "# 使用.loc切片访问行\n", "print(df.loc['A':'C'])\n", "\n", "# 使用.iloc切片访问行\n", "print(df.iloc[0:3])\n", "\n", "# 使用.loc切片访问列\n", "print(df.loc[:, 'apples':'oranges'])\n", "\n", "# 使用.iloc切片访问列\n", "print(df.iloc[:, 0:2])\n"]}, {"cell_type": "code", "execution_count": null, "id": "7b2d88fc", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}