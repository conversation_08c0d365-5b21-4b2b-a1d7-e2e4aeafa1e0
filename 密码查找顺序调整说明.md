# 密码查找顺序调整说明

## 🔄 调整内容

### 原始密码查找顺序
1. **password.txt** 文件
2. **密码字典** (passdict.txt)
3. **无密码** 解压

### 新的密码查找顺序
1. **解压密码文件** (解压密码.txt等)
2. **password.txt** 文件  
3. **密码字典** (passdict.txt)
4. **无密码** 解压

## 📁 支持的解压密码文件

### 文件名支持
程序会按顺序查找以下文件名：
- `解压密码.txt`
- `解压密码.TXT`
- `密码.txt`
- `密码.TXT`
- `password_extract.txt`
- `extract_password.txt`
- `unzip_password.txt`

### 密码格式支持
支持多种密码格式：
```
密码：your_password
密码:your_password
password:your_password
Password:your_password
PASSWORD:your_password
解压密码：your_password
解压密码:your_password
extract password:your_password
Extract Password:your_password
unzip password:your_password
Unzip Password:your_password
解压码：your_password
解压码:your_password
```

### 直接密码格式
如果文件中没有前缀，程序会将非注释行直接作为密码：
```
your_password_here
```

### 无密码指示
支持以下方式表示无密码：
```
密码：无
密码：none
密码：null
密码：空
密码：
无
none
null
空
```

### 注释支持
支持以下注释格式：
```
# 这是注释行
// 这也是注释行
```

## 🔍 查找机制

### 查找范围
- 从压缩文件所在目录开始
- 向上查找最多5级目录
- 直到找到原始选择的文件夹为止

### 查找示例
```
D:\Downloads\压缩包\
├── 解压密码.txt          ← 优先查找
├── password.txt          ← 次优先
├── passdict.txt          ← 第三优先
└── archive.zip
```

## 📊 处理逻辑

### 成功找到密码
1. 读取解压密码文件
2. 解析密码内容
3. 尝试解压
4. 成功则结束，失败则继续下一步

### 无密码指示
1. 如果文件指示无密码（如"密码：无"）
2. 直接尝试无密码解压
3. 成功则结束，失败则继续下一步

### 未找到文件
1. 如果未找到解压密码文件
2. 继续查找password.txt
3. 然后尝试密码字典
4. 最后尝试无密码解压

## 🎯 优势

### 1. 更灵活的密码管理
- 支持多种密码文件名
- 支持多种密码格式
- 支持中英文混合

### 2. 更好的用户体验
- 优先使用专门的解压密码文件
- 支持无密码指示
- 详细的日志记录

### 3. 向后兼容
- 保持对原有password.txt的支持
- 保持对密码字典的支持
- 不影响现有工作流程

## 📝 使用建议

### 创建解压密码文件
推荐在压缩文件所在目录创建`解压密码.txt`文件：
```
解压密码：your_password_here
```

### 批量处理
对于包含多个压缩文件的目录，可以在上级目录放置解压密码文件，程序会自动向上查找。

### 密码管理
- 使用解压密码文件存放特定压缩包的密码
- 使用password.txt存放通用密码
- 使用密码字典存放常用密码列表

## 🔧 技术实现

### 编码支持
支持多种文件编码：
- ANSI (优先)
- GBK
- GB2312  
- UTF-8
- Latin-1

### 错误处理
- 文件不存在：继续下一步
- 编码错误：尝试其他编码
- 读取失败：记录错误并继续

### 日志记录
详细记录密码查找和使用过程：
```
[12:00:10] ℹ️ 找到解压密码文件：D:\Downloads\解压密码.txt
[12:00:10] ℹ️ 使用 ansi 编码成功读取解压密码文件
[12:00:10] ℹ️ 从解压密码文件中解析到密码：test123
[12:00:10] ℹ️ 使用解压密码文件中的密码尝试解压...
[12:00:11] ✅ 成功解压文件：archive.zip
```

## 📋 更新的文件

- `解压缩GUI.py` - 图形界面版本
- `解压缩.py` - 命令行版本
- `测试解压密码文件.py` - 测试脚本

现在解压缩程序会优先查找专门的解压密码文件，提供更灵活和用户友好的密码管理方式！
