import os
import glob
import json
import xml.etree.ElementTree as ET
import pandas as pd
from tkinter import Tk, filedialog, messagebox, Listbox, Button, Entry, Label, Text, END, Menu, SINGLE, mainloop


class NfoModifier:
    def __init__(self, root):
        self.root = root
        self.root.title("NFO Modifier")

        self.folder_path = None
        self.file_path = None
        self.genre_df = None
        self.actor_df = None

        self.rule_log = []  # 用于记录规则的列表

        self.genres_set = set()
        self.actors_set = set()

        self.load_dicts()

        # 初始化界面
        self.scan_button = Button(root, text="Scan Folder", command=self.scan_folder)
        self.scan_button.grid(row=0, column=0, columnspan=2, padx=10, pady=10)

        self.genre_label = Label(root, text="Genres:")
        self.genre_label.grid(row=1, column=0, padx=10, pady=5)

        self.genre_listbox = Listbox(root, selectmode=SINGLE, width=40, height=20)
        self.genre_listbox.grid(row=2, column=0, padx=10, pady=5)
        self.genre_listbox.bind('<Button-3>', self.on_right_click_genre)
        self.genre_listbox.bind('<<ListboxSelect>>', self.on_genre_select)

        self.actor_label = Label(root, text="Actors:")
        self.actor_label.grid(row=1, column=1, padx=10, pady=5)

        self.actor_listbox = Listbox(root, selectmode=SINGLE, width=40, height=20)
        self.actor_listbox.grid(row=2, column=1, padx=10, pady=5)
        self.actor_listbox.bind('<Button-3>', self.on_right_click_actor)
        self.actor_listbox.bind('<<ListboxSelect>>', self.on_actor_select)

        self.delete_button = Button(root, text="Delete Selected", command=self.delete_selected)
        self.delete_button.grid(row=3, column=0, columnspan=2, padx=10, pady=5)

        self.replace_label = Label(root, text="Replace with:")
        self.replace_label.grid(row=4, column=0, padx=10, pady=5, sticky='e')

        self.replace_entry = Entry(root)
        self.replace_entry.grid(row=4, column=1, padx=10, pady=5, sticky='w')

        self.replace_button = Button(root, text="Replace Selected", command=self.replace_selected)
        self.replace_button.grid(row=4, column=1, padx=10, pady=5, sticky='e')

        self.info_label = Label(root, text="Planned Actions:")
        self.info_label.grid(row=5, column=0, columnspan=2, padx=10, pady=5)

        self.info_text = Text(root, width=80, height=10)
        self.info_text.grid(row=6, column=0, columnspan=2, padx=10, pady=5)

        self.apply_button = Button(root, text="Apply Changes", command=self.apply_changes)
        self.apply_button.grid(row=7, column=0, columnspan=2, padx=10, pady=10)

        self.genres_to_delete = set()
        self.genres_to_replace = {}
        self.actors_to_delete = set()
        self.actors_to_replace = {}

        self.load_log()
        self.display_items()

        # 在程序退出时保存字典文件
        root.protocol("WM_DELETE_WINDOW", self.on_closing)

        # 初始化上下文菜单
        self.genre_menu = Menu(root, tearoff=0)
        self.genre_menu.add_command(label="Delete", command=self.delete_genre)

        self.actor_menu = Menu(root, tearoff=0)
        self.actor_menu.add_command(label="Delete", command=self.delete_actor)

    def load_dicts(self):
        if os.path.exists('genres_dict.json'):
            with open('genres_dict.json', 'r', encoding='utf-8') as f:
                self.genres_set = set(json.load(f))
        if os.path.exists('actors_dict.json'):
            with open('actors_dict.json', 'r', encoding='utf-8') as f:
                self.actors_set = set(json.load(f))

    def save_dicts(self):
        with open('genres_dict.json', 'w', encoding='utf-8') as f:
            json.dump(list(self.genres_set), f, ensure_ascii=False)
        with open('actors_dict.json', 'w', encoding='utf-8') as f:
            json.dump(list(self.actors_set), f, ensure_ascii=False)

    def load_log(self):
        log_file = 'modification_log.txt'
        if os.path.exists(log_file):
            with open(log_file, 'r', encoding='utf-8') as log:
                lines = log.readlines()
                lines.reverse()  # 倒序处理，从最新到最旧
                existing_rules = set()  # 用于存储已经存在的规则，避免重复记录
                for line in lines:
                    if line.strip() not in existing_rules:
                        self.rule_log.insert(0, line.strip())
                        self.info_text.insert('1.0', line + "\n")  # 在前面插入规则到待办框
                        existing_rules.add(line.strip())
                        # 解析规则并更新集合
                        action, _, content = line.partition(': ')
                        content = content.strip()
                        if action == 'Delete Genre':
                            self.genres_to_delete.add(content)
                        elif action == 'Delete Actor':
                            self.actors_to_delete.add(content)
                        elif action.startswith('Replace Genre'):
                            original, new = content.split(' with ', 1)
                            self.genres_to_replace[original.strip()] = new.strip()
                        elif action.startswith('Replace Actor'):
                            original, new = content.split(' with ', 1)
                            self.actors_to_replace[original.strip()] = new.strip()

    def add_rule(self, rule):
        if rule not in self.rule_log:
            self.rule_log.insert(0, rule)
            self.info_text.insert('1.0', rule + "\n")  # 在前面插入新规则到待办框
            with open('modification_log.txt', 'a', encoding='utf-8') as log:
                log.write(rule + "\n")

    def delete_selected(self):
        selected_genre_index = self.genre_listbox.curselection()
        selected_actor_index = self.actor_listbox.curselection()

        if selected_genre_index:
            genre = self.genre_listbox.get(selected_genre_index[0])
            rule = f"Delete Genre: {genre}"
            self.genres_to_delete.add(genre)
            self.add_rule(rule)

        if selected_actor_index:
            actor = self.actor_listbox.get(selected_actor_index[0])
            rule = f"Delete Actor: {actor}"
            self.actors_to_delete.add(actor)
            self.add_rule(rule)

    def replace_selected(self):
        replace_text = self.replace_entry.get().strip()
        if not replace_text:
            messagebox.showwarning("Warning", "Replacement text cannot be empty")
            return

        selected_genre_index = self.genre_listbox.curselection()
        selected_actor_index = self.actor_listbox.curselection()

        if selected_genre_index:
            genre = self.genre_listbox.get(selected_genre_index[0])
            rule = f"Replace Genre: {genre} with {replace_text}"
            self.genres_to_replace[genre] = replace_text
            self.add_rule(rule)

        if selected_actor_index:
            actor = self.actor_listbox.get(selected_actor_index[0])
            rule = f"Replace Actor: {actor} with {replace_text}"
            self.actors_to_replace[actor] = replace_text
            self.add_rule(rule)

    def apply_changes(self):
        all_files = set(self.genre_df['File']).union(set(self.actor_df['File']))

        # 处理所有规则
        for rule in self.rule_log:
            rule_parts = rule.split(': ')
            if len(rule_parts) == 2:
                action, content = rule_parts
                if action == 'Delete Genre':
                    self.genres_to_delete.add(content.strip())
                elif action == 'Delete Actor':
                    self.actors_to_delete.add(content.strip())
                elif action.startswith('Replace Genre'):
                    original, new = content.split(' with ')
                    self.genres_to_replace[original.strip()] = new.strip()
                elif action.startswith('Replace Actor'):
                    original, new = content.split(' with ')
                    self.actors_to_replace[original.strip()] = new.strip()

        for file_path in all_files:
            self.modify_nfo(file_path)

        # 应用修改后更新列表
        self.update_list_after_changes()

        messagebox.showinfo("完成", "所有修改已应用。")

    def modify_nfo(self, file_path):
        try:
            tree = ET.parse(file_path)
            root = tree.getroot()

            modified = False  # 标记是否有修改
            changes = []  # 记录所有的修改

            # 创建新的 XML 树来保存不需要删除的行
            new_tree = ET.Element(root.tag)

            # 用于存储已经处理的 genre 和 actor
            processed_genres = set()
            processed_actors = set()

            # 修改 genres 和 actors
            for elem in root:
                if elem.tag == 'genre':
                    if not isinstance(elem.text, str) or not elem.text.strip():
                        changes.append(f"Removed invalid genre line: {ET.tostring(elem, encoding='unicode')}")
                        modified = True
                        continue
                    if elem.text in self.genres_to_delete:
                        changes.append(f"Deleted Genre: {elem.text}")
                        modified = True
                        processed_genres.add(elem.text)  # 记录已处理的 genre
                        continue
                    if elem.text in self.genres_to_replace:
                        changes.append(f"Replaced Genre: {elem.text} with {self.genres_to_replace[elem.text]}")
                        elem.text = self.genres_to_replace[elem.text]
                        modified = True
                        processed_genres.add(elem.text)  # 记录已处理的 genre
                elif elem.tag == 'actor':
                    actor_name = elem.find('name')
                    if actor_name is None or not isinstance(actor_name.text, str) or not actor_name.text.strip():
                        changes.append(f"Removed invalid actor line: {ET.tostring(elem, encoding='unicode')}")
                        modified = True
                        continue
                    if actor_name.text in self.actors_to_delete:
                        changes.append(f"Deleted Actor: {actor_name.text}")
                        modified = True
                        processed_actors.add(actor_name.text)  # 记录已处理的 actor
                        continue
                    if actor_name.text in self.actors_to_replace:
                        changes.append(
                            f"Replaced Actor: {actor_name.text} with {self.actors_to_replace[actor_name.text]}")
                        actor_name.text = self.actors_to_replace[actor_name.text]
                        modified = True
                        processed_actors.add(actor_name.text)  # 记录已处理的 actor
                new_tree.append(elem)

            # 从待处理的列表中移除已处理的 genre 和 actor
            self.genres_to_delete.difference_update(processed_genres)
            self.genres_to_replace = {k: v for k, v in self.genres_to_replace.items() if k not in processed_genres}
            self.actors_to_delete.difference_update(processed_actors)
            self.actors_to_replace = {k: v for k, v in self.actors_to_replace.items() if k not in processed_actors}

            # 保存修改后的XML树
            if modified:
                tree = ET.ElementTree(new_tree)
                tree.write(file_path, encoding='utf-8', xml_declaration=True)
                print(f"Modified file: {file_path}")
                for change in changes:
                    print(change)

        except ET.ParseError as e:
            print(f"Failed to parse {file_path}: {e}")

    def scan_folder(self):
        self.folder_path = filedialog.askdirectory(title="选择文件夹")
        if not self.folder_path:
            messagebox.showwarning("Warning", "没有选择文件夹")
            return

        output_file = os.path.join(self.folder_path, 'genre_actor_summary.xlsx')

        # 检查是否存在同名文件，如果存在则删除
        if os.path.exists(output_file):
            os.remove(output_file)

        genre_dict = {}
        actor_dict = {}

        # 遍历文件夹中的所有子文件夹和.nfo文件
        for subdir, _, _ in os.walk(self.folder_path):
            nfo_files = glob.glob(os.path.join(subdir, '*.nfo'))
            for nfo_file in nfo_files:
                genres = self.get_genres_from_nfo(nfo_file)
                actors = self.get_actors_from_nfo(nfo_file)

                for genre in genres:
                    if genre not in genre_dict:
                        genre_dict[genre] = []
                    genre_dict[genre].append(nfo_file)

                for actor in actors:
                    if actor not in actor_dict:
                        actor_dict[actor] = []
                    actor_dict[actor].append(nfo_file)

        # 将结果写入Excel文件的不同Sheet中（覆盖原有内容）
        with pd.ExcelWriter(output_file, engine='openpyxl', mode='w') as writer:
            genre_rows = [{'Genre': genre, 'File': file} for genre, files in genre_dict.items() for file in files]
            pd.DataFrame(genre_rows).to_excel(writer, sheet_name='Genres', index=False)

            actor_rows = [{'Actor': actor, 'File': file} for actor, files in actor_dict.items() for file in files]
            pd.DataFrame(actor_rows).to_excel(writer, sheet_name='Actors', index=False)

        messagebox.showinfo("完成", f"汇总结果已保存到 {output_file}")

        # 自动加载生成的Excel文件
        self.load_excel(output_file)

        # 更新和显示新的 genres 和 actors
        self.update_new_items()

    def get_genres_from_nfo(self, file_path):
        genres = []
        try:
            tree = ET.parse(file_path)
            root = tree.getroot()
            genre_elements = root.findall('genre')
            for genre_element in genre_elements:
                if isinstance(genre_element.text, str) and genre_element.text.strip():
                    genres.append(genre_element.text.strip())
        except ET.ParseError as e:
            print(f"Failed to parse {file_path}: {e}")
        return genres

    def get_actors_from_nfo(self, file_path):
        actors = []
        try:
            tree = ET.parse(file_path)
            root = tree.getroot()
            actor_elements = root.findall('actor')
            for actor_element in actor_elements:
                actor_name = actor_element.find('name')
                if actor_name is not None and isinstance(actor_name.text, str) and actor_name.text.strip():
                    actors.append(actor_name.text.strip())
        except ET.ParseError as e:
            print(f"Failed to parse {file_path}: {e}")
        return actors

    def load_excel(self, file_path=None):
        if not file_path:
            self.file_path = filedialog.askopenfilename(
                title="选择汇总Excel文件",
                filetypes=(("Excel files", "*.xlsx"), ("All files", "*.*"))
            )
        else:
            self.file_path = file_path

        if not self.file_path:
            messagebox.showwarning("Warning", "没有选择Excel文件")
            return

        try:
            self.genre_df = pd.read_excel(self.file_path, sheet_name='Genres', dtype={'Genre': str, 'File': str})
            self.actor_df = pd.read_excel(self.file_path, sheet_name='Actors', dtype={'Actor': str, 'File': str})

            self.display_items()
        except Exception as e:
            messagebox.showerror("Error", f"加载Excel文件失败：{str(e)}")

    def display_items(self):
        unique_genres = sorted(self.genres_set)
        unique_actors = sorted(self.actors_set)

        if self.genre_df is not None:
            unique_genres = sorted(self.genres_set.union(set(self.genre_df['Genre'])))
        if self.actor_df is not None:
            unique_actors = sorted(self.actors_set.union(set(self.actor_df['Actor'])))

        self.genre_listbox.delete(0, END)
        for genre in unique_genres:
            self.genre_listbox.insert(END, genre)

        self.actor_listbox.delete(0, END)
        for actor in unique_actors:
            self.actor_listbox.insert(END, actor)

    def update_new_items(self):
        new_genres = set(self.genre_df['Genre']) - self.genres_set - self.genres_to_delete
        new_actors = set(self.actor_df['Actor']) - self.actors_set - self.actors_to_delete

        # 排除在替换规则中的项目
        new_genres = {genre for genre in new_genres if genre not in self.genres_to_replace}
        new_actors = {actor for actor in new_actors if actor not in self.actors_to_replace}

        # 更新 genres
        self.genres_set.update(new_genres)
        self.display_new_items(self.genre_listbox, new_genres, self.genres_set)

        # 更新 actors
        self.actors_set.update(new_actors)
        self.display_new_items(self.actor_listbox, new_actors, self.actors_set)

    def update_list_after_changes(self):
        # 删除被删除和替换的项目
        self.genres_set -= self.genres_to_delete
        self.actors_set -= self.actors_to_delete

        for genre in self.genres_to_replace:
            self.genres_set.discard(genre)
            self.genres_set.add(self.genres_to_replace[genre])

        for actor in self.actors_to_replace:
            self.actors_set.discard(actor)
            self.actors_set.add(self.actors_to_replace[actor])

        self.display_items()

    def display_new_items(self, listbox, new_items, all_items):
        listbox.delete(0, END)
        for item in sorted(all_items):
            listbox.insert(END, item)
            if item in new_items:
                listbox.itemconfig(END, {'bg': 'blue'})

    def on_right_click_genre(self, event):
        try:
            self.genre_listbox.selection_clear(0, END)
            self.genre_listbox.selection_set(self.genre_listbox.nearest(event.y))
            self.genre_menu.post(event.x_root, event.y_root)
        finally:
            self.genre_menu.grab_release()

    def on_right_click_actor(self, event):
        try:
            self.actor_listbox.selection_clear(0, END)
            self.actor_listbox.selection_set(self.actor_listbox.nearest(event.y))
            self.actor_menu.post(event.x_root, event.y_root)
        finally:
            self.actor_menu.grab_release()

    def delete_genre(self):
        selected_genre_index = self.genre_listbox.curselection()
        if selected_genre_index:
            genre = self.genre_listbox.get(selected_genre_index[0])
            self.genres_set.discard(genre)
            self.genre_listbox.delete(selected_genre_index)
            self.save_dicts()
            print(f"Deleted Genre: {genre}")

    def delete_actor(self):
        selected_actor_index = self.actor_listbox.curselection()
        if selected_actor_index:
            actor = self.actor_listbox.get(selected_actor_index[0])
            self.actors_set.discard(actor)
            self.actor_listbox.delete(selected_actor_index)
            self.save_dicts()
            print(f"Deleted Actor: {actor}")

    def on_genre_select(self, event):
        self.actor_listbox.selection_clear(0, END)

    def on_actor_select(self, event):
        self.genre_listbox.selection_clear(0, END)

    def on_closing(self):
        self.save_dicts()
        self.root.destroy()


if __name__ == "__main__":
    root = Tk()
    app = NfoModifier(root)
    mainloop()
