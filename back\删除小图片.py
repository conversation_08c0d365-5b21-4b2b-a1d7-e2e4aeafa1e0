import os
from tkinter import Tk
from tkinter.filedialog import askdirectory
from PIL import Image


def check_and_delete_files(folder_path, extensions):
    # 初始化标志，用于检查是否存在符合要求的文件
    found_files = False

    # 遍历文件夹及其子文件夹里的所有文件
    for root, dirs, files in os.walk(folder_path):
        for filename in files:
            # 将文件名转换为小写
            filename_lower = filename.lower()
            # 检查文件是否为指定扩展名的文件
            if filename_lower.endswith(extensions):
                filepath = os.path.join(root, filename)
                # 删除文件
                print(f"删除文件: {filepath}")
                os.remove(filepath)
                found_files = True

    return found_files


def check_and_delete_low_resolution_images(folder_path):
    # 初始化标志，用于检查是否存在符合要求的图片
    found_images = False

    # 遍历文件夹及其子文件夹里的所有图像文件
    for root, dirs, files in os.walk(folder_path):
        for filename in files:
            # 将文件名转换为小写
            filename_lower = filename.lower()
            if filename_lower.endswith(('.jpg', '.png', '.jpeg')):
                filepath = os.path.join(root, filename)
                try:
                    with Image.open(filepath) as im:
                        # 获取图像宽度、高度和分辨率
                        width, height = im.size
                        resolution = f"{width}x{height}"
                        # 获取DPI信息
                        dpi = None
                        # 尝试从EXIF数据中获取DPI信息
                        exif = im._getexif()
                        if exif:
                            dpi = exif.get(283)  # 283 是 DPI 的标签
                        # 打印图像信息
                        #print(
                        #    f"文件路径: {filepath}\n宽度: {width}px\n高度: {height}px\n分辨率: {resolution}\nDPI: {dpi}\n")
                    # 如果分辨率小于800*800，就删除文件
                    if width < 800 or height < 800:
                        print(f"删除文件: {filepath}")
                        os.remove(filepath)
                    else:
                        found_images = True
                except Exception as e:
                    print(f"处理文件 {filepath} 时出错：{e}")
                    print("继续处理下一个文件。\n")
                    continue

    return found_images


def delete_empty_folders(folder_path):
    # 遍历文件夹及其子文件夹里的所有文件夹
    for root, dirs, files in os.walk(folder_path, topdown=False):
        for dir_name in dirs:
            dir_path = os.path.join(root, dir_name)
            if not os.listdir(dir_path):
                print(f"文件夹 {dir_path} 是空的，将被删除。")
                os.rmdir(dir_path)


if __name__ == "__main__":
    # 创建一个对话框，选择一个文件夹
    root = Tk()
    root.withdraw()
    selected_folder = askdirectory()

    # 删除htm和html文件
    found_html_files = check_and_delete_files(selected_folder, ('.htm', '.html', '.txt', '.url', '.info', '.db', '.mht', '.svg', 'editor.gif', 'signbg1.png'))
    if not found_html_files:
        print("没有找到杂项文件。")

    # 删除小分辨率图片
    found_images = check_and_delete_low_resolution_images(selected_folder)
    if not found_images:
        print("没有找到小分辨率图片。")

    # 删除空文件夹
    delete_empty_folders(selected_folder)

    input("按任意键退出...")

