import os
import shutil
from tkinter import Tk
from tkinter.filedialog import askdirectory

def get_earliest_separator_position(file_name):
    # 找到所有可能的分隔符位置
    positions = [file_name.find('_'), file_name.find(' '), file_name.find('-'), file_name.find(','), file_name.find('，')]
    # 过滤掉未找到的分隔符位置（即值为-1的情况）
    positions = [pos for pos in positions if pos != -1]
    # 返回最早出现的分隔符位置
    return min(positions) if positions else -1

def organize_files_by_prefix(folder_path):
    files = [f for f in os.listdir(folder_path) if os.path.isfile(os.path.join(folder_path, f))]

    for file in files:
        pos = get_earliest_separator_position(file)
        if pos != -1:
            prefix = file[:pos].strip()
        else:
            prefix = file.rsplit('.', 1)[0].strip()

        # 显示提取到的文件夹名
        print(f"文件: {file} 提取到的文件夹名: {prefix}")

        # 构建子文件夹路径
        subfolder_path = os.path.join(folder_path, prefix)

        if not os.path.exists(subfolder_path):
            try:
                os.makedirs(subfolder_path)
            except Exception as e:
                print(f"创建文件夹 {subfolder_path} 失败: {e}")
                continue

        source_path = os.path.join(folder_path, file)
        destination_path = os.path.join(subfolder_path, file)

        try:
            shutil.move(source_path, destination_path)
        except Exception as e:
            print(f"移动文件 {source_path} 到 {destination_path} 失败: {e}")

    other_folder_path = os.path.join(folder_path, "其他")
    if not os.path.exists(other_folder_path):
        try:
            os.makedirs(other_folder_path)
        except Exception as e:
            print(f"创建文件夹 {other_folder_path} 失败: {e}")

    for subfolder in os.listdir(folder_path):
        subfolder_path = os.path.join(folder_path, subfolder)
        if os.path.isdir(subfolder_path) and subfolder != "其他":
            subfolder_files = [f for f in os.listdir(subfolder_path) if os.path.isfile(os.path.join(subfolder_path, f))]
            if len(subfolder_files) < 2:
                for file in subfolder_files:
                    source_path = os.path.join(subfolder_path, file)
                    destination_path = os.path.join(other_folder_path, file)
                    try:
                        shutil.move(source_path, destination_path)
                    except Exception as e:
                        print(f"移动文件 {source_path} 到 {destination_path} 失败: {e}")
                try:
                    os.rmdir(subfolder_path)
                except OSError as e:
                    print(f"删除文件夹 {subfolder_path} 失败: {e}")

if __name__ == "__main__":
    root = Tk()
    root.withdraw()

    folder_path = askdirectory(title="选择文件夹")

    if folder_path:
        organize_files_by_prefix(folder_path)
        print(f"{folder_path}中的文件已按照前缀整理到子文件夹中")
    else:
        print("未选择文件夹")

    root.quit()
