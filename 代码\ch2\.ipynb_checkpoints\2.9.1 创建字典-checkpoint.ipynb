{"cells": [{"cell_type": "code", "execution_count": 1, "id": "adcbbeba", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'102': '张三', '105': '李四', '109': '王五'}\n", "dict1数据类型是： 3\n", "{102: '张三', 105: '李四', 109: '王五'}\n", "dict3数据类型是： <class 'dict'>\n"]}], "source": ["dict1 = {'102': '张三', '105': '李四', '109': '王五'}\n", "print(dict1)\n", "\n", "print('dict1数据类型是：', len(dict1)) \n", "dict2 = dict(((102, '张三'), (105, '李四'), (109, '王五')))\n", "print(dict2)\n", "dict3 = {}\n", "print('dict3数据类型是：', type(dict3)) "]}, {"cell_type": "code", "execution_count": null, "id": "f4ad3930", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}