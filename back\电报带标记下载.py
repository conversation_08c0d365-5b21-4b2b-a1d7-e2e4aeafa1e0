import re
import subprocess
import tkinter as tk
import pyperclip
import time
import os
from datetime import datetime

def get_timestamp():
    now = datetime.now()
    timestamp = now.strftime("%m%d%H%M%S")
    return timestamp
class AddressGenerator:
    def __init__(self, root):
        self.root = root
        self.root.title("地址生成器")

        # 设置窗口大小
        self.root.geometry("600x450")
        self.root.attributes('-topmost', True)

        # 创建第一个地址输入框
        self.first_label = tk.Label(root, text="起始地址:")
        self.first_label.pack()
        self.first_entry = tk.Entry(root, width=60)
        self.first_entry.pack()
        # 绑定点击事件，自动粘贴剪贴板内容
        self.first_entry.bind("<Button-删除扩展名中的汉字>", self.paste_clipboard)

        # 创建第二个地址输入框
        self.second_label = tk.Label(root, text="结束地址:")
        self.second_label.pack()
        self.second_entry = tk.Entry(root, width=60)
        self.second_entry.pack()
        # 绑定点击事件，自动粘贴剪贴板内容
        self.second_entry.bind("<Button-删除扩展名中的汉字>", self.paste_clipboard)

        # 创建MovName输入框
        self.mov_name_label = tk.Label(root, text="文件名前缀:")
        self.mov_name_label.pack()
        self.mov_name_entry = tk.Entry(root, width=60)
        self.mov_name_entry.pack()
        # 绑定点击事件，自动粘贴剪贴板内容
        self.mov_name_entry.bind("<Button-删除扩展名中的汉字>", self.paste_clipboard)

        # 创建按钮框架
        self.button_frame = tk.Frame(root)
        self.button_frame.pack()

        # 创建批量下载按钮
        self.download_button = tk.Button(self.button_frame, text="批量下载", command=self.generate_addresses)
        self.download_button.pack(side=tk.LEFT, padx=5)

        # 创建单独下载按钮
        self.single_download_button = tk.Button(self.button_frame, text="单独下载", command=self.send_single_address)
        self.single_download_button.pack(side=tk.LEFT, padx=5)

        # 创建地址列表显示框
        self.addresses_text = tk.Text(root, height=20, width=80)
        self.addresses_text.pack()

        # 初始化地址列表
        self.addresses = []

        # 在程序启动时执行特定命令
        #os.system("start cmd /K cd /d D:/tdl_Windows_64bit")


    def paste_clipboard(self, event):
        # 清空输入框内容
        event.widget.delete(0, tk.END)
        # 获取剪贴板内容并粘贴到点击的输入框
        clipboard_content = pyperclip.paste()
        event.widget.insert(tk.END, clipboard_content)

    def generate_addresses(self):
        # 获取第一个地址、第二个地址和MovName
        first_address = self.first_entry.get()
        second_address = self.second_entry.get()
        mov_name = self.mov_name_entry.get()

        # 使用正则表达式查找地址中的末尾数字序列
        first_numbers = [int(num) for num in re.findall(r'/(\d+)\D*$', first_address)]
        second_numbers = [int(num) for num in re.findall(r'/(\d+)\D*$', second_address)]

        # 清空地址列表
        self.addresses = []

        # 生成地址列表
        for number in range(first_numbers[0], second_numbers[0] + 1):
            new_address = re.sub(r'/\d+\D*$', f'/{number}', first_address)
            new_address = f".\\tdl dl -u {new_address} --template \"{mov_name}_{{{{ .DialogID }}}}_{{{{ .FileName }}}}\""
            self.addresses.append(new_address)

        # 显示地址列表
        self.show_addresses()

        # 清空输入框内容
        self.clear_input_fields()

        # 开始发送地址到命令行窗口
        self.send_addresses_to_cmd()

    def clear_input_fields(self):
        # 清空输入框内容
        self.first_entry.delete(0, tk.END)
        self.second_entry.delete(0, tk.END)
        #self.mov_name_entry.delete(0, tk.END)

    def show_addresses(self):
        # 清空地址列表显示框
        self.addresses_text.delete(1.0, tk.END)

        # 将地址列表显示在地址列表显示框中
        for address in self.addresses:
            self.addresses_text.insert(tk.END, address + "\n")

    def send_addresses_to_cmd(self):
        # 指定保存批处理文件的目录
        bat_file_dir = r'D:\tdl_Windows_64bit'

        # 构建要运行的批处理文件的完整路径
        timestamp = get_timestamp()
        bat_file_path = os.path.join(bat_file_dir, f"teldl_{timestamp}.bat")

        # 打开批处理文件，准备写入命令
        with open(bat_file_path, 'a', encoding='cp936') as bat_file:
            # 遍历地址列表，将每个地址写入批处理文件
            for address in self.addresses:
                self.write_command_to_file(address, bat_file_dir, bat_file)

        # 运行批处理文件
        #self.run_batch_file(bat_file_path)

    def write_command_to_file(self, command, directory, bat_file):
        # 写入切换目录命令
        bat_file.write(f'cd /d {directory}\n')
        # 写入下载命令
        bat_file.write(command + '\n')

    def run_batch_file(self, batch_file):
        subprocess.Popen(["start", "cmd", "/C", batch_file], shell=True, creationflags=subprocess.CREATE_NEW_CONSOLE)

    def send_single_address(self):
        # 获取第一个地址
        first_address = self.first_entry.get()
        mov_name = self.mov_name_entry.get()
        address = f".\\tdl dl -u {first_address} --template \"{mov_name}_{{{{ .DialogID }}}}_{{{{ .FileName }}}}\""

        # 清空输入框内容
        self.clear_input_fields()

        # 指定保存批处理文件的目录
        bat_file_dir = r'D:\tdl_Windows_64bit'

        # 构建要运行的批处理文件的完整路径
        timestamp = get_timestamp()
        bat_file_path = os.path.join(bat_file_dir, f"steldl_{timestamp}.bat")

        # 打开批处理文件，准备写入命令
        with open(bat_file_path, 'a', encoding='cp936') as bat_file:
            self.write_command_to_file(address, bat_file_dir, bat_file)

        # 运行批处理文件
        #self.run_batch_file(bat_file_path)


# 创建主窗口
root = tk.Tk()

# 创建应用程序实例
app = AddressGenerator(root)

# 运行程序
root.mainloop()