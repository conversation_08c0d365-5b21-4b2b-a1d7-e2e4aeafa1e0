{"cells": [{"cell_type": "code", "execution_count": 2, "id": "d1872b87", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["------apples[:3]-------\n", "a    3\n", "b    2\n", "c    0\n", "dtype: int64\n", "------: apples[0:3]-------\n", "a    3\n", "b    2\n", "c    0\n", "dtype: int64\n"]}], "source": ["import pandas as pd\n", "data = {'a' : 3, 'b' : 2, 'c' : 0, 'd' : 1}\n", "apples = pd.Series(data)\n", "print(\"------apples[:3]-------\")\n", "print(apples[:3])\n", "print(\"------: apples[0:3]-------\")\n", "print(apples[0:3])"]}, {"cell_type": "code", "execution_count": null, "id": "eb79c8a0", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}