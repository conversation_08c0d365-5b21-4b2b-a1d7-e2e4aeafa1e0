# -*- coding: utf-8 -*-
"""
快速创建测试文件用于验证文件后缀规范化功能
"""
import os

# 在当前目录创建测试文件夹
test_dir = "测试文件后缀规范化"

if not os.path.exists(test_dir):
    os.makedirs(test_dir)

# 创建测试文件
test_files = [
    # 需要处理的文件
    "测试文件.txt汉字",
    "压缩文件.7", 
    "旧格式.z",
    
    # 不需要处理的文件
    "正常文件.txt",
    "正常图片.jpg",
    "正常视频.mp4",
]

for filename in test_files:
    file_path = os.path.join(test_dir, filename)
    if not os.path.exists(file_path):
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(f"测试内容 - {filename}")
        print(f"✅ 创建: {filename}")
    else:
        print(f"⏭️ 已存在: {filename}")

print(f"\n测试目录: {os.path.abspath(test_dir)}")
print("现在可以在GUI中测试文件后缀规范化功能")
print("预期处理结果:")
print("- 测试文件.txt汉字 -> 测试文件.txt")
print("- 压缩文件.7 -> 压缩文件.7z")
print("- 旧格式.z -> 旧格式.7z")
print("- 其他文件不会被处理")
