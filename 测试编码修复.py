# -*- coding: utf-8 -*-
"""
测试编码修复功能
"""
import os

def create_test_encoding_files():
    """创建不同编码的测试文件"""
    test_dir = os.path.join(os.getcwd(), "测试编码修复")
    
    if not os.path.exists(test_dir):
        os.makedirs(test_dir)
        print(f"创建测试目录：{test_dir}")
    else:
        print(f"测试目录已存在：{test_dir}")
    
    # 测试内容
    test_content = "解压密码：caobi996.com"
    
    # 创建不同编码的文件
    encoding_tests = [
        ("解压密码_GBK.txt", "gbk"),
        ("解压密码_UTF8.txt", "utf-8"),
        ("解压密码_GB2312.txt", "gb2312"),
        ("解压密码_ANSI.txt", "ansi"),
    ]
    
    created_files = []
    
    for filename, encoding in encoding_tests:
        file_path = os.path.join(test_dir, filename)
        
        if os.path.exists(file_path):
            print(f"⏭️ 文件已存在，跳过：{filename}")
            continue
            
        try:
            with open(file_path, 'w', encoding=encoding) as f:
                f.write(test_content)
            created_files.append((file_path, encoding))
            print(f"✅ 创建文件：{filename} (编码: {encoding})")
        except Exception as e:
            print(f"❌ 创建文件失败：{filename} - {e}")
    
    return test_dir, created_files

def test_encoding_detection():
    """测试编码检测功能"""
    print("\n=== 测试编码检测功能 ===")
    
    try:
        # 导入解压缩模块
        import sys
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        from 解压缩 import read_extract_password_from_file, contains_garbled_text
        
        test_dir, created_files = create_test_encoding_files()
        
        # 测试每个编码文件
        for file_path, original_encoding in created_files:
            filename = os.path.basename(file_path)
            print(f"\n测试文件：{filename} (原始编码: {original_encoding})")
            
            try:
                password = read_extract_password_from_file(file_path)
                if password:
                    print(f"  结果：成功读取密码 - {password}")
                    # 检查是否包含乱码
                    if contains_garbled_text(password):
                        print(f"  警告：密码可能包含乱码")
                    else:
                        print(f"  ✅ 密码读取正确")
                else:
                    print(f"  结果：未找到有效密码")
            except Exception as e:
                print(f"  错误：{e}")
        
        return test_dir
        
    except ImportError as e:
        print(f"❌ 导入模块失败：{e}")
        return None
    except Exception as e:
        print(f"❌ 测试失败：{e}")
        return None

def test_garbled_detection():
    """测试乱码检测功能"""
    print("\n=== 测试乱码检测功能 ===")
    
    try:
        from 解压缩 import contains_garbled_text
        
        test_cases = [
            ("正常中文", "解压密码：test123", False),
            ("正常英文", "password:test123", False),
            ("混合内容", "解压密码：caobi996.com", False),
            ("乱码内容1", "瑙ｅ帇瀵嗙爜锛歝est123", True),
            ("乱码内容2", "锛歝aobi996.com", True),
            ("空内容", "", False),
            ("纯ASCII", "test123456", False),
        ]
        
        for description, text, expected in test_cases:
            result = contains_garbled_text(text)
            status = "✅ 正确" if result == expected else "❌ 错误"
            print(f"{description}: {text}")
            print(f"  预期: {'乱码' if expected else '正常'}, 检测: {'乱码' if result else '正常'} - {status}")
            
    except ImportError as e:
        print(f"❌ 导入模块失败：{e}")
    except Exception as e:
        print(f"❌ 测试失败：{e}")

def show_file_raw_content(test_dir):
    """显示文件的原始内容（用于调试）"""
    print(f"\n=== 文件原始内容 ===")
    
    for filename in os.listdir(test_dir):
        if filename.endswith('.txt'):
            file_path = os.path.join(test_dir, filename)
            print(f"\n文件：{filename}")
            
            # 尝试不同编码读取
            encodings = ['gbk', 'gb2312', 'utf-8', 'ansi', 'latin-1']
            for encoding in encodings:
                try:
                    with open(file_path, 'r', encoding=encoding) as f:
                        content = f.read().strip()
                        print(f"  {encoding}: {content}")
                        break
                except UnicodeDecodeError:
                    print(f"  {encoding}: 解码失败")
                except Exception as e:
                    print(f"  {encoding}: 错误 - {e}")

if __name__ == "__main__":
    print("=== 编码修复功能测试 ===")
    
    # 测试乱码检测
    test_garbled_detection()
    
    # 测试编码检测
    test_dir = test_encoding_detection()
    
    if test_dir:
        # 显示文件原始内容
        show_file_raw_content(test_dir)
        
        print(f"\n=== 修复说明 ===")
        print(f"1. 调整编码优先级：GBK > GB2312 > UTF-8 > ANSI > Latin-1")
        print(f"2. 添加乱码检测：自动识别并跳过乱码编码")
        print(f"3. 特殊模式检测：识别常见的中文乱码模式")
        print(f"4. 字符比例分析：其他字符占比>30%视为乱码")
        
        print(f"\n测试目录：{test_dir}")
        print(f"现在可以在GUI程序中测试编码修复功能")
    
    print("\n测试完成！")
