
/*==============================================================*/
/* Table: Stocks                                                */
/*==============================================================*/
create table Stocks
(
   Symbol               varchar(10) primary key ,
   Company              varchar(50) not null,
   Industry             varchar(10) not null
);


/*==============================================================*/
/* Table: HistoricalQuote                                       */
/*==============================================================*/
create table HistoricalQuote
(
   HDate                date  primary key ,
   Open                 decimal(8,4),
   High                 decimal(8,4),
   Low                  decimal(8,4),
   Close                decimal(8,4),
   Volume               bigint,
   Symbol               varchar(10)  REFERENCES Stocks (Symbol)
);

insert into Stocks (Symbol, Company, Industry) values ('AAPL', 'Apple Inc.', 'Technology');

insert  into `historicalquote`(`HDate`,`Open`,`High`,`Low`,`Close`,`Volume`,`Symbol`) values ('2023-01-22','177.3000','177.7800','176.6016','177.0000',27052000,'AAPL'),('2023-01-23','177.3000','179.4400','176.8200','177.0400',32395870,'AAPL'),('2023-01-24','177.2500','177.3000','173.2000','174.2200',51368540,'AAPL'),('2023-01-25','174.5050','174.9500','170.5300','171.1100',41438280,'AAPL'),('2023-01-26','172.0000','172.0000','170.0600','171.5100',39075250,'AAPL'),('2023-01-29','170.1600','170.1600','167.0700','167.9600',50565420,'AAPL'),('2023-01-30','165.5250','167.3700','164.7000','166.9700',45635470,'AAPL'),('2023-01-31','166.8700','168.4417','166.5000','167.4300',32234520,'AAPL'),('2023-02-01','167.1650','168.6200','166.7600','167.7800',44453230,'AAPL'),('2023-02-02','166.0000','166.8000','160.1000','160.5000',85957050,'AAPL'),('2023-02-05','159.1000','163.8800','156.0000','156.4900',72215320,'AAPL'),('2023-02-06','154.8300','163.7200','154.0000','163.0300',68171940,'AAPL'),('2023-02-07','163.0850','163.4000','159.0685','159.5400',51467440,'AAPL'),('2023-02-08','160.2900','161.0000','155.0300','155.1500',54145930,'AAPL'),('2023-02-09','157.0700','157.8900','150.2400','156.4100',70583530,'AAPL'),('2023-02-12','158.5000','163.8900','157.5100','162.7100',60774900,'AAPL'),('2023-02-13','161.9500','164.7500','161.6500','164.3400',32483310,'AAPL'),('2023-02-14','163.0450','167.5400','162.8800','167.3700',40382890,'AAPL'),('2023-02-15','169.7900','173.0900','169.0000','172.9900',50908540,'AAPL'),('2023-02-16','172.3600','174.8200','171.7700','172.4300',40113790,'AAPL'),('2023-02-20','172.0500','174.2600','171.4200','171.8500',33690660,'AAPL'),('2023-02-21','172.8300','174.1200','171.0100','171.0700',37378070,'AAPL'),('2023-02-22','171.8000','173.9500','171.7100','172.5000',30953760,'AAPL'),('2023-02-23','173.6700','175.6500','173.5400','175.5000',33772050,'AAPL'),('2023-02-26','176.3500','179.3900','176.2100','178.9700',37353670,'AAPL'),('2023-02-27','179.1000','180.4800','178.1600','178.3900',38885510,'AAPL'),('2023-02-28','179.2600','180.6150','178.0500','178.1200',37568080,'AAPL'),('2023-03-01','178.5400','179.7750','172.6600','175.0000',48706170,'AAPL'),('2023-03-02','172.8000','176.3000','172.4500','176.2100',38426060,'AAPL'),('2023-03-05','175.2100','177.7400','174.5200','176.8200',27825140,'AAPL'),('2023-03-06','177.9100','178.2500','176.1300','176.6700',23273160,'AAPL'),('2023-03-07','174.9400','175.8500','174.2700','175.0300',31686450,'AAPL'),('2023-03-08','175.4800','177.1200','175.0700','176.9400',24518850,'AAPL'),('2023-03-09','177.9600','180.0000','177.3900','179.9800',32130360,'AAPL'),('2023-03-12','180.2900','182.3900','180.2100','181.7200',32191070,'AAPL'),('2023-03-13','182.5900','183.5000','179.2400','179.9700',31464170,'AAPL'),('2023-03-14','180.3200','180.5200','177.8100','178.4400',29334630,'AAPL'),('2023-03-15','178.5000','180.2400','178.0701','178.6500',22676520,'AAPL'),('2023-03-16','178.6500','179.1200','177.6200','178.0200',38313330,'AAPL'),('2023-03-19','177.3200','177.4700','173.6600','175.3000',32931110,'AAPL'),('2023-03-20','175.2400','176.8000','174.9400','175.2400',19620520,'AAPL'),('2023-03-21','175.0400','175.0900','171.2600','171.2700',36387880,'AAPL'),('2023-03-22','170.0000','172.6800','168.6000','168.8500',41335980,'AAPL'),('2023-03-23','168.3900','169.9200','164.9400','164.9400',40984540,'AAPL'),('2023-03-26','168.0700','173.1000','166.4400','172.7700',36778560,'AAPL'),('2023-03-27','173.6800','175.1500','166.9200','168.3400',39824200,'AAPL'),('2023-03-28','167.2500','170.0200','165.1900','166.4800',41464880,'AAPL'),('2023-03-29','167.8050','171.7500','166.9000','167.7800',38116290,'AAPL'),('2023-04-02','167.8800','168.9400','164.4700','166.6800',37425640,'AAPL'),('2023-04-03','167.6400','168.7455','164.8800','168.3900',30237640,'AAPL'),('2023-04-04','164.8800','172.0100','164.7700','171.6100',34581850,'AAPL'),('2023-04-05','172.5800','174.2304','172.0800','172.8000',26750260,'AAPL'),('2023-04-06','170.9700','172.4800','168.2000','168.3800',34949690,'AAPL'),('2023-04-09','169.8800','173.0900','169.8450','170.0500',28976200,'AAPL'),('2023-04-10','173.0000','174.0000','171.5300','173.2500',28567110,'AAPL'),('2023-04-11','172.2300','173.9232','171.7000','172.4400',22401040,'AAPL'),('2023-04-12','173.4100','175.0000','173.0400','174.1400',22858840,'AAPL'),('2023-04-13','174.7800','175.8400','173.8500','174.7300',25100510,'AAPL'),('2023-04-16','175.0301','176.1900','174.8301','175.8200',21561320,'AAPL'),('2023-04-17','176.4900','178.9365','176.4100','178.2400',26575010,'AAPL'),('2023-04-18','177.8100','178.8200','176.8800','177.8400',20544600,'AAPL'),('2023-04-19','174.9500','175.3900','172.6600','172.8000',34693280,'AAPL'),('2023-04-20','170.5950','171.2184','165.4300','165.7200',65270950,'AAPL');
