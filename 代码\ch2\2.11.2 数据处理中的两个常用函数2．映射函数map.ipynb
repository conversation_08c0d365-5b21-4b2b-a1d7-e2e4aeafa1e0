{"cells": [{"cell_type": "code", "execution_count": 1, "id": "6634f6a3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>']\n", "<filter object at 0x000001D4B9E9F340>\n", "['<PERSON>', '<PERSON>']\n", "[]\n"]}], "source": ["users1 = ['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>']\n", "print(users1)\n", "\n", "users_filter = filter(lambda u: u.startswith('T'), users1) \n", "\n", "print(users_filter)\n", "users2 = list(users_filter)\n", "print(users2)\n", "\n", "users3 = list(users_filter) \n", "print(users3)"]}, {"cell_type": "code", "execution_count": null, "id": "0503c658", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}