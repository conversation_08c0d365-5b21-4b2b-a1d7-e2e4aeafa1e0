{"cells": [{"cell_type": "code", "execution_count": 3, "id": "06155d2e", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "\n", "plt.rcParams['font.family'] = ['SimHei'] # 设置中文字体\n", "plt.rcParams['axes.unicode_minus'] = False # 设置负号显示\n", "\n", "x = [-5, -4, 2, 1]  # x轴坐标数据 \n", "y = [7, 8, 9, 10]  # y轴坐标数据 \n", "# 绘制线段\n", "plt.plot(x, y, 'b', label='线1', linewidth=2)\n", "\n", "plt.title('绘制折线图')  # 添加图表标题 \n", "\n", "plt.ylabel('y轴')  # 添加y轴标题\n", "plt.xlabel('x轴')  # 添加x轴标题\n", "\n", "plt.legend()  # 设置图例\n", "# 以分辨率 72 来保存图片\n", "plt.savefig('折线图', dpi=72) \n", "\n", "plt.show()  # 显示图形 "]}, {"cell_type": "code", "execution_count": null, "id": "04438f98", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}