@echo off
:: Check for admin rights
net session >nul 2>&1
if %errorLevel% == 0 (
    echo Already running as administrator.
    set TCL_LIBRARY=C:\Users\<USER>\AppData\Local\Programs\Python\Python312\tcl\tcl8.6\
    set TK_LIBRARY=C:\Users\<USER>\AppData\Local\Programs\Python\Python312\tcl\tk8.6\
    python "%~dp0\symlinks.py"
) else (
    echo Requesting administrator privileges...
    powershell -Command "Start-Process cmd -ArgumentList '/c cd /d %~dp0 && set TCL_LIBRARY=C:\Users\<USER>\AppData\Local\Programs\Python\Python312\tcl\tcl8.6 && set TK_LIBRARY=C:\Users\<USER>\AppData\Local\Programs\Python\Python312\tcl\tk8.6 && python symlinks.py' -Verb RunAs"
)
pause
