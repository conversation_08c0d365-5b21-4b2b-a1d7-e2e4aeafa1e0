{"cells": [{"cell_type": "code", "execution_count": 1, "id": "9daee66f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Grade = A\n"]}], "source": ["score = 95\n", "if score >= 90:\n", "    grade = 'A'\n", "elif score >= 80:\n", "    grade = 'B'\n", "elif score >= 70:\n", "    grade = 'C'\n", "elif score >= 60:\n", "    grade = 'D'\n", "else:\n", "    grade = 'F'\n", "\n", "print(\"Grade = \" + grade)"]}, {"cell_type": "code", "execution_count": null, "id": "ff04e26a", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}