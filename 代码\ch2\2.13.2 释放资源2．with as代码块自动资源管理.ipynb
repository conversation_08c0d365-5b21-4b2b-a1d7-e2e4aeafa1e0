{"cells": [{"cell_type": "code", "execution_count": 1, "id": "3483f300", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["日期 = 2023-08-18 00:00:00\n"]}], "source": ["import datetime as dt\n", "\n", "f_name = 'data/date.txt'\n", "def read_date_from_file(filename):\n", "    try:\n", "        with open(filename) as file: \n", "            in_date = file.read()\n", "\n", "        in_date = in_date.strip()\n", "        date = dt.datetime.strptime(in_date, '%Y-%m-%d')\n", "        return date\n", "    except ValueError as e:\n", "        print('处理ValueError异常')\n", "    except OSError as e:\n", "        print('处理OSError异常')\n", "        \n", "if __name__ == '__main__': \n", "    date = read_date_from_file(f_name)\n", "    print('日期 = {0}'.format(date))"]}, {"cell_type": "code", "execution_count": null, "id": "72020781", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}