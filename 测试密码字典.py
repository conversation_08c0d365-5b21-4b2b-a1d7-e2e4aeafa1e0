# -*- coding: utf-8 -*-
"""
测试密码字典编辑功能
"""
import os
import tempfile

def create_test_password_dict():
    """创建一个测试用的密码字典文件"""
    # 创建临时目录
    test_dir = tempfile.mkdtemp()
    dict_path = os.path.join(test_dir, 'passdict.txt')
    
    # 创建包含中文的密码字典（使用ANSI编码）
    test_passwords = """# 测试密码字典
# 每行一个密码，程序会按顺序尝试
123456
password
测试密码
中文密码123
"""
    
    try:
        # 使用ANSI编码保存
        with open(dict_path, 'w', encoding='ansi') as f:
            f.write(test_passwords)
        print(f"测试密码字典已创建：{dict_path}")
        return dict_path
    except UnicodeEncodeError:
        # 如果ANSI编码失败，使用GBK
        with open(dict_path, 'w', encoding='gbk') as f:
            f.write(test_passwords)
        print(f"测试密码字典已创建（GBK编码）：{dict_path}")
        return dict_path

def test_read_password_dict(dict_path):
    """测试读取密码字典文件"""
    encodings = ['ansi', 'gbk', 'gb2312', 'utf-8', 'latin-1']
    
    for encoding in encodings:
        try:
            with open(dict_path, 'r', encoding=encoding) as f:
                content = f.read()
                print(f"✅ 使用 {encoding} 编码成功读取文件")
                print(f"内容预览：{content[:50]}...")
                return content
        except UnicodeDecodeError:
            print(f"❌ 使用 {encoding} 编码读取失败")
            continue
        except Exception as e:
            print(f"❌ 读取文件时出错：{e}")
            continue
    
    print("❌ 所有编码格式都无法读取文件")
    return None

if __name__ == "__main__":
    print("=== 密码字典编码测试 ===")
    
    # 创建测试文件
    dict_path = create_test_password_dict()
    
    # 测试读取
    content = test_read_password_dict(dict_path)
    
    if content:
        print("\n=== 密码列表 ===")
        lines = content.split('\n')
        passwords = [line.strip() for line in lines if line.strip() and not line.strip().startswith('#')]
        for i, pwd in enumerate(passwords, 1):
            print(f"{i}. {pwd}")
    
    print(f"\n测试文件位置：{dict_path}")
    print("可以手动检查文件内容和编码")
