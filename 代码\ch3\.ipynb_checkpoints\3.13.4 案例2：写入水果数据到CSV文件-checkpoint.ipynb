{"cells": [{"cell_type": "code", "execution_count": 5, "id": "b19f13b4", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "data = { 'apples': [3, 2, 0, 1],\n", "         'oranges': [0, 1, 2, 3],\n", "         'bananas': [1, 2, 1, 0]}\n", "df = pd.DataFrame(data, index=['June','<PERSON>','<PERSON>','<PERSON>'])\n", "df"]}, {"cell_type": "code", "execution_count": null, "id": "7c7bed5b", "metadata": {}, "outputs": [], "source": ["df.to_csv('data/水果.csv', header=['苹果','桔子','香蕉'],encoding='gbk')"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}