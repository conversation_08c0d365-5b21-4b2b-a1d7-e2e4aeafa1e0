import os
import subprocess
from tkinter import filedialog
import tkinter as tk


def unzip_files(folder_path):
    for root, dirs, files in os.walk(folder_path):
        for file in files:
            file_path = os.path.join(root, file)
            if file.endswith(('.zip', '.ZIP', '.rar', '.tar', '.7z', '.7Z', '.7z.001', '.7z.01', '.zip.001')):
                print(f"解压缩文件: {file_path}")
                password = get_password(root)
                print(f"密码：{password}")
                extract_with_7z(file_path, root, password)


def extract_with_7z(file_path, extract_to, password):
    # Check if file already exists in the extraction directory
    extracted_file = os.path.join(extract_to, os.path.splitext(os.path.basename(file_path))[0])
    if os.path.exists(extracted_file):
        print(f"跳过文件 {extracted_file}，因为已存在相同文件名的文件。")
        return

    # Try extracting with password
    command = [r'C:\Program Files\7-Zip\7z.exe', 'x', file_path, f'-p{password}', '-aos']
    #command = ['7z', 'x', file_path, f'-p{password}']
    process = subprocess.Popen(command, cwd=extract_to, stdout=subprocess.PIPE, stderr=subprocess.STDOUT,
                               universal_newlines=True)
    for line in process.stdout:
        print(line, end='')  # 显示进度和状态信息
    process.wait()
    if process.returncode != 0:  # Password incorrect
        print("密码错误。尝试不使用密码解压。")
        # Try extracting without password
        command = [r'C:\Program Files\7-Zip\7z.exe', 'x', file_path, f'-p{password}']
        #command = ['7z', 'x', file_path]
        subprocess.run(command, cwd=extract_to)
    else:
        print("解压缩成功。")


def get_password(folder):
    password_file = os.path.join(folder, 'password.txt')

    # Check if password.txt exists in the given folder
    if os.path.isfile(password_file):
        return read_password_from_file(password_file)

    # Check parent folders
    parent_folder = os.path.dirname(folder)
    while parent_folder and parent_folder != folder:
        password_file = os.path.join(parent_folder, 'password.txt')
        if os.path.isfile(password_file):
            return read_password_from_file(password_file)

        parent_folder = os.path.dirname(parent_folder)

    return ''


def read_password_from_file(file_path):
    with open(file_path, 'rb') as f:
        for line in f:
            try:
                line_decoded = line.decode('utf-8')
            except UnicodeDecodeError:
                line_decoded = line.decode('gbk', errors='replace')
            if '密码：' in line_decoded:
                password = line_decoded.strip().split('密码：')[1]
                return password
    return ''


def select_folder():
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口
    folder_path = filedialog.askdirectory()  # 显示文件夹选择对话框
    if folder_path:
        unzip_files(folder_path)


select_folder()
