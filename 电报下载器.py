import os
import re
import subprocess
import threading
import time
import tkinter as tk
import shutil
from threading import Thread
import tkinter.messagebox as messagebox
import pyperclip
import json

class AddressGenerator:
    def __init__(self, root):
        self.root = root
        self.root.title("电报文件下载器_by <PERSON><PERSON><PERSON>")
        self.root.geometry("600x450")
        self.root.attributes('-topmost', True)

        self.first_label = tk.Label(root, text="起始地址:")
        self.first_label.pack()
        self.first_entry = tk.Entry(root, width=60)
        self.first_entry.pack()
        self.first_entry.bind("<Button-1>", self.paste_clipboard)

        self.second_label = tk.Label(root, text="结束地址:")
        self.second_label.pack()
        self.second_entry = tk.Entry(root, width=60)
        self.second_entry.pack()
        self.second_entry.bind("<Button-1>", self.paste_clipboard)

        self.mov_name_label = tk.Label(root, text="文件名前缀:")
        self.mov_name_label.pack()
        self.mov_name_entry = tk.Entry(root, width=60)
        self.mov_name_entry.pack()
        self.mov_name_entry.bind("<Button-1>", self.paste_clipboard)

        self.button_frame = tk.Frame(root)
        self.button_frame.pack()

        self.download_button = tk.Button(self.button_frame, text="批量地址", command=self.add_addresses)
        self.download_button.pack(side=tk.LEFT, padx=5)

        self.single_download_button = tk.Button(self.button_frame, text="单独地址", command=self.add_single_address)
        self.single_download_button.pack(side=tk.LEFT, padx=5)

        self.start_download_button = tk.Button(self.button_frame, text="开始下载", command=self.start_download)
        self.start_download_button.pack(side=tk.LEFT, padx=5)
        self.start_download_button["state"] = "disabled"

        self.stop_download_button = tk.Button(self.button_frame, text="停止下载", command=self.stop_download)
        self.stop_download_button.pack(side=tk.LEFT, padx=5)
        self.stop_download_button["state"] = "disabled"
        self.stop_flag = False
        self.stop_event = threading.Event()

        # 添加导入缺失链接按钮
        self.import_missing_button = tk.Button(self.button_frame, text="导入缺失链接", command=self.import_missing_links)
        self.import_missing_button.pack(side=tk.LEFT, padx=5)

        # 添加修正前缀数量按钮
        self.fix_prefix_count_button = tk.Button(self.button_frame, text="修正前缀数量", command=self.fix_prefix_count)
        self.fix_prefix_count_button.pack(side=tk.LEFT, padx=5)

        # 创建地址文本框和滚动条
        self.addresses_frame = tk.Frame(root)
        self.addresses_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        self.addresses_text = tk.Text(self.addresses_frame, height=12, width=80)
        self.addresses_scrollbar = tk.Scrollbar(self.addresses_frame, orient=tk.VERTICAL, command=self.addresses_text.yview)
        self.addresses_text.configure(yscrollcommand=self.addresses_scrollbar.set)
        
        self.addresses_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        self.addresses_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 绑定文本变化事件
        self.addresses_text.bind('<<Modified>>', self.on_text_modified)

        # 创建前缀显示区域和滚动条
        self.prefix_frame = tk.Frame(root)
        self.prefix_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.prefix_text = tk.Text(self.prefix_frame, height=8, width=80)
        self.prefix_scrollbar = tk.Scrollbar(self.prefix_frame, orient=tk.VERTICAL, command=self.prefix_text.yview)
        self.prefix_text.configure(yscrollcommand=self.prefix_scrollbar.set)
        
        self.prefix_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        self.prefix_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        self.addresses = []
        self.timer = None
        self.initial_file_list = []
        self.final_file_list = []
        self.prefix_count = {}
        self.prefix_addresses = {}

        # 绑定关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

    def paste_clipboard(self, event):
        event.widget.delete(0, tk.END)
        clipboard_content = pyperclip.paste()
        event.widget.insert(tk.END, clipboard_content)

    def add_addresses(self):
        # 获取用户输入
        first_address = self.first_entry.get()
        second_address = self.second_entry.get()
        # 处理电影名称，去掉换行符和其他非目录可用字符
        mov_name = self.clean_input(self.mov_name_entry.get())
        # 在生成链接时将前缀中的下划线替换为空格
        mov_name_with_spaces = mov_name.replace('_', ' ')

        # 从地址中提取数字部分
        first_numbers = [int(num) for num in re.findall(r'/(\d+)\D*$', first_address)]
        second_numbers = [int(num) for num in re.findall(r'/(\d+)\D*$', second_address)]

        new_addresses = []
        # 生成新的地址范围
        for number in range(first_numbers[0], second_numbers[0] + 1):
            # 替换原地址中的数字部分
            new_address = re.sub(r'/\d+\D*$', f'/{number}', first_address)
            # 构造新的命令行地址，使用带空格的mov_name_with_spaces
            new_address = f"d:/tdl_Windows_64bit/.\\tdl dl -u {new_address} --template \"{mov_name_with_spaces}_{{{{.MessageID}}}}_{{{{.FileName}}}}\""
            new_addresses.append(f'{new_address} & timeout /t 5 & exit')

        # 将生成的地址添加到全局地址列表
        self.addresses.extend(new_addresses)

        # 记录前缀需下载的数量和对应地址（使用带空格的mov_name_with_spaces作为键）
        if mov_name_with_spaces in self.prefix_count:
            self.prefix_count[mov_name_with_spaces] += len(new_addresses)
            self.prefix_addresses[mov_name_with_spaces].extend(new_addresses)
        else:
            self.prefix_count[mov_name_with_spaces] = len(new_addresses)
            self.prefix_addresses[mov_name_with_spaces] = new_addresses

        # 更新界面显示
        self.show_addresses()
        # 更新前缀数量显示
        self.update_prefix_count_display()
        # 清空输入框
        self.clear_input_fields()
        # 启用下载按钮
        self.start_download_button["state"] = "normal"
        # 保存地址到文件
        self.save_addresses_to_file()

    def clean_input(self, input_string):
        # 清除换行符和其他非目录可用字符（允许字母、数字、下划线、连字符和空格）
        # 首先移除换行和回车符
        cleaned_string = input_string.replace('\n', '').replace('\r', '')
        # 移除 '#' 字符
        cleaned_string = cleaned_string.replace('#', '')
        cleaned_string = cleaned_string.replace('・', ' ')
        # 使用正则表达式替换掉不允许的字符
        cleaned_string = re.sub(r'[<>:"/\\|?*]+', '_', cleaned_string)
        # 附加处理：移除多余的空白字符
        cleaned_string = re.sub(r'\s+', ' ', cleaned_string).strip()
        return cleaned_string

    def add_single_address(self):
        first_address = self.first_entry.get()
        # 处理电影名称，去掉换行符和其他非目录可用字符
        mov_name = self.clean_input(self.mov_name_entry.get())
        # 在生成链接时将前缀中的下划线替换为空格
        mov_name_with_spaces = mov_name.replace('_', ' ')
        
        new_address = f"d:/tdl_Windows_64bit/.\\tdl dl -u {first_address} --template \"{mov_name_with_spaces}_{{{{.MessageID}}}}_{{{{.FileName}}}}\""
        new_addresses = [f'{new_address} & timeout /t 5 >nul & exit']
        self.addresses.extend(new_addresses)

        # 记录前缀需下载的数量和对应地址（使用带空格的mov_name_with_spaces作为键）
        if mov_name_with_spaces in self.prefix_count:
            self.prefix_count[mov_name_with_spaces] += 1
            self.prefix_addresses[mov_name_with_spaces].extend(new_addresses)
        else:
            self.prefix_count[mov_name_with_spaces] = 1
            self.prefix_addresses[mov_name_with_spaces] = new_addresses

        self.show_addresses()
        # 更新前缀数量显示
        self.update_prefix_count_display()
        self.clear_input_fields()
        self.start_download_button["state"] = "normal"
        self.save_addresses_to_file()

    def clear_input_fields(self):
        self.first_entry.delete(0, tk.END)
        self.second_entry.delete(0, tk.END)

    def show_addresses(self):
        """显示地址时不触发on_text_modified"""
        # 暂时解绑事件
        self.addresses_text.unbind('<<Modified>>')
        self.addresses_text.delete(1.0, tk.END)
        for address in self.addresses:
            self.addresses_text.insert(tk.END, address + "\n")
        # 重新绑定事件
        self.addresses_text.bind('<<Modified>>', self.on_text_modified)
        self.addresses_text.edit_modified(False)

    def on_text_modified(self, event):
        """当文本框内容改变时触发"""
        if self.addresses_text.edit_modified():
            # 获取文本框中的所有内容
            content = self.addresses_text.get(1.0, tk.END).strip()
            # 更新地址列表
            new_addresses = [line for line in content.split('\n') if line.strip()]
            
            # 清空并重建prefix_addresses
            self.prefix_addresses = {}
            self.prefix_count = {}
            
            # 遍历新地址，重建prefix_addresses和prefix_count
            for address in new_addresses:
                # 从地址中提取前缀
                match = re.search(r'--template "([^_]+)', address)
                if match:
                    prefix = match.group(1)
                    if prefix in self.prefix_addresses:
                        self.prefix_addresses[prefix].append(address)
                        self.prefix_count[prefix] += 1
                    else:
                        self.prefix_addresses[prefix] = [address]
                        self.prefix_count[prefix] = 1
            
            self.addresses = new_addresses
            # 更新前缀数量显示
            self.update_prefix_count_display()
            # 保存到文件
            self.save_addresses_to_file()
            # 重置修改标志
            self.addresses_text.edit_modified(False)

    def start_download(self):
        self.generate_backup()
        self.start_download_button["state"] = "disabled"
        self.download_button["state"] = "disabled"
        self.single_download_button["state"] = "disabled"
        self.stop_download_button["state"] = "normal"

        self.stop_flag = False
        self.stop_event.clear()

        # 记录开始下载前的文件列表
        self.initial_file_list = self.get_current_file_list()

        thread = Thread(target=self.execute_download)
        thread.start()

    def generate_backup(self):
        backup_file_path = "z:/work/addresses_backup.txt"
        shutil.copy("z:/work/addresses.txt", backup_file_path)

    def execute_download(self):
        while self.addresses:
            #if not self.check_cmd_window():
            addresses_copy = self.addresses[:]
            for address in addresses_copy:
                if self.stop_event.is_set():
                    print("下载已停止。")
                    break  # 改用break而不是return，这样可以继续执行后面的检查
                if self.send_address_to_cmd(address):
                    self.addresses.remove(address)
                    self.show_addresses()
                    self.root.update()
                    time.sleep(5)  # 等待命令完成
                self.save_addresses_to_file()
            if self.stop_event.is_set():
                break  # 如果是停止状态，跳出主循环
            else:
                print("正在下载，等待10秒重试...")
                time.sleep(10)  # 等待 10 秒重试

        # 无论是正常完成还是停止，都执行以下操作
        self.stop_download()
        
        # 记录最终的文件列表
        self.final_file_list = self.get_current_file_list()

        # 计算每个前缀的实际下载文件和对应的消息ID
        actual_prefix_files = {}
        for file in self.final_file_list:
            try:
                parts = file.split('_')
                if len(parts) >= 2:
                    prefix = parts[0]
                    message_id = parts[1]
                    if prefix in actual_prefix_files:
                        actual_prefix_files[prefix].append((message_id, file))
                    else:
                        actual_prefix_files[prefix] = [(message_id, file)]
            except Exception as e:
                print(f"Error processing file {file}: {e}")

        # 比较预期和实际下载数量，并记录缺失文件的链接和缺失数量
        missing_files_info = []
        missing_links = []
        self.prefix_missing_count = {}  # 记录每个前缀缺失数量
        
        for prefix, addresses in self.prefix_addresses.items():
            # 获取已下载文件的消息ID集合
            actual_files = actual_prefix_files.get(prefix, [])  # 已下载文件
            downloaded_msg_ids = {msg_id for msg_id, _ in actual_files}
            
            # 获取待下载列表中的消息ID集合
            pending_msg_ids = set()
            for address in self.addresses:
                match = re.search(r'/(\d+)(?:\s|$)', address)
                if match:
                    message_id = match.group(1)
                    pending_msg_ids.add(message_id)
            
            # 遍历该前缀的所有地址，检查每个消息ID是否存在
            missing_count = 0
            prefix_missing_links = []
            
            for address in addresses:
                match = re.search(r'/(\d+)(?:\s|$)', address)
                if match:
                    message_id = match.group(1)
                    # 如果消息ID既不在已下载也不在待下载中，就是缺失的
                    if message_id not in downloaded_msg_ids and message_id not in pending_msg_ids:
                        missing_count += 1
                        prefix_missing_links.append(address)
            
            if missing_count > 0:
                self.prefix_missing_count[prefix] = missing_count
                missing_files_info.append(f"{prefix}: 缺失 {missing_count}")
                missing_links.extend(prefix_missing_links)

        if not missing_files_info:
            messagebox.showinfo("下载完成", "所有文件下载完成且数量相符。")
            # 只在下载完成且没有缺失文件时清空
            if not self.addresses:
                self.prefix_count = {}
                self.prefix_addresses = {}
                self.prefix_missing_count = {}
        else:
            missing_info_str = "\n".join(missing_files_info)
            messagebox.showwarning("警告", 
                f"下载文件数量不匹配，缺失文件信息如下：\n{missing_info_str}\n"
                f"缺失的下载链接已保存到 missing_files_info.txt")
            
            # 保存缺失信息和对应链接到文件
            with open("z:/work/missing_files_info.txt", "w", encoding="utf-8") as f:
                f.write(f"下载时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n\n")
                f.write("缺失文件信息:\n")
                f.write(missing_info_str)
                f.write("\n\n缺失文件对应的下载链接:\n")
                for link in missing_links:
                    f.write(f"{link}\n")

        # 只在下载列表为空时提示
        if not self.addresses:
            messagebox.showinfo("提示", "下载列表为空。")

        # 更新前缀区显示
        self.update_prefix_count_display()
        
        # 修正前缀数量
        self.fix_prefix_count()

    def stop_download(self):
        self.start_download_button["state"] = "normal"
        self.download_button["state"] = "normal"
        self.single_download_button["state"] = "normal"
        self.stop_download_button["state"] = "disabled"

        self.stop_flag = True
        self.stop_event.set()

        if self.timer:
            self.root.after_cancel(self.timer)

        self.save_addresses_to_file()

    def send_address_to_cmd(self, address):
        #while self.check_cmd_window():
        #    print("正在下载，等待10秒重试...")
        #    time.sleep(10)

        try:
            if not self.stop_flag:
                batch_content = f"""
                    @echo off
                    cd d:\\tdl_Windows_64bit
                    {address}
                    pause
                    """

                batch_file_path = os.path.join(os.getenv('TEMP'), 'temp_cmd.bat')
                with open(batch_file_path, 'w') as batch_file:
                    batch_file.write(batch_content)

                process = subprocess.Popen(["cmd.exe", "/c", batch_file_path], creationflags=subprocess.CREATE_NEW_CONSOLE)
                process.wait()
                return True
        except Exception as e:
            print(f"Error sending address to cmd: {e}")
        return False

    def get_current_file_list(self):
        download_folder = "D:/tdl_Windows_64bit/downloads"
        return [name for name in os.listdir(download_folder) if os.path.isfile(os.path.join(download_folder, name))]

    def on_closing(self):
        # 只保存当前状态而不清空数据
        self.save_addresses_to_file()
        self.root.destroy()

    def save_addresses_to_file(self):
        try:
            data = {
                "addresses": self.addresses,
                "prefix_count": self.prefix_count,
                "prefix_addresses": self.prefix_addresses
            }
            
            # 清理数据中的非法Unicode字符
            def clean_data(obj):
                if isinstance(obj, dict):
                    return {k: clean_data(v) for k, v in obj.items()}
                elif isinstance(obj, list):
                    return [clean_data(x) for x in obj]
                elif isinstance(obj, str):
                    # 移除或替换非法Unicode字符
                    return obj.encode('utf-16', 'surrogatepass').decode('utf-16', 'replace')
                return obj
            
            cleaned_data = clean_data(data)
            
            with open("z:/work/addresses.txt", "w", encoding="utf-8") as f:
                json.dump(cleaned_data, f, ensure_ascii=False, indent=4)
        except Exception as e:
            print(f"保存文件时出错: {str(e)}")
            # 如果出错，尝试使用更安全的方式保存
            try:
                with open("z:/work/addresses.txt", "w", encoding="utf-8") as f:
                    json.dump(data, f, ensure_ascii=True, indent=4)
            except Exception as e2:
                print(f"备用保存方式也失败: {str(e2)}")
                messagebox.showerror("错误", "保存地址文件时出错，请检查文件权限或磁盘空间。")

    def load_addresses_from_file(self):
        try:
            with open("z:/work/addresses.txt", "r", encoding="utf-8") as f:
                content = f.read().strip()
                if content:
                    try:
                        data = json.loads(content)
                        self.addresses = data.get("addresses", [])
                        self.prefix_count = data.get("prefix_count", {})
                        self.prefix_addresses = data.get("prefix_addresses", {})
                    except json.JSONDecodeError as e:
                        print(f"解析JSON时出错: {str(e)}")
                        # 在清空数据前先备份原始文件
                        self.backup_addresses_file_with_timestamp("JSON解析错误")
                        self.addresses = []
                        self.prefix_count = {}
                        self.prefix_addresses = {}
                else:
                    # 文件存在但为空，也进行备份
                    self.backup_addresses_file_with_timestamp("文件为空")
                    self.addresses = []
                    self.prefix_count = {}
                    self.prefix_addresses = {}
        except FileNotFoundError:
            print("地址文件不存在")
            self.addresses = []
            self.prefix_count = {}
            self.prefix_addresses = {}
        except Exception as e:
            print(f"加载文件时出错: {str(e)}")
            # 在清空数据前先备份原始文件
            self.backup_addresses_file_with_timestamp(f"加载错误-{str(e)[:20]}")
            self.addresses = []
            self.prefix_count = {}
            self.prefix_addresses = {}

        self.show_addresses()
        # 更新前缀数量显示
        self.update_prefix_count_display()
        if self.addresses:
            self.start_download_button["state"] = "normal"
            self.stop_download_button["state"] = "normal"
        else:
            self.start_download_button["state"] = "disabled"
            self.stop_download_button["state"] = "disabled"

    def backup_addresses_file_with_timestamp(self, error_type):
        """在清空地址数据前创建带时间戳的备份文件"""
        try:
            # 检查原始文件是否存在
            if not os.path.exists("z:/work/addresses.txt"):
                return
            
            # 生成带时间戳的备份文件名
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            # 清理错误类型字符串，移除不能用于文件名的字符
            error_type = re.sub(r'[<>:"/\\|?*]', '_', error_type)
            backup_file_path = f"z:/work/addresses_error_{error_type}_{timestamp}.bak"
            
            # 复制原始文件到备份文件
            shutil.copy("z:/work/addresses.txt", backup_file_path)
            print(f"已创建地址文件错误备份: {backup_file_path}")
            
            # 显示提示消息
            messagebox.showwarning("加载错误", 
                f"加载地址文件时出错: {error_type}\n"
                f"原始文件已备份到: {backup_file_path}")
        except Exception as e:
            print(f"创建备份文件时出错: {str(e)}")
            # 即使备份失败也继续执行，不影响主程序

    def import_missing_links(self):
        try:
            with open("z:/work/missing_files_info.txt", "r", encoding="utf-8") as f:
                content = f.read()
                # 查找所有下载链接
                links = []
                in_links_section = False
                for line in content.split('\n'):
                    if "缺失文件对应的下载链接:" in line:
                        in_links_section = True
                        continue
                    if in_links_section and line.strip() and "d:/tdl_Windows_64bit" in line:
                        links.append(line.strip())
                
                if not links:
                    messagebox.showinfo("提示", "没有找到缺失的下载链接。")
                    return

                # 将找到的链接添加到地址列表
                self.addresses.extend(links)
                
                # 更新界面显示
                self.show_addresses()
                self.start_download_button["state"] = "normal"
                
                # 从链接中提取前缀信息并更新计数
                for link in links:
                    match = re.search(r'--template "([^_]+)', link)
                    if match:
                        prefix = match.group(1)
                        if prefix in self.prefix_count:
                            self.prefix_count[prefix] += 1
                            self.prefix_addresses[prefix].append(link)
                        else:
                            self.prefix_count[prefix] = 1
                            self.prefix_addresses[prefix] = [link]
                
                # 清空缺失文件记录
                self.prefix_missing_count = {}
                # 更新前缀显示
                self.update_prefix_count_display()
                # 清空缺失文件信息文件
                with open("z:/work/missing_files_info.txt", "w", encoding="utf-8") as f:
                    f.write("")
                
                messagebox.showinfo("成功", f"已导入 {len(links)} 个缺失链接。")
                
        except FileNotFoundError:
            messagebox.showerror("错误", "找不到缺失链接文件(z:/work/missing_files_info.txt)")
        except Exception as e:
            messagebox.showerror("错误", f"导入缺失链接时出错：{str(e)}")

    def update_prefix_count_display(self):
        """更新前缀数量显示，始终与地址列表实时一致"""
        # 统计缺失数量（如果有）
        prefix_missing = getattr(self, 'prefix_missing_count', {})

        if not self.prefix_count and not prefix_missing:
            self.prefix_text.delete(1.0, tk.END)
            self.prefix_text.insert(tk.END, "当前没有待下载的前缀")
            return

        display_text = "当前待下载的前缀数量：\n"
        # 优先显示有缺失的前缀
        for prefix in sorted(set(list(self.prefix_count.keys()) + list(prefix_missing.keys()))):
            missing = prefix_missing.get(prefix, 0)
            count = self.prefix_count.get(prefix, 0)
            if missing > 0:
                display_text += f"{prefix}: 缺失{missing}个\n"
            elif count > 0:
                display_text += f"{prefix}: {count}个文件\n"
            # 已全部下载且无缺失则不显示
        self.prefix_text.delete(1.0, tk.END)
        self.prefix_text.insert(tk.END, display_text)

    def fix_prefix_count(self):
        """修正前缀数量，只关注待下载列表中的文件"""
        if not self.addresses:
            messagebox.showinfo("提示", "当前没有下载地址")
            return

        # 清空并重建prefix_addresses和prefix_count
        self.prefix_addresses = {}
        self.prefix_count = {}
        
        # 只遍历当前待下载列表中的地址
        for address in self.addresses:
            # 从地址中提取前缀
            match = re.search(r'--template "([^_]+)', address)
            if match:
                prefix = match.group(1)
                if prefix in self.prefix_addresses:
                    self.prefix_addresses[prefix].append(address)
                    self.prefix_count[prefix] += 1
                else:
                    self.prefix_addresses[prefix] = [address]
                    self.prefix_count[prefix] = 1

        # 更新显示
        self.update_prefix_count_display()
        # 保存到文件
        self.save_addresses_to_file()

    def check_prefix_count_match(self):
        """检查待下载列表中的文件与前缀数量的匹配度"""
        if not self.addresses:
            return True

        # 临时存储实际的前缀计数
        actual_prefix_count = {}

        # 只遍历当前待下载列表中的地址，计算实际的前缀数量
        for address in self.addresses:
            match = re.search(r'--template "([^_]+)', address)
            if match:
                prefix = match.group(1)
                actual_prefix_count[prefix] = actual_prefix_count.get(prefix, 0) + 1

        # 检查是否有不匹配的情况
        mismatches = []
        for prefix, count in actual_prefix_count.items():
            expected_count = self.prefix_count.get(prefix, 0)
            if count != expected_count:
                mismatches.append(f"{prefix}: 预期 {expected_count}, 实际 {count}")

        # 检查是否有多余的前缀
        for prefix in self.prefix_count:
            if prefix not in actual_prefix_count:
                mismatches.append(f"{prefix}: 预期 {self.prefix_count[prefix]}, 实际 0")

        if mismatches:
            # 显示警告信息
            warning_text = "待下载列表中的文件与前缀数量不匹配：\n" + "\n".join(mismatches)
            messagebox.showwarning("警告", warning_text)
            return False
        return True


root = tk.Tk()
app = AddressGenerator(root)
app.load_addresses_from_file()
root.mainloop()
app.save_addresses_to_file()
