{"cells": [{"cell_type": "code", "execution_count": 5, "id": "e97e29f9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["------apples['a':'c']-------\n", "a    3\n", "b    2\n", "c    0\n", "dtype: int64\n", "------apples['a':'d']-------\n", "a    3\n", "b    2\n", "c    0\n", "d    1\n", "dtype: int64\n", "------apples[:'d']-------\n", "a    3\n", "b    2\n", "c    0\n", "d    1\n", "dtype: int64\n"]}], "source": ["import pandas as pd\n", "data = {'a' : 3, 'b' : 2, 'c' : 0, 'd' : 1}\n", "apples = pd.Series(data)\n", "print(\"------apples['a':'c']-------\")\n", "print(apples['a':'c'])\n", "print(\"------apples['a':'d']-------\")\n", "print(apples['a':'d'])\n", "print(\"------apples[:'d']-------\")\n", "print( apples[:'d'])"]}, {"cell_type": "code", "execution_count": null, "id": "b1a0e4d8", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}