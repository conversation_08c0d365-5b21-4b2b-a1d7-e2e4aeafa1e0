import tkinter as tk
from tkinter import filedialog, messagebox, simpledialog
import os
import re
import shutil
import subprocess

def select_folder():
    """让用户选择一个文件夹，并返回它的路径。"""
    root = tk.Tk()
    root.withdraw()  # 不显示主窗口
    folder_path = filedialog.askdirectory()  # 显示对话框让用户选择文件夹
    root.destroy()  # 确保选择后销毁临时窗口
    return folder_path

def get_folder_size(folder_path):
    """计算指定文件夹的总大小（包括子文件夹内的文件）。"""
    total_size = 0
    for dirpath, dirnames, filenames in os.walk(folder_path):
        for f in filenames:
            fp = os.path.join(dirpath, f)
            if os.path.exists(fp):
                total_size += os.path.getsize(fp)
    return total_size

def find_folders_with_exact_match(folder_path, pattern):
    """搜索符合给定模式的子文件夹，并按前缀分组。"""
    folder_prefixes = {}
    for item in os.listdir(folder_path):
        item_path = os.path.join(folder_path, item)
        if os.path.isdir(item_path):
            # 在控制台打印当前处理的文件夹
            print(f"正在处理: {item}")

            match = pattern.match(item)
            if match:
                prefix = match.group(1)
                size = get_folder_size(item_path)
                folder_prefixes.setdefault(prefix, []).append((item_path, size))
    return folder_prefixes

def delete_folder(folder_path):
    """删除指定文件夹。"""
    try:
        shutil.rmtree(folder_path)
        messagebox.showinfo("删除成功", f"文件夹 '{os.path.basename(folder_path)}' 删除成功。")
        return True
    except Exception as e:
        messagebox.showerror("删除失败", f"无法删除文件夹 '{os.path.basename(folder_path)}'.\n错误信息: {str(e)}")
        return False

def show_popup_menu(event, folder_listbox, folder_paths):
    """显示右键菜单，并为删除操作提供回调函数。"""
    def delete_folder_from_menu():
        index = folder_listbox.curselection()[0]
        folder_path = folder_paths[index][0]  # Now using the updated folder_paths structure
        confirm = messagebox.askyesno("确认删除", f"您确定要删除文件夹 '{os.path.basename(folder_path)}' 吗?")
        if confirm:
            if delete_folder(folder_path):
                folder_listbox.delete(tk.ANCHOR)
                folder_paths.pop(index)

    popup_menu = tk.Menu(folder_listbox, tearoff=0)
    popup_menu.add_command(label="删除", command=delete_folder_from_menu)
    popup_menu.tk_popup(event.x_root, event.y_root)

def open_folder(folder_path):
    """打开指定文件夹。"""
    try:
        os.startfile(folder_path)  # Windows
    except AttributeError:
        try:
            subprocess.Popen(['open', folder_path])  # macOS
        except:
            subprocess.Popen(['xdg-open', folder_path])  # Linux

if __name__ == '__main__':
    def start_search():
        folder_path = select_folder()
        if folder_path:
            # 清空列表框
            folder_listbox.delete(0, tk.END)
            folder_paths.clear()

            # 查找匹配的文件夹
            folder_prefixes = find_folders_with_exact_match(folder_path, pattern)

            # 添加搜索结束提示
            messagebox.showinfo("搜索结束", "搜索完成！")

            # 将匹配的文件夹添加到列表框
            for prefix, paths in folder_prefixes.items():
                if len(paths) > 1:  # 只有当有两个以上的匹配时显示
                    # 按大小排序，默认选择最小的文件夹
                    paths.sort(key=lambda x: x[1])
                    for path, size in paths:
                        display_text = f"{os.path.basename(path)} - {size / (1024 * 1024):.2f} MB"
                        folder_listbox.insert(tk.END, display_text)
                        folder_paths.append((path, size))
                    # 默认选择每组中最小的文件夹
                    folder_listbox.selection_set(folder_listbox.size() - len(paths))

    root = tk.Tk()
    root.title("文件夹管理器")

    # 解析文件夹名的正则表达式
    pattern = re.compile(r'^([a-zA-Z]+-\d+)')

    # 创建列表框以显示文件夹
    folder_listbox = tk.Listbox(root, width=50, height=20, selectbackground='blue', selectmode=tk.MULTIPLE)
    folder_listbox.pack(padx=10, pady=10)

    # 存储文件夹路径和大小
    folder_paths = []

    # 添加打开文件夹按钮
    open_button = tk.Button(root, text="选择文件夹", command=start_search)
    open_button.pack(pady=5)

    # 添加删除按钮
    def delete_selected_folder():
        try:
            selected_indices = list(folder_listbox.curselection())
            for index in reversed(selected_indices):
                folder_path = folder_paths[index][0]
                if delete_folder(folder_path):
                    folder_listbox.delete(index)
                    folder_paths.pop(index)
        except IndexError:
            messagebox.showwarning("警告", "请先选择一个文件夹。")

    delete_button = tk.Button(root, text="删除文件夹", command=delete_selected_folder)
    delete_button.pack(pady=5)

    # 绑定右键菜单事件
    folder_listbox.bind("<Button-3>", lambda event: show_popup_menu(event, folder_listbox, folder_paths))

    root.mainloop()
