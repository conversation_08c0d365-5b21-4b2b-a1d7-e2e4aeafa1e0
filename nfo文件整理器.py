# -*- coding: utf-8 -*-
import os
import glob
import json
import xml.etree.ElementTree as ET
import pandas as pd
from tkinter import Tk, filedialog, messagebox, Listbox, Button, Entry, Label, Text, END, Menu, SINGLE
from pypinyin import pinyin, Style


class NfoModifier:
    def __init__(self, root):
        self.root = root
        self.root.title("NFO Modifier")

        self.folder_path = None
        self.file_path = None
        self.genre_df = None
        self.actor_df = None

        self.rule_log = []  # 用于记录规则的列表
        self.genres_to_delete = set()
        self.genres_to_replace = {}
        self.actors_to_delete = set()
        self.actors_to_replace = {}

        self.genres_set = set()
        self.actors_set = set()

        self.create_widgets()
        self.load_dicts()
        self.load_log()
        self.update_list_after_changes()

    def create_widgets(self):
        # 初始化界面
        self.scan_button = Button(self.root, text="Scan Folder", command=self.scan_folder)
        self.scan_button.grid(row=0, column=0, columnspan=2, padx=10, pady=10)

        self.genre_label = Label(self.root, text="Genres:")
        self.genre_label.grid(row=1, column=0, padx=10, pady=5)

        self.genre_listbox = Listbox(self.root, selectmode=SINGLE, width=40, height=20)
        self.genre_listbox.grid(row=2, column=0, padx=10, pady=5)
        self.genre_listbox.bind('<Button-3>', self.on_right_click_genre)
        self.genre_listbox.bind('<<ListboxSelect>>', self.on_genre_select)

        self.actor_label = Label(self.root, text="Actors:")
        self.actor_label.grid(row=1, column=1, padx=10, pady=5)

        self.actor_listbox = Listbox(self.root, selectmode=SINGLE, width=40, height=20)
        self.actor_listbox.grid(row=2, column=1, padx=10, pady=5)
        self.actor_listbox.bind('<Button-3>', self.on_right_click_actor)
        self.actor_listbox.bind('<<ListboxSelect>>', self.on_actor_select)

        self.delete_button = Button(self.root, text="Delete Selected", command=self.delete_selected)
        self.delete_button.grid(row=3, column=0, columnspan=2, padx=10, pady=5)

        self.replace_label = Label(self.root, text="Replace with:")
        self.replace_label.grid(row=4, column=0, padx=10, pady=5, sticky='e')

        self.replace_entry = Entry(self.root)
        self.replace_entry.grid(row=4, column=1, padx=10, pady=5, sticky='w')

        self.replace_button = Button(self.root, text="Replace Selected", command=self.replace_selected)
        self.replace_button.grid(row=4, column=1, padx=10, pady=5, sticky='e')

        self.info_label = Label(self.root, text="Planned Actions:")
        self.info_label.grid(row=5, column=0, columnspan=2, padx=10, pady=5)

        self.info_text = Text(self.root, width=80, height=10)
        self.info_text.grid(row=6, column=0, columnspan=2, padx=10, pady=5)
        self.info_text.bind('<Button-3>', self.on_right_click_rule_log)

        self.apply_button = Button(self.root, text="Apply Changes", command=self.apply_changes)
        self.apply_button.grid(row=7, column=0, columnspan=2, padx=10, pady=10)

        # 初始化上下文菜单
        self.genre_menu = Menu(self.root, tearoff=0)
        self.genre_menu.add_command(label="Delete", command=self.delete_genre)
        self.genre_menu.add_command(label="Copy", command=self.copy_genre)

        self.actor_menu = Menu(self.root, tearoff=0)
        self.actor_menu.add_command(label="Delete", command=self.delete_actor)
        self.actor_menu.add_command(label="Copy", command=self.copy_actor)

        self.rule_log_menu = Menu(self.root, tearoff=0)
        self.rule_log_menu.add_command(label="Delete", command=self.delete_rule_log)
        self.rule_log_menu.add_command(label="Copy", command=self.copy_rule_log)

        # 在程序退出时保存字典文件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

    def load_dicts(self):
        genres_file_path = r'z:\work\genres_dict.json'
        actors_file_path = r'z:\work\actors_dict.json'
        # 加载流派字典
        if os.path.exists(genres_file_path):
            with open(genres_file_path, 'r', encoding='utf-8') as f:
                self.genres_set.update(json.load(f))
        else:
            print(f"文件 '{genres_file_path}' 不存在。")

        # 加载演员字典
        if os.path.exists(actors_file_path):
            with open(actors_file_path, 'r', encoding='utf-8') as f:
                self.actors_set.update(json.load(f))
        else:
            print(f"文件 '{actors_file_path}' 不存在。")

    def save_dicts(self):
        genres_file_path = r'z:\work\genres_dict.json'
        actors_file_path = r'z:\work\actors_dict.json'
        
        # 处理 NaN 值，将其转换为字符串
        genres_list = [str(genre) if pd.notna(genre) else '' for genre in self.genres_set]
        actors_list = [str(actor) if pd.notna(actor) else '' for actor in self.actors_set]

        # 保存流派字典
        with open(genres_file_path, 'w', encoding='utf-8') as f:
            json.dump(genres_list, f, ensure_ascii=False)

        # 保存演员字典
        with open(actors_file_path, 'w', encoding='utf-8') as f:
            json.dump(actors_list, f, ensure_ascii=False)

    def load_log(self):
        log_file = r'z:\work\modification_log.txt'
        if os.path.exists(log_file):
            with open(log_file, 'r', encoding='utf-8') as log:
                lines = log.readlines()
                lines.reverse()  # 倒序处理，从最新到最旧
                existing_rules = set()  # 用于存储已经存在的规则，避免重复记录
                for line in lines:
                    if line.strip() not in existing_rules:
                        self.rule_log.insert(0, line.strip())
                        self.process_rule(line.strip())
                        self.info_text.insert('1.0', line + "\n")  # 在前面插入规则到待办框
                        existing_rules.add(line.strip())

    def process_rule(self, rule):
        # 解析规则并更新集合
        action, _, content = rule.partition(': ')
        content = content.strip()
        if action == 'Delete Genre':
            self.genres_to_delete.add(content)
            self.genres_set.discard(content)
        elif action == 'Delete Actor':
            self.actors_to_delete.add(content)
            self.actors_set.discard(content)
        elif action.startswith('Replace Genre'):
            original, new = content.split(' with ', 1)
            self.genres_to_replace[original.strip()] = new.strip()
            self.genres_set.discard(original.strip())
        elif action.startswith('Replace Actor'):
            original, new = content.split(' with ', 1)
            self.actors_to_replace[original.strip()] = new.strip()
            self.actors_set.discard(original.strip())

    def add_rule(self, rule):
        if rule not in self.rule_log:
            self.rule_log.insert(0, rule)
            self.process_rule(rule)  # 立即处理规则
            self.info_text.insert('1.0', rule + "\n")  # 在前面插入新规则到待办框
            with open(r'z:\work\modification_log.txt', 'a', encoding='utf-8') as log:
                log.write(rule + "\n")

    def delete_selected(self):
        selected_genre_index = self.genre_listbox.curselection()
        selected_actor_index = self.actor_listbox.curselection()

        if selected_genre_index:
            genre = self.genre_listbox.get(selected_genre_index[0])
            rule = f"Delete Genre: {genre}"
            self.add_rule(rule)

        if selected_actor_index:
            actor = self.actor_listbox.get(selected_actor_index[0])
            rule = f"Delete Actor: {actor}"
            self.add_rule(rule)

    def replace_selected(self):
        replace_text = self.replace_entry.get().strip()
        if not replace_text:
            messagebox.showwarning("Warning", "Replacement text cannot be empty")
            return

        selected_genre_index = self.genre_listbox.curselection()
        selected_actor_index = self.actor_listbox.curselection()

        if selected_genre_index:
            genre = self.genre_listbox.get(selected_genre_index[0])
            rule = f"Replace Genre: {genre} with {replace_text}"
            self.add_rule(rule)

        if selected_actor_index:
            actor = self.actor_listbox.get(selected_actor_index[0])
            rule = f"Replace Actor: {actor} with {replace_text}"
            self.add_rule(rule)

    def apply_changes(self):
        if self.genre_df is None and self.actor_df is None:
            messagebox.showwarning("Warning", "No data to apply changes.")
            return

        all_files = set()
        if self.genre_df is not None:
            all_files.update(self.genre_df['File'].dropna().unique())
        if self.actor_df is not None:
            all_files.update(self.actor_df['File'].dropna().unique())

        # 对每个文件逐条应用规则
        for file_path in all_files:
            if self.folder_path and file_path.startswith(self.folder_path):
                for rule in self.rule_log:
                    self.apply_rule_to_nfo(file_path, rule)

        self.update_list_after_changes()
        self.save_dicts()
        messagebox.showinfo("完成", "所有修改已应用。")

    def apply_rule_to_nfo(self, file_path, rule):
        try:
            tree = ET.parse(file_path)
            root = tree.getroot()

            action, _, content = rule.partition(': ')
            content = content.strip()

            modified = False  # 标记是否有修改

            if action == 'Delete Genre':
                for elem in root.findall('genre'):
                    if elem.text == content:
                        root.remove(elem)
                        modified = True

            elif action == 'Delete Actor':
                for elem in root.findall('actor'):
                    name = elem.find('name')
                    if name is not None and name.text == content:
                        root.remove(elem)
                        modified = True

            elif action.startswith('Replace Genre'):
                original, new = content.split(' with ', 1)
                for elem in root.findall('genre'):
                    if elem.text == original.strip():
                        elem.text = new.strip()
                        modified = True

            elif action.startswith('Replace Actor'):
                original, new = content.split(' with ', 1)
                for elem in root.findall('actor'):
                    name = elem.find('name')
                    if name is not None and name.text == original.strip():
                        name.text = new.strip()
                        modified = True

            # 检查并替换演员为素人
            for elem in root.findall('actor'):
                name = elem.find('name')
                if name is not None and name.text == 'N/A':
                    name.text = '素人'
                    modified = True

            if modified:
                tree.write(file_path, encoding='utf-8', xml_declaration=True)
                print(f"Modified file: {file_path} with rule: {rule}")

        except ET.ParseError as e:
            print(f"Failed to parse {file_path}: {e}")

    def update_list_after_changes(self):
        self.genre_listbox.delete(0, END)
        self.actor_listbox.delete(0, END)

        if self.genre_df is not None:
            self.genres_set.update(self.genre_df['Genre'].dropna().unique())

        if self.actor_df is not None:
            self.actors_set.update(self.actor_df['Actor'].explode().dropna().unique())

        # 获取需要显示的项目（排除已删除和替换的）
        display_genres = self.genres_set - self.genres_to_delete - set(self.genres_to_replace.keys())
        display_actors = self.actors_set - self.actors_to_delete - set(self.actors_to_replace.keys())

        # 使用拼音排序
        sorted_genres = self.sort_by_pinyin(display_genres)
        sorted_actors = self.sort_by_pinyin(display_actors)

        for genre in sorted_genres:
            self.genre_listbox.insert(END, genre)

        for actor in sorted_actors:
            self.actor_listbox.insert(END, actor)

    def load_nfo_files(self):
        # 扫描文件夹，加载所有NFO文件并解析
        if self.folder_path:
            genre_files = glob.glob(os.path.join(self.folder_path, '*.nfo'))
            actor_files = glob.glob(os.path.join(self.folder_path, '*.actors'))

            if genre_files:
                self.genre_df = pd.DataFrame(columns=['File', 'Genre'])
                for file in genre_files:
                    root = ET.parse(file).getroot()
                    file_name = os.path.basename(file)
                    genre = root.findtext('genre')
                    self.genre_df = self.genre_df.append({'File': file_name, 'Genre': genre}, ignore_index=True)

            if actor_files:
                self.actor_df = pd.DataFrame(columns=['File', 'Actor'])
                for file in actor_files:
                    file_name = os.path.basename(file)
                    with open(file, 'r', encoding='utf-8') as f:
                        actors = f.read().splitlines()
                    self.actor_df = self.actor_df.append({'File': file_name, 'Actor': actors}, ignore_index=True)

            self.update_list_after_changes()

    def scan_folder(self):
        self.folder_path = filedialog.askdirectory(title="选择文件夹")
        if not self.folder_path:
            messagebox.showwarning("Warning", "没有选择文件夹")
            return

        output_file = os.path.join(self.folder_path, 'genre_actor_summary.xlsx')

        # 检查是否存在同名文件，如果存在则删除
        if os.path.exists(output_file):
            os.remove(output_file)

        genre_dict = {}
        actor_dict = {}

        # 遍历文件夹中的所有子文件夹和.nfo文件
        for subdir, _, _ in os.walk(self.folder_path):
            nfo_files = glob.glob(os.path.join(subdir, '*.nfo'))
            for nfo_file in nfo_files:
                genres = self.get_genres_from_nfo(nfo_file)
                actors = self.get_actors_from_nfo(nfo_file)

                for genre in genres:
                    if genre not in genre_dict:
                        genre_dict[genre] = []
                    genre_dict[genre].append(nfo_file)

                for actor in actors:
                    if actor not in actor_dict:
                        actor_dict[actor] = []
                    actor_dict[actor].append(nfo_file)

        # 将结果写入Excel文件的不同Sheet中（覆盖原有内容）
        with pd.ExcelWriter(output_file, engine='openpyxl', mode='w') as writer:
            genre_rows = [{'Genre': genre, 'File': file} for genre, files in genre_dict.items() for file in files]
            pd.DataFrame(genre_rows).to_excel(writer, sheet_name='Genres', index=False)

            actor_rows = [{'Actor': actor, 'File': file} for actor, files in actor_dict.items() for file in files]
            pd.DataFrame(actor_rows).to_excel(writer, sheet_name='Actors', index=False)

        messagebox.showinfo("完成", f"汇总结果已保存到 {output_file}")

        # 自动加载生成的Excel文件
        self.load_excel(output_file)

        # 更新和显示新的 genres 和 actors
        self.update_new_items()

    def update_new_items(self):
        new_genres = set(self.genre_df['Genre']) - self.genres_set - self.genres_to_delete
        new_actors = set(self.actor_df['Actor']) - self.actors_set - self.actors_to_delete

        # 排除在替换规则中的项目
        new_genres = {genre for genre in new_genres if genre not in self.genres_to_replace}
        new_actors = {actor for actor in new_actors if actor not in self.actors_to_replace}

        # 更新 genres
        self.genres_set.update(new_genres)
        self.display_new_items(self.genre_listbox, new_genres, self.genres_set)

        # 更新 actors
        self.actors_set.update(new_actors)
        self.display_new_items(self.actor_listbox, new_actors, self.actors_set)

    def display_new_items(self, listbox, new_items, all_items):
        listbox.delete(0, END)
        # 确保所有项目都是字符串类型
        all_items_str = [str(item) for item in all_items]
        for item in sorted(all_items_str):
            listbox.insert(END, item)
            if item in new_items:
                listbox.itemconfig(END, {'bg': 'blue'})

    def load_excel(self, file_path=None):
        if not file_path:
            self.file_path = filedialog.askopenfilename(
                title="选择汇总Excel文件",
                filetypes=(("Excel files", "*.xlsx"), ("All files", "*.*"))
            )
        else:
            self.file_path = file_path

        if not self.file_path:
            messagebox.showwarning("Warning", "没有选择Excel文件")
            return

        try:
            self.genre_df = pd.read_excel(self.file_path, sheet_name='Genres', dtype={'Genre': str, 'File': str})
            self.actor_df = pd.read_excel(self.file_path, sheet_name='Actors', dtype={'Actor': str, 'File': str})

            # 处理 NaN 和非字符串类型
            self.genre_df['Genre'] = self.genre_df['Genre'].apply(lambda x: str(x) if pd.notna(x) else '')
            self.actor_df['Actor'] = self.actor_df['Actor'].apply(lambda x: str(x) if pd.notna(x) else '')

            self.display_items()
        except Exception as e:
            messagebox.showerror("Error", f"加载Excel文件失败：{str(e)}")

    def display_items(self):
        unique_genres = self.genres_set
        unique_actors = self.actors_set

        if self.genre_df is not None:
            unique_genres = self.genres_set.union(set(self.genre_df['Genre']))
        if self.actor_df is not None:
            unique_actors = self.actors_set.union(set(self.actor_df['Actor']))

        # 使用拼音排序
        sorted_genres = self.sort_by_pinyin(unique_genres)
        sorted_actors = self.sort_by_pinyin(unique_actors)

        self.genre_listbox.delete(0, END)
        for genre in sorted_genres:
            self.genre_listbox.insert(END, genre)

        self.actor_listbox.delete(0, END)
        for actor in sorted_actors:
            self.actor_listbox.insert(END, actor)

    def get_genres_from_nfo(self, file_path):
        genres = []
        try:
            tree = ET.parse(file_path)
            root = tree.getroot()
            genre_elements = root.findall('genre')
            for genre_element in genre_elements:
                if isinstance(genre_element.text, str) and genre_element.text.strip():
                    genres.append(genre_element.text.strip())
        except ET.ParseError as e:
            print(f"Failed to parse {file_path}: {e}")
        return genres

    def get_actors_from_nfo(self, file_path):
        actors = []
        try:
            tree = ET.parse(file_path)
            root = tree.getroot()
            actor_elements = root.findall('actor')
            for actor_element in actor_elements:
                actor_name = actor_element.find('name')
                if actor_name is not None and isinstance(actor_name.text, str) and actor_name.text.strip():
                    actors.append(actor_name.text.strip())
        except ET.ParseError as e:
            print(f"Failed to parse {file_path}: {e}")
        return actors

    def delete_genre(self):
        # 从流派列表中删除选定的流派
        selected_genre_index = self.genre_listbox.curselection()
        if selected_genre_index:
            genre = self.genre_listbox.get(selected_genre_index[0])
            self.genre_listbox.delete(selected_genre_index[0])
            self.genres_set.discard(genre)
            self.add_rule(f"Delete Genre: {genre}")

    def delete_actor(self):
        # 从演员列表中删除选定的演员
        selected_actor_index = self.actor_listbox.curselection()
        if selected_actor_index:
            actor = self.actor_listbox.get(selected_actor_index[0])
            self.actor_listbox.delete(selected_actor_index[0])
            self.actors_set.discard(actor)
            self.add_rule(f"Delete Actor: {actor}")

    def on_right_click_genre(self, event):
        # 右键点击流派列表
        selected_genre_index = self.genre_listbox.nearest(event.y)
        self.genre_listbox.selection_clear(0, END)
        self.genre_listbox.selection_set(selected_genre_index)
        self.genre_listbox.activate(selected_genre_index)
        self.genre_menu.post(event.x_root, event.y_root)

    def on_right_click_actor(self, event):
        # 右键点击演员列表
        selected_actor_index = self.actor_listbox.nearest(event.y)
        self.actor_listbox.selection_clear(0, END)
        self.actor_listbox.selection_set(selected_actor_index)
        self.actor_listbox.activate(selected_actor_index)
        self.actor_menu.post(event.x_root, event.y_root)

    def copy_genre(self):
        # 复制流派名称到剪贴板
        selected_genre_index = self.genre_listbox.curselection()
        if selected_genre_index:
            genre = self.genre_listbox.get(selected_genre_index[0])
            self.root.clipboard_clear()
            self.root.clipboard_append(genre)

    def copy_actor(self):
        # 复制演员名称到剪贴板
        selected_actor_index = self.actor_listbox.curselection()
        if selected_actor_index:
            actor = self.actor_listbox.get(selected_actor_index[0])
            self.root.clipboard_clear()
            self.root.clipboard_append(actor)

    def on_right_click_rule_log(self, event):
        # 右键点击规则日志文本框
        self.rule_log_menu.post(event.x_root, event.y_root)

    def copy_rule_log(self):
        # 复制规则日志中选定的条目到剪贴板
        try:
            selected_rule_log_index = self.info_text.index('current')
            rule_log_line = self.info_text.get(selected_rule_log_index + ' linestart', selected_rule_log_index + ' lineend')
            self.root.clipboard_clear()
            self.root.clipboard_append(rule_log_line.strip())
        except:
            pass

    def delete_rule_log(self):
        # 删除规则日志中选定的条目
        selected_rule_log_index = self.info_text.index('current')
        if selected_rule_log_index:
            rule_log_line = self.info_text.get(selected_rule_log_index + ' linestart',
                                               selected_rule_log_index + ' lineend')
            self.rule_log.remove(rule_log_line.strip())
            self.info_text.delete(selected_rule_log_index + ' linestart', selected_rule_log_index + ' lineend')
            with open(r'z:\work\modification_log.txt', 'w', encoding='utf-8') as log:
                log.writelines([f"{rule}\n" for rule in self.rule_log])

    def on_genre_select(self, event):
        # 选择流派列表中的条目时不在替换文本框中显示选定的流派
        pass

    def on_actor_select(self, event):
        # 选择演员列表中的条目时不在替换文本框中显示选定的演员
        pass

    def on_closing(self):
        # 关闭应用程序时保存规则和字典文件
        self.save_dicts()
        self.root.destroy()

    def sort_by_pinyin(self, items):
        """按照拼音对列表进行排序"""
        def get_pinyin(s):
            # 获取汉字的拼音，转换为小写并连接
            return ''.join([p[0].lower() for p in pinyin(s, style=Style.NORMAL)])
        
        return sorted(items, key=get_pinyin)

if __name__ == "__main__":
        root = Tk()
        app = NfoModifier(root)
        root.mainloop()
