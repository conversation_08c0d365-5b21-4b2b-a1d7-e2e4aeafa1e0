import os
import pyautogui
from PIL import ImageGrab
from datetime import datetime
import tkinter as tk
from tkinter import messagebox


class ScreenShotApp:
    def __init__(self):
        self.save_directory = "d:/tdl_Windows_64bit/downloads/"
        if not os.path.exists(self.save_directory):
            os.makedirs(self.save_directory)

        self.root = tk.Tk()
        self.root.title("屏幕截图程序")

        # 设置窗口总是在最上层
        self.root.wm_attributes("-topmost", True)

        # 获取屏幕尺寸
        screen_width, screen_height = pyautogui.size()

        # 设置窗口位置为右下角
        window_width = 200  # 窗口宽度
        window_height = 100  # 窗口高度
        x_pos = screen_width - window_width - 100  # 离右边缘10像素
        y_pos = screen_height - window_height - 100  # 离底边缘10像素
        self.root.geometry(f"{window_width}x{window_height}+{x_pos}+{y_pos}")

        self.screenshot_button = tk.But<PERSON>(self.root, text="截图", command=self.capture_screen)
        self.screenshot_button.pack(side="left", padx=5, pady=5)

        self.quit_button = tk.Button(self.root, text="退出", command=self.quit_app)
        self.quit_button.pack(side="left", padx=5, pady=5)

        # 鼠标事件绑定
        self.root.bind("<ButtonPress>", self.on_mouse_press)
        self.root.bind("<B1-Motion>", self.on_mouse_move)
        self.root.bind("<ButtonRelease>", self.on_mouse_release)

        # 记录鼠标位置和窗口偏移量
        self.start_x = None
        self.start_y = None
        self.offset_x = None
        self.offset_y = None

    def on_mouse_press(self, event):
        self.start_x = event.x_root
        self.start_y = event.y_root
        self.offset_x = self.start_x - self.root.winfo_x()
        self.offset_y = self.start_y - self.root.winfo_y()

    def on_mouse_move(self, event):
        if self.start_x is not None and self.start_y is not None:
            x = event.x_root - self.offset_x
            y = event.y_root - self.offset_y
            self.root.geometry(f"+{x}+{y}")

    def on_mouse_release(self, event):
        self.start_x = None
        self.start_y = None
        self.offset_x = None
        self.offset_y = None

    def capture_screen(self):
        current_time = datetime.now().strftime("%Y%m%d_%H%M%S")
        screen_width, screen_height = pyautogui.size()
        screenshot = ImageGrab.grab(bbox=(0, 0, screen_width, screen_height))
        save_path = os.path.join(self.save_directory, f"screenshot_{current_time}.jpg")
        screenshot.save(save_path, quality=95)
        #print(f"截图已保存至 {save_path}")

        # 创建一个新的顶级窗口来显示消息
        self.message_box = tk.Toplevel(self.root)
        self.message_box.title("提示")

        # 计算消息框的位置
        message_width = 800
        message_height = 200
        x_pos = (screen_width - message_width) // 2
        y_pos = (screen_height - message_height) // 2
        self.message_box.geometry(f"{message_width}x{message_height}+{x_pos}+{y_pos}")

        # 在窗口中显示消息
        message_text = f"截图已保存至\n{save_path}"
        message_label1 = tk.Label(self.message_box, text=message_text)
        message_label1.pack(padx=20, pady=5)

        # 安排一个定时器，在1秒后关闭信息提醒窗口
        self.message_box.after(1200, self.close_message_box)

    def close_message_box(self):
        if self.message_box:
            self.message_box.destroy()

    def quit_app(self):
        #if messagebox.askokcancel("退出", "确定要退出应用程序吗？"):
        self.root.destroy()


if __name__ == "__main__":
    app = ScreenShotApp()
    app.root.mainloop()
