{"cells": [{"cell_type": "code", "execution_count": 1, "id": "fc7e0e34", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Hello World\n", "Hello World\n", "C:\\Users\\<USER>\\OneDrive\\原稿\n", "Hello\n", " World\n"]}], "source": ["s1 = 'Hello World'\n", "s2 = \"Hello World\"\n", "s3 = '\\u0048\\u0065\\u006c\\u006c\\u006f\\u0020\\u0057\\u006f\\u0072\\u006c\\u0064'\n", "s4 = \"\\u0048\\u0065\\u006c\\u006c\\u006f\\u0020\\u0057\\u006f\\u0072\\u006c\\u0064\"\n", "\n", "print(s3)\n", "print(s4)\n", "\n", "s5 = r'C:\\Users\\<USER>\\OneDrive\\原稿'\n", "print(s5)\n", "\n", "s6 = '''Hello\n", " World'''\n", "print(s6)"]}, {"cell_type": "code", "execution_count": null, "id": "8ff3d23a", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}