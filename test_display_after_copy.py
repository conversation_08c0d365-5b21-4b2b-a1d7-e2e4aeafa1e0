#!/usr/bin/env python3
# -*- coding: utf-8 -*-

# 测试剪贴后的显示效果
from html_link_extractor_gui import HTMLLinkExtractor

def test_display_after_copy():
    """测试剪贴后显示是否正确更新"""
    
    # 模拟您的情况
    mock_results = [
        {
            'file_path': 'test.html',
            'line_number': 1,
            'encoding': 'utf-8',
            'links': ['showfilesbot_1V_D1t7H5U2y0i7z6p8W1h6'],
            'context_lines': [
                '您的文件码已生成，点击复制：',
                'showfilesbot_1V_D1t7H5U2y0i7z6p8W1h6 通用解码器：ShowFilesBot',
                '牢梦'
            ]
        },
        {
            'file_path': 'test.html',
            'line_number': 2,
            'encoding': 'utf-8',
            'links': ['showfilesbot_1P_q1o7s5u2w0q4n9h2D5R3'],
            'context_lines': [
                '文件分享链接：',
                'showfilesbot_1P_q1o7s5u2w0q4n9h2D5R3 96分钟高清拍摄',
                '这个有流出来吗，哪位大佬有啊'
            ]
        },
        {
            'file_path': 'test.html',
            'line_number': 3,
            'encoding': 'utf-8',
            'links': ['showfilesbot_5V_O1q754n2B0V4D4W007H4', 'showfilesbot_7V_X1w7G38253t6y9u3t7V6'],
            'context_lines': [
                '多链接组开始',
                'showfilesbot_5V_O1q754n2B0V4D4W007H4#晨光日记',
                'showfilesbot_7V_X1w7G38253t6y9u3t7V6#爽',
                '多链接组结束'
            ],
            'merged_count': 64
        }
    ]
    
    print("原始结果:")
    for i, result in enumerate(mock_results, 1):
        merged_info = f"（合并了 {result.get('merged_count', 1)} 个重合项）" if result.get('merged_count', 1) > 1 else ""
        print(f"匹配项 {i}{merged_info}:")
        print(f"  链接: {result['links']}")
        print(f"  上下文: {result['context_lines']}")
        print()
    
    # 模拟剪贴操作
    extractor = HTMLLinkExtractor()
    
    # 模拟剪贴前3个链接的逻辑
    copied_links = []
    new_results = []
    links_copied = 0
    
    for i, result in enumerate(mock_results):
        result_links = result['links']
        links_to_copy = []
        
        # 从当前结果中取链接，直到达到3个总数
        for link in result_links:
            if links_copied < 3:
                copied_links.append(link)
                links_to_copy.append(link)
                links_copied += 1
            else:
                break
        
        # 计算剩余链接
        remaining_links = [link for link in result_links if link not in links_to_copy]
        
        if remaining_links:
            # 如果还有剩余链接，创建新的结果保留它们
            new_result = result.copy()
            new_result['links'] = remaining_links

            # 更新上下文文本，移除已剪贴的链接
            updated_context = []
            for line in result['context_lines']:
                if line.strip():
                    updated_line = line
                    # 从文本中移除已剪贴的链接
                    for copied_link in links_to_copy:
                        updated_line = updated_line.replace(copied_link, '')

                    # 清理多余的空格和标点
                    updated_line = ' '.join(updated_line.split())  # 移除多余空格
                    updated_line = updated_line.replace('  ', ' ')  # 移除双空格
                    updated_line = updated_line.replace(' ,', ',')  # 修复逗号前的空格
                    updated_line = updated_line.replace(' .', '.')  # 修复句号前的空格
                    updated_line = updated_line.replace(',,', ',')  # 移除双逗号
                    updated_line = updated_line.strip(' ,.')  # 移除首尾的空格、逗号、句号

                    if updated_line:  # 只添加非空行
                        updated_context.append(updated_line)
                else:
                    updated_context.append(line)

            new_result['context_lines'] = updated_context
            new_results.append(new_result)
        
        # 如果已经复制了3个链接，将后续的所有结果直接保留
        if links_copied >= 3:
            remaining_results = mock_results[i + 1:]
            new_results.extend(remaining_results)
            break
    
    print("剪贴后的结果:")
    print(f"已剪贴的链接: {copied_links}")
    print("\n剩余结果:")
    
    for i, result in enumerate(new_results, 1):
        merged_info = f"（合并了 {result.get('merged_count', 1)} 个重合项）" if result.get('merged_count', 1) > 1 else ""
        print(f"匹配项 {i}{merged_info}:")
        print(f"  包含链接: {result['links']}")
        print(f"  上下文:")
        for line in result['context_lines']:
            print(f"    {line}")
        print()

if __name__ == "__main__":
    test_display_after_copy()
