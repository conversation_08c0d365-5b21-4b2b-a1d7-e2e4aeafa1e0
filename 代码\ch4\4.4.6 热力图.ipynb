{"cells": [{"cell_type": "code", "execution_count": 9, "id": "71b724de", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1000x800 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import pandas as pd\n", "import seaborn as sns\n", "import matplotlib.pyplot as plt\n", "\n", "# 设置中文显示\n", "plt.rcParams['font.family'] = ['SimHei']\n", "plt.rcParams['axes.unicode_minus'] = False\n", "\n", "# 创建测试数据\n", "data = pd.DataFrame({\n", "    '股票1': [10, 12, 8, 15, 9],\n", "    '股票2': [20, 18, 25, 22, 24],\n", "    '股票3': [7, 9, 6, 8, 10],\n", "    '股票4': [13, 11, 14, 10, 12]\n", "})\n", "\n", "# 提取多个股票的收盘价数据列\n", "股票_prices = data[['股票1', '股票2', '股票3', '股票4']]\n", "\n", "# 计算收盘价之间的相关性\n", "correlation_matrix = 股票_prices.corr()\n", "\n", "# 绘制热力图\n", "plt.figure(figsize=(10, 8))  # 设置图形大小\n", "sns.heatmap(correlation_matrix, annot=True, cmap='coolwarm')\n", "\n", "# 设置图形标题\n", "plt.title('股票 Prices Correlation')\n", "\n", "# 显示图形\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": null, "id": "486a0c55", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}