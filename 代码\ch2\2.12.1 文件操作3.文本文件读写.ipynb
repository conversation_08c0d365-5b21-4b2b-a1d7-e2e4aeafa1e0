{"cells": [{"cell_type": "code", "execution_count": 1, "id": "20877cfa", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['Hello World.\\n', '世界，您好。']\n", "文件复制成功\n"]}], "source": ["f_name = 'test.txt'\n", "\n", "with open(f_name, 'r', encoding='utf-8') as f: \n", "    lines = f.readlines() \n", "    print(lines)\n", "    copy_f_name = 'copy.txt'\n", "    with open(copy_f_name, 'w', encoding='utf-8') as copy_f: \n", "        copy_f.writelines(lines) \n", "        print('文件复制成功')"]}, {"cell_type": "code", "execution_count": null, "id": "88eb3665", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}