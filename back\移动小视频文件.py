import os
import shutil
import cv2
from tkinter import Tk, filedialog

def select_folder():
    """打开对话框选择文件夹"""
    root = Tk()
    root.withdraw()  # 隐藏主窗口
    folder_path = filedialog.askdirectory(title="选择文件夹")
    return folder_path

def get_video_resolution(video_path):
    """获取视频的分辨率"""
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        print(f"无法打开视频文件: {video_path}")
        return None
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    cap.release()
    return width, height

def move_low_resolution_videos(folder_path):
    """移动低分辨率视频到lowre文件夹，同时保留目录结构"""
    lowre_folder = os.path.join(folder_path, 'lowre')
    os.makedirs(lowre_folder, exist_ok=True)  # 创建lowre文件夹

    for root, dirs, files in os.walk(folder_path):
        for file in files:
            if file.lower().endswith(('.mp4', '.avi', '.mov', '.mkv', '.flv', '.ts')):  # 视频文件格式
                video_path = os.path.join(root, file)
                resolution = get_video_resolution(video_path)
                if resolution and (resolution[0] < 720 and resolution[1] < 720):
                    # 保留原有文件夹结构
                    relative_path = os.path.relpath(root, folder_path)
                    target_folder = os.path.join(lowre_folder, relative_path)
                    os.makedirs(target_folder, exist_ok=True)  # 创建目标文件夹
                    target_path = os.path.join(target_folder, file)
                    print(f"移动文件: {video_path} -> {target_path}，分辨率: {resolution}")
                    shutil.move(video_path, target_path)  # 移动文件

if __name__ == "__main__":
    selected_folder = select_folder()  # 选择文件夹
    if selected_folder:
        move_low_resolution_videos(selected_folder)  # 处理视频文件
    else:
        print("未选择任何文件夹")
