{"cells": [{"cell_type": "code", "execution_count": null, "id": "b7c36346", "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "import numpy as np\n", "\n", "# 设置中文字体\n", "plt.rcParams['font.family'] = ['SimHei']\n", "plt.rcParams['axes.unicode_minus'] = False\n", "\n", "\n", "def drowsubbar():\n", "    \"\"\"绘制饼状图\"\"\"\n", "\n", "    x1 = [1, 3, 5, 7, 9]  # x1轴坐标数据\n", "    y1 = [5, 2, 7, 8, 2]  # y1轴坐标数据\n", "\n", "    x2 = [2, 4, 6, 8, 10]  # x2轴坐标数据\n", "    y2 = [8, 6, 2, 5, 6]  # y2轴坐标数据\n", "\n", "    # 绘制柱状图\n", "    plt.bar(x1, y1, label='柱状图1')\n", "    plt.bar(x2, y2, label='柱状图2')\n", "\n", "    plt.title('绘制柱状图')  # 添加图表标题\n", "\n", "    plt.ylabel('y轴')  # 添加y轴标题\n", "    plt.xlabel('x轴')  # 添加x轴标题\n", "\n", "    plt.title('绘制散点图')\n", "\n", "\n", "def drowsubpie():\n", "    \"\"\"绘制饼状图\"\"\"\n", "\n", "    # 各种活动标题列表\n", "    activies = ['工作', '睡', '吃', '玩']\n", "    # 各种活动所占时间列表\n", "    slices = [8, 7, 3, 6]\n", "    # 各种活动在饼状图中的颜色列表\n", "    cols = ['c', 'm', 'r', 'b']\n", "\n", "    plt.pie(slices, labels=activies, colors=cols,\n", "            shadow=True, explode=(0, 0.1, 0, 0), autopct='%.1f%%')\n", "\n", "    plt.title('绘制饼状图')\n", "\n", "\n", "def drowsubline():\n", "    \"\"\"绘制折线图\"\"\"\n", "\n", "    x = [5, 4, 2, 1]  # x轴坐标数据\n", "    y = [7, 8, 9, 10]  # y轴坐标数据\n", "\n", "    # 绘制线段\n", "    plt.plot(x, y, 'b', label='线1', linewidth=2)\n", "\n", "    plt.title('绘制折线图')  # 添加图表标题\n", "\n", "    plt.ylabel('y轴')  # 添加y轴标题\n", "    plt.xlabel('x轴')  # 添加x轴标题\n", "\n", "    plt.legend()  # 设置图例\n", "\n", "\n", "def drowssubscatter():\n", "    \"\"\"绘制散点图\"\"\"\n", "\n", "    n = 1024\n", "    x = np.random.normal(0, 1, n)\n", "    y = np.random.normal(0, 1, n)\n", "\n", "    plt.scatter(x, y)\n", "\n", "    plt.title('绘制散点图')\n", "\n", "\n", "plt.subplot(2, 2, 1)  # 替换(221)\n", "drowsubbar()\n", "\n", "plt.subplot(2, 2, 2)  # 替换(222)\n", "drowsubpie()\n", "\n", "plt.subplot(2, 2, 3)  # 替换(223)\n", "drowsubline()\n", "\n", "plt.subplot(2, 2, 4)  # 替换(224)\n", "drowssubscatter()\n", "\n", "plt.tight_layout()  # 调整布局\n", "\n", "plt.show()  # 显示图形\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}