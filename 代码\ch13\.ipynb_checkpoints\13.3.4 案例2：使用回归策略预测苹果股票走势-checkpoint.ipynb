{"cells": [{"cell_type": "code", "execution_count": 1, "id": "700d213b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["均方误差 (MSE): 0.553726274089948\n"]}, {"data": {"text/plain": ["['model.pkl']"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.linear_model import LinearRegression\n", "from sklearn.metrics import mean_squared_error\n", "import joblib\n", "\n", "# 1. 数据准备和处理\n", "data = pd.read_csv('data/AAPL.csv')\n", "data['Close'] = data['Close'].str.replace('$', '').astype(float)\n", "data['Open'] = data['Open'].str.replace('$', '').astype(float)\n", "data['High'] = data['High'].str.replace('$', '').astype(float)\n", "data['Low'] = data['Low'].str.replace('$', '').astype(float)\n", "\n", "# 提取特征和目标变量\n", "X = data[['Volume', 'Open', 'High', 'Low']]\n", "y = data['Close']\n", "\n", "# 划分训练集测试集\n", "X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3, random_state=1)\n", "\n", "# 2. 模型训练\n", "model = LinearRegression()\n", "model.fit(X_train, y_train)\n", "\n", "# 3. 测试集预测\n", "y_pred = model.predict(X_test)\n", "\n", "# 4. 模型评估\n", "mse = mean_squared_error(y_test, y_pred)\n", "print(f\"均方误差 (MSE): {mse}\")\n", "\n", "# 保存模型数据\n", "joblib.dump(model, 'model.pkl')"]}, {"cell_type": "markdown", "id": "d1c2abab", "metadata": {}, "source": ["### 预测股票走势"]}, {"cell_type": "code", "execution_count": 2, "id": "dacf1031", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["预测结果： 186.8574072889791\n", "预测结果： 186.257860664635\n", "预测结果： 184.10770494393964\n", "预测结果： 185.93719141281886\n", "预测结果： 184.57037449339362\n", "预测结果： 185.60661648948147\n", "预测结果： 183.3857326924576\n", "预测结果： 183.75000533180457\n", "预测结果： 183.10016909961496\n", "预测结果： 181.8261013581762\n", "预测结果： 179.94003987015927\n", "预测结果： 179.66272055047042\n", "预测结果： 178.45223991648865\n", "预测结果： 179.95636123056215\n", "预测结果： 180.48631322458314\n", "预测结果： 178.77667060084391\n", "预测结果： 177.62040380763523\n", "预测结果： 178.3089116835725\n", "预测结果： 175.0859834552677\n", "预测结果： 173.17338879527563\n"]}], "source": ["import pandas as pd\n", "import joblib\n", "\n", "# 加载模型\n", "loaded_model = joblib.load('model.pkl')\n", "\n", "# 新数据准备\n", "new_data = pd.read_csv('data/HistoricalData_1687681340565.csv')\n", "new_data['Close'] = new_data['Close'].str.replace('$', '').astype(float)\n", "new_data['Open'] = new_data['Open'].str.replace('$', '').astype(float)\n", "new_data['High'] = new_data['High'].str.replace('$', '').astype(float)\n", "new_data['Low'] = new_data['Low'].str.replace('$', '').astype(float)\n", "\n", "# 删除或保持\"Volume\"特征列为空值\n", "# new_data.drop('Volume', axis=1, inplace=True)\n", "new_data.drop('Close', axis=1, inplace=True)\n", "new_data.drop('Date', axis=1, inplace=True)\n", "\n", "predicted_labels = loaded_model.predict(new_data)\n", "\n", "# 输出预测结果\n", "for label in predicted_labels:\n", "    print(\"预测结果：\", label)"]}, {"cell_type": "markdown", "id": "74557c42", "metadata": {}, "source": ["### 可视化分析"]}, {"cell_type": "code", "execution_count": 4, "id": "1a9e2134", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: >"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAigAAAGKCAYAAAAmMbr9AAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjcuMSwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy/bCgiHAAAACXBIWXMAAA9hAAAPYQGoP6dpAABQ9UlEQVR4nO3deXhU9aH/8feZNZOQhYSQEEiCgsgmCIqgqIDs+1bFitbWhVrXSn/WYq2K3op1u31atdperUv1esXKYlF2FS0giiICYV8CZIEA2Ugy6/n9MWSUCkogyZlkPq/nmeeRM5PJJ23gfOac72KYpmkiIiIiEkVsVgcQERER+U8qKCIiIhJ1VFBEREQk6qigiIiISNRRQREREZGoo4IiIiIiUUcFRURERKKOCoqIiIhEHYfVAU5HKBSioKCAxMREDMOwOo6IiIicAtM0qaioICsrC5vt+6+RNMmCUlBQQHZ2ttUxRERE5DTs3buXdu3afe9rmmRBSUxMBMI/YFJSksVpRERE5FSUl5eTnZ0dOY9/nyZZUGpv6yQlJamgiIiINDGnMjxDg2RFREQk6qigiIiISNRRQREREZGoo4IiIiIiUUcFRURERKKOCoqIiIhEHRUUERERiToqKCIiIhJ1VFBEREQk6qigiIiISNRRQREREZGoo4IiIiIiUadJbhYoIk1ffn4+VVVVp/W1ubm5eDyeek4kItFEBUVEGt3ChQt57LHHTvvrO3XqxF/+8hfsdns9phKRaKJbPCLSqKqqqnjhhRcACDk9hFwJdXqYho2tW7fy/vvvW/yTiEhD0hUUEWlU//u//8uRI0cIuZM42n0i2Op2FcRZtJG4vZ/y4osvcsUVVxAfH99ASUXESrqCIiKNpri4mLfeegsAb/aFdS4nAP7WnQm5kzhy5AhvvPFGfUcUkSihgiIijcI0TZ588km8Xi+BxEwCKbmn90Y2O97sPkD4asy2bdvqMaWIRAsVFBFpFO+++y6fffYZpmGnJrc/GMZpv1cgJQd/y/YEg0FmzZqFz+erx6QiEg1UUESkwRUWFvLcc88B4G13IaYn+cze0DDw5l5MyBHHzp07efXVV+shpYhEExUUEWlQXq+XmTNnUlNTQyAxE39G13p5X9PpwZt7CQBvvPEGa9asqZf3FZHooIIiIg3GNE2efvppNm/ejGl3U3PWZWd0a+c/BVLb42vViVAoxMMPP8y+ffvq7b1FxFoqKCLSYP75z3+yaNEiTAyqOw7CdCfW+/fw5l5MMCGdyspK7r///tNenVZEoosKiog0iNWrV38z7iT7IoJJWQ3zjWx2qjsOJuT0sHv3bh555BECgUDDfC8RaTQqKCJS77788kseeOABQqEQ/rSO9Tbu5GRMVzzVHQdjGnZWrVrFrFmzCAaDDfo9RaRh1bmgrFixgrFjx5KVlYVhGMydO/e454uLi/npT39KVlYW8fHxjBgx4jvrFNTU1HDbbbeRlpZGixYtmDx5MsXFxWf0g4hIdNi4cSMzZszA5/PhT8mhpv2l9Tru5GRCLVpT3fEKTMPGsmXLeOqppwiFQg3+fUWkYdS5oBw9epSePXvy7LPPfuc50zSZMGECO3fuZN68eXz55Zfk5uYyZMgQjh49Gnnd3Xffzbvvvsvs2bP56KOPKCgoYNKkSWf2k4iI5bZu3cq9994bnrGTlEVNh4Fga7wLtcGUbGrOHoCJwXvvvcczzzyDaZqN9v1FpP4Y5hn87TUMgzlz5jBhwgQg/I/Tueeey4YNG+jWrRsAoVCIzMxMHn30UW666SbKyspIT0/njTfe4Ec/+hEAmzdvpkuXLqxatYp+/fr94PctLy8nOTmZsrIykpKSTje+iNSjtWvX8rvf/Y6qqioCLTKo7jQM7E5LsjhKtuHZ9TEAw4cP55577sHh0NZjIlary/m7Xj/aeL1eAOLi4r75BjYbbrebTz75BAj/I+b3+xkyZEjkNZ07dyYnJ4dVq1ad9H3Ly8uPe4hI9Fi2bBn33ntvuJwktqG601DLyglAoNU5VJ91GSYGixYt4r777tPsHpEmpl4LSm3RmDFjBkeOHMHn8/GHP/yBffv2UVhYCEBRUREul4uUlJTjvjYjI4OioqITvu+sWbNITk6OPLKzs+sztoicgbfffjsyc8bf8qxjV05cVscKl5RzhmDaHKxZs4bp06dTWlpqdSwROUX1WlCcTifvvPMOW7duJTU1lfj4eD744ANGjhyJ7QzuQ8+YMYOysrLIY+/evfWYWkROh8/n4+mnn+aZZ54J/7l112NjTuq+Q3FDCaZkU3XuCEyHm82bN3PLLbdoc0GRJqLeR69dcMEFrFu3jtLSUgoLC1m4cCGHDh3i7LPPBiAzMxOfz/edTzLFxcVkZmae8D3dbjdJSUnHPUTEOgcOHOCuu+5i/vz5QHh/HW9O30aZrVNXoRatOdp5DCF3IkVFRdx22228//77VscSkR/QYMPrk5OTSU9PZ9u2bXz++eeMHz8eCBcYp9PJsmXLIq/dsmUL+fn5XHzxxQ0VR0Tqydq1a7n55pvJy8vDtLuo6jQMX5seUVlOapmeZI52HUcgOTty6/mpp57SLsgiUazOw9orKyvZvn175M+7du1i3bp1pKamkpOTw+zZs0lPTycnJ4evv/6au+66iwkTJjBs2DAgXFxuvPFGpk+fTmpqKklJSdxxxx1cfPHFpzSDR0SsEQgEeP3113nllVcIhUIE49PC6440wPL1DcLhpvqcIbgKv8K9/wveffddtm3bxm9/+1uNaxOJQnWeZvzhhx8yaNCg7xy//vrrefnll/nTn/7EE088QXFxMW3atOEnP/kJv/vd73C5vhk0V1NTw69+9Sv+93//F6/Xy/Dhw3nuuedOeovnP2masUjjys/PZ9asWeTl5QHgb3UONbkXg61pTt21l+3Ds+NDjKAPt9vNLbfcwvjx489orJyI/LC6nL/PaB0Uq6igiDSOUCjEnDlzeOGFF/D5fJh2FzW5FxNIPTuqb+mcCsNbSdzuj3GUh2cYXnjhhfz617+mdevWFicTab5UUETkjBUWFvLEE0/wxRdfAIRXhj3rMkxXgsXJ6pFp4jyQh3vfZxihIAkJCdx5550MGzYMo4kXMJFopIIiIqfN7/fz1ltv8eqrr+L1ejFtDrzZffCnd27yV01Oxqgpw7NzBfajBwHo3bs3d911F7m5uRYnE2leVFBE5LR88cUX/PGPfyQ/Px+AQGImNe37Y8YlW5ysEZghXIVf4ypYh2EGcTgcTJkyheuuu+641bFF5PSpoIhInRw6dIi//OUvLF26FICQIw5v9kUE0jo026smJ2N4K4jbsxpHWXhByIyMDO6880769+9vcTKRpk8FRUROSU1NDW+//TZvvPEGVVVVmBj4W3fG27Y3ONxWx7OOaeIozcedvxqbL7wTe58+ffj5z39Ox44dLQ4n0nSpoIjI9woGgyxatIiXXnqJkpKS8LGEVtTkXkIooZXF6aJI0I+r4CtcxRswzBCGYTBs2DBuvPFGzfYROQ0qKCJyQqZp8umnn/LCCy+wa9cuAEKuFnjbXdAspg43FKOmHPe+tTiPhP83c7lcTJ48mWuuuYbExCayUJ1IFFBBEZHv2LBhAy+++CJffvklAKbdhTfrfPytu0TVBn/RzFZ5APe+z3FUhHdeT0pKYurUqYwfP14DaUVOgQqKiER8/fXXvPzyy6xduxYA07Djz+iKt02P2B5ncrpME3vZXtx7P8deUwpAy5Ytufrqqxk3bhwej8fafCJRTAVFRFi/fj0vv/xyZKE10zDwp52DL+t8THcLi9M1A2YIR8l23IXrsHkrAUhJSeHqq69m/PjxKioiJ6CCIhKjTNNk/fr1vPLKK8cXk1bn4GvTs+ls7NeUhEI4Dm3HXfgVNm8FEC4qU6ZMYfz48cTHx1scUCR6qKCIxJhgMMi///1v3nzzTTZt2gSAadi+VUx0xaTBhUI4Du/AXbAuUlRatGjB+PHjmTRpEmlpaRYHFLGeCopIjPB6vSxatIi33nqLffv2AcfGmLQ6B1+bHiomVjBDOA7tCF9RqSkHwOl0MmzYMK666iotny8xTQVFpJkrKytj3rx5vPPOO5SWlgLhWTm+1l3wZ3TFdGr8g+WOLfbmKvwa+9EDkcP9+/dnypQpnHfeedqQUGKOCopIM7V7927eeecdFi9eTE1NDRBex8SX0Q1/eiewOy1OKCdiryjGWfQ1jtJ8aitJly5dmDx5MgMGDMDp1P9vEhtUUESakWAwyMqVK5kzZ05k4CtAMD4NX2Z3AqlngWGzMKGcKqO6DFfx1zhLdmCYQQBSU1MZO3Ys48aN0zgVafZUUESagbKyMt577z3mzp1LcXExACYGgZQc/BldCSZmauXXJsrwV+M8uAXngTxs/moA7HY7AwcOZNKkSXTt2lW3f6RZUkERacK2bt3K3LlzWbp0KT6fDwDT7saX3gl/6y4a+NqchII4juzBdWAT9spvxql06tSJiRMnMmjQIK1QK82KCopIE1NVVcXy5ct599132bJlS+R4MD4VX+uuBNLOBpvDwoTS0GxHS3AdyMNxaGfk9k+LFi0YNmwYY8eO5ayzzrI4ociZU0ERaSK2bdvGu+++y9KlS6mqqgLC65cEWubib92FYIsM3caJMYa/BmfJFpwHtmDzVUaOd+/enbFjxzJw4EDcbm1RIE2TCopIFKuuro5cLdm8eXPkeMidhC/9XAKtOmqasIT3/Cnfj/PAlmOzf8L/VLdo0YLhw4czduxY2rdvb21GkTpSQRGJMqZpsmnTJt5//32WL1/+3asl6ecSTGyjqyVyQoavCmfJVpwHtx53VaVbt26MHDmSQYMGkZCQYGFCkVOjgiISJQ4fPszixYt5//332bNnT+R4yJ2IP/1c/K3O0dUSOXVmCHvZfpwHt+Ao3Ru5qhIXF8eAAQMYNWoUPXr00AwgiVoqKCIWCgQCfPrpp7z33nusWrWKUCgEgGmzE2h5Fv5W52iKsJwxw1eF89B2HCXbsNeURY63bduWkSNHMnz4cNLT0y1MKPJdKigiFti5cyeLFi1i8eLFHDlyJHI8mJCOP70T/tSzwO6yMKE0S6aJrfIAzpJtOA/vxAgFALDZbPTp04fhw4fTv39/DayVqKCCItJIjhw5wtKlS1m8eDHbtm2LHA854gi06oi/1TmEPC0tTCgxJejHcWQ3zoNbcVQWRw4nJCRwxRVXMGzYMLp3765bQGIZFRSRBuT1elm1ahWLFi3i008//eYWjmEjkJJNIK0jgeRssGn5ebGOUVOGs2Q7zkPbsfmORo5nZWUxfPhwhg0bRps2bSxMKLFIBUWkntXOwlm0aBHLly+nsvKbmRTBhFb40zriTzsbHFr1U6KMaWKvKAqPVzm8K3ILCKBnz54MHz6cAQMGaBaQNAoVFJF6sm/fPpYsWcKSJUsoKCiIHA85E/C36kAgrSMhT4p1AUXqIujHcWQPzkPbsZcXRHZWdrlc9O/fn6FDh3LRRRfhcGjVYmkYKigiZ6C0tJQPPviAJUuWsGnTpshx0+YIr1mS1pFgUhvtICxNmuGtxHl4B46SHdhrSiPHk5OTGTRoEMOGDaNLly4aryL1SgVFpI68Xi8rV65kyZIlfPrppwSD4b1QTAyCSVn4W3UkkJIDdqfFSUXqmWliqzqE89AOHId2YgtUR55q164dQ4cOZciQIbRt29bCkNJcqKCInIJgMMhXX33FkiVLWLFiBUePfjOQMBifhj+tI4G0szCd8RamFGlEZgh7eQHOkh04SvccN16lW7duDBkyhEGDBpGSkmJdRmnSVFBETsI0TbZv387SpUtZtmwZJSUlkedCrgT8aR0IpHXQ1GCRyHiVHcfGq4RPFXa7nT59+jB06FD69+9PXJwGhsupU0ER+Q9FRUUsW7aMJUuWsHv37shx0+7Cn3oWgbQO2jlY5CQMXxWOwzvDZaXqUOS4x+Ph8ssvZ8iQIfTq1UuDa+UHqaCIEP49+fDDD1m6dCnr16+PHDcN+7H1SjoQSG4HNruFKUWaFlt1KY5DO3Ae3oHN+810+5YtWzJ48GCGDBnCueeeq8G1ckIqKBKzahdRW7p0KatXryYQCN9DN4FgYpvwLZyWueDQst8iZ6R2if3DO3Ac3oUt4I08lZ2dHRlcm5WVZWFIiTYqKBJTQqFQZLDrRx99dPxgV0/qsXElZ2O6tBCVSIMIBbGX7w/PBDqSj2EGI091796doUOHMnDgQJKTky0MKdFABUViws6dO1myZAlLly7l4MGDkeMhVwL+1GODXeM12FWkUQV9/zG4Nsxut9O3b1+GDh3KJZdcos0LY5QKijRbJSUlLF26lCVLlrBjx47IcdPuwt+yfXiwa2KmBruKRIGTDa5NSEhgwIABDB06lJ49e2LTvlUxQwVFmpWqqio++eQTFi9ezBdffHH85nzJxwa7prQDm2YQNHmmCbVrb9gcKprNiK36SHhw7aGd2HzfDK7NyMhgyJAhDBs2jNzcXAsTSmNQQZEmLxgM8uWXX7J48WJWrFhBTU3NN8+1aB3enC/1LA12bW6CfhK/eA2Ait7XaeXe5sg0sVcW4yjZjvPILoygP/LUueeey7Bhw7jiiito2VK3Z5sjFRRpsnbv3s2iRYtYsmTJ8YuouROP7RjcATNO/583WyoosSUUwFGaj7NkB/byfRjHTkc2m42LLrqIESNGcMkll+ByuSwOKvWlLudvXRMXy1VUVLB8+XIWLlxIXl5e5Lhpd+NPPQt/q46EEtJ1uV+kubE5CKSeTSD1bAx/NY7Du3Ae2g5HS1i9ejWrV68mMTGRwYMHM2LECK2vEmNUUMQSgUCAzz//nIULF/Lvf/8bvz98mdc0jPC4klYdCSRnaxE1kRhhOj34M7riz+h6bDG47ThLtlNRUcHcuXOZO3cu7du3Z8SIEQwdOpS0tDSrI0sD0y0eaVR79uzh/fffZ/HixRw+fDhyPOhpib9Vp/B6JU6PhQnFUrrFI99mhrCXF+Is2YbjyJ7I+irfvgXUv39/nE79njQVusUjUaW6upoPP/yQBQsWsGHDhsjxkCOOQNrZ+FudQyhen4ZE5D8YNoLJbQkmt4WAF+fhXThLtsPRA5FbQMnJyQwfPpzRo0drFlAzoyso0iBM02Tr1q3861//YtmyZVRVVYWPY4T3wWl1jvbBke/SFRQ5BUZ1Gc6SbTgPbcfmr4oc7969O2PGjGHAgAF4PLoSG400i0csU1FRwdKlS1mwYAHbt2+PHA+5E/G36oS/1TmYrngLE0pUU0GRujBD2Mv24Ty4FUfpXgzCp7OEhAQGDx7MmDFj6NSpk8Uh5dt0i0ca3ebNm5k3bx7Lli3D5/MBxxZSa9kef3ongoltNAtHROqXYSOYkkMwJQfDVxW+qlKyhaNHK5k/fz7z58+nU6dOjB8/nsGDBxMXF2d1YqkDXUGR0+b1evnwww+ZO3fucdODg54U/K3Oxd+qAzj0D4LUga6gyJkyTewVhTgPbjk2sDa88nRiYiIjR45k3LhxtGvXzuKQsUtXUKRBFRYWMn/+fBYsWEB5eTnwraslrbsQbNFaV0tExBqGQTApi2BSFoa/BkfJNlwH86ioqOCtt97irbfeok+fPkycOJG+fftit2scXLRSQZFTYpoma9eu5Z///CerV6+m9sJbyJWAP70z/vROmh4sIlHFdMbhb3Me/sxu2Mv24zqQh71sH5999hmfffYZmZmZjB8/njFjxpCYmGh1XPkPusUj38vv97Ns2TJmz5593O7BgaS2+Ft3JpCSDYZ2IpV6ols80sCMmnJcBzfjPLgNI+gFIC4ujtGjR/OjH/2INm3aWJywedMsHjljFRUVzJ8/n3feeYdDh8LbpJs2B/5WnfBldMGMS7Y4oTRLKijSWEIBHId34SragL36CBBeAO6yyy7jqquuolu3bhYHbJ40BkVOW0FBAW+//TbvvfdeZAfhkDMef0ZXfOnnavdgEWkebI7wekxpHbGXF+Aq2oCjfD8fffQRH330Ed27d+eqq66if//+GqdiERUUAWDv3r289tprLF26lFAoPOo96GmJL/M8AqlnaUE1EWmeDINgcluqk9tiqzqCq3gDjkM72LBhAxs2bCAnJ4frrruOK664QkWlkekWT4zbs2cPr732GsuXL48Uk0BSW3yZ3QkmZWk2jjQu3eKRKGD4q3AW5+E6kIcRDK/rlJ2dHSkqDoc+258ujUGRH7R79+5IMan9FQikZOPN6kUooZXF6SRmqaBINAn6cBXn4SraEBlQ27ZtW6677jqGDBmionIaVFDkpAoKCvif//kfPvjgg0gx8afk4Ms6X8VErKeCItEo6Md1YBPOog3YAuGikpWVxQ033MAVV1yBzaaZjKdKBUW+o6Kigtdee405c+bg9/sB8LfMDRcT7SQs0UIFRaJZ0I/zwGZcRV9jC4QnEXTu3Jlbb72VHj16WByuadAsHonw+/3MmzePV199NbLqayApC292HxUTEZG6sDvDC7+17oyreCOuwq/ZvHkzd955J5dddhk///nPtYx+PVJBaaZM0+STTz7h+eefZ//+/UB4jxxv9kUEk9pq8KuIyOmyO/FlnY8//Vxc+7/AeXArH3/8MStXrmTChAlcf/31urpfD1RQmqEDBw7wxz/+kZUrVwIQcnjwteuNv9U5WvVVRKSemE4P3vb98Wd0xb33Myjbxz//+U+WL1/OnXfeycCBAzH0YfC0qaA0I6FQiPnz5/PXv/6VqqoqTMOGL7M7vjY9dS9fRKSBhDwtqe40DHvZftz5n3LkyBFmzpzJkiVL+OUvf0nr1q2tjtgkqaA0E3v27OGJJ55gw4YNAAQT0qk561JCnpYWJxMRiQ3B5LZUdRuPq/ArXIXrWblyJevWrWPatGmMGzdOs33qqM7/a61YsYKxY8eSlZWFYRjMnTv3uOcrKyu5/fbbadeuHR6Ph65du/L8888f95ray17fftxyyy1n9IPEKtM0efvtt7npppvYsGEDps1BTU4/qrqMVjkREWlsNju+tr2p6jaeYEI6VVVV/PGPf+Tuu++mpKTE6nRNSp0LytGjR+nZsyfPPvvsCZ+fPn06Cxcu5B//+Ad5eXn88pe/5Pbbb2f+/PnHve7mm2+msLAw8nj88cdP7yeIYZWVlTz44IM888wz+P1+AsntONp9Ev6MrhprIiJioZCnJVVdRlOT0w/T5uCrr77ipptu4vPPP7c6WpNR51s8I0eOZOTIkSd9fuXKlVx//fUMHDgQgGnTpvHCCy+wZs0axo0bF3ldfHw8mZmZdU8sAGzbto0HH3yQgoICTMOGN/si/K27aHaOiEi0MGz4M7oSSG6LZ/tySkuPcM8993D99ddz3XXXaW+fH1DvH7MvueQS5s+fz/79+zFNkw8++ICtW7cybNiw4173+uuv06pVK7p3786MGTOoqqo66Xt6vV7Ky8uPe8SyBQsWcOutt1JQUEDI1YKqzqOPXTVRORERiTZmXDJVXcfia9UJ0zR5+eWXuffee2P+XPZD6n2Q7J///GemTZtGu3btcDgc2Gw2/va3v3H55ZdHXnPNNdeQm5tLVlYW69ev595772XLli288847J3zPWbNmMXPmzPqO2uSYpslrr73GSy+9BEAgOZvqsy8Hh9viZCIi8r1sDrxnXUowMYO4PSv5/PPPufPOO3nyySdp1UrbjJxIgxSU1atXM3/+fHJzc1mxYgW33XYbWVlZDBkyBAjf9ql13nnn0aZNGwYPHsyOHTvo0KHDd95zxowZTJ8+PfLn8vJysrOz6zt6VAuFQvzlL39h9uzZAHizzseX1UtXTUREmpBAq3Ooik/Ds3Uxu3fv5o477uDJJ5+kbdu2VkeLOvV6i6e6upr77ruPp59+mrFjx9KjRw9uv/12pkyZwpNPPnnSr+vbty8A27dvP+HzbrebpKSk4x6xJBAI8Pjjj0fKSU12X3xte6uciIg0QaH41PBMS3cihYWF3HHHHezcudPqWFGnXguK3+/H7/d/Z6633W4nFAqd9OvWrVsHQJs2beozTrNgmiZPPPEECxcuxMSg+qzL8Gd2szqWiIicAdOdSFXn0QQ9LTl8+DB3/fKX7Nu3z+pYUaXOt3gqKyuPu9Kxa9cu1q1bR2pqKjk5OQwYMIB77rkHj8dDbm4uH330Ea+++ipPP/00ADt27OCNN95g1KhRpKWlsX79eu6++24uv/xy7QZ5Am+88QaLFi3CxKCm4yACLdtbHUlEROqB6YqnqvMo4rcsoqK8hBkzZvDcc8+RmJhodbSoYJimadblCz788EMGDRr0nePXX389L7/8MkVFRcyYMYPFixdz+PBhcnNzmTZtGnfffTeGYbB3716uvfZaNmzYwNGjR8nOzmbixIncf//9p3zrpi7bNTdlH3/8Mb/73e8AqMnpF56pI9KcBf0kfvEaABW9r9MWDRITDH8V8ZvexeY7Su/evXn88cdxOJrnQu91OX/XuaBEg1goKDt27ODW227DW1ODr3UXvLkXWx1JpOGpoEiMslUdIj5vAUYowIQJE/jlL39pdaQGUZfzt5YbjULBYJDHH38cb00NgaQsvDl9rY4kIiINKBSfRvXZAwGYO3cuX375pbWBooAKShSaN28eW7ZswbQ7qTnrci1bL82fGcLwVmB4KyOHDG8lhrcCzJMPsBdpToItc/ClnwvAf//3f+P3+y1OZC2d+aJMSUkJ//M//wOAt92FmK54ixOJNDzDd5QW62fTYuOcyLEWG+fQYv1sDN9RC5OJNC5vuwsJOeLIz8/n//7v/6yOYykVlCjz6quvUlVVRTChFf5jTVpERGKEwx25rf/aa69RVlZmcSDrqKBEkcrKShYvXgyAt10f3doREYlBgdSzCcan4vV6ef/9962OYxmdAaPIokWLqKmpIehJIZionZ5FRGKSYYR3pwfmz5//vQudNmcqKFHCNE3mzZsHEP7F1DL2IiIxy5/aAdPuoqCggM8++8zqOJZQQYkSRUVF5OfnYxoG/rTvbpgoIiIxxO7An3o2AGvWrLE4jDVUUKLE+vXrAQjFtwK7y+I0IiJitWBS+Fb/V199ZXESa6igRInaX0CNPREREYBgi/D5YMeOHVRUVFicpvGpoESJLVu2ABBs0driJCIiEg1MVzwhdwtM0zxuk95YoYISJQ4fPgxAyN3C4iQiIhItQq7wzsa154hYooISBQKBAKWlpQCYTq0cKyIiYabTA8ChQ4csTtL4VFCiQFlZGaZpYmJgOtxWxxERkShRW1B0BUUsEQwGw/9hGFo9VkREIsxj54TIeSKG6GwYBex2e/g/tGuriIh8i3HsvOBwOCxO0vhUUKJA7S+eASopIiLyDdMEvvVBNoaooESBuLi4b/4Q9FsXREREoooRCp8TjjtPxAgVlCjgdrtJSEgAwOavtjiNiIhEC8NXBUBaWprFSRqfCkqUaNWqFQCGv8riJCIiEi1qzwkqKGKZ2l8+w3fU4iQiIhItagtK7YfYWKKCEiWysrIAsHljb78FERE5gYAXW8ALQGZm7O3TpoISJdq1aweArabc4iQiIhINas8HaWlpxMfH3irjKihRom3btgDYasosTiIiItHA5g0XlNoPsLFGBSVKZGdnA8ca87F57yIiErtqP7CqoIil2rZti81mwwj5NZNHRESwVZcCkJuba20Qi6igRAmn06nbPCIiElF7LsjJybE4iTVUUKJI7S9hbWsWEZEYZZqRQbIqKGK5yBUUTTUWEYlphu8ohhnE4XDQunVrq+NYQgUlinxTUDTVWEQkltV+UM3MzIzJnYxBBSWq1BYUo0ZXUEREYlntB9XaRTxjkQpKFElPTwfA5tdy9yIisax225OMjAyLk1hHBSWKRPbjCfohGLA4jYiIWMU4trN9LG4SWEsFJYokJCTgdrsB7WosIhLLbDG8i3EtFZQoYhgGKSkp4f8O1FgbRkRELGMc2yQwOTnZ4iTWUUGJMpErKKGgxUlERMQyofBt/ri4OIuDWEcFJcpEfhlDGoMiIhKzjn1Irf3QGotUUKKMy+UCdAVFRCSW1Z4DnE6nxUmso4ISZfx+PwCmTf/XiIjELCN8DggEYvdqus6CUcbn84X/wxabKweKiAiYNjvwrXNCDFJBiTJeb3jktmnYLU4iIiKWUUFRQYk2ZWXh7bVxxO7AKBGRWGfaw+eAyDkhBqmgRJGqqiqOHg0vbxxyJVicRkRErFJ7Djh48KDFSayjghJFSkpKADDtTrDH7shtEZFYZ7riARUUiRKFhYWArp6IiMQ689h5oPa8EItUUKLIrl27AAjFpVgbRERELBX0tARg9+7d1gaxkApKFIkUlPhUi5OIiIiVQp4UIHzrv7y83NowFlFBiSI7duwAvvnFFBGRGGV3EXK1AL45N8QaFZQoUVVVxc6dOwEIxreyOI2IiFgtmBA+F2zatMniJNZQQYkSW7ZsIRQKEXIlYLpbWB1HREQsFmzRGoCNGzdanMQaKihR4uuvvwa++YUUEZHYVns+2LBhA6ZpWpym8amgRIn169cDEGyRYXESEWuNGTOGV199lTFjxmAYBoavyupIIpYIxadh2uyUl5fH5GweFZQo4PP5vrmCktTG4jQi1rrqqqvIycnhqquuwjRNbL5KqyOJWMNmj3xo/fLLLy0O0/hUUKJAXl4eXq+XkCNOa6BIzHvrrbfIz8/nrbfewjCMyEwGkVgUTAx/aP3iiy8sTtL4HFYHkG+acTCxDRiGxWlErLVgwQL+9a9/YRgGpmlGlvwWiUWBpDa498O6desIBoPY7bGz072uoESBSEHR7R2RyGDAWBwUKPKfQgmtMG1OKisrY249FBUUi9XU1ESmkAVUUERE5NsMG8HETCD2xqGooFhsw4YNBAIBQs4ETHeS1XFERCTK1H54VUGRRrVhwwaAcEPW+BMREfkPtVdQNm7cSCgUsjhN41FBsVjt7Z1gi3SLk4iISDQKeVIxbXYqKirYu3ev1XEajQqKhUKhEHl5eYBWkBURkZOw2SJ7tMXSvjwqKBYqKiqisrIS07AT8qRaHUdERKJU6NhV9q1bt1qcpPGooFio9lJdKC4JbPq/QkRETqx2Ec99+/ZZG6QR6axoodpftFCcZu+IiMjJ1Z4nVFCkURQUFAAQ0vRiERH5HrUFpaioCL/fb3GaxqGCYqHKyvAmaKYjzuIkIiISzUx7+DxhmibV1dUWp2kcdS4oK1asYOzYsWRlZWEYBnPnzj3u+crKSm6//XbatWuHx+Oha9euPP/888e9pqamhttuu420tDRatGjB5MmTKS4uPqMfpCnyer3h/7DFzt4KIiJyGmw2TMJrZUXOHc1cnQvK0aNH6dmzJ88+++wJn58+fToLFy7kH//4B3l5efzyl7/k9ttvZ/78+ZHX3H333bz77rvMnj2bjz76iIKCAiZNmnT6P0UT9U1B0Z6NIiLyA46dK2KloNT5zDhy5EhGjhx50udXrlzJ9ddfz8CBAwGYNm0aL7zwAmvWrGHcuHGUlZXx4osv8sYbb3DFFVcA8Pe//50uXbqwevVq+vXrd3o/SRMU2ZXSDFobREREoptpghleRTZWdjSu9zEol1xyCfPnz2f//v2YpskHH3zA1q1bGTZsGABr167F7/czZMiQyNd07tyZnJwcVq1adcL39Hq9lJeXH/doDtLTw/PaDe9Ri5OIiEg0MwI1GGYQwzBo1aqV1XEaRb0XlD//+c907dqVdu3a4XK5GDFiBM8++yyXX345EB6B7HK5SElJOe7rMjIyKCoqOuF7zpo1i+Tk5MgjOzu7vmNbonXr8OqxNp8KioiInJxx7DzRsmVLnE6nxWkaR4MUlNWrVzN//nzWrl3LU089xW233cbSpUtP+z1nzJhBWVlZ5NFc9iLIyMgAwFZTam0QERGJaraaMuCb80YsqNfRmdXV1dx3333MmTOH0aNHA9CjRw/WrVvHk08+yZAhQ8jMzMTn81FaWnrcVZTi4mIyMzNP+L5utxu3212fUaNCz549AbBXHcLwV2M6PRYnEhGRaOQoDS/Qdv7551sbpBHV6xUUv9+P3+/H9h/Lttvt9sgW0RdccAFOp5Nly5ZFnt+yZQv5+flcfPHF9Rkn6qWlpXHuuecCYC9tHleFRESknpkhHGXhghJL58k6X0GprKxk+/btkT/v2rWLdevWkZqaSk5ODgMGDOCee+7B4/GQm5vLRx99xKuvvsrTTz8NQHJyMjfeeCPTp08nNTWVpKQk7rjjDi6++OKYmsFT6+KLL2bLli04j+whkN7J6jgiIhJl7BVFGEEvSUlJdO3a1eo4jabOBeXzzz9n0KBBkT9Pnz4dgOuvv56XX36ZN998kxkzZjB16lQOHz5Mbm4uv//977nlllsiX/Pf//3f2Gw2Jk+ejNfrZfjw4Tz33HP18OM0PYMGDeKVV17BUbYXW9UhQvFpVkcSEZEo4ir4CoABAwbgcMTOulmGaZqm1SHqqry8nOTkZMrKykhKavr72Dz88MMsX74cf0oONecM+eEvEGlmDG8FLdbPPuFzlT2uxHQnNnIikehgLy8kfsv7OBwO/vGPf5x0rGZTUZfzt/biiQI//elPsdlsOEvzsVUetDqOiIhEA9PEtf8LAMaMGdPky0ldqaBEgZycHIYOHQpA3K4VEIyNnSpFROTknMWbcFQW43K5mDp1qtVxGp0KSpT4xS9+QatWrbDXlBGXv9rqOCIiYiFb5UHc+z4D4NZbb42sPB5LVFCiREpKCvfff3/4Vk/JNhwl26yOJCIiVgj48Oz8AMMMcfnllzN+/HirE1lCBSWKnH/++fz0pz8FIG73SuzlBdYGEhGRxhUM4Nm+DJu3kszMTO655x4Mw7A6lSVUUKLM1KlT6d+/P4YZxLNtCfbyQqsjiYhIYwgF8GxfgqOikPj4eGbOnEliYuzOYFNBiTJ2u50HH3yQfv36YYRUUkREYkIogGfbUhzlhXg8Hh5//PHISuOxSgUlCrlcLmbOnMlFF12EEQqES4qWwhcRaZ4CXjzbluAoLyAuLo4//OEPdO/e3epUllNBiVJut5tHHnmEPn36YIQCxG9bEl5NsOmtqyciIidhqz5Cwqb5OMoLI+WkR48eVseKCiooUcztdvPoo48ybty48J/3ryVuxwdaJ0VEpBlwHNlD/KZ/YfNWkJmZyTPPPBPZ5V5UUKKe0+lk+vTp/OpXv8LhcOA8spv4vH9h1JRbHU1ERE6HGcK1/ws825dhhPz07t2b559/no4dO1qdLKqooDQRY8eO5emnn6Zly5bYq4+QsHEuzuJNuuUjItKE2KpLic/7F+6CdQBMnjyZxx9/nJSUFEtzRSMVlCakR48evPDCC/Tq1QsjFCAufzWeLQsxvJVWRxMRke9jhnAWbSB+4zzsR0tISEjgt7/9LXfccUdM7VBcFyooTUzr1q156qmnuPPOO4mLi8NRUUjChjk4D27R1RQRkShk1JTj2fw+cXvXYJhB+vTpw9///vfIHmxyYqptTZDNZmPSpElcdNFFPPbYY2zYsIG43f/GcWgH3px+hOJTrY4oIiKhAK6iDbgKvwqva+XxcNtttzF69OiYXR22LgzTbHofu8vLy0lOTqasrIykpCSr41gqGAzy9ttv8+KLL+Lz+TAx8LfujLdtb3C4rY4ncmrMEIbvKAQDtNg4B4DKbhPB7sB0JYChi73ShJgmjtI9uPPXYPOFb8H37t2be+65hzZt2lgczlp1OX/rCkoTZ7fbmTJlCgMGDOD555/nww8/xHUgD8fhnfjaXoA/vZP+cZfoZ9gw3YnHTaE33S3A7rQwlEjd2aqP4M7/FMexvdTS09O59dZbGThwoK6a1JEKSjORmZnJQw89xJdffsmf/vQndu3aRdyelTgPbsbbrg/BpCzQXw4RkQZh+KtxFXyF80AeBiZOp5Orr76aa665Bo/HY3W8JkkFpZnp1asXf/vb35g/fz4vvfQSlZWHid+6iEBiJt62FxBKzLA6oohI8xHwhseZFG/ECAUAuPTSS7n11lvJysqyOFzTpoLSDDkcDiZNmsQVV1zB66+/zty5c6GiCMfmBQSSs/G2600oPs3qmCIiTVfQj+vAJlyFX2MEfQB06dKFm266iQsuuMDicM2DCkozlpKSwm233caVV17JK6+8wvvvv4+jbC+Osr34U8/G27YXZlyy1TFFRJqOUBDnwS24Cr/C5q8GoH379tx00030799f40zqkWbxxJC9e/fy0ksv8cEHHwBgYhBIbY+vTU9NTZboEPST+MVrAFT0vk6DZCV6BP3hYlK0AZu/CoA2bdrws5/9jMGDB2O32y0O2DTU5fytghKDtm3bxt///ndWrlwZOeZPyQkXlRbpFiaTmKeCItEm4MN1YBPO4k3YAjVAeGbOtddey6hRo3A69TtaF5pmLN/rnHPO4dFHH2X79u28/vrrfPjhhzhL83GW5hNIysLXpifBxEzN+hGRmGX4a3AWb8R1IC8yxiQrK4trrrmG4cOHq5g0Al1BEfLz83njjTdYsmQJwWAQgGBCa3yZ3Qm0zNE6KtJ4dAVFLGbUlOMq3oSzZGtkVk779u259tprGThwoPbNOUO6xSOnpbCwkDfffJP33nsPvz+8YFbInYgvszv+tHPArr+Y0sBUUMQitsoDuIo24DiyB4PwabFTp05cd9119O/fH5tNH9TqgwqKnJFDhw4xd+5c5s2bR3l5OQAhhxt/6y74W3fBdGrRIWkgKijSmEwTR2k+zqINOCqLI4cvuugipkyZQu/evTUrp56poEi9qK6uZuHChcyePZuCgvCyzaZhx9+qA/6MboQ8LS1OKM2OCoo0hqAf56HtuIo3YqsJfwhzOBwMHTqUK6+8krPPPtvigM2XBslKvfB4PEycOJFx48bxySef8Oabb5KXl4fr4FZcB7eGB9RmdCWYnK0BtSIS9QxvZXhGzsGtkYGvLVq0YPz48UycOJFWrVpZnFC+TQVFfpDdbmfAgAFcfvnlfP3117z99tt88sknOMoLcJQXEHIn4cvogr9VJ33iFZHoYprYK4txFm86bnxJVlYWkydPZuTIkcTHx1scUk5EBUVOmWEY9OjRgx49elBYWMjcuXNZsGABlZXlxOV/invfF/jTz8HXuitmnG69iYiFQkEch3fiKt6EvepQ5HDv3r350Y9+RL9+/TTwNcppDIqckaqqKpYsWcLbb7/N3r17ATCBYHI2vowuBJPa6vaPnDqNQZEzZPiO4jywGefBLZGF1VwuF0OHDmXy5MkaX2IxjUGRRhMfH8/48eMZO3Ysn332Gf/85z9Zs2ZNZM+fUFwSvtZd8bfqCHaX1XFFpDk6yW2cVq1aMXHiREaPHk1KSoq1GaXOVFCkXthsNvr27Uvfvn3Jz89n3rx5vP/++1RVlROXvxr3vs/xtzp2+8ejDQpFpB6EAjgP7cRZvAl79eHI4Z49ezJx4kQuvfRSLazWhOkWjzSYqqoqFi1axJw5c8jPz48c/2b2TzutUivH0y0eOQWGtwLngc24Dm7FCHoBcLvdDBkyhIkTJ9KxY0eLE8rJ6BaPRIX4+HgmTpzIhAkTWLt2LXPmzGHlypXfmv3TAl96F/zpncDhtjquiEQz08ReXoDzQB6O0nxqR7ZlZmYyYcIERo0apQ+szYwKijQ4wzC48MILufDCCyksLGTevHksWLCAiooK4vZ9hnv/F/jTOuDP6EIoPs3quCISTYJ+nCXbcB7Iw15TFjl84YUXMnHiRPr164fdbrcwoDQU3eIRS9TU1LBs2TLeeecdduzYETkeaJGBv3UXAi3bg6YAxh7d4pFjbNWlOA/k4SzZjhEK7w0WHx/PiBEjGD9+PLm5uRYnlNOhWzwS9eLi4hg9ejSjRo3i66+/Zs6cOaxYsQIqi3FUFhNyxuNv3Rl/+rna+0ckVpgm9rK9uIrzcJTvjxzOyclh4sSJDBs2jISEBAsDSmNSQRFLfXvxt5KSEt59913mz5/PkSNHcO//AlfBOgKpZ+PL6EooQctQizRLAR/Okm24DmzC5q0Awv82XHLJJUycOJELLrhAm/bFIN3ikajj8/n48MMPeeedd9i8eXPkeLBFa3ytu+r2T3OmWzwxJXwbZ9Ox2zgBILw3zqhRo5gwYQJZWVkWJ5T6pls80qS5XC6GDRvGsGHD2LRpE++88w4ffvghVB7AU3ng2O2fLvhanwuOOKvjikhdmCb28v24ijYedxunffv2TJo0iaFDh+Lx6LauqKBIlOvatStdu3blF7/4BfPnz//W7Z+1uArX4U/riD+jGyFPitVRReT7BAM4D20PL6pWUwp8cxtn8uTJ9OrVS7dx5Di6xSNNSu3tn9mzZ7Nt27bI8UByO3wZ3QgmZWnvn6ZMt3iaHcNXFb6Nc3ALtkB4UbX4+HhGjRrFpEmTdBsnxugWjzRbtbd/hg4dyldffcXs2bPDi7+V7cNRto+gJwV/Rnf8aR3AprURRKxiqzqEq2gDjsO7MMwQEF5UbfLkyYwaNUqzceQHqaBIk2QYBueffz7nn38++/fv55133uG9996juroU++5PcO1fiz+jK770zlqlVqSxHFvt1VW04bjxJeeddx5XXnkl/fv316Jqcsp0i0eajcrKShYsWMDbb7/NwYMHATBtDvzp5+LL6IbpbmFxQvlBusXTNJkhHId34SragL3qEBDeQHTgwIFcddVVdO7c2eKAEi10i0diUosWLZgyZQqTJk1i+fLlvPnmm+zatQtX8UacBzaF11PJPI9QfKrVUUWah2PL0LuKNmDzVQLhRRhHjRrFlVdeSZs2bSwOKE2ZCoo0O06nk+HDhzNs2DDWrFnDm2++yZdffonz0A6ch3bgT8nB16YnoRbpVkcVaZoCPlwH8nAWb8QWqAEgJSWFSZMmMX78eJKTky0OKM2BCoo0W4Zh0LdvX/r27cvmzZt58803+eijj3CW5uMszSeQ1BZfVk+CiZlWRxVpGgI1uIo34SrehBH0AZCVlcWUKVMYMWIEbrfGe0n9UUGRmNC5c2ceeugh8vPzef3111myZAmO8v04yvcTSMzE16anpiiLnIThr8JVtBHngbzIiq+5ublce+21DBo0CIdDpxKpfxokKzGpsLCQN954g4ULF+L3h3dKDSak423bW0XFShokG1UMfzWuwvU4D2zGMIMAdOzYkeuuu47LLrsMm7ackDqqy/lbBUVi2oEDB/i///s/3n33XXy+8CXrQGImvra9devHCioo0SHgxVX4Na4DmyJXTLp06cJPfvIT+vXrpxVf5bSpoIjU0eHDh3njjTeYN29e5IpKIKkt3nYXaBflxqSCYq2gH1fxRlxFX2MEw38PunTpwg033MCFF16oYiJnTNOMReooNTWV22+/nauuuorXXnuN9957D8r349i0H39KLr52vQl5WlodU6RhhAI4D+ThKvw6MiunQ4cO3HDDDVxyySUqJmIJXUEROYH9+/fz6quvsmTJEkKhECYG/vRO+Nr2xnRqp9UGoysojcs0cRzeiXvf2sg6JtnZ2fzsZz9j4MCBGmMi9U63eETqyZ49e3jxxRdZsWIFAKbNia9ND3yZ3cCmC5D1TgWl0dgrinHv/RT70RIA0tPTueGGGxg6dKhm5UiD0S0ekXqSm5vLww8/zPr163nuuefYvHkz7v1rcR7cjLftBQTSOmjGjzQpRk057n2f4zyyGwCPx8M111zDlVdeSVxcnLXhRL5FBUXkFPTo0YPnnnuO5cuX87e//Y3i4mI8u1YQPLCJmtxLNJBWol/Qj6vgK1zFGzDMEDabjdGjR/PTn/6UtLQ0q9OJfIcKisgpstlsDBkyhMsuu4y3336b119/naqjJcRvmo+/dRe8bXtr52SJPqaJozQfd/5qbL6jAPTp04df/OIXnH322RaHEzk5FRSROnK73UydOpURI0bwl7/8haVLl+I6kIfj8C682X0IpHXUbR+JCkZNOXH5n+Io2wtARkYGd9xxB/3799fMHIl6KigipyktLY3777+fUaNG8cc//pH8/Hw8uz4mULINb+4lhDwpVkeUWBUK4Spaj6vgKwwziMPh4Oqrr+baa6/VOBNpMjSHTOQM9e7dmxdffJFp06YRFxeHo6KI+I3zcBZ+DWbI6ngSY2xVh4jPm497/xcYZjDy+3nTTTepnEiToisoIvXA6XRyzTXXMHjwYJ566inWrFlD3L7PcB7ZRc1Zl2mRN2l4oSCuwq9wFX6FYZokJSVx5513MnjwYN3OkSZJV1BE6lFGRgZ/+MMfuPfee0lISMB+tIT4jfNwFX6lqynSYGzHBmu7C9ZhmCaXX345L7/8MkOGDFE5kSarzgVlxYoVjB07lqysLAzDYO7cucc9bxjGCR9PPPFE5DXt27f/zvOPPfbYGf8wItHAMAxGjhzJ3//+9/DGamYI9761xG9+D8NbaXU8aU5ME1fheuLz3sVefYTk5GQefPBBZs6cSWpqqtXpRM5InQvK0aNH6dmzJ88+++wJny8sLDzu8dJLL2EYBpMnTz7udQ8//PBxr7vjjjtO7ycQiVKtW7dm1qxZ/OY3vyE+IQF75QESNs7FcXiX1dGkGTD8VXi2LsK97/PjrpoMGjRIV02kWajzGJSRI0cycuTIkz6fmXn8FvXz5s1j0KBB35lvn5iY+J3XijQ3hmEwYsQIevTowSOPPEJeXh6eHR/gKy/Am90X7BoGJnVnL91L3K6PsQVqcLvd3HHHHYwePVrFRJqVBh2DUlxczIIFC7jxxhu/89xjjz1GWloavXr14oknniAQCJz0fbxeL+Xl5cc9RJqSrKws/vznPzN16lQMw8B1cAvxm+Zjqy61Opo0JWYI197PiN+2BFughg4dOvDXv/6VMWPGqJxIs9OgH99eeeUVEhMTmTRp0nHH77zzTnr37k1qaiorV65kxowZFBYW8vTTT5/wfWbNmsXMmTMbMqpIg3M4HNx888307t2bRx99lEOHDhG/6V1qzr6cQMtcq+NJlDP8NcTt/ABHeSEAEydO5JZbbsHt1urF0jyd0W7GhmEwZ84cJkyYcMLnO3fuzNChQ/nzn//8ve/z0ksv8fOf/5zKysoT/mXzer14vd7In8vLy8nOztZuxtJkHTlyhIceeoivvvoKAG/W+fiyemkFWu1mfEK2o4fwbF+GzVdJXFwcv/nNbxg4cKDVsUTqrC67GTfYLZ6PP/6YLVu2cNNNN/3ga/v27UsgEGD37t0nfN7tdpOUlHTcQ6Qpa9myJU899VRk8Li7YB2ebUsh4P2Br5RY4yjZTnzev7D5Kmnbti3PPfecyonEhAYrKC+++CIXXHABPXv2/MHXrlu3DpvNRuvWrRsqjkjUcTgc3HHHHdx33324XC4cZXuJz1ugqcgSZpq49n+BZ9cKDDNIv379eP7557XBn8SMOo9BqaysZPv27ZE/79q1i3Xr1pGamkpOTg4QvoQze/Zsnnrqqe98/apVq/j0008ZNGgQiYmJrFq1irvvvptrr72Wli212qbEnmHDhtG+fXvuu+8+SkpKiM97l+pzhhJKaGV1NLFKKEjc7k9wHtoBwNSpU7nxxhux2bS2psSOOv+2f/755/Tq1YtevXoBMH36dHr16sUDDzwQec2bb76JaZr8+Mc//s7Xu91u3nzzTQYMGEC3bt34/e9/z913381f//rXM/gxRJq2Tp068dxzz9GhQwds/mriN7+H/Ui+1bHECgEvnq2LcB7agc1m45577uHmm29WOZGYc0aDZK1Sl0E2Ik3J0aNHeeihh/jss88AqMm9BH/rzhanakQxPkjW8B3Fs2Uh9poy4uPjmTlzJn369LE6lki9iYpBsiJSdwkJCcyaNYvRo0cDELdnJa7C9RanksZg1JQRn7cAe00Z6enpPPPMMyonEtNUUESijMPh4P/9v//HNddcA4B73+e49n0OTe9ip5wiW9Vh4vPew+arpF27djzzzDMaDCsxTwVFJAoZhsG0adO4+eabAXAXrsedv0olpRmyVR4kfvN72ALVdOjQgT/96U9kZGRYHUvEciooIlFs6tSp3H333eHl8Q9sxr1HJaU5sVUeIH7rQoygj27duvHHP/5RuxCLHKOCIhLlxo8fz29+85tje/iopDQXtsqDxG9dhBH007NnT5544gkSExOtjiUSNbSVqkgTMHz4cCC8yabr4GYAvLkXa2n8Juo/y8ljjz2Gx+OxOpZIVNEVFJEmYvjw4dx7773fXEnZ+6mupDRBtqpDx8qJjx49ejBr1iyVE5ETUEERaUJGjBjBr3/9awBcxZtwFXxpcSKpC6O6DM+WcDnp3r07jz32GPHx8VbHEolKKigiTczIkSO58847gfAmg87Cry1OJKfC8FYQv2UhtkANnTp1UjkR+QEqKCJN0KRJkyJTkOP2fYbz4BaLE8n3MfxV4XLiP0pubi6PP/44LVq0sDqWSFRTQRFpoqZOnfrNYm67V+I4stvaQHJiQR+erYuxeSto06YNTz75JCkpKVanEol6KigiTdjNN9/M6NGjMTCJ2/ER9ooiqyPJt4WCeLYtw151mJYtW/LEE0+Qnp5udSqRJkEFRaQJMwyDu+++m/79+2OYQTzblmKrOmx1LAEwQ8Tt/AhHRSEej4fHHnuMdu3aWZ1KpMlQQRFp4hwOBw888ADnnXcexrHbCYa30upYMc+9dw3OI7txOBw88sgjnHvuuVZHEmlSVFBEmgG3282jjz5K+/btsfmr8GxbAgGv1bFilrNoA67iTQDMmDGDCy+80OJEIk2PCopIM5GYmMhjjz1GWloa9uojeLYvh1DQ6lgxx3F4F3F71wBwyy23MHjwYIsTiTRNKigizUhmZmZk2XRHRSFxuz/RarONyFZRTNzOFQBMmDCBKVOmWJxIpOlSQRFpZs455xwefvhhbDYbzkM7tNpsIzFqyvFsX4phBunfvz933HEHhvZKEjltKigizVCfPn2YPn06EF5t1lGyzeJEzVyghviti7EFvHTu3Jn7778fu91udSqRJk0FRaSZGjNmTGQht7jdn2AvL7Q4UTN1bK0Tm7ecjIwMHn30UW3+J1IPVFBEmrGbbrqJQYMGYZgmnu3LsFWXWh2peTFN4nZ9jKOymISEBP7whz+QmppqdSqRZkEFRaQZs9ls/OY3v6F79+7hNVK2LcHwV1sdq9lwFXyJ8/BO7HY7Dz/8MO3bt7c6kkizoYIi0sy53W7+67/+izZt2mDzVhC3fRmEAlbHOjGbg4re11HR+zqwOaxO870cJdtxF6wDYPr06VxwwQXWBhJpZlRQRGJASkoKjz32GAkJCTgqDxC3K0qnHxsG2J3hRxTPgLFXFIWncAM//vGPGT16tMWJRJofFRSRGJGbm8sjjzyC3W7HeXgnrmOf/qVuwtOJl2GYIS6//HJuvvlmqyOJNEsqKCIxpHfv3tx9990AuAu+xHFoh8WJmpiANzyO59h04vvuuw+bTf+MijQE/c0SiTFjxozh6quvBiBu18fYK4otTtREhEJ4ti/HXlNG69at+f3vf09cXJzVqUSaLRUUkRg0bdo0Lr30UgwzRNz2ZRg15VZHim6miXvPyvD2AXFxPProo6SlpVmdSqRZU0ERiUE2m43f/va3dOrUCVugBs+2pRDwWR0rajmLN+Aq2YrNZuOBBx6gY8eOVkcSafZUUERilMfj4fe//z2tWrXCXlOKZ8cHYIasjhV17Efyidv7GQC/+MUvuOSSSyxOJBIbVFBEYlh6ejqPPvoo7rg4HOX7ced/anWkqGKrOoRn54cAjB07lh/96EfWBhKJISooIjGuU6dO3P/b3wLgOpCH80CexYmig+GvwrNtKUYoQO/evbnrrru0O7FII1JBEREuu+yyyHoe7j2rsZfttziRxUKB8AaAvqNkZ2czc+ZMHI7oXtlWpLlRQRERAK655hqGDx+OgYlnxwcY1WVWR7KGaRK36xPsRw+SmJjIrFmzSExMtDqVSMxRQRERAAzD4Fe/+lVkY8H4bUsg4LU6VqNzFa6PbAA4c+ZM2rVrZ3UkkZikgiIiES6Xi0ceeYSMjAxs3vLwzJ5Q7MzscRzZjXv/WgDuuusuevfubXEikdilgiIix2nZsiWPPvoocXFxOMoLcO+NjZk9tqrDxO1cAcDEiRMZN26cxYlEYpsKioh8R4cOHbj//vsxDCM8s+fgFqsjNSjDXxOZsXPBBRdw2223WR1JJOapoIjICV166aXccMMNALj3rMJeUWRxogYSChG3Yzk2XyVt27bloYce0owdkSiggiIiJ3XttdcycODAY3v2LMfwVlodqd65967GUVFEfHw8v//97zVjRyRKqKCIyEkZhsG9995Lhw4dwnv2bF8GoYDVseqN8+BWXAc2YxgG999/P+3bt7c6kogco4IiIt+rds+epKQk7FWHiNuzCkzT6lhnzFZ5EPeelQD87Gc/0x47IlFGBUVEflBmZiYPPPAANpsNZ8k2nAc3Wx3pjBj+ajzbl2OYIfr378+1115rdSQR+Q8qKCJySi688EKmTZsGgDt/NbaKYosTnSYzRNyOD7D5w8vY33fffdhs+qdQJNrob6WInLIpU6YwaNAgDPPYcvj+aqsj1Zlr31ocFUV4PB7+67/+i4SEBKsjicgJqKCIyCkzDIN77rmHnJwcbP4q4nZ+BGbTWWnWfiQfd9HXANx7773k5uZanEhETkYFRUTqJD4+npkzZ+I+ttKsq2Cd1ZFOieGtwLMrvFLs5MmTGThwoLWBROR7qaCISJ2dddZZ/L9f/QoAd8E67OUFFif6AaFg+JZU0EfXrl255ZZbrE4kIj9ABUVETsvQoUMZM2YMAHE7P4rq8Sju/V9gP1pCYmIiDz74IE6n0+pIIvIDVFBE5LTdfvvttG/fHpu/mrhdH0fl+ij2sn24jo07+fWvf01GRobFiUTkVKigiMhpi4uL44EHHsDlcuEo24ezeJPVkY5j+KsjOxRPmDCByy67zOJEInKqVFBE5IycffbZ3HrrrQC4932OrfqIxYmOMU3cu/+NLVDDWWedxS9+8QurE4lIHaigiMgZGz9+PBdddBGGGQxfsQgFrY6Eo2QbztJ8HA4Hv/3tb3G73VZHEpE6UEERkTNWu6lg7X49Vk89NrwVxOV/CsANN9xAx44dLc0jInWngiIi9SItLY3p06cD4Cpcj63qkDVBTJO43f/GCPnp3r07U6ZMsSaHiJwRFRQRqTcDBw5kwIABGJjE7frEklVmHYe2hxeQc7n4zW9+g91ub/QMInLmVFBEpF7deeedJCYmhm/1FG1o1O9t+Ksit3Z+9rOf0a5du0b9/iJSf1RQRKRepaWlRWb1uPZ/ieGtaLTv7c5fgxH00alTJ6688spG+74iUv9UUESk3o0YMYJevXphmEHcx65oNDR7eSHOwzsxDINf/epXOByORvm+ItIwVFBEpN4ZhsFdd92F3W7HWZqPvXRvw37DUAh3/ioAxo0bx7nnntuw309EGpwKiog0iPbt2zN58mSA8LiQBlwbxXkgD3t1KUlJSdx4440N9n1EpPGooIhIg/npT39KamoqNm85zoObG+abBLy4C74EYNq0aSQlJTXM9xGRRqWCIiINJj4+nhtuuAEgvHhbwFfv38NVuB4j6KN9+/aMHDmy3t9fRKyhgiIiDWrEiBHk5ORgC3hxFa2v1/c2vJW4jm1Q+POf/1xrnog0IyooItKgHA4HP//5zwFwFW/C8FfX23u7Cr/CMIP07NmTfv361dv7ioj16lxQVqxYwdixY8nKysIwDObOnXvc84ZhnPDxxBNPRF5z+PBhpk6dSlJSEikpKdx4441UVlae8Q8jItHpkksuoXPnzhihAM56WrzN8FbiLNkGwE033YRhGPXyviISHepcUI4ePUrPnj159tlnT/h8YWHhcY+XXnoJwzAio/kBpk6dysaNG1myZAn/+te/WLFiBdOmTTv9n0JEopphGPzkJz8BwHUgD8Nfc8bv6Spaj2GG6N27N+edd94Zv5+IRJc6r2Q0cuTI7x2IlpmZedyf582bx6BBgzj77LMByMvLY+HChXz22WdceOGFAPz5z39m1KhRPPnkk2RlZdU1kog0ARdffDHnnHMO27Ztw1m8EV+7C077vQxfFc6DWwEixUdEmpcGHYNSXFzMggULjluXYNWqVaSkpETKCcCQIUOw2Wx8+umJV5z0er2Ul5cf9xCRpsUwDKZOnQoQnnIcCpz2ezkP5GGYIbp37875559fTwlFJJo0aEF55ZVXSExMZNKkSZFjRUVFtG7d+rjXORwOUlNTKSoqOuH7zJo1i+Tk5MgjOzu7IWOLSAO59NJLycjIwBbw4jy04/TeJBTAeXALAD/60Y/qMZ2IRJMGLSgvvfQSU6dOJS4u7ozeZ8aMGZSVlUUee/c28LLZItIgHA5HZDyas3gjmGad38N5aCe2QA0ZGRlceuml9R1RRKJEgxWUjz/+mC1btnDTTTcddzwzM5MDBw4cdywQCHD48OHvjF+p5Xa7SUpKOu4hIk3TqFGjiIuLw15dir2yuM5f7zyQB8DEiRO1IaBIM9ZgBeXFF1/kggsuoGfPnscdv/jiiyktLWXt2rWRY8uXLycUCtG3b9+GiiMiUaJFixZcccUVAJFpwqfKVnUIe9UhHA4HI0aMaIh4IhIl6lxQKisrWbduHevWrQNg165drFu3jvz8/MhrysvLmT179neungB06dKFESNGcPPNN7NmzRr+/e9/c/vtt3P11VdrBo9IjBg1ahQAjsO7IHjqy987D4YLTf/+/UlJSWmIaCISJepcUD7//HN69epFr169AJg+fTq9evXigQceiLzmzTffxDRNfvzjH5/wPV5//XU6d+7M4MGDGTVqFJdeeil//etfT/NHEJGmplu3buTk5GCEAjgO7z61LwqFcBwOD6ytLTgi0nwZpnkao9QsVl5eTnJyMmVlZRqPItJEvfbaa7z44osEktpSfe7wH3y9vWwf8VsX07JlS2bPnq3xJyJNUF3O39qLR0QsMXDgQADs5QUQ+OGVZZ2HdwFw+eWXq5yIxAAVFBGxRHZ2Nh07dsTAxHlkz/e/OBTEcew1tcVGRJo3FRQRscyAAQMAsJd+/9pG9soDGEEfKSkp9OjRozGiiYjFVFBExDL9+vUDwFFeAKHgSV/nKAsXmL59+2K32xslm4hYSwVFRCzTsWNH0tLSMEIB7BUn3uoCwF66D0BrJYnEEBUUEbGMYRiR0uEo23/i1/iOYq8pxWazHbfJqIg0byooImKp2t2IT3YFpfZ4x44dtayASAzRXD0RsVRtQbFVHcJeUYxpO36MiaM0vEr1f26bISLNmwqKiFiqdevWtGnThsLCQuI3Lzjp61RQRGKLCoqIWG7q1Km8/vrrhEKhEz7frl07jT8RiTFa6l5EREQahZa6FxERkSZNBUVERESijgqKiIiIRB0VFBEREYk6KigiIiISdVRQREREJOqooIiIiEjUUUERERGRqKOCIiIiIlFHBUVERESijgqKiIiIRB0VFBEREYk6KigiIiISdRxWBzgdtRswl5eXW5xERERETlXtebv2PP59mmRBqaioACA7O9viJCIiIlJXFRUVJCcnf+9rDPNUakyUCYVCFBQUkJiYiGEYVscRkXpUXl5OdnY2e/fuJSkpyeo4IlKPTNOkoqKCrKwsbLbvH2XSJAuKiDRf5eXlJCcnU1ZWpoIiEsM0SFZERESijgqKiIiIRB0VFBGJKm63mwcffBC32211FBGxkMagiIiISNTRFRQRERGJOiooIiIiEnVUUERERCTqqKCIiIhI1FFBERERkaijgiIiIiJRRwVFREREoo4KioiIiESd/w9WbRTVkuYSywAAAABJRU5ErkJggg==", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import seaborn as sns\n", "\n", "# 预测结果数据\n", "predictions = [186.8574072889791, 186.257860664635, 184.10770494393964, 185.93719141281886, 184.57037449339362, \n", "               185.60661648948147, 183.3857326924576, 183.75000533180457, 183.10016909961496, 181.8261013581762, \n", "               179.94003987015927, 179.66272055047042, 178.45223991648865, 179.95636123056215, 180.48631322458314, \n", "               178.77667060084391, 177.62040380763523, 178.3089116835725, 175.0859834552677, 173.1733887952756]\n", "\n", "# 绘制小提琴图\n", "sns.violinplot(y=predictions)\n"]}, {"cell_type": "code", "execution_count": 6, "id": "71ec0ccd", "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAiYAAAHECAYAAAANquFSAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjcuMSwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy/bCgiHAAAACXBIWXMAAA9hAAAPYQGoP6dpAABMNklEQVR4nO3dd3RU1f428Gf6pLdJgyQkEDoECFW8oGJFgoii0lQUrD+5ekFUbIj6CpZrQxClg6CComDjgtJEkV6TUBITkkDapE2SKZmy3z8CIyGUAEnOyeT5rDVLz5kzM9/ZJDNP9tl7H4UQQoCIiIhIBpRSF0BERER0BoMJERERyQaDCREREckGgwkRERHJBoMJERERyQaDCREREckGgwkRERHJBoMJERERyQaDCVEzVlRUhMzMzBr7duzYgZKSEmkKIqJmj8GEqBmbP38+Ro8eXWPfmDFjcPPNN1/W8zz33HN4+eWXL/v1f//9dxQWFtbYZzKZcPTo0ct6npMnT7r/3+Vy4csvv0Rqaupl1/PRRx9h+PDhMBqNl/1YIqofDCZEzZiXlxe0Wq17++jRo0hPT8fTTz99yceWlpYiMzMTmZmZmDdvHgoKCtzbZ245OTkXfY4JEybg0UcfrbHvm2++QadOnXDo0KE6v49hw4bhgQceAAAolUr897//xZtvvlnnx5/x5Zdfwmg0wmAwXPZjiah+qKUugIga1//93/9h8ODBSEpKglJZ82+TRYsWITw8HPfdd597nxACVVVVcDgc8PHxce//+OOPMW3aNPf2vHnzMG/evBrPFx0djaysrPPWcfjwYRw7dgwLFy5073O5XHj//fehVCqxatUqdO3a9ZLv53//+x/27NmDmTNnuve9/vrrGDp0KB577DEMHDiw1mPy8/NRXFwMnU7nboNjx45hx44dmDNnTq3TW0IIOJ1OWCwWtG/fvkaYI6J6JoioWYmOjhZz584VQggxa9Yscd111wkhhLDZbCIyMlIAOO+tVatWNZ7nnXfeES1bthRCCFFRUSHGjRsnVq1aJRwOhxBCiFWrVom2bdtesI7HHntMJCYm1ti3ZMkS4efnJ7Zs2SICAwPF+vXrL/peHA6H6NWrl7jxxhtr3TdkyBARFxcniouLa933xhtvXPB9XuqWkZFx0ZqI6OrwVA5RM6PVaqHT6WrtX7p0KcrKypCSkoLc3Fzk5ubiiSeewODBg3HixAls3rwZTqcTlZWVsNvtUCgU7sd6e3vjrrvuwrx583D48GH3fqVSCSEEbDYbHA6He39ubi6WLVuGIUOGuPedOHECkyZNwttvv42BAwfik08+wfDhw7Fhw4YLvpd33nkH+/fvx/vvv1/rvs8++wwVFRW44447YDKZatz33HPPwWq1wuFwQAiBTZs2QaFQYMuWLbBYLJg2bRrS09MhhKh1i42NrVM7E9EVkjAUEZEE2rRpIxYtWiSE+KfHpKqqSsTHx4suXbqId955x33smDFjxMSJE93bGzZsuOKehu+++879PA8//LAAIKZNmyaEECI7O1u0b99ejBo1qkatM2fOFCqVSrzyyivCbDbXuG/Xrl1Cp9OJ119//YLvdefOnSIoKEh07txZJCcnn/cYi8Ui2rdvL8aPHy+EEMJsNgsAYuvWrZdsSyKqf+wxISJ8+OGHKCoqwoQJEzBz5kwIIQBUj7uIi4tzH9e3b18cOnQIR44cwd13342kpCQcP34c33//Pf744w8cP37cfXvrrbfQvXt3HD16FIcPH8YNN9wAANi4cSMWL16M6OhoAMDWrVvRr18/xMXFYfHixTXqev755zF79mzMnDkTvXr1gt1uBwAcP34cQ4cORWJiIm6//XakpKTgyJEjtW5+fn5YuHAhKisrkZiYiLfffrvWex8/fjzKy8vx7LPPIicnB/n5+QCAkpIS5OTkuG/nzh4ioobBwa9EhDvvvBOdOnVCr169MGnSJOzfvx9dunRBcnIy+vXr5z7Oz88PXbp0QXl5ObZs2YK33noL8fHxmDVrFubPn48nnngCL7/8MgIDAxEcHAy9Xo927dq5H282mzFmzBjceeed7lNBqampuPHGG/HKK68gMTERa9euRevWrQEAUVFRmD17Nnbt2gWj0QiNRgMhBB555BGEhobixRdfRK9evS763jp06IBdu3Zh3LhxuOaaa2rc98ILL2DFihUAgI4dO9a4b9iwYTW2r7/+emzatOkyW5aILhd7TIgIbdu2xZAhQxAeHo5rrrkG33zzDbZu3QqVSoWePXvWOLaiogL33HMPdDqdew2Ujz76CD/88AMOHz4MnU6He++9F6+++ipiYmJqPNbb2xsLFizAJ5984t732GOPYcmSJVAqlUhOTkZAQID7vtLSUggh0K1bN9x4440AAIVCgbVr12LDhg0YPHgwLBYLnE4nIiMj3b09Z24PP/wwoqOjYTAY8OOPP7pn6DgcDjz11FN477338PDDDyMgIAB2ux12ux1WqxUAsG3bNve+55577rzjcoio/rHHhKgZKSgoQE5ODhYvXoylS5di//79SEhIqHHM2LFjMX36dBw5cgTDhw+vNTVWrVajd+/eePnll2tMHx40aBAGDRoEALjtttvQo0cPjB8/vlYNt99++3lrs9ls0Gg0CA4OrvFaanXtjyl/f3/4+/sDAFQqFYqKipCbm1trevHJkycRFRVV6/GjRo3C999/j2XLlsHPzw/ffvttrddRqVTufQqF4rx1EFH9Y48JUTNiNBrh5+eH2NhYPPnkk5g0aVKtY0aPHg2TyYTVq1fjqaeeqnW/Xq/Hl19+iQEDBkChUJz3Nn78eLz44osIDw9H796961RbeXk5QkNDa8z2qas1a9ZAo9HgX//6V439J0+edI9lOdu7776LtWvXYtSoUZf9WkTUsBhMiJqRTp064dSpU1i8eDFGjBiBwMDAWsdkZGRArVZDp9NBo9Gc93m8vLzw0ksvoaSkBJ999hmuvfZalJSUoKSkBNHR0fjll19QUlKCp556Cnq9vk61paeno0WLFpf9nioqKvDmm29ixIgR7l6UMy4UTGJjYzF48GAA1Yu6XUpdjiGi+sFgQtTMXChsAMCGDRswaNAg9O3bF6NGjcKNN96I77//vtZxKpUKer0egYGB8Pb2hlqtRmBgIAIDA6FUKuHr64vAwEDodDqoVKo61fXDDz+ge/ful/VeSkpKMHjwYJSWltaacVNYWOgOShdjs9kueN+ePXvwyiuvYOXKlQgNDb2s2ojoyvCkKVEz5nQ64XQ6kZOTg9dffx3z58/HhAkT8Mknn0CtVmPKlCkYPnw4+vbti4cffhgDBw50L1FfXFyMtLQ05Ofnw2KxIC0tDUD1wNKTJ08iLS0NpaWlsFgsSElJgU6nQ5s2bWq89hnbtm3DypUrsXnz5jrV7XK5sHr1arz44oswGo34+eef3QHk0KFD+Oqrr7B161ao1epag3fPdW4wOdM7IoRAWFgYZs6cie7du+OZZ56pU21EdHUYTIiaMavVCqvVijlz5mDNmjVYuXIlRowY4b7/v//9L4YNG+Ye6Dpy5Eikp6dDrVZj6dKlWLp0qfvYs6cV/9///R+A6vBht9vRvXt3XH/99Vi/fr37GJvNBqfTiR07duCuu+7CfffdV2uMiN1ur7Fi7Bnbtm3DmDFjMGjQIKxbt849vRgA4uPjsWDBAsTHx2P16tWXvCDf2LFjMXbsWPf2mcDkcDgQHR0Ns9l80V4mIqpfCnFmJSUiatYqKytrzLJpaLfccgu6deuG66+/Hh988AHWrFlT6/XVajW++OILjBw5stbj09LSEB8fX+91lZaWIigoCBs2bMBNN91U789PRBfHYEJERESywcGvREREJBsMJkRERCQbDCZEREQkGwwmREREJBtNbrqwy+XCqVOn4Ofnd0VLVxMREVHjE0KgvLwcLVq0gFJ54X6RJhdMTp06dcmVHImIiEiesrOzz3txzTOaXDDx8/MDUP3Gzr0uBhEREcmTyWRCdHS0+3v8QppcMDlz+ubsy54TERFR03CpYRgc/EpERESywWBCREREssFgQkRERLLBYEJERESywWBCREREssFgQkRERLLBYEJERESywWBCREREssFgQkRERLLBYEJERESyIXkwef755zF06FCpyyAiIiIZkPRaOQcPHsScOXNw4MABKcsgIiIimZCsx8TlcuHRRx/Ff/7zH7Ru3VqqMoiIiEhGJAsmc+fOxaFDhxAbG4u1a9eiqqpKqlKIiIhIJhRCCNHYL1pRUYG4uDhERETgrrvuwtatW1FZWYktW7bAy8urxrE2mw02m829bTKZEB0djbKyMvj7+zd26URXJCsrC0ajUeoymgSDwYCYmBipyyCiemYymRAQEHDJ729JxpisXr0alZWV2LRpEwwGAxwOB7p27Yply5bh0UcfrXHsjBkzMH36dCnKJKoXWVlZ6NCxIyxms9SlNAle3t44kprKcELUTEkSTHJyctCvXz8YDIbqItRqJCQkIC0trdaxU6dOxaRJk9zbZ3pMiJoKo9EIi9mMMc+/i/CYNlKXI2v5WelY/vYUGI1GBhOiZkqSYBIVFQWLxVJj34kTJ9C/f/9ax+p0Ouh0usYqjajBhMe0QVTbzlKXQUQka5IMfh0yZAhSUlIwd+5c5OTk4OOPP8aBAwdw1113SVEOERERyYQkwSQkJAQ///wzlixZgnbt2uGjjz7CypUreYqGiIiomZNsgbVrr70W27dvl+rliYiISIYkX5KeiIiI6AwGEyIiIpINBhMiIiKSDQYTIiIikg0GEyIiIpINBhMiIiKSDQYTIiIikg0GEyIiIpINBhMiIiKSDQYTIiIikg0GEyIiIpINBhMiIiKSDQYTIiIikg0GEyIiIpINBhMiIiKSDQYTIiIikg0GEyIiIpINBhMiIiKSDQYTIiIikg0GEyIiIpINBhMiIiKSDQYTIiIikg0GEyIiIpINBhMiIiKSDQYTIiIikg0GEyIiIpINBhMiIiKSDQYTIiIikg0GEyIiIpINBhMiIiKSDQYTIiIikg0GEyIiIpINBhMiIiKSDQYTIiIikg0GEyIiIpINBhMiIiKSDQYTIiIikg0GEyIiIpINBhMiIiKSDQYTIiIikg0GEyIiIpINBhMiIiKSDQYTIiIikg0GEyIiIpINBhMiIiKSDQYTIiIikg0GEyIiIpINBhMiIiKSDUmCyb///W8oFAr3LT4+XooyiIiISGbUUrzo7t278dNPP6F///4AAJVKJUUZREREJDONHkwcDgeSk5MxcOBA+Pr6NvbLExERkYw1ejA5dOgQXC4XunfvjpMnT+K6667D559/jpiYmPMeb7PZYLPZ3Nsmk6mxSiUikrWsrCwYjUapy2gSDAbDBb9nSF4aPZikpKSgffv2mDVrFgwGA/7zn//g0Ucfxbp16857/IwZMzB9+vRGrpKISN6ysrLQoWNHWMxmqUtpEry8vXEkNZXhpAlo9GAyZswYjBkzxr09Z84cxMXFwWQywd/fv9bxU6dOxaRJk9zbJpMJ0dHRjVIrEZFcGY1GWMxmjHn+XYTHtJG6HFnLz0rH8renwGg0Mpg0AZIMfj1bWFgYXC4XcnNzzxtMdDoddDqdBJUREclfeEwbRLXtLHUZRPWm0acLT5kyBStWrHBvb9++HUqlkr0gRERE1Pg9Jt26dcPLL7+M8PBwOJ1OTJw4EQ888AC8vb0buxQiIiKSmUYPJmPHjkVycjLuvvtuqFQqjB07Fm+99VZjl0FEREQyJMkYkxkzZmDGjBlSvDQRERHJGK+VQ0RERLLBYEJERESywWBCREREssFgQkRERLLBYEJERESywWBCREREssFgQkRERLLBYEJERESywWBCREREssFgQkRERLLBYEJERESywWBCREREssFgQkRERLLBYEJERESywWBCREREssFgQkRERLLBYEJERESywWBCREREssFgQkRERLLBYEJERESywWBCREREssFgQkRERLLBYEJERESywWBCREREssFgQkRERLLBYEJERESywWBCREREssFgQkRERLLBYEJERESywWBCREREssFgQkRERLLBYEJERESywWBCREREssFgQkRERLLBYEJERESywWBCREREssFgQkRERLLBYEJERESywWBCREREssFgQkRERLLBYEJERESywWBCREREssFgQkRERLLBYEJERESywWBCREREssFgQkRERLLBYEJERESywWBCREREsiF5MLntttuwePFiqcsgIiIiGZA0mCxfvhz/+9//pCyBiIiIZESyYFJcXIzJkyejffv2UpVAREREMqOW6oUnT56M4cOHw2KxXPQ4m80Gm83m3jaZTA1dGhEReaDU1FSpS2gSDAYDYmJiJHt9SYLJpk2b8NtvvyE5ORkTJ0686LEzZszA9OnTG6kyIiLyNKbiQgDA2LFjJa6kafDy9saR1FTJwkmjBxOr1YrHHnsMn376Kfz8/C55/NSpUzFp0iT3tslkQnR0dEOWSEREHsRSUd3TPuSxl9A+oafE1chbflY6lr89BUajsfkEkzfeeAO9e/fGkCFD6nS8TqeDTqdr4KqIiMjThbRohai2naUugy6h0YPJihUrUFhYiMDAQACA2WzGypUrsXPnTsyZM6exyyEiIiIZafRg8vvvv8PhcLi3n332WfTr1w/jxo1r7FKIiIhIZho9mERFRdXY9vX1hcFggMFgaOxSiIiISGYkmy58Bld9JSIiojMkX5KeiIiI6AwGEyIiIpINBhMiIiKSDQYTIiIikg0GEyIiIpINyWflEBG5XAI2hwvldkDbogP259lQdqQAVU4XHE4Bh8sFtVIJjUoBjVoJrUoJvUaJAC8tAr01CPDSQKPi31lEnoDBhIgahcXuRGG5DSWVVTBZ7Siz2GGyOGCy2mFzuE4fpUXk/e/h9a3FwNbiy3p+H60Kgd5aBHhpEOKrRYS/HhEBp2/+eoT76xEZoEewjxYKhaL+3yAR1QsGEyKqd06XQF6ZFTmlZhSW21BQbkO51XHJx6kVApaSfMS3ioK/rw80KgXUKiXUSgWcLgG704Uqpwt2h4DZ7kCZ2Q7T6eetrHKissqCk6WWi76Gl0aF6GAvxAR7IyrIGzHB1bfoYG9EB3vBW8uPRSIp8TeQiK6aEAJFlVU4UWRGdokZJ0sscLhEreMCvDQI8dHC36v69Iu/lxr+eg28tSro1Crkpqfg/ZkT8MOePUhMTKzTaztdAuVWO0rNdpRa7Cg1V8FYUYW8MgvyTFbklVnd/zVWVMFid+JYfgWO5Vec9/kMvjp3cIkJ9kZ0UHVoiQryQkSAnqeMzuJwuWC1u2C1O2F3umA/fdrN6RRwuIT7NNy5PwkKAGqlEmqVAmpldfjUqZXw0qrgpVFBp1ayV6sZYzAhoitWVGHDsYIKHM8vR4nZXuM+L40K0UFeCA/QI8xPh1A/HXRqVb3XoFIqEOitRaC39pLH2hxOnCq1IrvYjKxiM7KLq4NUVrEZWUVmmKwOGCtsMFbYsC+rtNbjlQogwl+PlkFeiAryRstAL0QFeaFlkBdaBnohzF8PX13T+1gVQqDK+U/IsNqdsNidsNpdp/975uY66z4n7M7a4bM+KAB4aVXw1/8TXgO8NAjy1sLgp22QnyOSj6b3G0REkrLYnUg5ZUJKrgnFlVXu/SqFAtHBXog+3dMQIsOxHDq1CnEGH8QZfM57f5nZ7g4q7vBSYkF2sRknSy2ocrhwqsyKU2VW7MosOe9zeGtV7iAW5qdHqJ8OBl/t6R6i6i/Ys2++ejW0qqvrIRBCwCkEnE4Bu1PA5nDC5nBV3+z//L/V4YTN7oLNUTtknKeDq04UAPQaFbTq6lNuKqUCmtOn39Sq6u1z35sQZ3pTqntUHM7qwc+WKieqnNU9LOYqJ8xVTuSZar+mv14Ng291G7cI9EIke7I8CoMJEV2SEAJ5JisO5ZThWEEFnKe/xVQKBWJCvNEuzBdxoT5N/i/ZAG8NArwD0KVlQK37XC4BY6UNOSUWnCypHsuSc/q0VU6JBadKLag8/WWaWWRGZpH5sl5bp64+naE7fSrj7NMZQojq0yECEKe3y81WRE1cju+zNXBmpV39m0d175OXRgW9Rgm9RnX6pjy9T+X+79n76vu0y5nTQ5W26oHRZwZIl1nsKKqoQoXNAZO1+va3sRJAdU9WmF91T1b06d4slVJeoZjqjsGEiC5ICIH0wkrsyixGQbnNvT/UT4eElgFoG+YLnaZph5G6UioVCPPTI8xPj8SYoPMeU2lzuAf7FpRbUWCyobDChqIKG8os9tM3B0yn/7/C9s+A4DO9GqjDIOEzVN4BOPdsigKoFXBq/L9a5R7PoT8rhHhpVFCfp3ejsamVSvjqlPDVqRHur691v9XuhLHChsJyG/LLbThZYkGFzVE9jshkxZ4TJdCqlIgN8UbrUF/EhnhL8C7oajCYEFEtLiGQVlCBnZnFKKqoPl2jUirQLswXCVGBCPfXSf4FJkc+OjV8dGrEXuBU0bkcThfM9n9Or1SfevnnVIuAgALV7axQVIcOhUIBhQLISDuGsaPuw0OvfIjo1u2rT6OoFFAppA8XDUmvUSEqqHpG1Rkmi/10D5YFmUWVMFc5caygAscKKqBUAEFoCa82fa74dBU1LgYTInITQiCtsAJ/pRej2FwdSLQqJbpFB6B7dCCn0tYztUoJf5USqN0xcEmqYi3sxiz4qqsDUXPmf3r8TsdIf/dpx78LK/G3sRLFlVUogj/CRryKvyxOVB4vROcWAQj2ufRgaZJG8/5pJiK3gnIrth4zutcB0amV6B4diO7RgdA3k9M11PQpFApEBnghMsAL18YbYKywYfOOfciqVAI+QdibVYq9WaVoFeKNxJggRAd5eXQPU1PEYELUzFXaHPgzvQgpudXTH1RKBXrGBCGxVWCTH8xKZPDVIQ4F+HPOC7j9pYWw+Mfgb2MlThSZcaLIDIOvFokxQWgX7scBszLBYELUTAkhcOhkGf5IK0KVs3pJ+Hbhvrg23gB/vUbi6ojqmcsJg9qG7t1aoNRchf3ZpUg+ZYKxogrrU/KxI6MY17QOQbtwX/agSIzBhKgZKjFX4bfUAvdpm3B/HQa2DUWLQC+JKyNqeIHeWlzfPgz9Wofg0Mky7MsqRZnFjnXJedh9QotrWocgzuDDgCIRBhOiZsTlEtibXYK//i6G0yWgUSnQv40BCVEBUPJDmJoZvUaF3rHB6BYViP3ZpdiTVQJjRRV+OJiLyAA9rm8fijC/KxiZTFeFwYSomSiz2LHucB7yTFYAQEywN27sEAZ/L562oeZNq1aiT1wwEqICsOdECfZnlyK3zIqvdmaja8sAXNMmhAPAGxGDCVEzcDSvHBuPFKDK6YJWrcR1bUPRMdKPXdVEZ9FrVLg23oBuUYH4Pa0Qx/IrcPBkGY4XVKB/fAg6R/rzd6YRMJgQebAqhwtbjhW6Z9xEBuhxW+cI9pIQXYSvXo3BXSLRpYUZm48VoriyekzW0dxy3NwpnL8/DYxXPSLyUEUVNny5KwspuSYoAPSJC8aIxCh+qBLVUXSwN0b3icGAeAPUSgVySi34YscJHDpZBiG4jGxDYY8JkQc6XlCODSn5sDsFfHVq3No5vMYS3kRUNyqlAomtgtA61AcbUvJxqsyKjUcKkF5QgRs7hsGPU+vrHXtMiDyISwj8mW7Ez4fyYHcKRAV5YXSfGIYSoqsU6K3F3T2jMKCtASqlAieKzVi+IwvphRVSl+Zx2GNC5CFsdifWJechs8gMAOgRHYh/xRug5GqWRPVCqVAgMSYIsSE+WJ+Sh3yTDT8ezEWPmEBc28bAlWPrCXtMiDxAmcWOr3dnI7PIDJVSgVs7hWNgu1CGEqIGEOyjxT09o9EjOhAAsC+rFN/syYHJape2MA/BYELUxOWZrPh6VzZKzHb46tS4t2cUOkT6S10WkUdTKRUY2C4USQmR0KqVyDNZsWJHFjKLKqUurcljMCFqwv4urMC3e3JgsTth8NXivl7RCPPnSpVEjaVNqC9G94lBmJ8ONocLa/efwr6sEs7auQoMJkRN1IHsUvx4MBcOl0CrEG/c0zMavnoOGyNqbAFeGtzTKwqdIv0hAGw9bsRvRwrgdDGcXAl+ihE1MUIIbP+7CLsySwAAXVr44/r2YRx4RyQhtVKJmzqGIcRXi23HjUg+ZUKp2Y4hXSPhpeVy9pfjsntMqqqq8Pjjj1/0mFmzZiE9Pf2KiyKi8xNCYPOxQncouaZ1CAZ1YCghkgPF6Vk7Q7u1gFalxMlSC77alYUSc5XUpTUpl91jotFosGTJEpw6dQotWrRAu3bt0L9/f/Tq1QtqtRp//vknpk6dij59+qBNmzYNUTNRs+RyCWxIzceRvHIAwA3tQ5EQFShtUURUS5zBB/f2isIPB3NRZrFj1e4c3NG9BSI4/qtOLrvHRKFQICgoCE8++ST69OmD4uJivPDCC4iJicHkyZMxbNgwfPbZZ+jbt29D1EvULDmcLvx0KBdH8sqhUAC3dg5nKCGSsRBfHe7pGYUwPx0sdidW783BCc7YqZM695gsWrQI0dHRGDBgAPR6PW677Tb3ffv378fkyZMxd+5c9OzZE6NHj26QYomaI7vThR8P5iKruHqNktu7RKB1qK/UZRHRJfjo1Lg7MQo/HjqF7GIL1h44hZs7haNDBKfzX0yde0x2796NZ555BgEBATAajXj11VcxZswYtGnTBk8++STuu+8+GI1GtG7dGpMmTWrImomaDbvThR8OnkJWsRkalQLDurVgKCFqQrRqJYZ1a4l24b5wCeB/yfnYn10qdVmyVucek9mzZwMADh48iGXLlmH79u3YvHkzXnjhBbzxxhvu4+bMmYNu3brh3nvvxTXXXFP/FRM1E3anCz8cOIXsEkt1KOneEi0DvaQui4guk0qpwG2dI+CtNWJ/dim2HCuE0yXQs1WQ1KXJUp2DyeTJk1FWVob27dujpKQEX3zxBZ5//nnExsYiKSkJDz74IIYOHYoBAwbglVdewcmTJxuybiKPxlBC5FkUCgUGtjVAq1JiZ2YxtqUZ4RQCfWKDpS5Ndup8Kuett97CwIEDUVFRAbVajbFjxyIpKQnjx4/H9u3bsXz5csTFxaFjx4544IEHMGLEiIasm8hjMZQQeSaFQoFr2oSgX+vqMLI9vQh//V3EVWLPUecek5EjR0Kn0yE3Nxe5ubno2LEjvvvuO/Tr1w8BAQH4/vvvMWbMGGzcuBEnTpxAq1atGrJuIo/kcFXPvmEoIfJcfeNCoFIo8Ed6EXZkFMMlBK5pHQKFgusRAZfRY3LXXXdh2LBh6N+/P9LS0qBWq3HnnXdiwIABKCkpwe+//44jR47gk08+wRNPPNGQNRN5JJdLYN3hPJwoMkOtVGBYN4YSIk/VKzYYA9oaAAC7MkuwI6NY4orko87B5Nprr8X7778Pf39/DBs2DFlZWdDr9UhJSQEAvPTSSwgODsZdd90Fi8WCPXv2NFjRRJ5GiOrF09ILK6FSKJCUEImWQQwlRJ4sMSYIA0+Hkx0ZxdhzokTiiuShzqdyfvvtN7zwwgsQQuDo0aOYNWsWBgwYgF27dkGv12Pr1q04deoUAGDMmDHYtm0bevbs2WCFE3kKIQQ2HS10L542uGsEWoX4SF0WETWCHjFBcLgE/kwvwrY0I9QqBbo188UT6xxMHnnkEQDV18oZPHgwfHx88OOPP8JgMODbb78FALRo0QJAde9Kx44dG6BcIs/zR3oRDp0sAwDc2ikCbbhOCVGz0js2GHanC7syS7D5aCE0KiU6RTbfRdjqFExsNht69OiBlJQUaLVazJgxA6mpqVAq/zkT9OGHH2LixInQ6/W4+eab8ddff6FDhw4NVjiRJ9ibVeLuvr2xQxjaR/hJXBERSeGa1iGwOwX2Z5fi15R8aJQKtA1vnp8HdQomOp2uxmjhffv24f777691XHBwMIYNG4Z58+bVKZSUlpbi6NGjaNeuHYKCuNAMNS9H88rx+3EjAODaNiHo0jJA4oqISCpn1jmxO11IPmXC/5LzodeoEB3sLXVpja7Og1+dTicsFgsAQKvV4u6778bhw4eRkZGByspKREREoGXLlpg2bRruueeeSz7fqlWrEBsbiwkTJiAqKgqrVq268ndB1MScKKrE+pQ8AED36ECuAElEUCgUGNQhDPFhvnAKgR8P5qKw3CZ1WY2uzsEkMzMTcXFxiIiIwF9//QUAyM3NRWpqKn799Vc899xz6NixI3Q63SWfq6ysDE8++SS2bt2KQ4cOYfbs2ZgyZcqVvwuiJqTAZMVPh3LhEkC7MF8MbGvg+gVEBABQKhS4tVM4WgZ6ocrpwvf7T8JksUtdVqOq8+DXNm3aIDk5GTabDXfeeSdWrlyJQYMGAaieVaBQKGAwGPD444/D4XBc9ArDJpMJH374IRISEgAAiYmJKCoqusq3QiR/peYqfL//FOxOgaggL9zcOZyhhIhqUKuUGJoQiVV7c1BUUYXv9p/EvT2j4aVVSV1ao6hTMLHb7XA6nQCqx5v07dsX33//PVQqFRQKBVwuF2w2G6ZPn441a9Zg4MCBGDBgAKKjo8/7fNHR0RgzZoz7uT/44AMMHz68nt4SkTxZncCv+0/BYnci1FeHpIRIqJV17rQkomZEp1Hhzm4tsXJPNkrNdqw5cBJ3J0ZBo/L8z4w6BRO1Wo2lS5fCbrdj/vz5eO211zB69GgsWbIEH3zwAaKjozFixAj3LJ1x48ZBo9Fc8nkPHDiAQYMGQavVIjU19bzH2Gw22Gz/nGMzmUx1KZlIVhQaPf4oVKOsyg5/vRrDureATt08/vohoivjq1fjzu4tsWp3NvJNNvwvOQ9DukZ6fC9rnaKXQqFAnz59IITArFmzAAD79++HUqlEVFQU5s6di8TERGRkZAAA3n33XURERFzyeRMSErB+/Xq0bdsWEyZMOO8xM2bMQEBAgPt2oV4YIrlyugRC73wBpVVKeGlUuLNHS/jo6nwWlYiasWAfLZK6tYBKoUB6YSX+SPf8YQ917hOaPHkypk2bhqKiIrz44osoKSnBokWLsHfvXvTt2xc2mw29evXCwYMH6/ziCoUCPXv2xJIlS7B69WqUlpbWOmbq1KkoKytz37Kzs+v8/ERSE0Jg/j4TvFr3gkohcEe3Fgjy1kpdFhE1IS0DvXBTpzAAwJ4TJTh8ekFGT1XnP9uCgoKg0+nw3HPPAQBUKhX8/f0RHh4OlUqFxx9/HDk5ORg+fDj27NmDwMDACz7Xli1b8OOPP+Ldd98FUD39WKFQ1Fiw7QydTlenmT5EcrRgWwb+l26GEC70MTgREaCXuiQiaoI6RPij1GzHjoxibDpaAH8vDWI8dI2TOveYPPXUUygrK0NFRQUqKirgdDoxePBg/PzzzwgPD8dTTz2Fd999F4mJiXjjjTcu+lzt2rXD559/js8//xzZ2dl48cUXccstt8Dfv/kuwUueZ93hPPy/n6vHTpVsXIAW3kLiioioKesbF4z24X5wCeCnQ7korqySuqQGUedgolAooNFooNFooNPp4HA4oNFo8Mgjj2DJkiWYOHEiAOCFF17At99+CyEu/CEcGRmJb775Bh999BE6d+4Ms9mMpUuXXv27IZKJA9mleObrfRACuK2NN8p3r5G6JCJq4hQKBW7qGIbIAD2qHC6sPXAKVrtT6rLqXZ1P5QQEBGDatGnu7f79+0Oj0eC+++7Dfffd554507NnT/zwww+XHDV88803Izk5+QrLJpKvnBIzxi/ZDavdhevbh2J8VxU+k7ooIvIIapUSSQmR+HpXNsosdvxyOA/DurWAUuk5M3WueEL0wIEDa2yfPQ6ka9euV14RURNmstrx8OJdMFbY0CHCD5+MToTKgz4wiEh63lo1khJaQK1UIKvYjG3pRqlLqleev1ILUSOxO134v+V7cSy/AmF+Oix6qDd8OS2YiBpAqJ8Ot3QOBwDsyypFaq7nrPHFYEJUT15bm4zfjxvhpVFh4bjeiAzwkrokIvJgbcP80Cc2GADw25EC5JVZJa6ofjCYENWDZdszsXxHFhQK4ONRPdClZYDUJRFRM9CvdTBaG3zgdAn8eOgUKm0OqUu6agwmRFfpjzQjXvshBQDw3K0dcHOncIkrIqLmQqFQ4JbO4Qj20aLS5sTPh3PhdDXtpQkYTIiuQqaxEk8u3wunS2B4j5Z4/LrWUpdERM2MTq1CUkIktColTpVa8WcTHwzLYEJ0hUxWO8Yv2YUyix3dowMx466uHn9xLSKSpyBvrbu3dm9WKY4XlEtc0ZVjMCG6Ak6XwMQV+5BeWIkIfz0+v78n9BpeLZiIpBMf5ovEmEAAwK8pBShpoivDMpgQXYGZv6Riy7FC6DVKzHugF8L8eQ0cIpLetW0MaBnohSqnCz8dyoXd6ZK6pMvGYEJ0mVbtzsa83zMAAO/d0w1dozgDh4jkQalUYHCXCHhrVSiqrMJvqQUXvUSMHDGYEF2G3ZnFeOm7wwCAfw+KR1JCC4krIiKqyUenxu1dIqFQAEfzy3Ewp0zqki4LgwlRHZ0steDxL/agyunCbZ0j8MxN7aQuiYjovFoGeeFf8QYAwNbjhcgts0hcUd0xmBDVQaXNgQlLdsNYUYWOkf54/75uHnXRLCLyPD2iAxEf5guXAH4+lAdLVdO4EjGDCdEluFwCk1ceQGquCQZfLeY90BPeWl4Dh4jkTaFQ4OaO4Qj01qDC5sD6lLwmMd6EwYToEj787TjWJedBo1Jg7tieiArylrokIqI60aqVuL1LJFRKBTKLzNiXXSp1SZfEYEJ0ET8cOIWPfzsOAHhreFf0On3BLCKipiLUT4eBbavHm/yRZkSeSd4X+2MwIbqAQzlleHbVAQDAIwPicE+vaIkrIiK6Ml1bBiA+tHq8yS+HcmFzyHe8CYMJ0XkUmKx4ZOlu2BwuXN8+FC8M7ih1SUREV0yhUOCmjmHw16thsjpkvb4JgwnROax2Jx5Ztgd5JivahPrg41E9oOIMHCJq4nQaFQZ3iYRSARwvqMDhkyapSzovBhOiswghMHX1IRzILkWAlwYLHuwNf71G6rKIiOpFRIAe/dtUjzfZcrwQxgqbxBXVxmBCdJa5W/7Gd/tOQqVUYM6YRMQafKQuiYioXiXGBCI2xBtOl8DPMryeDoMJ0Wm/puTjnf8dAQBMG9oJ155eNZGIyJMoFArc0ikCPjoVSsx2bDpaIHVJNTCYEAE4mleOp7/aByGAMX1jcH+/VlKXRETUYLy0KtzWOQIKAKm55TiaVy51SW4MJtTsFVdWYfySXaiscqJf62C8dkdnKBQc7EpEni0qyBu9T6/NtPFoAUwWu8QVVWMwoWatyuHCE1/sQU6JBTHB3vh0TE9oVPy1IKLmoW9cMCID9KhyuLAuOQ8uGcwg5icwNVtCCLy65jB2ZBTDV6fG/Ad7IchHK3VZRESNRqlU4NbOEdCqlMgts+KISfpYIH0FRBJZ+EcmvtqVDaUCmDWqB9qF+0ldEhFRowvw0uCGDqEAgNQyFXQtpV1QksGEmqVNRwvw/35KAQC8eHtH3NAhTOKKiIik0yHCHx0i/AAoYBj6LCqrpJtCzGBCzc7x/HL8e8U+uARwb68ojP9XnNQlERFJ7vr2ofBWCagDwrH4gHSrwjKYULNSPQNnN8ptDvSJDcabd3blDBwiIgA6tQp9DA5Yc5Jxd0dfyepgMKFm48wMnKxiM6KCvPDp2ERo1fwVICI6I0QnkL/8eUT4qiWrgZ/K1CwIITBt7T8zcBY82BshvjqpyyIionMwmFCzsOiPTHy5MxsKBfDxqO5oH8EZOEREcsRgQh5v89ECvHlmBs7gjhjUIVziioiI6EIYTMijpRWUY+LpGTj39IzChAGcgUNEJGcMJuSxiipseGjxrn9m4Azvwhk4REQyx2BCHslqd+KRpbuRXXz6GjhjE6FTq6Qui4iILoHBhDyOyyUwedUB7M0qhb9ejYXjOAOHiKipYDAhj/Pe+qP46WAuNCoFPru/F+LDpFsoiIiILg+DCXmUlbuyMWdzOgBgxl0JuKZNiMQVERHR5WAwIY/xR5oRL353CAAwcVA8RvSMkrgiIiK6XAwm5BGO55fj8S/2wOESuKNbC0y6uZ3UJRER0RVgMKEmr7D89LRgqwO9WgXhnREJnBZMRNREMZhQk1Zpc2D8kl3IKbGgVYg3Pn+gF/QaTgsmImqqGEyoybI7XXhy+V4czClDkLcGi8b1RrCPVuqyiIjoKjCYUJMkhMAL3x7ClmOF0GuUWDiuN1qHclowEVFTx2BCTdJ764/i2705UCkVmD06ET1igqQuiYiI6gGDCTU5y7ZnYvam6rVK/t+dXXBjR14tmIjIU0gWTNasWYPWrVtDrVaje/fuSE1NlaoUakLWHc7Dq2uTAQD/uakdRvaJkbgiIiKqT5IEk/T0dDz00EOYOXMmTp48iXbt2mHChAlSlEJNyK7MYvz7q30QAhjVJwb/vjFe6pKIiKieSRJMUlNTMXPmTNx7770IDw/HE088gX379klRCjURx/LLMWHJblQ5XLipYzjeGNaZa5UQEXkgtRQvmpSUVGP76NGjaNu27XmPtdlssNls7m2TydSgtWVlZcFoNDboa3gKg8GAmJiGP5VyoqgSY+fvQJnFjsSYQMwa1QNqFYdHeTKe2r00thF5KkmCydmqqqrw3//+F5MmTTrv/TNmzMD06dMbpZasrCx06NgRFrO5UV6vqfPy9saR1NQGDSd5ZVaMmb8DBeU2dIjww8JxveGl5QJqnspUXAgAGDt2rMSVNB0VFRVSl0BUryQPJtOmTYOPj88Fx5hMnTq1RmgxmUyIjo5ukFqMRiMsZjPGPP8uwmPaNMhreIr8rHQsf3sKjEZjgwWT4soqjF2wAzklFsSGeGPp+D4I9OYCap7MUlHdIzrksZfQPqGnxNXIW+rOLfhlyUewWq1Sl0JUryQNJhs3bsTs2bPx119/QaPRnPcYnU4HnU7XqHWFx7RBVNvOjfqaVJPJaseDC3ciraACkQF6fDGhL8L89FKXRY0kpEUr/g5eQn5WutQlEDUIyU7UZ2RkYNSoUZg9ezY6deokVRkkQ5YqJyYs3o1DJ8sQ4qPFsvF9ERXkLXVZRETUCCTpMbFYLEhKSsKwYcMwfPhw9zlSHx8fzrRo5qocLjyxfA92ZhbDT6fGkof7ID6MS80TETUXkvSYrF+/HikpKZg3bx78/PzctxMnTkhRDsmEw+nCf1bux+ajp69/81BvdGkZIHVZRETUiCTpMRk2bBiEEFK8NMmUw+nC5FUH8NPBXGhUCnx2fy/0jg2WuiwiImpkXAyCJHcmlKzZfwpqpQKfjE7Ede1CpS6LiIgkwGBCknK6RI1QMntMIm7tHCF1WUREJBEGE5KM0yUwaeV+hhIiInJjMCFJnBtKPhnNUEJERAwmJAGnS2DyOaHkti4MJUREJIMl6al5qT3QtQdDCRERuTGYUKOxOZyYuGIf1qfknxVKIqUui4iIZITBhBqFucqBx5btwe/HjdCqlZgzOhE3dQqXuiwiIpIZBhNqcCarHQ8v2oXdJ0rgrVVh3gO9cG28QeqyiIhIhhhMqEEVVdjw4KKdOHzSBH+9Gose6oOerYKkLouIiGSKwYQaTHaxGQ8s3IkMYyVCfLRYOr4POrfgtW+IiOjCGEyoQSSfKsO4RbtQWG5Dy0AvLB3fB21CeZVgIiK6OAYTqnd/phnx6LI9qLA50CHCD0se7oNwf73UZRERURPAYEL16seDpzDp6wOocrrQNy4Ynz/QCwFeGqnLIiKiJoLBhOqFEAJzt/yNt9cdAQAM7hKBD+7rDr1GJXFlRETUlDCY0FWzO114+bvD+Hp3NgBgXP9YvJLUCSqlQuLKiIioqWEwoatSWeXCgwt34s/0IigVwKtJnTDu2jipyyIioiaKwYSumDowAlM3FiHH5IC3VoVPRvfAoA5czZWIiK4cgwldkXyrAhEPfIAckwMR/nosGNeLa5QQEdFVYzChyyKEwL7sUmwrUEPl5Ye2wRose+xaRARwOjAREV09pdQFUNPhcLqwISUfvx83AlCg4tCveOOGEIYSIiKqNwwmVCcmix2r9uQgNa8cCgWQEOhA0c8fQqvizBsiIqo/PJVDl5RhrMT/kvNgc7igVysxuGsklEUZUpdFREQeiMGELsglBP76uwi7MksAAOH+OtzeJRL+XhrkFElcHBEReSQGEzqvSpsD65LzkFNiAQAkRAVgQFsD1Eqe/SMioobDYEK1ZBZVYn1yPix2JzQqBQZ1CEOHCH+pyyIiomaAwYTcHC4X/kwvwr6sUgBAiI8Wg7tEIMRXJ21hRETUbDCYEACgxFyFdYfzUFBuA3D61E28AWoVT90QEVHjYTBp5oQQOHiyDNuOG+FwCeg1StzcMRytQ32lLo2IiJohBpNmzGSx49fUfGSfHuAaFeSFWztFwFfPHwsiIpIGv4GaISEEkk+Z8PtxI6qcLqiVCvwr3oCEqAAoFFwwjYiIpMNg0sxUWB349Ug+ThSZAQCRAXrc3CkcQd5aiSsjIiJiMGk2hBBIzSvH1mOFsDlcUCkV6N86BN1jAqFkLwkREckEg0kzUFJZhY1HC9yLpYX763BLpwgE+7CXhIiI5IXBxIM5nC7sPlGC3ZklcAoBtVKBvnHBSIwJglLJXhIiIpIfBhMPlV1sxsajBSg12wEArUK8cUP7MAR4aSSujIiI6MIYTDyMucqB348bcSSvHADgrVXh+nahiA/z5YwbIiKSPQYTD+F0CRzILsWOjGJUOV0AgISWAegfHwKdWiVxdURERHXDYNLECSGQUVSJ348ZUWqpPm0T5qfDDe3DEBGgl7g6IiKiy8Ng0oQVV1Zh67FCnCiuXpPES6PCtfEh6BjpzynARETUJDGYNEEWuxM7M4pxIKcUQgBKBdAjOgi944J42oaIiJo0BpMmxO50YX92KXZnlrjHkbQ2+OBfbQ1cuZWIiDwCg0kT4HIJJOeasOPvIlRWOQEABl8t/hVvQKsQH4mrIyIiqj8MJjImhEB6YSX+TDei5PR6JP56Na5pHYL2EX6c/ktERB6HwUSGhBDIKjbjr7+LkWeyAqge2No7NghdowKgViolrpCIiKhhMJjIyJlAsiOjGLll1YFEo1KgR3QQElsFcmArERF5PAYTGRBCILvEgr/+LnIHEpVSgYSWAejZKgg+Ov4zERFR88BvPAkJIZBTYsFfGUU4VfpPIOnaMgC9GEiIiKgZkuybz2g0onfv3ti0aRNiY2OlKkMSZwLJjoxinCy1ADgdSFoEoGdsEHwZSIiIqJmS5BvQaDQiKSkJmZmZUry8ZIQQyDBWYveJkhqnbLq08Eev2GAGEiIiavYk+SYcOXIkRo8ejR07dkjx8o3O6RI4nl+O3SdKUFRZBaA6kHRu4Y/erYLhq2cgISIiAiQKJvPmzUNcXByefvrpSx5rs9lgs9nc2yaTqSFLq1d2pwspp0zYk1WCcqsDAKBVKZEQFYDu0YEeMYYkNTVV6hJkj21ERFR3knwzxsXF1fnYGTNmYPr06Q1YTf2z2Z04cLIM+7NKYbFXr9TqpVGhR0wgEqICPGLar6m4EAAwduxYiStpOioqKqQugYhI9mT/J/vUqVMxadIk97bJZEJ0dLSEFV1Ypc2BfdmlOJRT5r6Wjb9ejcRWQegc6Q+1ynMWRrNUVPdcDXnsJbRP6ClxNfKWunMLflnyEaxWq9SlEBHJnuyDiU6ng06nk7qMiyqurMLerBIcySuH0yUAACE+WvSKDUK7MD8olZ67dHxIi1aIattZ6jJkLT8rXeoSiIiaDNkHE7k6syja3qwSnCgyu/dHBujRq1UQ4gw+vJYNERHRZWIwuUxOl8Cx/HLsyypFYcU/g3LbhPqgR0wQWgToGUiIiIiuEINJHVntThw6WYYDOaWotFUPaFUrFejUwh/dowMR5K2VuEIiIqKmT9JgIoSQ8uXrpNRchf3ZpUjJNcHurK7XW6tCt+hAJLQMgF7T9GfYEBERyQV7TC7gVGn1+JH0wkr3vhBfLRJjgtAu3BdqpefMsCEiIpILBpOzOF0C3u2vxaY8NYqzctz7W4V4o0d0IGKCvTl+hIiIqAExmJz29a4s/HddIULvnIriKkClUKB9hB8SYwIR4ivv6cpERESegsHktMwiMwoqnXCay9A5whf/Soj3iCXjiYiImhIOlDhtXP9YPJboj5OfPozOgU6GEiIiIgkwmJwW7q/HrfE+EA7bpQ8mIiKiBsFgQkRERLLBYEJERESywWBCREREssFgQkRERLLBYEJERESywWBCREREssFgQkRERLLBYEJERESywWBCREREssFgQkRERLLBYEJERESywWBCREREssFgQkRERLLBYEJERESywWBCREREssFgQkRERLLBYEJERESywWBCREREssFgQkRERLLBYEJERESywWBCREREssFgQkRERLLBYEJERESywWBCREREssFgQkRERLLBYEJERESywWBCREREssFgQkRERLLBYEJERESywWBCREREssFgQkRERLLBYEJERESywWBCREREssFgQkRERLLBYEJERESywWBCREREssFgQkRERLLBYEJERESywWBCREREssFgQkRERLLBYEJERESywWBCREREsiFZMDl8+DB69+6NoKAgTJkyBUIIqUohIiIimZAkmNhsNgwdOhQ9e/bE7t27kZKSgsWLF0tRChEREcmIJMHkl19+QVlZGd5//320adMGb731FhYsWCBFKURERCQjaile9MCBA+jXrx+8vb0BAAkJCUhJSTnvsTabDTabzb1dVlYGADCZTPVeV0VFBQAg53gybBZzvT+/J8nPSgcA5GUeQ7qPt8TVyBvbqu7YVnXHtqo7tlXdFeZkAKj+Pqzv79kzz3epoRsKIcHgjsmTJ8NqtWL27NnufaGhoTh27BiCgoJqHPvaa69h+vTpjV0iERERNYDs7GxERUVd8H5JekzUajV0Ol2NfXq9HmazuVYwmTp1KiZNmuTedrlcKC4uRkhICBQKRaPU2xBMJhOio6ORnZ0Nf39/qcuRDNuhGtuBbXAG26Ea26GaJ7WDEALl5eVo0aLFRY+TJJgEBwfj8OHDNfaVl5dDq9XWOlan09UKMYGBgQ1ZXqPy9/dv8j9s9YHtUI3twDY4g+1Qje1QzVPaISAg4JLHSDL4tXfv3ti+fbt7OyMjAzabDcHBwVKUQ0RERDIhSTAZOHAgTCYTFi1aBAB46623cNNNN0GlUklRDhEREcmEZGNM5s+fj1GjRmHKlClQKpXYvHmzFKVIRqfTYdq0abVOUzU3bIdqbAe2wRlsh2psh2rNsR0kmZVzRl5eHvbs2YN+/fohJCREqjKIiIhIJiQNJkRERERn40X8iIiISDYYTIiIiEg2GEwakNFoRFxcHDIzMwEAixcvhkKhqHU79wKGdrsdXbt29YgBwVfaBvfddx8mTpzY+AU3kMtthzfeeAPh4eHw9fXFHXfcAaPRKF3xVO/O/XkAgPnz5yM6Ohre3t64/vrr8ffff7vvW7NmDVq3bg21Wo3u3bsjNTVVgqrr3+W2w9luu+02j7n465W2w/PPP4+hQ4c2YqWNRFCDKCwsFH379hUAREZGhhBCCJvNJkpKSty37OxsYTAYRFpaWo3HvvnmmwKA2LRpU+MXXo+utA1++uknERYWJkpKSqQpvJ5dbjts2bJFdO7cWRw5ckQcP35c3H777eL++++X9k3Uk8LCQhEbG+tuByGEmDdvnoiKihJeXl7iuuuuE+np6e77Dh06JHr16iUCAwPFs88+K1wulwRV16/z/TykpaWJ6OhosWfPHnHixAnx8MMPiwEDBrjvCwoKEl9//bXIy8sT99xzj+jfv7+E76B+XG47nO2LL74QAMSiRYsat+gGcKXtcODAAeHr61vj98VTsMekgYwcORKjR4+usU+r1SIwMNB9W7p0KYYPH442bdq4jzl+/Djee+89xMbGNnLF9e9K2qCyshJPPvkkZsyY4TEr/F5uO+zcuRO333472rdvj/j4eIwePRppaWkSVV9/jEYjkpKSavxVmJ6ejtdffx1r1qzBkSNH0KZNG4wbNw5A9QU8hw4dip49e2L37t1ISUnxiL+Qz/fzsG/fPvTr1w+JiYmIiYnBww8/7P43T01NxcyZM3HvvfciPDwcTzzxBPbt2ydF6fXqctvhjOLiYkyePBnt27dvzHIbzJW0g8vlwqOPPor//Oc/aN26dWOX3PCkTkae6u+//xZCiBop+GwWi0WEhYXVuu+GG24QM2fOFNddd12T7zG5kjaYMmWKiIyMFEuWLBHr168XTqezkaptOJfbDj///LOIj48X6enpIj8/X9xwww3i1VdfbcSKG8aNN94oPvrooxrtsGrVKnHPPfe4j9m2bZuIjIwUQgjx3XffiaCgIFFZWSmEEGL//v3i2muvbfS669v5fh6Sk5NFSEiI2LdvnygtLRWjR48WDzzwwHkf/+mnn4qEhITGKrfBXGk7jBs3Tjz++OPiwQcf9Igekytph9mzZwtvb2+xYMECsWbNGmGz2aQovcEwmDSwC30ZLViwQAwdOrTGvoULF4rExEThcDg8IpicUdc2yMzMFFqtVvTv319MmzZN9OjRQwwdOtQjwokQl/ezcOuttwoAAoDo3bu3MJvNjVRlw7ncD+DXXntNDB482P14l8slgoKCGr3uhnLuz8Njjz3m/jePi4sTBQUFtR5js9lEfHy8mDNnTiNW2rAupx02btwooqOjhclk8phgckZd26G8vFwYDAbRpUsX8eqrr4rrr7/eYz4jzuCpHInMnTsXjz/+uHu7sLAQU6dOxcKFC5vN0vzntsGSJUsQHh6O3377Da+99hq2bNmCbdu24ddff5WwyoZ3bjt88803yMrKQkpKCgoKCtC5c2eMHTtWwgrrR1xcXK19nTp1wogRI9CjRw8EBgZi+/bteO+99wBUX1X17McoFAqoVCqUlJQ0Ws2NZefOnfjhhx/w119/obS0FKNGjcLtt98Occ4yU9OmTYOPjw8mTJggUaUN62LtYLVa8dhjj+HTTz+Fn5+f1KU2qIu1w+rVq1FZWYlNmzZh+vTp2LBhA8rLy7Fs2TKpy643DCYSSEtLQ1paGm6++Wb3vmeeeQbjx49Ht27dJKys8ZyvDXJycnDTTTdBr9cDAPz8/NC2bVuPGF9xIedrh+XLl+OJJ55Ax44dERoaig8//BCrV69GaWmpdIU2kIt9AKvV6lrLcOv1epjNZomqbThffvklRo4cib59+yIgIABvvvkm0tPTceDAAfcxGzduxOzZs7FixQpoNBoJq204F2uHN954A71798aQIUOkLrPBXawdcnJy0K9fPxgMBgDVl3hJSEjwqM9JBhMJrFy5EklJSTU+XFasWIFZs2a5B0Nu27YNSUlJmDlzpoSVNpzztUFUVBQsFot72+VyIScnBy1btpSixEZxvnZwuVwoKChwb+fl5QEAnE5no9fX0C72ARwcHIzCwsIax5eXl0Or1UpUbcM599+8vLwcZrPZ/W+ekZGBUaNGYfbs2ejUqZNUZTa4i7XDihUrsGbNGvdn5IoVK/Dkk0/iySeflLDihnGxdjj3cxIATpw44VGfk5JcxK+5W7dunXvmwRkZGRk1tkeOHIlnnnkGt912WyNW1njO1wb33HMPevXqhW+//RZ9+/bFrFmzYLfbcdNNN0lTZCM4XzsMGDAA7777Llq2bAkvLy98+OGH6N+/v0deT8rlctVYo+XsD+DevXtj3rx57vsyMjJgs9kQHBwsRakNasCAAXjwwQeRmJiI8PBwzJ8/HxEREUhISIDFYkFSUhKGDRuG4cOHo6KiAgDg4+MDhUIhceX162Lt8Pvvv8PhcLiPffbZZ9GvX79avz+e4GLtEBsbi4kTJ2Lu3LlISkrC6tWrceDAAaxatUrqsuuPtENcPB/OGdBkNpuFVqsVqampF32cJw9+vVgbrFmzRiQkJAi9Xi+6dOki/vzzz0astGHVtR2sVqv497//LVq0aCG0Wq247rrr3ANHPQHOmZXj7e0t3n//fbF8+XJxww03iFatWomqqipht9tFaGioWLhwoRBCiAkTJoikpCQJK69fZ7eDy+USr7/+uoiJiREajUb06NFD7N27VwghxPfff+8eBHn27XwDqZuiurbDuTx58Oul2mHbtm2iX79+wsvLS7Ru3VqsXbtWoqobBi/iR0SNSqFQICMjA7GxsRBC4M0338T8+fORm5uLLl26YMGCBejRowcAYO3atRg1ahS8vLygVCqxefNmjz6VQUS8ujARyVxeXh727NmDfv36eeTpLCKqicGEiIiIZIOzcoiIiEg2GEyIiIhINhhMiIiISDYYTIiIiEg2GEyIqMFYLBb3gmCXcvToUaSmptb5uSdPnowjR464t/fs2eNR1wshaq4YTIio3mzbts29BgkAfPXVV+jQoUOtJbTP5/XXX8esWbPq9DppaWmYPXs2AgMD3fs+/PBDPP/887BarZddNxHJB4MJEV0Vm82G+fPnw2q1Qq/X17jw3vLly/HKK6/Ay8vLvc9ut8Plcrm327ZtCx8fH/z0009YsWKF+1oogYGB7gs6nmvevHkYNWoUIiIiAADJyclYv3497rjjDjz77LMN9E6JqDEwmBDRVXE4HHjkkUdqXbfl8OHD2Lp1K1566SUYDAb4+vrCy8sLvr6+2LBhg/s4b29vLF++HEVFRRg+fDjWrl2L0tJS5OTk1Lq6MACUlJRg3rx5uOuuuwAAVqsVDz30EGbPno1PPvkEBw8exNSpU8ElmoiaJgYTIroqZ8LDuSHitddewzvvvAOj0Qij0Yj7778f7733Hmw2G2666SaYzWY4HA53oFGpVBgxYgTmzZvnDhUKhQJCiBqngl5//XWUlJRAo9GgsrISw4cPR2JiIkaMGAG1Wo01a9Zgw4YNuOWWWy5rzAoRyQODCRHVuy1btmDPnj149913sXfvXgBAdnY2YmJiAAAHDhxAeHg4wsLCcOjQITz00EMwGAx48MEH8csvvyA0NBStWrVCeXk5QkJC3Kdsfv31VyxcuBC9e/dGVVUVrrnmGrRt2xYjR45EZWUlAMBoNOLtt99GREQEXnzxRWkagIiumFrqAojI86SkpODdd9/F999/j82bNyMxMREHDx5EQkICACAxMRHl5eUoKSlBZGQk0tPTsXfvXqSkpOCRRx6Bl5cX8vLy0K9fP2RmZrqfNyMjA2+88QY2btwIrVaLpUuXonv37oiIiMAff/yBNm3aYN26dfjrr7+wfPlyOJ1OiVqAiK4UgwkR1bsnnngCQPWpmI8++ghJSUlQKpVo1aqV+xin04mnn34agwcPRnBwMNq3b48vv/wSjz76KJ5++ml8/fXX8PX1rfG8jzzyCABg48aNAIDu3bsDAKqqqtCiRQsAgEajgUajAVB9eoiImhYGEyK6Kjt37gQADB06FKmpqTAYDO77hgwZgkcffRRTpkzByJEjazyusLAQTqcTs2fPBgBER0djwYIFsNlsyMzMRFFRET7++OM613H2zB8iaroYTIjoqhgMBjz++OMYOXIkdDodnnnmGfd9er0eI0aMwPz582utURIREYEvv/wSW7ZsgUqlwsmTJxEVFQWr1Yry8nKEhoZi7dq1SExMxPr16y/4+n///TciIyMb6u0RUSPj4Fciuirt2rXDp59+iuuuuw5qdc2/ddatW4cVK1YgIiICH330UY31S4DqXo5t27bh6NGjiIiIQGZmJr766isMHjwYmZmZmDlz5iV7Qj7//HPccMMN9f6+iEgaDCZEVG/OTPMtLS3FSy+9hBEjRuDDDz/E3r17sXHjRlxzzTVYtWoVzGYzANRa++R8zneM1WqFEALfffcd5s6di0mTJrnvc7lcXMOEqAnjqRwiqjc2mw02mw1PP/009u3bh99//929RP327dvx3nvv4YMPPsDPP/+MX375BUIIDBw4EEqlEuXl5YiNjYXdbofVakVsbCwsFgvMZjMCAwOxbNkyDB06FED1YFez2YyPP/4YixcvRuvWrWvVQERNk0LwTwsiqmcOhwNCCPfsmIYihKhTrwsRNR0MJkRERCQbHGNCREREssFgQkRERLLBYEJERESywWBCREREssFgQkRERLLBYEJERESywWBCREREssFgQkRERLLx/wF+3y8xEItcRwAAAABJRU5ErkJggg==", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import seaborn as sns\n", "import matplotlib.pyplot as plt\n", "# 设置中文显示\n", "plt.rcParams['font.family'] = ['SimHei']\n", "plt.rcParams['axes.unicode_minus'] = False\n", "# 预测结果数据\n", "predictions = [186.8574072889791, 186.257860664635, 184.10770494393964, 185.93719141281886, 184.57037449339362, \n", "               185.60661648948147, 183.3857326924576, 183.75000533180457, 183.10016909961496, 181.8261013581762, \n", "               179.94003987015927, 179.66272055047042, 178.45223991648865, 179.95636123056215, 180.48631322458314, \n", "               178.77667060084391, 177.62040380763523, 178.3089116835725, 175.0859834552677, 173.1733887952756]\n", "\n", "# 绘制Dist图（密度图）\n", "sns.histplot(data=predictions, kde=True)\n", "\n", "# 设置图表标题和轴标签\n", "plt.title('收盘价分布')\n", "plt.xlabel('收盘价')\n", "plt.ylabel('频数')\n", "\n", "# 显示图表\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": null, "id": "f075ad0b", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}