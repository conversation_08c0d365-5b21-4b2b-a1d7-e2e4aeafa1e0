{"cells": [{"cell_type": "code", "execution_count": 1, "id": "7f7d9aab", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["for循环实现的偶数的平方数列\n", "[0, 4, 16, 36, 64]\n"]}], "source": ["# 通过for循环实现的偶数的平方数列\n", "print('for循环实现的偶数的平方数列')\n", "n_list = []\n", "for x in range(10): \n", "    if x % 2 == 0: \n", "        n_list.append(x ** 2) \n", "print(n_list)"]}, {"cell_type": "code", "execution_count": null, "id": "b316120a", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}