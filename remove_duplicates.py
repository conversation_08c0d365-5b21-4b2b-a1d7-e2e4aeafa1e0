import tkinter as tk
from tkinter import filedialog
from tkinter import messagebox

def remove_duplicate_lines():
    # 创建主窗口（但不显示）
    root = tk.Tk()
    root.withdraw()

    # 打开文件选择对话框
    file_path = filedialog.askopenfilename(
        title="选择文本文件",
        filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")]
    )

    if not file_path:
        messagebox.showinfo("提示", "未选择文件")
        return

    try:
        # 读取文件内容
        with open(file_path, 'r', encoding='utf-8') as file:
            lines = file.readlines()

        # 去除重复行并保持原始顺序
        unique_lines = []
        seen = set()
        for line in lines:
            line = line.strip()
            if line and line not in seen:
                seen.add(line)
                unique_lines.append(line)

        # 保存结果到新文件
        output_path = file_path.rsplit('.', 1)[0] + '_去重.txt'
        with open(output_path, 'w', encoding='utf-8') as file:
            file.write('\n'.join(unique_lines))

        messagebox.showinfo("成功", f"处理完成！\n原始行数：{len(lines)}\n去重后行数：{len(unique_lines)}\n结果已保存至：{output_path}")

    except Exception as e:
        messagebox.showerror("错误", f"处理文件时出错：{str(e)}")

if __name__ == "__main__":
    remove_duplicate_lines() 