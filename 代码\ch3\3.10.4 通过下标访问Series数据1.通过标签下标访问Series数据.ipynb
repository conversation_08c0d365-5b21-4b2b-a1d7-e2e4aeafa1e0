{"cells": [{"cell_type": "code", "execution_count": 4, "id": "1256b71e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["3\n"]}], "source": ["import pandas as pd\n", "data = {'a' : 3, 'b' : 2, 'c' : 0, 'd' : 1}\n", "apples = pd.Series(data)\n", "# 通过标签下标访问数据\n", "print(apples['a'])"]}, {"cell_type": "code", "execution_count": null, "id": "ec0947f5", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}