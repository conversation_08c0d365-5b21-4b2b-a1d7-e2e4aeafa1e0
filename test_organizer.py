#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试文件整理工具
"""

try:
    import tkinter as tk
    print("tkinter 导入成功")
except ImportError as e:
    print(f"tkinter 导入失败: {e}")

try:
    from file_organizer import FileOrganizer
    print("FileOrganizer 导入成功")
    
    # 创建应用实例
    app = FileOrganizer()
    print("FileOrganizer 实例创建成功")
    
    # 运行应用
    print("启动应用...")
    app.run()
    
except Exception as e:
    print(f"错误: {e}")
    import traceback
    traceback.print_exc()
