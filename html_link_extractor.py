#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HTML解码链接提取器
用于从HTML文件中提取特定格式的解码链接，并汇总包含链接的行及其上下文
"""

import re
import os
import sys
from pathlib import Path
import argparse
from typing import List, Tuple, Dict
import tkinter as tk
from tkinter import filedialog, messagebox, ttk
import threading

class HTMLLinkExtractor:
    def __init__(self):
        # 定义各种链接格式的正则表达式模式
        self.patterns = [
            # showfilesbot_数字字母_字母数字组合 格式
            r'showfilesbot_\d+[VP]_[A-Za-z0-9]+',
            # showfilesbot_数字字母_数字字母_字母数字组合 格式  
            r'showfilesbot_\d+[VP]_\d+[VP]_[A-Za-z0-9]+',
            # 字母数字=_grp 格式
            r'[A-Za-z0-9]+=_grp',
            # @filepan_bot:_数字字母_数字字母_字母数字 格式
            r'@filepan_bot:_\d+[VP]_\d+[VP]_[A-Za-z0-9]+',
        ]
        
        # 编译正则表达式以提高性能
        self.compiled_patterns = [re.compile(pattern, re.IGNORECASE) for pattern in self.patterns]
    
    def extract_links_from_text(self, text: str) -> List[str]:
        """从文本中提取所有匹配的链接"""
        found_links = []
        for pattern in self.compiled_patterns:
            matches = pattern.findall(text)
            found_links.extend(matches)
        return found_links
    
    def process_html_file(self, file_path: str) -> List[Dict]:
        """处理单个HTML文件，返回包含链接的行及其上下文"""
        results = []
        
        try:
            # 尝试不同的编码格式读取文件
            encodings = ['utf-8', 'gbk', 'gb2312', 'ansi', 'latin-1']
            content = None
            used_encoding = None
            
            for encoding in encodings:
                try:
                    with open(file_path, 'r', encoding=encoding) as f:
                        content = f.read()
                        used_encoding = encoding
                        break
                except UnicodeDecodeError:
                    continue
            
            if content is None:
                print(f"警告: 无法读取文件 {file_path}，尝试了所有编码格式")
                return results
            
            lines = content.split('\n')
            
            for i, line in enumerate(lines):
                # 检查当前行是否包含目标链接
                found_links = self.extract_links_from_text(line)
                
                if found_links:
                    # 获取上下文（上一行和下一行）
                    context = {
                        'file_path': file_path,
                        'line_number': i + 1,
                        'encoding': used_encoding,
                        'links': found_links,
                        'previous_line': lines[i-1].strip() if i > 0 else '',
                        'current_line': line.strip(),
                        'next_line': lines[i+1].strip() if i < len(lines) - 1 else ''
                    }
                    results.append(context)
            
        except Exception as e:
            print(f"处理文件 {file_path} 时出错: {str(e)}")
        
        return results
    
    def process_directory(self, directory_path: str) -> List[Dict]:
        """处理目录中的所有HTML文件"""
        all_results = []
        html_files = []
        
        # 查找所有HTML文件
        for root, dirs, files in os.walk(directory_path):
            for file in files:
                if file.lower().endswith(('.html', '.htm')):
                    html_files.append(os.path.join(root, file))
        
        print(f"找到 {len(html_files)} 个HTML文件")
        
        for file_path in html_files:
            print(f"正在处理: {file_path}")
            results = self.process_html_file(file_path)
            all_results.extend(results)
        
        return all_results
    
    def save_results(self, results: List[Dict], output_file: str = 'extracted_links.txt'):
        """将结果保存到文件"""
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write("HTML解码链接提取结果\n")
                f.write("=" * 50 + "\n\n")
                
                for i, result in enumerate(results, 1):
                    f.write(f"结果 {i}:\n")
                    f.write(f"文件: {result['file_path']}\n")
                    f.write(f"行号: {result['line_number']}\n")
                    f.write(f"编码: {result['encoding']}\n")
                    f.write(f"找到的链接: {', '.join(result['links'])}\n")
                    f.write("-" * 30 + "\n")
                    
                    if result['previous_line']:
                        f.write(f"上一行: {result['previous_line']}\n")
                    
                    f.write(f"当前行: {result['current_line']}\n")
                    
                    if result['next_line']:
                        f.write(f"下一行: {result['next_line']}\n")
                    
                    f.write("\n" + "=" * 50 + "\n\n")
                
                f.write(f"\n总计找到 {len(results)} 个匹配项")
            
            print(f"结果已保存到: {output_file}")
            
        except Exception as e:
            print(f"保存结果时出错: {str(e)}")
    
    def print_summary(self, results: List[Dict]):
        """打印结果摘要"""
        if not results:
            print("未找到任何匹配的链接")
            return
        
        print(f"\n找到 {len(results)} 个匹配项:")
        print("-" * 50)
        
        # 统计各种链接类型
        link_types = {}
        for result in results:
            for link in result['links']:
                if 'showfilesbot_' in link:
                    link_type = 'showfilesbot'
                elif '_grp' in link:
                    link_type = 'grp格式'
                elif '@filepan_bot:' in link:
                    link_type = 'filepan_bot'
                else:
                    link_type = '其他'
                
                link_types[link_type] = link_types.get(link_type, 0) + 1
        
        print("链接类型统计:")
        for link_type, count in link_types.items():
            print(f"  {link_type}: {count} 个")
        
        print(f"\n涉及文件数: {len(set(result['file_path'] for result in results))}")

class HTMLLinkExtractorGUI:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("HTML解码链接提取器")
        self.root.geometry("600x500")
        self.root.resizable(True, True)

        # 设置窗口居中
        self.center_window()

        self.extractor = HTMLLinkExtractor()
        self.results = []

        self.setup_ui()

    def center_window(self):
        """窗口居中显示"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')

    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(3, weight=1)

        # 标题
        title_label = ttk.Label(main_frame, text="HTML解码链接提取器",
                               font=('Arial', 14, 'bold'))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))

        # 文件选择区域
        ttk.Label(main_frame, text="选择HTML文件或文件夹:").grid(row=1, column=0, sticky=tk.W, pady=5)

        self.path_var = tk.StringVar()
        path_entry = ttk.Entry(main_frame, textvariable=self.path_var, width=50)
        path_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), padx=(10, 5), pady=5)

        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=1, column=2, padx=(5, 0), pady=5)

        ttk.Button(button_frame, text="选择文件",
                  command=self.select_file).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="选择文件夹",
                  command=self.select_folder).pack(side=tk.LEFT)

        # 操作按钮
        action_frame = ttk.Frame(main_frame)
        action_frame.grid(row=2, column=0, columnspan=3, pady=20)

        ttk.Button(action_frame, text="开始提取",
                  command=self.start_extraction).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(action_frame, text="保存结果",
                  command=self.save_results).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(action_frame, text="清空结果",
                  command=self.clear_results).pack(side=tk.LEFT)

        # 结果显示区域
        result_frame = ttk.LabelFrame(main_frame, text="提取结果", padding="5")
        result_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 0))
        result_frame.columnconfigure(0, weight=1)
        result_frame.rowconfigure(0, weight=1)

        # 创建文本框和滚动条
        self.result_text = tk.Text(result_frame, wrap=tk.WORD, font=('Consolas', 9))
        scrollbar = ttk.Scrollbar(result_frame, orient=tk.VERTICAL, command=self.result_text.yview)
        self.result_text.configure(yscrollcommand=scrollbar.set)

        self.result_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))

        # 状态栏
        self.status_var = tk.StringVar()
        self.status_var.set("就绪")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var,
                              relief=tk.SUNKEN, anchor=tk.W)
        status_bar.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(10, 0))

    def select_file(self):
        """选择HTML文件"""
        file_path = filedialog.askopenfilename(
            title="选择HTML文件",
            filetypes=[
                ("HTML文件", "*.html *.htm"),
                ("所有文件", "*.*")
            ]
        )
        if file_path:
            self.path_var.set(file_path)

    def select_folder(self):
        """选择文件夹"""
        folder_path = filedialog.askdirectory(title="选择包含HTML文件的文件夹")
        if folder_path:
            self.path_var.set(folder_path)

    def start_extraction(self):
        """开始提取链接"""
        path = self.path_var.get().strip()
        if not path:
            messagebox.showwarning("警告", "请先选择HTML文件或文件夹")
            return

        if not os.path.exists(path):
            messagebox.showerror("错误", f"路径不存在: {path}")
            return

        # 在新线程中执行提取操作，避免界面卡顿
        self.status_var.set("正在提取链接...")
        self.result_text.delete(1.0, tk.END)

        def extract_worker():
            try:
                if os.path.isfile(path):
                    self.results = self.extractor.process_html_file(path)
                else:
                    self.results = self.extractor.process_directory(path)

                # 在主线程中更新UI
                self.root.after(0, self.display_results)

            except Exception as e:
                self.root.after(0, lambda: self.show_error(f"提取过程中出错: {str(e)}"))

        thread = threading.Thread(target=extract_worker)
        thread.daemon = True
        thread.start()

    def display_results(self):
        """显示提取结果"""
        self.result_text.delete(1.0, tk.END)

        if not self.results:
            self.result_text.insert(tk.END, "未找到任何匹配的解码链接")
            self.status_var.set("提取完成 - 未找到链接")
            return

        # 显示统计信息
        link_count = sum(len(result['links']) for result in self.results)
        file_count = len(set(result['file_path'] for result in self.results))

        summary = f"提取完成！\n"
        summary += f"找到 {len(self.results)} 个匹配项\n"
        summary += f"总链接数: {link_count}\n"
        summary += f"涉及文件数: {file_count}\n"
        summary += "=" * 60 + "\n\n"

        self.result_text.insert(tk.END, summary)

        # 显示详细结果
        for i, result in enumerate(self.results, 1):
            output = f"匹配项 {i}:\n"
            output += f"文件: {os.path.basename(result['file_path'])}\n"
            output += f"行号: {result['line_number']}\n"
            output += f"编码: {result['encoding']}\n"
            output += f"链接: {', '.join(result['links'])}\n"
            output += "-" * 40 + "\n"

            if result['previous_line']:
                output += f"上一行: {result['previous_line']}\n"

            output += f"当前行: {result['current_line']}\n"

            if result['next_line']:
                output += f"下一行: {result['next_line']}\n"

            output += "\n" + "=" * 60 + "\n\n"

            self.result_text.insert(tk.END, output)

        self.status_var.set(f"提取完成 - 找到 {len(self.results)} 个匹配项")

        # 滚动到顶部
        self.result_text.see(1.0)

    def save_results(self):
        """保存结果到文件"""
        if not self.results:
            messagebox.showwarning("警告", "没有结果可保存")
            return

        file_path = filedialog.asksaveasfilename(
            title="保存结果",
            defaultextension=".txt",
            filetypes=[
                ("文本文件", "*.txt"),
                ("所有文件", "*.*")
            ],
            initialvalue="解码链接提取结果.txt"
        )

        if file_path:
            try:
                self.extractor.save_results(self.results, file_path)
                messagebox.showinfo("成功", f"结果已保存到: {file_path}")
            except Exception as e:
                messagebox.showerror("错误", f"保存失败: {str(e)}")

    def clear_results(self):
        """清空结果"""
        self.result_text.delete(1.0, tk.END)
        self.results = []
        self.status_var.set("就绪")

    def show_error(self, message):
        """显示错误信息"""
        messagebox.showerror("错误", message)
        self.status_var.set("出现错误")

    def run(self):
        """运行GUI"""
        self.root.mainloop()

def main():
    parser = argparse.ArgumentParser(description='从HTML文件中提取解码链接')
    parser.add_argument('path', nargs='?', help='HTML文件路径或包含HTML文件的目录路径')
    parser.add_argument('-o', '--output', default='extracted_links.txt', help='输出文件名（默认: extracted_links.txt）')
    parser.add_argument('--no-save', action='store_true', help='不保存结果到文件，只显示在控制台')
    parser.add_argument('--gui', action='store_true', help='启动图形界面模式')

    args = parser.parse_args()

    # 如果没有提供参数或指定了GUI模式，启动图形界面
    if args.gui or (not args.path and len(sys.argv) == 1):
        try:
            app = HTMLLinkExtractorGUI()
            app.run()
            return
        except ImportError:
            print("错误: 无法启动图形界面，请安装tkinter")
            print("或者使用命令行模式: python html_link_extractor.py <path>")
            sys.exit(1)
        except Exception as e:
            print(f"图形界面启动失败: {str(e)}")
            print("切换到命令行模式...")

    # 命令行模式
    if not args.path:
        args.path = '.'

    extractor = HTMLLinkExtractor()

    # 检查路径是否存在
    if not os.path.exists(args.path):
        print(f"错误: 路径 '{args.path}' 不存在")
        sys.exit(1)

    # 处理文件或目录
    if os.path.isfile(args.path):
        if not args.path.lower().endswith(('.html', '.htm')):
            print(f"警告: '{args.path}' 不是HTML文件")
        results = extractor.process_html_file(args.path)
    else:
        results = extractor.process_directory(args.path)

    # 显示摘要
    extractor.print_summary(results)

    # 保存结果
    if not args.no_save and results:
        extractor.save_results(results, args.output)

    # 显示前几个结果作为预览
    if results:
        print(f"\n前3个匹配项预览:")
        print("-" * 50)
        for i, result in enumerate(results[:3], 1):
            print(f"{i}. 文件: {os.path.basename(result['file_path'])}")
            print(f"   行号: {result['line_number']}")
            print(f"   链接: {', '.join(result['links'])}")
            if result['previous_line']:
                print(f"   上一行: {result['previous_line'][:100]}...")
            print(f"   当前行: {result['current_line'][:100]}...")
            if result['next_line']:
                print(f"   下一行: {result['next_line'][:100]}...")
            print()

if __name__ == "__main__":
    main()
