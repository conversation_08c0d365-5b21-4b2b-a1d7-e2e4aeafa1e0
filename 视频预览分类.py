import os
import tkinter as tk
from tkinter import filedialog, messagebox
from PIL import Image, ImageTk
import cv2
import shutil  # 添加这一行
import time
import psutil  # 添加这一行
import csv
import threading
from concurrent.futures import ThreadPoolExecutor
from functools import lru_cache
import numpy as np
from queue import Queue
import weakref
import vlc

class FileManager:
    def __init__(self, video_player):
        self.video_player = video_player


    def delete_video(self, video_path):
        pass

class VideoPlayer:
    def __init__(self, master):
        self.master = master
        self.player = None
        self.is_playing = False
        self.frame_queue = Queue(maxsize=10)
        
    def play_video(self, video_path, label):
        self.stop_video()
        self.is_playing = True
        
        def video_worker():
            cap = cv2.VideoCapture(video_path)
            while self.is_playing:
                ret, frame = cap.read()
                if not ret:
                    break
                    
                frame = cv2.resize(frame, (400, 300))
                if not self.frame_queue.full():
                    self.frame_queue.put(frame)
            cap.release()
            
        def update_label():
            if not self.is_playing:
                return
            if not self.frame_queue.empty():
                frame = self.frame_queue.get()
                img = Image.fromarray(cv2.cvtColor(frame, cv2.COLOR_BGR2RGB))
                img = ImageTk.PhotoImage(img)
                label.config(image=img)
                label.image = img
            self.master.after(30, update_label)
            
        threading.Thread(target=video_worker, daemon=True).start()
        update_label()

    def stop_video(self):
        self.is_playing = False
        while not self.frame_queue.empty():
            self.frame_queue.get()

VIDEO_CATEGORIES = ["风格", "人物", "标签","收藏","删除" ]
DEFAULT_CATEGORY_VALUE = "..."  # 默认值
class ThumbnailReference:
    def __init__(self, label, image, path):
        self.label = label
        self.image = image
        self.path = path

class VideoPreviewer:
    def __init__(self, master):
        self.master = master
        self.master.title("Video Previewer")

        self.folder_path = ""
        self.current_page = 1
        self.videos_per_page = 12

        self.thumbnail_labels = []
        self.thumbnail_refs = []  # 添加新的列表存储引用

        self.video_player = VideoPlayer(master)
        self.file_manager = FileManager(self.video_player)

        self.video_files_profiles = {}  # 添加这一行，用于保存视频文件的档案信息

        self.delete_confirmation = None  # 用于保存删除确认结果

        self.data_file_path = None  # 不在初始化时创建CSV文件路径

        self.thumbnail_cache = {}  # 添加缩略图缓存
        self.thread_pool = ThreadPoolExecutor(max_workers=4)  # 添加线程池

        self.create_widgets()

        # 创建CSV文件
        self.data_file_path = os.path.join(self.folder_path, "video_data.csv")
        self.create_csv_file()

        # 读取已存在的CSV文件内容
        self.load_existing_data()

    def delete_video(self):
        if hasattr(self.context_menu, 'video_path') and self.context_menu.video_path:
            # 弹出选择框，询问用户是否删除
            result = messagebox.askyesno("删除确认", "确定要删除吗？")

            if result:
                self.file_manager.delete_video(self.context_menu.video_path)
                self.clear_canvas()
                self.show_video_preview()


                # 记录删除操作到 CSV 文件
                self.record_delete_operation(os.path.basename(self.context_menu.video_path), result)

    def record_delete_operation(self, video_name, delete_result):
        # 记录删除操作到 CSV 文件
        if self.data_file_path is not None:
            try:
                with open(self.data_file_path, mode='a', newline='', encoding='utf-8') as file:
                    writer = csv.writer(file)
                    writer.writerow([video_name, "", "", "", "","去"])
            except Exception as e:
                print(f"Error recording delete operation: {str(e)}")


    def stop_video(self):
        self.video_player.stop_video()

    def create_widgets(self):
        self.canvas = tk.Canvas(self.master, width=800, height=600)
        self.canvas.pack()

        self.show_video_preview()

        button_frame = tk.Frame(self.master)
        button_frame.pack(side=tk.BOTTOM, pady=20)

        prev_button = tk.Button(button_frame, text="上一页", command=self.show_prev_page)
        prev_button.pack(side=tk.LEFT, padx=(20, 10))

        open_button = tk.Button(button_frame, text="打开", command=self.open_folder)
        open_button.pack(side=tk.LEFT, padx=10)

        next_button = tk.Button(button_frame, text="下一页", command=self.show_next_page)
        next_button.pack(side=tk.LEFT, padx=(10, 20))


        # 创建右键菜单
        self.context_menu = tk.Menu(self.master, tearoff=0)

        self.context_menu.add_command(label="收藏", command=lambda: self.record_selection('收藏', '藏'))  # 添加"收藏"选项

        self.context_menu.add_separator()  # 添加分隔线

        # 添加 "风格" 子菜单
        style_menu = tk.Menu(self.context_menu, tearoff=0)
        styles = ["日本", "孕妇", "野外", "自慰", "绿帽", "肛交", "尿道", "撒尿","口交"]
        for style in styles:
            style_menu.add_command(label=style, command=lambda s=style: self.record_selection('风格', s))
        self.context_menu.add_cascade(label="风格", menu=style_menu)

        # 添加 "人物" 子菜单
        character_menu = tk.Menu(self.context_menu, tearoff=0)
        characters = ["熟女", "老师", "OL", "黑人", "医护"]
        for character in characters:
            character_menu.add_command(label=character,
                                       command=lambda c=character: self.record_selection('人物', c))
        self.context_menu.add_cascade(label="人物", menu=character_menu)

        # 添加 "标签" 子菜单
        tag_menu = tk.Menu(self.context_menu, tearoff=0)
        tags = ["口爆", "白浆", "软肉", "大洞", "肉芽", "标签6", "标签7", "标签8"]
        for tag in tags:
            tag_menu.add_command(label=tag, command=lambda t=tag: self.record_selection('标签', t))
        self.context_menu.add_cascade(label="标签", menu=tag_menu)

        self.context_menu.add_separator()  # 添加分隔线

        self.context_menu.add_command(label="删除", command=lambda: self.record_selection('删除', '去'))

        # 绑定右键菜单到缩略图标签
        for label, _, _ in self.thumbnail_labels:
            label.bind("<Button-3>", self.show_context_menu)

    def open_folder(self):
        self.folder_path = filedialog.askdirectory()

        if self.folder_path:
            self.current_page = 1
            self.data_file_path = os.path.join(self.folder_path, "video_data.csv")  # 更新CSV文件路径
            self.show_video_preview()

            # 检查视频文件夹中是否存在CSV文件，如果存在则加载数据
            if os.path.exists(self.data_file_path):
                self.load_existing_data()

    def create_csv_file(self):
        # 创建CSV文件
        if self.data_file_path is not None and not os.path.exists(self.data_file_path):
            with open(self.data_file_path, mode='w', newline='', encoding='utf-8') as file:  # 使用 utf-8 编码
                writer = csv.writer(file)
                writer.writerow(['视频文件名'] + VIDEO_CATEGORIES)

    def load_existing_data(self):
        # 读取已存在的CSV文件内容
        with open(self.data_file_path, mode='r', encoding='utf-8') as file:
            reader = csv.reader(file)
            next(reader)  # 跳过表头
            for row in reader:
                video_name = row[0]
                style = row[1]
                person = row[2]
                label = row[3]

    def show_video_preview(self):
        if not self.folder_path:
            return
            
        def load_thumbnails():
            videos = self.get_video_files()
            start_index = (self.current_page - 1) * self.videos_per_page
            end_index = start_index + self.videos_per_page
            videos_to_display = videos[start_index:end_index]
            
            futures = []
            for video in videos_to_display:
                video_path = os.path.join(self.folder_path, video)
                future = self.thread_pool.submit(self.generate_thumbnail, video_path)
                futures.append((video, future))
                
            return futures
            
        futures = load_thumbnails()
        row, col = 0, 0
        self.thumbnail_labels = []
        self.thumbnail_refs = []  # 清空引用列表
        
        for video, future in futures:
            try:
                thumbnail = future.result()
                if thumbnail:
                    video_path = os.path.join(self.folder_path, video)
                    thumbnail_image = ImageTk.PhotoImage(thumbnail)
                    
                    label = tk.Label(self.canvas, image=thumbnail_image, 
                                   text=video[:40] + "..." if len(video) > 40 else video,
                                   compound=tk.TOP)
                    label.grid(row=row, column=col, padx=10, pady=10)
                    
                    # 创建引用对象并存储
                    ref_obj = ThumbnailReference(label, thumbnail_image, video_path)
                    self.thumbnail_labels.append((label, thumbnail_image, video_path))
                    self.thumbnail_refs.append(weakref.ref(ref_obj))
                    
                    label.bind("<Enter>", 
                             lambda event, path=video_path, img=thumbnail_image: 
                             self.show_preview(event, path, img))
                    label.bind("<Button-3>", 
                             lambda event, path=video_path: 
                             self.show_context_menu(event, path))
                    
                    col += 1
                    if col == 4:
                        col = 0
                        row += 1
                        
            except Exception as e:
                print(f"Error loading thumbnail: {e}")

    def show_preview(self, event, video_path, thumbnail_image):
        for label, img, _ in self.thumbnail_labels:
            label.config(image=img)

        cap = cv2.VideoCapture(video_path)

        while True:
            ret, frame = cap.read()
            if not ret:
                break

            frame = cv2.resize(frame, (400, 300))
            img = Image.fromarray(cv2.cvtColor(frame, cv2.COLOR_BGR2RGB))
            img = ImageTk.PhotoImage(img)

            event.widget.config(image=img)
            event.widget.image = img

            self.master.update()
            self.master.update_idletasks()

        cap.release()

    @lru_cache(maxsize=100)
    def generate_thumbnail(self, video_path):
        thumbnail_size = (400, 300)
        try:
            video_capture = cv2.VideoCapture(video_path)
            total_frames = int(video_capture.get(cv2.CAP_PROP_FRAME_COUNT))
            target_frame = min(900, total_frames - 1)
            video_capture.set(cv2.CAP_PROP_POS_FRAMES, target_frame)
            
            ret, frame = video_capture.read()
            video_capture.release()
            
            if ret:
                frame = cv2.resize(frame, thumbnail_size)
                thumbnail = Image.fromarray(cv2.cvtColor(frame, cv2.COLOR_BGR2RGB))
                return thumbnail
        except Exception as e:
            print(f"Error generating thumbnail: {e}")
        return None

    def get_video_files(self):
        video_extensions = ['.mp4', '.avi', '.mkv', '.mov', '.ts']
        video_files = [file for file in os.listdir(self.folder_path) if
                       os.path.isfile(os.path.join(self.folder_path, file)) and
                       any(file.lower().endswith(ext) for ext in video_extensions)]
        return video_files

    def show_prev_page(self):
        if self.current_page > 1:
            self.current_page -= 1
            self.clear_canvas()
            self.show_video_preview()

    def show_next_page(self):
        videos = self.get_video_files()
        total_pages = (len(videos) + self.videos_per_page - 1) // self.videos_per_page

        if self.current_page < total_pages:
            self.current_page += 1
            self.clear_canvas()
            self.show_video_preview()

    def clear_canvas(self):
        # 清理弱引用
        self.thumbnail_refs = [ref for ref in self.thumbnail_refs if ref() is not None]
        # 清理画布
        for widget in self.canvas.winfo_children():
            widget.destroy()
        # 清理标签列表
        self.thumbnail_labels = []

    def delete_video(self):
        if hasattr(self.context_menu, 'video_path') and self.context_menu.video_path:
            # 弹出选择框，询问用户是否删除
            result = messagebox.askyesno("删除确认", "确定要删除吗？")

            if result:
                self.file_manager.delete_video(self.context_menu.video_path)
                self.clear_canvas()
                self.show_video_preview()

                # 记录删除操作到 CSV 文件
                self.record_delete_operation(os.path.basename(self.context_menu.video_path), result)


    def show_context_menu(self, event, video_path=None):
        self.context_menu.video_path = video_path
        self.context_menu.post(event.x_root, event.y_root)

        # 根据视频文件路径获取或创建档案信息
        if video_path:
            if video_path not in self.video_files_profiles:
                self.video_files_profiles[video_path] = {category: DEFAULT_CATEGORY_VALUE for category in
                                                         VIDEO_CATEGORIES}
                self.load_profile(video_path)

    def favorite_video(self):
        if hasattr(self.context_menu, 'video_path') and self.context_menu.video_path:
            self.favorite_video_operation(self.context_menu.video_path)
            self.clear_canvas()
            self.show_video_preview()


    def apply_style(self, selected_style):
        # 根据所选的风格执行相应的操作
        self.record_selection("风格", selected_style)

    def detect_person(self, selected_person):
        # 根据所选的人物执行相应的操作
        self.record_selection("人物", selected_person)

    def add_label(self, selected_label):
        # 根据所选的标签执行相应的操作
        self.record_selection("标签", selected_label)

    def record_selection(self, category, value):
        # 记录用户选择到CSV文件
        if hasattr(self.context_menu, 'video_path') and self.context_menu.video_path:
            video_name = os.path.basename(self.context_menu.video_path)
            csv_row = [video_name, "-", "-", "-", "-","-"]  # 默认为"-"

            # 读取CSV文件中的内容
            rows = []
            if os.path.exists(self.data_file_path):
                with open(self.data_file_path, mode='r') as read_file:
                    reader = csv.reader(read_file)
                    rows = list(reader)

            # 更新或添加新的信息
            for row in rows:
                if row[0] == video_name:
                    row[VIDEO_CATEGORIES.index(category) + 1] = value
                    break
            else:
                csv_row[VIDEO_CATEGORIES.index(category) + 1] = value
                rows.append(csv_row)

            # 写入CSV文件
            with open(self.data_file_path, mode='w', newline='') as file:
                writer = csv.writer(file)
                writer.writerows(rows)

            # 更新内存中的档案信息
            self.video_files_profiles[self.context_menu.video_path][category] = value

    def load_profile(self, video_path):
        # 从CSV文件加载信息
        video_name = os.path.basename(video_path)

        if os.path.exists(self.data_file_path):
            with open(self.data_file_path, mode='r', encoding='utf-8') as file:
                reader = csv.reader(file)
                next(reader)  # 跳过表头
                for row in reader:
                    if len(row) > 0 and row[0] == video_name:
                        for category, value in zip(VIDEO_CATEGORIES, row[1:]):
                            self.video_files_profiles[video_path][category] = value
                        break



    def close_video_preview(self, video_path):
        for label, _, path in self.thumbnail_labels:
            if path == video_path:
                try:
                    label.destroy()
                except tk.TclError as e:
                    print(f"Error closing preview: {e}")
                break

if __name__ == "__main__":
    root = tk.Tk()
    app = VideoPreviewer(root)
    root.geometry("+300+300")
    root.mainloop()