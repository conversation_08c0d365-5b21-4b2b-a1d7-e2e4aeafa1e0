{"cells": [{"cell_type": "code", "execution_count": 5, "id": "8584583c", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["所有买入信号的日期： Series([], Name: Date, dtype: datetime64[ns])\n", "所有卖出信号的日期： 5    2023-06-15\n", "6    2023-06-14\n", "7    2023-06-13\n", "8    2023-06-12\n", "9    2023-06-09\n", "10   2023-06-08\n", "11   2023-06-07\n", "12   2023-06-06\n", "13   2023-06-05\n", "14   2023-06-02\n", "15   2023-06-01\n", "16   2023-05-31\n", "17   2023-05-30\n", "18   2023-05-26\n", "19   2023-05-25\n", "Name: Date, dtype: datetime64[ns]\n"]}], "source": ["# 导入需要的库\n", "import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "\n", "plt.rcParams['font.family'] = ['SimHei']  # 设置中文字体\n", "plt.rcParams['axes.unicode_minus'] = False  # 设置负号显示\n", "\n", "\n", "# 从文件中读取数据,并进行数据类型转换\n", "df = pd.read_csv('data/AAPL.csv', parse_dates=['Date'])\n", "df['Close'] = df['Close'].str.replace('$', '').astype(float)\n", "\n", "df['MA5'] = df['Close'].rolling(5,min_periods=1).mean()\n", "df['MA20'] = df['Close'].rolling(20,min_periods=1).mean()\n", "\n", "\n", "# 买入信号\n", "def buy_signal(row):   \n", "    if row.MA5 > row.MA20:\n", "        return True\n", "    else: \n", "        return False\n", "\n", "# 卖出信号\n", "def sell_signal(row):\n", "    if row.MA5 < row.MA20 or row['Close'] < row.MA20 * 0.92:\n", "        return True\n", "    else:\n", "        return False\n", "\n", "# 找出所有买入信号的日期\n", "buy_dates = df[df.apply(buy_signal, axis=1)]['Date']\n", "\n", "# 找出所有卖出信号的日期\n", "sell_dates = df[df.apply(sell_signal, axis=1)]['Date']\n", "\n", "# 打印买入和卖出信号\n", "print(\"所有买入信号的日期：\", buy_dates)\n", "print(\"所有卖出信号的日期：\", sell_dates)"]}, {"cell_type": "markdown", "id": "fd73d4eb", "metadata": {}, "source": ["### 绘制价格和信号图表"]}, {"cell_type": "code", "execution_count": 6, "id": "b5cf0446", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1200x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 绘制价格和信号图表\n", "plt.figure(figsize=(12, 6))\n", "plt.plot(df['Date'], df['Close'], label='Close Price')\n", "plt.scatter(buy_dates, df[df['Date'].isin(buy_dates)]['Close'], color='green', marker='^', label='Buy Signal')\n", "plt.scatter(sell_dates, df[df['Date'].isin(sell_dates)]['Close'], color='red', marker='v', label='Sell Signal')\n", "plt.title('贵州茅台股票价格和交易信号')\n", "plt.xlabel('日期')\n", "plt.ylabel('股价')\n", "plt.legend()\n", "plt.grid(True)\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": null, "id": "cba4e964", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}