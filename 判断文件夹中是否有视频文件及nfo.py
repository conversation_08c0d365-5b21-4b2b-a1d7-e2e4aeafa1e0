import os
import shutil
import tkinter as tk
from tkinter import filedialog
from xml.etree import ElementTree as ET

def select_folder_dialog():
    root = tk.Tk()
    root.withdraw()
    folder_path = filedialog.askdirectory(title="选择文件夹")
    return folder_path

def search_first_level_folders_for_nfo(folder_path):
    if not folder_path:
        print("未选择文件夹。")
        return

    work_dir = os.path.join(folder_path, "work")
    os.makedirs(work_dir, exist_ok=True)

    dirs = [d for d in os.listdir(folder_path) if os.path.isdir(os.path.join(folder_path, d))]

    for dir_name in dirs:
        dir_path = os.path.join(folder_path, dir_name)
        nfo_files = [f for f in os.listdir(dir_path) if f.endswith(".nfo")]
        video_files = [f for f in os.listdir(dir_path) if f.endswith((".mp4", ".mkv", ".avi", ".wmv"))]

        if not nfo_files or not video_files:
            shutil.move(dir_path, work_dir)
            print(f"移动文件夹 {dir_name} 到 'work' 文件夹中。")
        else:
            nfo_file_path = os.path.join(dir_path, nfo_files[0])
            studio_content = extract_studio_from_nfo(nfo_file_path)
            if not studio_content:
                shutil.move(dir_path, work_dir)
                print(f"移动文件夹 {dir_name} 到 'work' 文件夹中。")

def extract_studio_from_nfo(nfo_file_path):
    tree = ET.parse(nfo_file_path)
    root = tree.getroot()
    studio_element = root.find(".//studio")
    if studio_element is not None and studio_element.text:
        return studio_element.text
    return None

if __name__ == "__main__":
    folder_path = select_folder_dialog()
    search_first_level_folders_for_nfo(folder_path)
