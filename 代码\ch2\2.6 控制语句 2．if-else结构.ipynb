{"cells": [{"cell_type": "code", "execution_count": 1, "id": "48a6d755", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["及格\n"]}], "source": ["score = 95\n", "if score >= 60:\n", "    print(\"及格\")\n", "else:\n", "    print(\"不及格\")"]}, {"cell_type": "code", "execution_count": null, "id": "4b9e13ae", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}