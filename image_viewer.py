import json
import os
from tkinter import Tk, Label, Canvas, filedialog, Button, Scrollbar, HORIZONTAL, VERTICAL, Frame, messagebox
from PIL import Image, ImageTk
import tkinter as tk

class ImageViewer:
    def __init__(self, master):
        self.folder_selection_count = {}
        self.folder_path = None
        self.master = master
        self.master.title("Image Viewer")

        self.current_image_index = 0
        self.image_files = []
        self.image_scale = 1.0  # 初始缩放比例

        self.create_widgets()
        self.master.geometry("1280x1100")  # 设置窗口大小与屏幕一样大

        self.thumbnail_cache = {}
        self.visible_thumbnails = 1000  # 设置可见的缩略图数量
        self.first_visible_index = 0  # 设置第一个可见的缩略图索引
        self.get_next_batch_of_thumbnails = {}

        # 绑定缩略图滚动事件到处理函数上
        self.thumbnail_canvas.bind("<Configure>", self.on_thumbnail_scroll)

        # 常用文件夹记录
        self.common_folders = []
        self.load_common_folders()  # 在初始化时加载常用文件夹记录

        # 常用文件夹选择窗口
        self.move_to_window = None

    def create_widgets(self):
        # 主Canvas用于显示当前图像
        self.canvas = Canvas(self.master, width=1280, height=850)  # 设置Canvas大小与屏幕一样大
        self.canvas.pack()

        # 缩略图Canvas和滚动条
        self.thumbnail_canvas = Canvas(self.master, height=150)
        self.thumbnail_canvas.pack(side="bottom", fill="x")

        self.thumbnail_scrollbar = Scrollbar(self.master, orient=HORIZONTAL, command=self.thumbnail_canvas.xview)
        self.thumbnail_scrollbar.pack(side="bottom", fill="x")

        self.thumbnail_canvas.config(xscrollcommand=self.thumbnail_scrollbar.set)

        self.label = Label(self.master)
        self.label.pack()


        # 打开文件夹按钮
        self.open_button = Button(self.master, text="打开文件夹", command=self.open_folder)
        self.open_button.pack(side="left", padx=(450, 50))  # 设置为左对齐，并在左侧添加 10 像素的空白，右侧添加 5 像素的空白

        # 添加删除小图片按钮
        self.delete_small_button = Button(self.master, text="删除小图片", command=self.delete_small_images )
        self.delete_small_button.pack(side="left", padx=(30, 10))  # 设置为左对齐，并在左侧添加 5 像素的空白，右侧添加 10 像素的空白

        # 移动到按钮
        self.move_to_button = Button(self.master, text="移动到", command=self.show_move_to_panel)
        self.move_to_button.pack(side="left", padx=(60, 10))

        # 初始化缩略图列表
        self.thumbnail_frame = Frame(self.thumbnail_canvas)
        self.thumbnail_canvas.create_window((0, 0), window=self.thumbnail_frame, anchor="nw")
        self.thumbnail_frame.bind("<Configure>", self.update_thumbnail_scrollregion)

        # 绑定鼠标滚轮事件
        self.master.bind("<MouseWheel>", self.on_mouse_wheel)
        # 绑定右键删除事件
        self.canvas.bind("<Button-3>", self.delete_image)
        # 绑定键盘事件
        self.master.bind("<KeyRelease-minus>", self.zoom_out)
        self.master.bind("<KeyRelease-plus>", self.zoom_in)

    def open_folder(self):
        folder_path = filedialog.askdirectory()
        if folder_path:
            self.image_files = self.get_all_image_files(folder_path)
            self.current_image_index = 0
            self.show_image(self.label)
            self.show_thumbnails()

            # 将 folder_path 传递给 delete_small_images 方法
            self.delete_small_images(folder_path)

    def get_all_image_files(self, folder_path):
        all_image_files = []
        for root, dirs, files in os.walk(folder_path):
            for file in files:
                if file.lower().endswith(('.png', '.jpg', '.jpeg', '.gif', '.bmp')):
                    all_image_files.append(os.path.join(root, file))
        return all_image_files


    def show_image(self, label, scale_factor_wid=None, scale_factor_hei=None):
        if self.image_files:
            image_path = self.image_files[self.current_image_index]
            image = Image.open(image_path)
            MAX_WIDTH = 1280
            MAX_HEIGHT = 850

            # 获取原始图像大小
            original_width = image.width
            original_height = image.height

            # 计算缩放比例，保持原始比例
            #if MAX_WIDTH and original_width > MAX_WIDTH:
            scale_factor_wid = MAX_WIDTH / original_width
            #elif MAX_HEIGHT and original_height > MAX_HEIGHT:
            scale_factor_hei = MAX_HEIGHT / original_height
            #else:
            scale_factor = min(scale_factor_wid, scale_factor_hei)

            scaled_width = int(original_width * scale_factor)
            scaled_height = int(original_height * scale_factor)

            image = image.resize((scaled_width, scaled_height), Image.LANCZOS)

            photo = ImageTk.PhotoImage(image)

            self.canvas.config(width=scaled_width, height=scaled_height)
            self.canvas.create_image(0, 0, anchor="nw", image=photo)

            label_text = os.path.basename(image_path)
            #label.config(text=label_text)
            self.master.title("Image Viewer - " + label_text)
            label.update()  # 确保 Label 的更新

            self.canvas.image = photo

    def show_prev_image(self):
        if self.image_files:
            self.current_image_index = (self.current_image_index - 1) % len(self.image_files)
            self.show_image(self.label)

    def show_next_image(self):
        if self.image_files:
            self.current_image_index = (self.current_image_index + 1) % len(self.image_files)
            self.show_image(self.label)

    def delete_image(self, event):
        if self.image_files:
            image_path = self.image_files[self.current_image_index]
            os.remove(image_path)
            self.image_files.pop(self.current_image_index)

            if not self.image_files:
                self.label.config(text="No Image")
                self.canvas.delete("image")
            else:
                self.show_image(self.label)
                #self.show_thumbnails()

    def delete_small_images(self, folder_path):
        if folder_path is None:
            folder_path = self.folder_path
        #print(folder_path)

        if folder_path:
            deleted_files = []  # 用于存储已删除的文件信息

            for root, dirs, files in os.walk(folder_path):
                for filename in files:
                    if filename.endswith('.jpg') or filename.endswith('.png'):
                        filepath = os.path.join(root, filename)
                        with Image.open(filepath) as im:
                            # 获取图像分辨率
                            width, height = im.size

                        # 打印文件信息以进行调试
                        #print(f"文件: {filename}, 宽度: {width}, 高度: {height}")

                        # 如果分辨率小于 800*800，就删除文件
                        if width < 800 or height < 800:
                            os.remove(filepath)
                            deleted_files.append(filepath)  # 记录已删除的文件

            # 打印已删除的文件信息
            if deleted_files:
                for file in deleted_files:
                    print(f"已删除文件: {file}")
                #self.info_label.config(text="删除小图片操作已完成")
            else:
                print("未删除任何文件")
                #self.info_label.config(text="未删除任何文件")

    def show_thumbnails(self):
        # 清空缩略图列表
        for widget in self.thumbnail_frame.winfo_children():
            widget.destroy()

        # 显示缩略图
        last_visible_index = min(self.first_visible_index + self.visible_thumbnails, len(self.image_files))
        for i in range(self.first_visible_index, last_visible_index):
            thumbnail = self.get_thumbnail(self.image_files[i], width=150, index=i)
            thumbnail_label = Label(self.thumbnail_frame, image=thumbnail)
            thumbnail_label.grid(row=0, column=i - self.first_visible_index, padx=5)
            thumbnail_label.bind("<Button-1>", lambda event, index=i: self.thumbnail_clicked(index))

        # 更新缩略图列表的尺寸
        self.thumbnail_frame.update_idletasks()
        self.thumbnail_canvas.config(scrollregion=self.thumbnail_frame.bbox("all"))

    # 在滚动时更新可见的缩略图范围
    def on_thumbnail_scroll(self, event):
        scrollbar_position = self.thumbnail_scrollbar.get()[1]
        if scrollbar_position >= 0.9:  # 当滚动条接近底部时加载更多
            self.load_more_thumbnails()

    def load_more_thumbnails(self):
        # 根据需要加载更多缩略图
        new_thumbnails = self.get_next_batch_of_thumbnails()

        # 将新缩略图添加到现有列表
        self.image_files.extend(new_thumbnails)

        # 更新显示缩略图
        self.show_thumbnails()

    def get_thumbnail(self, image_path, width, index=None):
        image = Image.open(image_path)
        image.thumbnail((width * self.image_scale, width * self.image_scale))
        thumbnail = ImageTk.PhotoImage(image)

        thumbnail_label = Label(self.thumbnail_frame, image=thumbnail)
        thumbnail_label.grid(row=0, column=index, padx=5)
        thumbnail_label.bind("<Button-1>", lambda event, idx=index: self.thumbnail_clicked(idx))




        # 保存对 thumbnail 的引用
        thumbnail_label.thumbnail = thumbnail

        return thumbnail

    def thumbnail_clicked(self, index):
        self.current_image_index = index
        self.show_image(self.label)

    def update_thumbnail_scrollregion(self, event):
        self.thumbnail_canvas.config(scrollregion=self.thumbnail_frame.bbox("all"))

    def on_mouse_wheel(self, event):
        delta = event.delta
        if delta > 0:
            self.show_prev_image()
        elif delta < 0:
            self.show_next_image()

    def zoom_out(self, event):
        # 缩小图像，限制最小缩放比例
        self.image_scale -= 0.05
        self.show_image(self.label)

    def zoom_in(self, event):
        # 放大图像
        self.image_scale += 0.05
        self.show_image(self.label)

    def show_move_to_panel(self):
        self.move_to_window = tk.Toplevel(self.master)
        self.move_to_window.title("移动到")

        # 添加常用文件夹按钮
        for folder in self.common_folders:
            folder_button = tk.Button(self.move_to_window, text=folder, command=lambda f=folder: self.move_to_folder(f))
            folder_button.pack(side=tk.TOP, padx=10, pady=5)

        # 添加其他选项按钮
        other_button = tk.Button(self.move_to_window, text="其他", command=self.move_to_folder)
        other_button.pack(side=tk.TOP, padx=10, pady=5)

    def move_to_folder(self, destination_folder=None):
        if destination_folder is None:
            destination_folder = filedialog.askdirectory()

        if destination_folder:
            # 记录选择次数
            if destination_folder in self.folder_selection_count:
                self.folder_selection_count[destination_folder] += 1
            else:
                self.folder_selection_count[destination_folder] = 1

            current_image_path = self.image_files[self.current_image_index]
            new_image_path = os.path.join(destination_folder, os.path.basename(current_image_path))

            try:
                # 移动文件
                os.rename(current_image_path, new_image_path)
                messagebox.showinfo("移动成功", f"图片已成功移动到:\n{new_image_path}")

                # 清除文件列表
                self.image_files.pop(self.current_image_index)

                # 记录常用文件夹
                if destination_folder not in self.common_folders:
                    self.common_folders.append(destination_folder)
                    self.save_common_folders()

                if not self.image_files:
                    self.label.config(text="No Image")
                    self.canvas.delete("image")
                else:
                    self.show_image(self.label)
                    self.show_thumbnails()

                # 关闭常用文件夹选择窗口
                if self.move_to_window:
                    self.move_to_window.destroy()

            except Exception as e:
                messagebox.showerror("移动失败", f"移动图片时出错:\n{str(e)}")

            if self.folder_selection_count[destination_folder] % 5 == 0:
                self.sort_folders_by_selection_count()

    def sort_folders_by_selection_count(self):
        # 按照选择次数对文件夹排序
        sorted_folders = sorted(self.folder_selection_count.keys(), key=lambda x: self.folder_selection_count[x],
                                reverse=True)

        # 更新常用文件夹列表
        self.common_folders = sorted_folders

        # 更新播放列表和 current_index
        #self.update_file_list()

        # 检查是否需要更新 current_index
        #if self.current_index >= len(self.video_files):
        #    self.current_index = len(self.video_files) - 1

        # 播放下一个视频
        #self.play_selected_video()

        # 关闭常用文件夹选择窗口
        if self.move_to_window:
            self.move_to_window.destroy()

    def save_common_folders(self):
        with open('common_folders_pic.json', 'w') as file:
            json.dump(self.common_folders, file)

    def load_common_folders(self):
        try:
            with open('common_folders_pic.json', 'r') as file:
                self.common_folders = json.load(file)
        except FileNotFoundError:
            pass

if __name__ == "__main__":
    root = Tk()
    app = ImageViewer(root)
    root.mainloop()
