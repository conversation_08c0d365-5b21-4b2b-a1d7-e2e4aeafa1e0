{"cells": [{"cell_type": "code", "execution_count": 3, "id": "cad403af", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ts_code</th>\n", "      <th>trade_date</th>\n", "      <th>open</th>\n", "      <th>high</th>\n", "      <th>low</th>\n", "      <th>close</th>\n", "      <th>pre_close</th>\n", "      <th>change</th>\n", "      <th>pct_chg</th>\n", "      <th>vol</th>\n", "      <th>amount</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>600519.SH</td>\n", "      <td>20230626</td>\n", "      <td>1720.11</td>\n", "      <td>1730.00</td>\n", "      <td>1695.00</td>\n", "      <td>1709.00</td>\n", "      <td>1735.83</td>\n", "      <td>-26.83</td>\n", "      <td>-1.5457</td>\n", "      <td>23992.68</td>\n", "      <td>4098619.510</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>600519.SH</td>\n", "      <td>20230621</td>\n", "      <td>1740.00</td>\n", "      <td>1756.60</td>\n", "      <td>1735.00</td>\n", "      <td>1735.83</td>\n", "      <td>1743.46</td>\n", "      <td>-7.63</td>\n", "      <td>-0.4376</td>\n", "      <td>17720.61</td>\n", "      <td>3088635.934</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>600519.SH</td>\n", "      <td>20230620</td>\n", "      <td>1740.00</td>\n", "      <td>1765.00</td>\n", "      <td>1735.00</td>\n", "      <td>1743.46</td>\n", "      <td>1744.00</td>\n", "      <td>-0.54</td>\n", "      <td>-0.0310</td>\n", "      <td>20946.74</td>\n", "      <td>3659824.529</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>600519.SH</td>\n", "      <td>20230619</td>\n", "      <td>1790.00</td>\n", "      <td>1797.95</td>\n", "      <td>1738.00</td>\n", "      <td>1744.00</td>\n", "      <td>1797.69</td>\n", "      <td>-53.69</td>\n", "      <td>-2.9866</td>\n", "      <td>31699.92</td>\n", "      <td>5584248.725</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>600519.SH</td>\n", "      <td>20230616</td>\n", "      <td>1757.00</td>\n", "      <td>1800.00</td>\n", "      <td>1750.10</td>\n", "      <td>1797.69</td>\n", "      <td>1755.00</td>\n", "      <td>42.69</td>\n", "      <td>2.4325</td>\n", "      <td>37917.89</td>\n", "      <td>6742301.361</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>109</th>\n", "      <td>600519.SH</td>\n", "      <td>20230109</td>\n", "      <td>1835.00</td>\n", "      <td>1849.98</td>\n", "      <td>1807.82</td>\n", "      <td>1841.20</td>\n", "      <td>1803.77</td>\n", "      <td>37.43</td>\n", "      <td>2.0751</td>\n", "      <td>30977.23</td>\n", "      <td>5684181.147</td>\n", "    </tr>\n", "    <tr>\n", "      <th>110</th>\n", "      <td>600519.SH</td>\n", "      <td>20230106</td>\n", "      <td>1806.12</td>\n", "      <td>1811.90</td>\n", "      <td>1787.00</td>\n", "      <td>1803.77</td>\n", "      <td>1801.00</td>\n", "      <td>2.77</td>\n", "      <td>0.1538</td>\n", "      <td>24903.75</td>\n", "      <td>4480838.898</td>\n", "    </tr>\n", "    <tr>\n", "      <th>111</th>\n", "      <td>600519.SH</td>\n", "      <td>20230105</td>\n", "      <td>1737.00</td>\n", "      <td>1801.00</td>\n", "      <td>1733.00</td>\n", "      <td>1801.00</td>\n", "      <td>1725.01</td>\n", "      <td>75.99</td>\n", "      <td>4.4052</td>\n", "      <td>47942.85</td>\n", "      <td>8541587.089</td>\n", "    </tr>\n", "    <tr>\n", "      <th>112</th>\n", "      <td>600519.SH</td>\n", "      <td>20230104</td>\n", "      <td>1730.00</td>\n", "      <td>1738.70</td>\n", "      <td>1716.00</td>\n", "      <td>1725.01</td>\n", "      <td>1730.01</td>\n", "      <td>-5.00</td>\n", "      <td>-0.2890</td>\n", "      <td>20415.75</td>\n", "      <td>3523582.306</td>\n", "    </tr>\n", "    <tr>\n", "      <th>113</th>\n", "      <td>600519.SH</td>\n", "      <td>20230103</td>\n", "      <td>1731.20</td>\n", "      <td>1738.43</td>\n", "      <td>1706.01</td>\n", "      <td>1730.01</td>\n", "      <td>1727.00</td>\n", "      <td>3.01</td>\n", "      <td>0.1743</td>\n", "      <td>26033.80</td>\n", "      <td>4487760.231</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>114 rows × 11 columns</p>\n", "</div>"], "text/plain": ["       ts_code trade_date     open     high      low    close  pre_close  \\\n", "0    600519.SH   20230626  1720.11  1730.00  1695.00  1709.00    1735.83   \n", "1    600519.SH   20230621  1740.00  1756.60  1735.00  1735.83    1743.46   \n", "2    600519.SH   20230620  1740.00  1765.00  1735.00  1743.46    1744.00   \n", "3    600519.SH   20230619  1790.00  1797.95  1738.00  1744.00    1797.69   \n", "4    600519.SH   20230616  1757.00  1800.00  1750.10  1797.69    1755.00   \n", "..         ...        ...      ...      ...      ...      ...        ...   \n", "109  600519.SH   20230109  1835.00  1849.98  1807.82  1841.20    1803.77   \n", "110  600519.SH   20230106  1806.12  1811.90  1787.00  1803.77    1801.00   \n", "111  600519.SH   20230105  1737.00  1801.00  1733.00  1801.00    1725.01   \n", "112  600519.SH   20230104  1730.00  1738.70  1716.00  1725.01    1730.01   \n", "113  600519.SH   20230103  1731.20  1738.43  1706.01  1730.01    1727.00   \n", "\n", "     change  pct_chg       vol       amount  \n", "0    -26.83  -1.5457  23992.68  4098619.510  \n", "1     -7.63  -0.4376  17720.61  3088635.934  \n", "2     -0.54  -0.0310  20946.74  3659824.529  \n", "3    -53.69  -2.9866  31699.92  5584248.725  \n", "4     42.69   2.4325  37917.89  6742301.361  \n", "..      ...      ...       ...          ...  \n", "109   37.43   2.0751  30977.23  5684181.147  \n", "110    2.77   0.1538  24903.75  4480838.898  \n", "111   75.99   4.4052  47942.85  8541587.089  \n", "112   -5.00  -0.2890  20415.75  3523582.306  \n", "113    3.01   0.1743  26033.80  4487760.231  \n", "\n", "[114 rows x 11 columns]"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["# 导入tushare\n", "import tushare as ts\n", "\n", "# 初始化pro接口\n", "pro = ts.pro_api('18fc2c6fab6d4beebeb83ea1b0d5135d35368c2451ddac1145a95985')\n", "df = pro.daily(ts_code='600519.SH', start_date='20230101', end_date='20230701')\n", "df"]}, {"cell_type": "code", "execution_count": null, "id": "d22bfe1d", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}