# 智能解压缩工具 - 功能演示

## 🎯 新增功能

### 1. 密码字典编辑器

### 功能位置
- 在主界面中，"编辑密码字典"按钮位于"停止解压"按钮后面
- 点击后会打开可视化的密码字典编辑窗口

### 主要特性

#### 📝 编辑功能
- **可视化编辑**: 提供友好的文本编辑界面
- **语法高亮**: 支持注释行识别（以#开头）
- **实时统计**: 显示总行数、密码数量、注释数量
- **多编码支持**: 自动处理ANSI、GBK、UTF-8等编码格式

#### 🔧 便捷操作
- **清空功能**: 快速清空所有内容
- **保存功能**: 智能编码保存，优先使用ANSI编码
- **模板提供**: 新建文件时提供默认模板

#### 📊 智能统计
- 实时显示文件统计信息
- 区分密码行和注释行
- 帮助用户了解密码字典规模

### 使用方法

#### 1. 打开编辑器
```
主界面 → 选择文件夹 → 点击"编辑密码字典"
```

#### 2. 编辑密码
- 每行输入一个密码
- 使用 `#` 开头添加注释
- 常用密码建议放在前面

#### 3. 保存文件
- 点击"保存"按钮
- 自动使用合适的编码格式
- 保存到目标文件夹的 `passdict.txt`

### 编码处理机制

#### 读取文件
程序会按以下顺序尝试编码：
1. **ANSI** (Windows默认)
2. **GBK** (中文编码)
3. **GB2312** (简体中文)
4. **UTF-8** (通用编码)
5. **Latin-1** (兜底编码)

#### 保存文件
1. 优先使用 **ANSI** 编码保存
2. 如果包含特殊字符，自动切换到 **UTF-8**
3. 保存时会显示使用的编码格式

### 密码字典格式

#### 标准格式
```
# 密码字典文件
# 每行一个密码，程序会按顺序尝试
# 以 # 开头的行为注释

123456
password
admin
123123
111111
```

#### 支持的内容
- ✅ 纯数字密码
- ✅ 字母密码
- ✅ 数字字母组合
- ✅ 中文密码
- ✅ 特殊字符密码
- ✅ 注释行（以#开头）

### 常用密码列表

编辑器内置19个常用密码：
```
123456, password, admin, 123123, 111111, 000000,
qwerty, abc123, 12345678, 1234567890, root, user,
test, guest, login, pass, welcome, secret, default
```

### 错误处理

#### 编码错误
- 自动尝试多种编码格式
- 显示详细的错误信息
- 提供编码格式提示

#### 文件权限
- 自动创建目录
- 处理文件权限问题
- 提供清晰的错误提示

### 与解压功能的集成

#### 密码尝试顺序
1. **password.txt** 文件中的密码
2. **passdict.txt** 字典中的密码（按顺序）
3. **无密码** 解压尝试

#### 日志记录
- 记录使用的密码
- 显示解压成功的文件
- 统计密码字典的使用效果

### 性能优化

#### 智能缓存
- 避免重复读取文件
- 缓存编码检测结果
- 优化大文件处理

#### 用户体验
- 实时保存提示
- 编辑状态指示
- 快捷键支持

### 故障排除

#### 常见问题
1. **编码错误**: 程序会自动尝试多种编码
2. **保存失败**: 检查文件夹权限
3. **中文乱码**: 使用ANSI或GBK编码

#### 解决方案
- 查看操作日志获取详细信息
- 手动选择合适的编码格式
- 确保有足够的文件系统权限

## 🔄 更新内容

### 2. 文件后缀规范化

#### 功能位置
- 在主界面中，"规范文件后缀"按钮位于"编辑密码字典"按钮后面
- 点击后会显示功能说明并开始处理

#### 主要特性

##### 📝 自动处理规则
- **无扩展名大文件**: 文件大小 > 100MB 且无扩展名，自动添加 .7z
- **汉字清理**: 移除扩展名中的汉字字符
- **大文件重命名**: 文件大小 > 300MB 的特定格式文件重命名为 .zip
  - .pdf → .zip
  - .doc/.docx → .zip
  - .txt → .zip
  - .gif → .zip
- **扩展名标准化**:
  - .7 → .7z
  - .7zz → .7z
  - .z → .7z

##### 🤖 智能识别
- **分卷压缩文件识别**: 自动识别 .7z.001、.zip.002 等分卷文件，不会误处理
- **大文件智能处理**: 文件大小 > 500MB 的未知扩展名文件，弹出确认对话框

##### 📊 实时反馈
- 详细的处理日志
- 实时进度显示
- 处理结果统计

#### 使用方法

1. **选择文件夹**: 确保已选择要处理的文件夹
2. **点击按钮**: 点击"规范文件后缀"按钮
3. **确认处理**: 阅读功能说明后确认继续
4. **查看结果**: 在日志中查看处理结果

#### 处理示例

```
原文件名                    处理后
大文件(150MB)              大文件.7z
测试.txt汉字               测试.txt
压缩包.7                   压缩包.7z
大PDF(400MB).pdf          大PDF(400MB).zip
压缩包.7z.001             (不处理，分卷文件)
```

## 🔄 更新内容

### v1.2.0 新增功能
- ✅ 密码字典可视化编辑器
- ✅ 多编码格式自动处理
- ✅ 文件后缀规范化功能
- ✅ 智能分卷文件识别
- ✅ 实时统计信息显示
- ✅ 智能编码保存机制

### 界面改进
- 🔧 调整按钮布局，新增"规范文件后缀"按钮
- 🎨 优化窗口居中显示
- 📊 增加密码字典统计信息
- 💡 提供详细的使用说明和模板
- 🚀 集成文件预处理功能，解压前自动规范文件格式

### 兼容性
- ✅ 兼容原有的命令行版本
- ✅ 支持现有的密码文件格式
- ✅ 保持原有的解压逻辑不变
