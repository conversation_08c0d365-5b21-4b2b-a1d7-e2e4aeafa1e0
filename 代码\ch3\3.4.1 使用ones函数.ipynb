{"cells": [{"cell_type": "code", "execution_count": 3, "id": "45cfff61", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[[1. 1. 1.]\n", " [1. 1. 1.]]\n", "float64\n", "[[1. 1. 1.]\n", " [1. 1. 1.]]\n", "float64\n"]}], "source": ["import numpy as np\n", "a = np.ones((2, 3))\n", "print(a)\n", "print(a.dtype)\n", "b = np.ones((2, 3), dtype=float)\n", "print(b)\n", "print(b.dtype)"]}, {"cell_type": "code", "execution_count": null, "id": "2fa1176e", "metadata": {}, "outputs": [], "source": ["c = np.ones((3,)) # 生成一维数组\n", "print(c)\n", "print(c.dtype)\n", "\n", "d = np.ones((3, 1)) # 生成列向量数组\n", "print(d)\n", "print(d.dtype)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}