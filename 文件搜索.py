import tkinter as tk
from tkinter import filedialog, messagebox, ttk
import os
import subprocess
from datetime import datetime, timedelta
import logging
import sys
from pathlib import Path


class FileIndexer(tk.Tk):
    def __init__(self):
        super().__init__()
        self.title("文件和文件夹索引搜索工具")
        self.geometry("800x600")

        # 设置日志
        self.setup_logging()

        self.index_directory = r"Z:\work"  # 设置索引文件的存储目录
        self.ensure_index_directory()

        self.index_button = tk.Button(self, text="建立或更新索引", command=self.create_or_update_index)
        self.index_button.pack(pady=20)

        self.search_label = tk.Label(self, text="输入搜索关键词:")
        self.search_label.pack(pady=10)

        self.search_entry = tk.Entry(self)
        self.search_entry.pack(pady=5)
        self.search_entry.bind("<FocusIn>", self.paste_from_clipboard)  # 绑定输入框获得焦点事件
        self.search_entry.bind("<Return>", self.search)  # 绑定回车键

        self.search_button = tk.Button(self, text="搜索", command=self.search)
        self.search_button.pack(pady=5)

        # 离线搜索按钮
        self.offline_search_button = tk.Button(self, text="离线搜索", command=self.offline_search)
        self.offline_search_button.pack(pady=5)

        # 使用 Listbox 组件来显示搜索结果
        self.result_list = tk.Listbox(self, width=100, height=30)
        self.result_list.pack(pady=10)
        self.result_list.bind("<Double-Button-1>", self.open_location)  # 双击打开路径

        self.file_index_path = ""
        self.video_extensions = {'.mp4', '.avi', '.mkv', '.mov', '.flv', '.wmv', '.webm'}
        
        # 添加状态标签
        self.status_label = tk.Label(self, text="就绪")
        self.status_label.pack(pady=5)

    def setup_logging(self):
        """设置日志记录"""
        log_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'logs')
        os.makedirs(log_dir, exist_ok=True)
        log_file = os.path.join(log_dir, f'file_indexer_{datetime.now().strftime("%Y%m%d")}.log')
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )

    def ensure_index_directory(self):
        """确保索引目录存在"""
        try:
            os.makedirs(self.index_directory, exist_ok=True)
        except Exception as e:
            logging.error(f"创建索引目录失败: {str(e)}")
            messagebox.showerror("错误", f"无法创建索引目录: {str(e)}")

    def create_or_update_index(self):
        folder_path = filedialog.askdirectory()
        if folder_path:
            folder_name = os.path.basename(folder_path)
            self.file_index_path = os.path.join(self.index_directory, f"{folder_name}_index.txt")

            if os.path.exists(self.file_index_path):
                file_time = os.path.getmtime(self.file_index_path)
                file_date = datetime.fromtimestamp(file_time)
                if datetime.now() - file_date > timedelta(days=7):
                    self.update_index(folder_path)
                else:
                    messagebox.showinfo("信息", "现有索引文件仍在有效期内。")
            else:
                self.update_index(folder_path)
        else:
            messagebox.showwarning("警告", "没有选择文件夹。")

    def update_index(self, folder_path):
        try:
            # 创建进度窗口
            progress_window = tk.Toplevel(self)
            progress_window.title("正在创建索引")
            progress_window.geometry("400x150")
            
            progress_label = tk.Label(progress_window, text="正在扫描文件...")
            progress_label.pack(pady=10)
            
            progress_bar = ttk.Progressbar(progress_window, length=300, mode='determinate')
            progress_bar.pack(pady=10)
            
            status_label = tk.Label(progress_window, text="")
            status_label.pack(pady=5)

            # 预扫描统计各类文件数量
            total_dirs = 0
            total_video_files = 0
            total_other_files = 0
            
            for root, dirs, files in os.walk(folder_path):
                total_dirs += len(dirs)
                for file in files:
                    if os.path.splitext(file)[1].lower() in self.video_extensions:
                        total_video_files += 1
                    else:
                        total_other_files += 1
            
            total_items = total_dirs + total_video_files + total_other_files
            current_item = 0
            indexed_dirs = 0
            indexed_videos = 0
            skipped_files = []
            
            with open(self.file_index_path, 'w', encoding='utf-8') as index_file:
                for root, dirs, files in os.walk(folder_path):
                    try:
                        # 更新进度
                        current_item += len(dirs) + len(files)
                        progress = (current_item / total_items) * 100
                        progress_bar['value'] = progress
                        status_label.config(text=f"已处理 {current_item}/{total_items} 个项目\n"
                                              f"已索引文件夹: {indexed_dirs}/{total_dirs}\n"
                                              f"已索引视频: {indexed_videos}/{total_video_files}")
                        progress_window.update()
                        
                        # 写入文件夹路径
                        for dir_name in dirs:
                            try:
                                dir_path = os.path.join(root, dir_name)
                                index_file.write(f"{dir_path}\n")
                                indexed_dirs += 1
                            except Exception as e:
                                logging.error(f"无法写入文件夹路径 {dir_path}: {str(e)}")
                                skipped_files.append(dir_path)
                        
                        # 写入文件路径
                        for name in files:
                            try:
                                file_path = os.path.join(root, name)
                                if os.path.splitext(name)[1].lower() in self.video_extensions:
                                    index_file.write(f"{file_path}\n")
                                    indexed_videos += 1
                            except Exception as e:
                                logging.error(f"无法写入文件路径 {file_path}: {str(e)}")
                                skipped_files.append(file_path)
                    except Exception as e:
                        logging.error(f"处理目录 {root} 时出错: {str(e)}")
                        continue

            progress_window.destroy()
            
            if skipped_files:
                messagebox.showwarning("警告", f"部分文件无法被索引，共 {len(skipped_files)} 个。\n详细信息请查看日志文件。")
            
            summary = (f"索引创建完成。\n"
                     f"总项目数: {total_items}\n"
                     f"文件夹总数: {total_dirs}\n"
                     f"视频文件总数: {total_video_files}\n"
                     f"其他文件总数: {total_other_files}\n"
                     f"已索引文件夹: {indexed_dirs}\n"
                     f"已索引视频: {indexed_videos}\n"
                     f"跳过项目数: {len(skipped_files)}")
            
            messagebox.showinfo("信息", summary)
            logging.info(f"索引创建完成。总项目数: {total_items}, "
                        f"文件夹总数: {total_dirs}, "
                        f"视频文件总数: {total_video_files}, "
                        f"其他文件总数: {total_other_files}, "
                        f"已索引文件夹: {indexed_dirs}, "
                        f"已索引视频: {indexed_videos}, "
                        f"跳过项目数: {len(skipped_files)}")
            
        except Exception as e:
            logging.error(f"创建索引时出错: {str(e)}")
            messagebox.showerror("错误", f"创建索引失败: {str(e)}")

    def search(self, event=None):
        keyword = self.search_entry.get()
        self.result_list.delete(0, tk.END)  # 清空列表
        if not keyword:
            messagebox.showwarning("警告", "请输入搜索关键词。")
            return
        if self.file_index_path and os.path.exists(self.file_index_path):
            try:
                with open(self.file_index_path, 'r', encoding='utf-8') as index_file:
                    found = False
                    for line in index_file:
                        if keyword.lower() in line.lower():
                            self.result_list.insert(tk.END, line.strip())
                            found = True
                    if not found:
                        messagebox.showinfo("信息", "没有找到匹配项。")
            except FileNotFoundError:
                messagebox.showerror("错误", "索引文件未找到，请先创建或更新索引。")
            except Exception as e:
                logging.error(f"搜索时出错: {str(e)}")
                messagebox.showerror("错误", f"搜索失败: {str(e)}")
        else:
            messagebox.showerror("错误", "索引文件未找到，请先创建或更新索引。")

    def offline_search(self):
        keyword = self.search_entry.get()
        self.result_list.delete(0, tk.END)  # 清空列表
        if not keyword:
            messagebox.showwarning("警告", "请输入搜索关键词。")
            return

        try:
            index_files = [f for f in os.listdir(self.index_directory) if
                           f.endswith('_index.txt') and os.path.isfile(os.path.join(self.index_directory, f))]

            if not index_files:
                messagebox.showinfo("信息", "未找到任何索引文件。")
                return

            found = False
            for index_file in index_files:
                try:
                    with open(os.path.join(self.index_directory, index_file), 'r', encoding='utf-8') as file:
                        for line in file:
                            if keyword.lower() in line.lower():
                                self.result_list.insert(tk.END, line.strip())
                                found = True
                except Exception as e:
                    logging.error(f"读取索引文件 {index_file} 时出错: {str(e)}")
                    continue

            if not found:
                messagebox.showinfo("信息", "没有找到匹配项。")
        except Exception as e:
            logging.error(f"离线搜索时出错: {str(e)}")
            messagebox.showerror("错误", f"离线搜索失败: {str(e)}")

    def paste_from_clipboard(self, event=None):
        # 清空输入框
        self.search_entry.delete(0, tk.END)
        try:
            clipboard_content = self.clipboard_get()  # 获取剪贴板内容
            if len(clipboard_content) <= 10:  # 判断长度
                self.search_entry.insert(0, clipboard_content)  # 将剪贴板内容插入到输入框
        except tk.TclError:
            pass  # 如果剪贴板内容不可用，则忽略错误

    def open_location(self, event):
        try:
            # 获取选中的项目路径
            selection = self.result_list.get(self.result_list.curselection())
            file_path = os.path.abspath(selection)
            folder_path = os.path.dirname(file_path)
            logging.info(f"打开文件位置: {file_path}")  # 调试信息，打印文件路径

            # 打开文件位置
            if os.name == 'nt':  # Windows
                subprocess.Popen(f'explorer /select,"{file_path}"')
            elif os.name == 'posix':  # macOS, Linux
                subprocess.Popen(['open', '--', folder_path])

        except Exception as e:
            logging.error(f"打开文件位置时出错: {str(e)}")
            messagebox.showerror("错误", f"无法打开文件位置: {str(e)}")


if __name__ == "__main__":
    app = FileIndexer()
    app.mainloop()
