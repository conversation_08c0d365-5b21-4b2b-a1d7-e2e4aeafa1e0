{"cells": [{"cell_type": "code", "execution_count": 1, "id": "e03ab846", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\IPython\\core\\pylabtools.py:152: UserWarning: Glyph 25104 (\\N{CJK UNIFIED IDEOGRAPH-6210}) missing from current font.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\IPython\\core\\pylabtools.py:152: UserWarning: Glyph 20132 (\\N{CJK UNIFIED IDEOGRAPH-4EA4}) missing from current font.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\IPython\\core\\pylabtools.py:152: UserWarning: Glyph 37327 (\\N{CJK UNIFIED IDEOGRAPH-91CF}) missing from current font.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\IPython\\core\\pylabtools.py:152: UserWarning: Glyph 32929 (\\N{CJK UNIFIED IDEOGRAPH-80A1}) missing from current font.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\IPython\\core\\pylabtools.py:152: UserWarning: Glyph 31080 (\\N{CJK UNIFIED IDEOGRAPH-7968}) missing from current font.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\IPython\\core\\pylabtools.py:152: UserWarning: Glyph 25910 (\\N{CJK UNIFIED IDEOGRAPH-6536}) missing from current font.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\IPython\\core\\pylabtools.py:152: UserWarning: Glyph 30424 (\\N{CJK UNIFIED IDEOGRAPH-76D8}) missing from current font.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\IPython\\core\\pylabtools.py:152: UserWarning: Glyph 20215 (\\N{CJK UNIFIED IDEOGRAPH-4EF7}) missing from current font.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\IPython\\core\\pylabtools.py:152: UserWarning: Glyph 19982 (\\N{CJK UNIFIED IDEOGRAPH-4E0E}) missing from current font.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\IPython\\core\\pylabtools.py:152: UserWarning: Glyph 20851 (\\N{CJK UNIFIED IDEOGRAPH-5173}) missing from current font.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\IPython\\core\\pylabtools.py:152: UserWarning: Glyph 31995 (\\N{CJK UNIFIED IDEOGRAPH-7CFB}) missing from current font.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "plt.rcParams['font.family'] = ['SimHei'] # 设置中文字体\n", "plt.rcParams['axes.unicode_minus'] = False # 设置负号显示\n", "\n", "# 股票数据\n", "closing_prices = [100, 110, 120, 115, 105]  # 收盘价数据\n", "volume = [1000, 1500, 2000, 1800, 1200]  # 成交量数据\n", "\n", "# 绘制散点图\n", "plt.scatter(closing_prices, volume)\n", "\n", "# 设置图表标题和轴标签\n", "plt.title('股票收盘价与成交量关系')\n", "plt.xlabel('收盘价')\n", "plt.ylabel('成交量')\n", "\n", "# 显示图形\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": null, "id": "6b08a357", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}