import re
import subprocess
import threading
import tkinter as tk
import psutil
import pyautogui
import pyperclip
import time
import pygetwindow as gw
from threading import Thread

class AddressGenerator:
    def __init__(self, root):
        self.root = root
        self.root.title("地址生成器")

        # 设置窗口大小
        self.root.geometry("1000x800")
        self.root.attributes('-topmost', True)

        # 创建第一个地址输入框
        self.first_label = tk.Label(root, text="起始地址:")
        self.first_label.pack()
        self.first_entry = tk.Entry(root, width=60)
        self.first_entry.pack()
        # 绑定点击事件，自动粘贴剪贴板内容
        self.first_entry.bind("<Button-1>", self.paste_clipboard)

        # 创建第二个地址输入框
        self.second_label = tk.Label(root, text="结束地址:")
        self.second_label.pack()
        self.second_entry = tk.Entry(root, width=60)
        self.second_entry.pack()
        # 绑定点击事件，自动粘贴剪贴板内容
        self.second_entry.bind("<Button-1>", self.paste_clipboard)

        # 创建MovName输入框
        self.mov_name_label = tk.Label(root, text="文件名前缀:")
        self.mov_name_label.pack()
        self.mov_name_entry = tk.Entry(root, width=60)
        self.mov_name_entry.pack()
        # 绑定点击事件，自动粘贴剪贴板内容
        self.mov_name_entry.bind("<Button-1>", self.paste_clipboard)

        # 创建按钮框架
        self.button_frame = tk.Frame(root)
        self.button_frame.pack()

        # 创建批量下载按钮
        self.download_button = tk.Button(self.button_frame, text="批量地址", command=self.add_addresses)
        self.download_button.pack(side=tk.LEFT, padx=5)

        # 创建单独下载按钮
        self.single_download_button = tk.Button(self.button_frame, text="单独地址", command=self.add_single_address)
        self.single_download_button.pack(side=tk.LEFT, padx=5)

        # 创建开始下载按钮
        self.start_download_button = tk.Button(self.button_frame, text="开始下载", command=self.start_download)
        self.start_download_button.pack(side=tk.LEFT, padx=5)
        self.start_download_button["state"] = "disabled"  # 初始状态为禁用

        # 创建停止按钮
        self.stop_download_button = tk.Button(self.button_frame, text="停止下载", command=self.stop_download)
        self.stop_download_button.pack(side=tk.LEFT, padx=5)
        self.stop_download_button["state"] = "disabled"  # 初始状态为禁用
        # 初始化 stop_flag
        self.stop_flag = False
        self.stop_event = threading.Event()

        # 创建地址列表显示框
        self.addresses_text = tk.Text(root, height=20, width=80)
        self.addresses_text.pack()

        # 初始化地址列表
        self.addresses = []

        # 初始化计时器
        self.timer = None

    def paste_clipboard(self, event):
        # 清空输入框内容
        event.widget.delete(0, tk.END)
        # 获取剪贴板内容并粘贴到点击的输入框
        clipboard_content = pyperclip.paste()
        event.widget.insert(tk.END, clipboard_content)

    def add_addresses(self):
        # 获取第一个地址、第二个地址和MovName
        first_address = self.first_entry.get()
        second_address = self.second_entry.get()
        mov_name = self.mov_name_entry.get()

        # 使用正则表达式查找地址中的末尾数字序列
        first_numbers = [int(num) for num in re.findall(r'/(\d+)\D*$', first_address)]
        second_numbers = [int(num) for num in re.findall(r'/(\d+)\D*$', second_address)]

        # 生成地址列表
        new_addresses = []
        for number in range(first_numbers[0], second_numbers[0] + 1):
            new_address = re.sub(r'/\d+\D*$', f'/{number}', first_address)
            new_address = f"d:/tdl_Windows_64bit/.\\tdl dl -u {new_address} --template \"{mov_name}_{{{{ .MessageID }}}}_{{{{ .FileName }}}}\""
            new_addresses.append(f'{new_address}  & timeout /t 5 >nul & exit')

        # 将新地址添加到地址列表中
        self.addresses.extend(new_addresses)

        # 显示地址列表
        self.show_addresses()

        # 清空输入框内容
        self.clear_input_fields()

        # 启用开始下载按钮
        self.start_download_button["state"] = "normal"

        # 保存地址列表到文件
        self.save_addresses_to_file()

    def add_single_address(self):
        # 获取第一个地址
        first_address = self.first_entry.get()
        mov_name = self.mov_name_entry.get()
        # 生成地址列表
        new_addresses = []
        new_address = f"d:/tdl_Windows_64bit/.\\tdl dl -u {first_address} --template \"{mov_name}_{{{{ .MessageID }}}}_{{{{ .FileName }}}}\""
        new_addresses.append(f'{new_address}  & timeout /t 5 >nul & exit')
        # 添加单个地址到地址列表
        self.addresses.extend(new_addresses)

        # 显示地址列表
        self.show_addresses()

        # 清空输入框内容
        self.clear_input_fields()

        # 启用开始下载按钮
        self.start_download_button["state"] = "normal"

        # 保存地址列表到文件
        self.save_addresses_to_file()

    def clear_input_fields(self):
        # 清空输入框内容
        self.first_entry.delete(0, tk.END)
        self.second_entry.delete(0, tk.END)
        # self.mov_name_entry.delete(0, tk.END)

    def show_addresses(self):
        # 清空地址列表显示框
        self.addresses_text.delete(1.0, tk.END)

        # 将地址列表显示在地址列表显示框中
        for address in self.addresses:
            self.addresses_text.insert(tk.END, address + "\n")

    def check_cmd_window(self):
        windows = gw.getWindowsWithTitle('cmd')  # 获取所有标题中包含'cmd'的窗口
        if windows:
            print("CMD窗口存在")
            return True
        else:
            print("CMD窗口不存在")
            return False

    def check_cmd_window_1(self):
        # 遍历所有正在运行的进程
        for proc in psutil.process_iter(['pid', 'name']):
            if proc.info['name'] == 'cmd.exe':
                print("CMD窗口存在")
                return True
        print("CMD窗口不存在")
        return False

    def start_download(self):
        # 禁用开始下载按钮和添加地址按钮
        self.start_download_button["state"] = "disabled"
        #self.download_button["state"] = "disabled"
        #self.single_download_button["state"] = "disabled"
        # 启用停止下载按钮
        self.stop_download_button["state"] = "normal"

        # 如果停止按键被按下，则停止下载
        #if self.stop_flag:
        #    print("下载已停止。")
        #    return

        # 创建一个新线程来执行下载操作
        # 重新设置停止标志为 False
        self.stop_flag = False
        self.stop_event.clear()
        thread = Thread(target=self.execute_download)
        thread.start()

    def execute_download(self):
        while self.addresses:
            if not self.check_cmd_window():
                for address in self.addresses[:]:
                    if self.stop_event.is_set():
                        print("下载已停止。")
                        return
                    # 发送地址到命令行窗口
                    if self.send_address_to_cmd(address):
                        # 从地址列表中删除已发送的地址
                        self.addresses.remove(address)
                        # 更新地址列表的显示
                        self.show_addresses()
                        # 立即更新文本框的显示
                        self.root.update()
                # 保存到文件
                self.save_addresses_to_file()
            else:
                # 如果有命令提示符进程在运行，则暂停发送命令
                print("正在下载，等待10秒重试...")

    def stop_download(self):
        # 启用开始下载按钮和添加地址按钮
        self.start_download_button["state"] = "normal"
        self.download_button["state"] = "normal"
        self.single_download_button["state"] = "normal"
        # 禁用停止下载按钮
        self.stop_download_button["state"] = "disabled"

        # 设置停止标志
        self.stop_flag = True
        # 设置停止事件
        self.stop_event.set()

        # 清除计时器
        if self.timer:
            self.root.after_cancel(self.timer)

        # 保存地址列表到文件
        self.save_addresses_to_file()

    def send_address_to_cmd(self, address):
        # 检查是否存在 cmd 窗口
        while self.check_cmd_window():
            #cmd_content = self.get_cmd_window_content()
            #if "CPU:" in cmd_content:
            print("正在下载，等待10秒重试...")
            #else:
            #    pyautogui.write('exit')
            #    pyautogui.press('enter')
            time.sleep(10)  # 等待 10 秒

        try:
            if not self.stop_flag:  # 检查停止标志
                # 复制字符串到剪贴板
                pyperclip.copy(address)
                # 打开一个新的 cmd 窗口
                proc = subprocess.Popen(["cmd.exe"], creationflags=subprocess.CREATE_NEW_CONSOLE)
                # 等待一秒钟，以确保新窗口完全打开
                time.sleep(0.5)
                pyautogui.write('cd d:\\tdl_Windows_64bit')
                # 模拟按下回车键
                pyautogui.press('enter')
                # 模拟粘贴操作（Ctrl + V）
                pyautogui.hotkey('ctrl', 'v')
                # 模拟按下回车键，以执行粘贴的命令
                pyautogui.press('enter')
                # 等待命令执行完成
                # proc.wait()
                return True
        except Exception as e:
            print(f"Error sending address to cmd: {e}")
        return False

    def get_cmd_window_content(self):
        try:
            # 获取命令提示符窗口
            cmd_window = gw.getWindowsWithTitle('C:\\windows\\SYSTEM32\\cmd.exe')[0]

            # 切换到命令提示符窗口
            cmd_window.activate()

            # 发送快捷键Ctrl + A，选择所有内容
            pyautogui.hotkey('ctrl', 'a')

            # 发送快捷键Ctrl + C，复制内容到剪贴板
            pyautogui.hotkey('ctrl', 'c')

            # 等待一段时间以确保内容已经被复制
            time.sleep(0.5)

            # 从剪贴板中获取内容
            cmd_content = pyperclip.paste()

            return cmd_content
        except IndexError:
            print("未找到命令提示符窗口")
            return None

    def save_addresses_to_file(self):
        # 保存地址列表到文件
        with open("D:/tdl_Windows_64bit/addresses.txt", "w") as f:
            for address in self.addresses:
                f.write(address + "\n")

    def load_addresses_from_file(self):
        # 从文件加载地址列表
        try:
            with open("d:/tdl_Windows_64bit/addresses.txt", "r") as f:
                addresses = f.readlines()
                # 去除每行末尾的换行符
                addresses = [address.strip() for address in addresses]
        except FileNotFoundError:
            # 如果文件不存在，则创建一个空的地址列表
            addresses = []

        # 将加载的地址列表作为初始值传递给每个模块
        self.addresses = addresses
        self.show_addresses()

        # 检查地址列表是否为空，如果不为空，则启用下载按钮
        if self.addresses:
            self.start_download_button["state"] = "normal"
            self.stop_download_button["state"] = "normal"
        else:
            self.start_download_button["state"] = "disabled"
            self.stop_download_button["state"] = "normal"


# 创建主窗口
root = tk.Tk()

# 创建应用程序实例
app = AddressGenerator(root)

# 加载地址列表文件（如果存在）
app.load_addresses_from_file()

# 运行程序
root.mainloop()

# 在程序退出时保存地址列表到文件
app.save_addresses_to_file()
