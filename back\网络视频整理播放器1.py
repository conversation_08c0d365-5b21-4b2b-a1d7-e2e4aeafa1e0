import csv
import mimetypes
import shutil
import sys
import tkinter as tk
from tkinter import filedialog, messagebox
import vlc
import os
import json
import cv2
from PIL import Image, ImageTk
from concurrent.futures import ThreadPoolExecutor
import threading
from tkinter import messagebox
import subprocess
import PIL


class VideoPlayer:
    def __init__(self, master):
        self.last_selected_folder = None
        self.master = master
        self.master.title("Jiang's 视频播放器")

        self.filename_label = tk.Label(self.master, text="", background="black", foreground="white", font=("Arial", 12))
        self.filename_label.pack(side=tk.TOP, pady=5, fill=tk.X)

        self.is_favorite = False
        self.favorite_status = {}
        self.file_states = {}

        self.folder_selection_count = {}

        # 添加缩略图标签
        self.thumbnail_label = tk.Label(self.master)
        self.thumbnail_label.pack(pady=10)

        # 创建一个VLC播放器实例
        self.Instance = vlc.Instance("--no-xlib")
        self.player = self.Instance.media_player_new()

        # 在 __init__ 方法中初始化 thumbnail_cache
        self.thumbnail_cache = {}


        # 创建一个线程池用于异步生成预览图
        self.executor = ThreadPoolExecutor(max_workers=8)

        # 文件列表和当前索引
        self.video_files = []
        self.current_index = 0

        # 文件夹路径
        self.folder_path = ""

        # 是否循环播放
        self.loop_play = True

        # 常用文件夹记录
        self.common_folders = []
        self.load_common_folders()  # 在初始化时加载常用文件夹记录

        # 常用文件夹选择窗口
        self.move_to_window = None

        # 创建GUI元素
        self.create_widgets()

        # 创建定时器更新进度条
        self.update_progress()

        # 创建一个 Canvas 用于显示预览图，并添加滚动条
        self.canvas = tk.Canvas(self.master, height=150, width=800)
        self.canvas.pack(side=tk.TOP, pady=10)
        self.scrollbar = tk.Scrollbar(self.master, orient=tk.HORIZONTAL, command=self.canvas.xview)
        self.scrollbar.pack(side=tk.TOP, fill=tk.X)
        self.canvas.configure(xscrollcommand=self.scrollbar.set)

        # 显示所有预览图
        self.display_all_thumbnails_threaded([])

        self.master.protocol("WM_DELETE_WINDOW", self.on_closing)

        # 绑定鼠标点击事件
        self.canvas.bind("<Button-1>", lambda event: self.seek_backward())
        self.canvas.bind("<Button-3>", lambda event: self.seek_forward())
        # 设置主窗口焦点
        #self.master.focus_set()

        self.image_count_label = tk.Label(self.master, text="", font=("Helvetica", 9), justify="left")

    def generate_and_cache_thumbnail_threaded(self, video_file):
        # 创建一个线程来执行生成和缓存缩略图的操作
        thumbnail_thread = threading.Thread(target=self.generate_and_cache_thumbnail, args=(video_file,))
        thumbnail_thread.start()

    def update_file_list_threaded(self):
        # 创建一个线程来执行更新文件列表的操作
        update_thread = threading.Thread(target=self.update_file_list)
        update_thread.start()

    def refresh_thumbnails_threaded(self):
        # 创建一个线程来执行重新显示预览图的操作
        refresh_thread = threading.Thread(target=self.refresh_thumbnails)
        refresh_thread.start()

    def display_all_thumbnails_threaded(self, thumbnails):
        # 使用线程池异步执行显示预览图的操作
        self.executor.submit(self.display_all_thumbnails, thumbnails)



    def display_all_thumbnails(self, thumbnails):
        # 清空 Canvas
        self.canvas.delete("all")

        # 获取视频播放窗口的宽度
        video_frame_width = self.video_frame.winfo_width()

        # 定义预览图之间的间隔
        thumbnail_spacing = 10  # 预览图之间的间隔

        # 显示所有预览图
        for i, thumbnail in enumerate(thumbnails):
            if thumbnail:
                # 计算当前预览图在 Canvas 中的位置
                x_position = i * (thumbnail.width() + thumbnail_spacing)

                # 如果缩略图不在缓存中，则异步生成并缓存它
                if i not in self.thumbnail_cache:
                    self.executor.submit(self.generate_and_cache_thumbnail_threaded, i)

                # 创建点击事件处理函数
                click_handler = lambda index=i: self.play_from_thumbnail(index)

                # 创建预览图标签，绑定点击事件
                image_label = tk.Label(self.canvas, image=thumbnail)
                image_label.image = thumbnail
                image_label.bind("<Button-1>", lambda event, handler=click_handler: handler())
                self.canvas.create_window(x_position, 0, anchor=tk.NW, window=image_label)

                # 如果预览图未缓存，则异步生成并缓存
                if i not in self.thumbnail_cache:
                    self.executor.submit(self.generate_and_cache_thumbnail_threaded, i)

        # 设置 Canvas 的宽度与视频播放窗口等宽
        self.canvas.configure(width=video_frame_width)

        # 更新 Canvas 的可滚动区域
        self.canvas.configure(scrollregion=self.canvas.bbox("all"))

    def generate_and_cache_thumbnail(self, index):
        # 异步生成缩略图并缓存
        video_path = os.path.join(self.folder_path, self.video_files[index])
        thumbnail = self.generate_thumbnail(video_path)
        self.thumbnail_cache[index] = thumbnail

        # 生成后，刷新缩略图显示
        self.master.after(0, self.refresh_thumbnails_threaded)

    def refresh_thumbnails(self):
        # 重新显示所有预览图
        thumbnails = [self.thumbnail_cache.get(i) for i in range(len(self.video_files))]
        self.display_all_thumbnails_threaded(thumbnails)

    def play_from_thumbnail(self, index):
        # 用户点击预览图时，直接播放相应文件
        self.current_index = index
        self.play_selected_video()

    def create_widgets(self):
        # 文件列表
        self.file_listbox = tk.Listbox(self.master, selectmode=tk.SINGLE)
        self.file_listbox.pack_forget()  # 使用 pack_forget() 隐藏文件列表
        self.file_listbox.bind('<ButtonRelease-1>', self.listbox_click)

        # 视频播放窗口
        self.video_frame = tk.Frame(self.master)
        self.video_frame.pack(side=tk.TOP, fill=tk.BOTH, expand=True)

        # 将vlc播放器窗口嵌入到tkinter Frame中
        self.player.set_hwnd(self.video_frame.winfo_id())

        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_slider = tk.Scale(self.master, variable=self.progress_var, from_=0, to=100, orient=tk.HORIZONTAL,
                                        command=self.set_progress)
        self.progress_slider.pack(side=tk.TOP, fill=tk.X, padx=10, pady=5)

        # 时间标签
        self.time_label = tk.Label(self.master, text="00:00 / 00:00")
        self.time_label.pack(side=tk.TOP, padx=10)

        # 控制按钮
        self.controls_frame = tk.Frame(self.master)
        self.controls_frame.pack(side=tk.TOP, fill=tk.BOTH, expand=False, padx=0)

        self.create_control_buttons()
        # 调整主窗口的大小和位置
        screen_width = self.master.winfo_screenwidth()
        screen_height = self.master.winfo_screenheight()
        window_width = int(screen_width / 2)
        window_height = int(screen_height * 3 / 4)  # 设置为屏幕高度的 3/4
        window_x = int(screen_width / 4)
        window_y = int(screen_height / 8)  # 在屏幕上方留有 1/8 的空间
        self.master.geometry(f"{window_width}x{window_height}+{window_x}+{window_y}")

        # 监听鼠标点击事件
        self.progress_slider.bind("<Button-1>", self.mouse_click_callback)

    def mouse_click_callback(self, event):
        # 获取鼠标点击的位置
        x = event.x

        # 计算鼠标点击位置在进度条上的百分比（这里的系数决定了跳转的幅度）
        jump_coefficient = 0.10  # 调整这个系数以控制跳转的幅度
        progress_percentage = x / self.progress_slider.winfo_width() * (1 + jump_coefficient)

        # 将百分比转换为秒数
        duration = self.player.get_length()
        position = int(progress_percentage * duration)

        # 设置播放器的时间
        self.player.set_time(position)

    def set_progress(self, value):
        # 将进度百分比转换为秒数
        duration = self.player.get_length()
        position = int(float(value) * duration / 100)
        self.player.set_time(position)

    def create_control_buttons(self):
        # 选择文件夹按钮
        self.select_button = tk.Button(self.controls_frame, text="选择文件夹", command=self.open_folder)
        self.select_button.pack(side=tk.LEFT, padx=10)

        # 停止按钮
        self.stop_button = tk.Button(self.controls_frame, text="停止", command=self.stop_video)
        self.stop_button.pack(side=tk.LEFT, padx=10)

        # 上一个文件按钮
        self.prev_button = tk.Button(self.controls_frame, text="上一个文件", command=self.play_prev)
        self.prev_button.pack(side=tk.LEFT, padx=10)

        # 下一个文件按钮
        self.next_button = tk.Button(self.controls_frame, text="下一个文件", command=self.play_next)
        self.next_button.pack(side=tk.LEFT, padx=10)

        # 前进1/4按钮
        self.forward_1_4_button = tk.Button(self.controls_frame, text="前进1/6", command=self.forward_1_4)
        self.forward_1_4_button.pack(side=tk.LEFT, padx=10)

        # 后退10秒按钮
        self.backward_button = tk.Button(self.controls_frame, text="后退10秒", command=self.backward_10_seconds)
        self.backward_button.pack(side=tk.LEFT, padx=10)

        # 前进10秒按钮
        self.forward_button = tk.Button(self.controls_frame, text="前进10秒", command=self.forward_10_seconds)
        self.forward_button.pack(side=tk.LEFT, padx=10)


        # 添加"更名"按钮
        self.rename_button = tk.Button(self.controls_frame, text="更名", command=self.rename_video)
        self.rename_button.pack(side=tk.LEFT, padx=10)

        # 移动到按钮
        self.move_to_button = tk.Button(self.controls_frame, text="移动到", command=self.show_move_to_panel)
        self.move_to_button.pack(side=tk.LEFT, padx=10)

        # 删除按钮
        self.delete_button = tk.Button(self.controls_frame, text="删除", command=self.delete_video)
        self.delete_button.pack(side=tk.LEFT, padx=10)

        #self.favorite_button = tk.Button(self.controls_frame, text="收藏", command=self.favorite_video)
        #self.favorite_button.pack(side=tk.LEFT, padx=10)

        # 创建一个按钮，用于移动整个文件夹
        self.move_whole_folder_button = tk.Button(self.controls_frame, text="移动整个文件夹",
                                                  command=self.show_folder_move_to_panel)
        self.move_whole_folder_button.pack(side=tk.LEFT, padx=10)

        # 删除文件夹按钮
        self.delete_button = tk.Button(self.controls_frame, text="删除文件夹", command=self.delete_folder)
        self.delete_button.pack(side=tk.LEFT, padx=10)

        # 添加清理文件夹按钮
        self.clean_folder_button = tk.Button(self.controls_frame, text="清理文件夹", command=self.clean_folder)
        self.clean_folder_button.pack(side=tk.LEFT, padx=10)


    def play_video(self):
        if hasattr(self, 'media'):
            self.player.set_media(self.media)

        if self.player.get_media():
            self.player.play()

        video_filename = self.video_files[self.current_index]
        self.filename_label.config(text=f"文件名: {video_filename}")

    def pause_video(self):
        self.player.pause()

    def stop_video(self):
        self.player.stop()

    def load_video_files(self):
        # 显示所有预览图
        thumbnails = [self.generate_thumbnail(os.path.join(self.folder_path, video_file))
                      for video_file in self.video_files]
        self.display_all_thumbnails_threaded(thumbnails)
        self.favorite_status = {video_file: False for video_file in self.video_files}

    def open_folder(self, source_parent_folder=None):
        # 如果提供了文件夹路径参数，则使用它作为默认文件夹路径
        default_folder = source_parent_folder if source_parent_folder else self.last_selected_folder

        # 请求用户选择一个文件夹
        selected_folder = filedialog.askdirectory(initialdir=default_folder)

        if selected_folder:
            # 设置上次选择的文件夹路径
            self.last_selected_folder = selected_folder

            # 获取所选文件夹及其子文件夹中的所有视频文件
            self.folder_path = selected_folder
            self.video_files = []

            for root, dirs, files in os.walk(selected_folder):
                for file in files:
                    if file.lower().endswith(('.mp4', '.avi', '.mkv', '.mov', '.ts', '.wmv')):
                        self.video_files.append(os.path.join(root, file))

            # 更新 Listbox 中的文件列表
            self.update_file_list_threaded()

            # 显示图片数量
            self.show_image_count()

            if self.video_files:
                # 播放第一个选定的视频
                self.play_selected_video()
            else:
                print("在所选文件夹中找不到视频文件。")

            # 如果需要的话，加载视频文件
            self.load_video_files()

    def show_image_count(self):
        if self.folder_path:
            folder_image_count = self.count_images_in_folder(self.folder_path)
            #subfolder_image_counts = self.count_images_in_subfolders(self.folder_path)
            #if folder_image_count is not None or subfolder_image_counts:
            #    total_images = folder_image_count + sum(subfolder_image_counts.values())

            info_message = f"\n目录下共有 {folder_image_count} 张图片。\n"
            #    info_message += f"\n根目录下共有 {folder_image_count} 张图片。 \n"
            #    for subfolder, count in subfolder_image_counts.items():
            #        info_message += f"子文件夹 '{subfolder}' 中共有 {count} 张图片。\n"

            response = messagebox.askquestion("图片数量信息", info_message + "\n是否进行图片处理？")

            if response == "yes":
                    # 在这里调用图片处理程序
                self.handle_images()

                # 在界面上更新显示图片数量的部分
                #self.image_count_label.config(text=f"总共 {total_images} 张图片")
            else:
               # messagebox.showwarning("错误", "在处理文件夹时发生错误。")
               #self.image_count_label.config(text="Error")
                pass
        else:
            self.image_count_label.config(text="No Folder Selected")

    def count_images_in_folder(self, folder_path):
        image_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp']
        image_count = 0

        for root, dirs, files in os.walk(folder_path):
            # 如果是根目录，直接统计文件数量
            #if root == folder_path:
            for file in files:
                lower_case_file = file.lower()
                if any(lower_case_file.endswith(ext) for ext in image_extensions):
                        image_count += 1
            #else:
                # 如果是子文件夹，不统计文件数量，避免重复计算
            #    break

        return image_count

    def count_images_in_subfolders(self, root_folder, batch_size=10):
        result = {}
        processed_folders = 0

        def print_progress(subfolder_image_counts):
            for subfolder, count in subfolder_image_counts.items():
                print(f"子文件夹 '{subfolder}' 中共有 {count} 张图片。")
            sys.stdout.flush()

        for root, dirs, files in os.walk(root_folder):
            if dirs and root != root_folder:  # 排除根目录
                current_folder = os.path.relpath(root, root_folder)
                current_folder_path = os.path.join(root_folder, current_folder)
                image_count = self.count_images_in_folder(current_folder_path)
                result[current_folder] = image_count

                processed_folders += 1
                if processed_folders % batch_size == 0:
                    print_progress(result)

        # 打印最终结果
        print_progress(result)

        return result

    def handle_images(self):
        # 设置外部模块的路径，假设在项目根目录下
        image_handle_path = "./image_viewer.py"

        try:
            # 调用外部模块的命令
            subprocess.run(["python", image_handle_path], check=True)
        except subprocess.CalledProcessError as e:
            print(f"Error running image_handle.py: {e}")


    def play_next(self):
        if self.video_files:
            # 保存当前索引以检查是否到达最后一个文件
            current_index_before = self.current_index
            self.current_index = (self.current_index + 1) % len(self.video_files)

            # 如果当前索引为0且之前索引不为0，表示已经播放到最后一个文件
            if self.current_index == 0 and current_index_before != 0:
                if not self.loop_play:
                    self.stop_video()
                return

            self.play_selected_video()
            self.update_file_list_threaded()  # 更新文件列表，确保正确高亮正在播放的文件

    def play_prev(self):
        if self.video_files:
            self.current_index = (self.current_index - 1) % len(self.video_files)
            self.play_selected_video()

    def play_selected_video(self):
        video_path = os.path.join(self.folder_path, self.video_files[self.current_index])
        self.media = self.Instance.media_new(video_path)
        self.play_video()

    def delete_video(self):
        if self.video_files:
            confirmation = messagebox.askyesno("确认删除", "确认要删除当前视频文件吗？")
            if confirmation:
                # 停止播放
                self.stop_video()

                video_path = os.path.join(self.folder_path, self.video_files[self.current_index])
                os.remove(video_path)
                self.video_files.pop(self.current_index)

                # 如果删除后列表为空，停止播放
                if not self.video_files:
                    self.stop_video()
                else:
                    # 如果删除的是最后一个文件，播放前一个文件
                    if self.current_index == len(self.video_files):
                        self.current_index -= 1
                    self.play_selected_video()
                    # 更新播放列表
                self.update_file_list_threaded()

    def forward_10_seconds(self):
        current_time = self.player.get_time()
        new_time = current_time + 10000  # 10 seconds in milliseconds
        self.player.set_time(new_time)

    def backward_10_seconds(self):
        current_time = self.player.get_time()
        new_time = max(current_time - 10000, 0)  # Ensure the time is non-negative
        self.player.set_time(new_time)

    def forward_1_4(self):
        current_time = self.player.get_time()
        total_duration = self.player.get_length()

        if total_duration > 0:
            # 计算前进1/4的时间
            forward_time = current_time + total_duration // 6
            # 设置新的播放时间
            self.player.set_time(forward_time)

    def update_progress(self):
        if self.player.get_length() > 0:
            position = self.player.get_time()
            duration = self.player.get_length()

            # 更新进度条
            progress_percentage = (position / duration) * 100
            self.progress_var.set(progress_percentage)

            # 更新时间标签
            current_time_str = self.format_time(position)
            total_time_str = self.format_time(duration)
            time_label_text = f"{current_time_str} / {total_time_str}"
            self.time_label.config(text=time_label_text)

        self.master.after(100, self.update_progress)

    def format_time(self, milliseconds):
        seconds, milliseconds = divmod(milliseconds, 1000)
        minutes, seconds = divmod(seconds, 60)
        hours, minutes = divmod(minutes, 60)
        return f"{hours:02}:{minutes:02}:{seconds:02}"

    def update_file_list(self):
        self.file_listbox.delete(0, tk.END)  # 清空列表

        # 用于存储所有文件的预览图
        all_thumbnails = []

        for i, video_file in enumerate(self.video_files):
            self.file_listbox.insert(tk.END, video_file)
            if i == self.current_index:
                self.file_listbox.selection_set(i)  # 高亮当前正在播放的文件

            # 生成当前文件的预览图
            video_path = os.path.join(self.folder_path, video_file)
            thumbnail = self.generate_thumbnail(video_path)
            all_thumbnails.append(thumbnail)

        # 显示所有预览图
        self.display_all_thumbnails_threaded(all_thumbnails)


    def listbox_click(self, event):
        selected_index = self.file_listbox.curselection()
        if selected_index:
            self.current_index = selected_index[0]
            self.play_selected_video()

    def show_move_to_panel(self):
        self.move_to_window = tk.Toplevel(self.master)
        self.move_to_window.title("移动到")

        # 添加常用文件夹按钮
        for folder in self.common_folders:
            folder_button = tk.Button(self.move_to_window, text=folder, command=lambda f=folder: self.move_to_folder(f))
            folder_button.pack(side=tk.TOP, padx=10, pady=5)

        # 添加其他选项按钮
        other_button = tk.Button(self.move_to_window, text="其他", command=self.move_to_folder)
        other_button.pack(side=tk.TOP, padx=10, pady=5)

    def show_folder_move_to_panel(self):
        self.move_to_window = tk.Toplevel(self.master)
        self.move_to_window.title("移动到")

        # 添加常用文件夹按钮
        for folder in self.common_folders:
            folder_button = tk.Button(self.move_to_window, text=folder, command=lambda f=folder: self.move_whole_folder(f))
            folder_button.pack(side=tk.TOP, padx=10, pady=5)

        # 添加其他选项按钮
        other_button = tk.Button(self.move_to_window, text="其他", command=self.move_whole_folder)
        other_button.pack(side=tk.TOP, padx=10, pady=5)

    def move_to_folder(self, destination_folder=None):
        if destination_folder is None:
            destination_folder = filedialog.askdirectory()

        if destination_folder:
            # 记录选择次数
            if destination_folder in self.folder_selection_count:
                self.folder_selection_count[destination_folder] += 1
            else:
                self.folder_selection_count[destination_folder] = 1
            print(self.folder_selection_count)

            current_video_path = os.path.join(self.folder_path, self.video_files[self.current_index])
            print(current_video_path)
            new_video_path = os.path.join(destination_folder, os.path.basename(current_video_path))
            print(new_video_path)

            try:
                # 停止播放
                self.stop_video()

                # 关闭当前播放的视频文件
                self.player.set_media(None)
                self.player.stop()

                # 移动文件
                os.rename(current_video_path, new_video_path)
                messagebox.showinfo("移动成功", f"视频已成功移动到:\n{new_video_path}")

                # 清除播放列表
                self.video_files.pop(self.current_index)

                # 记录常用文件夹
                if destination_folder not in self.common_folders:
                    self.common_folders.append(destination_folder)
                    if len(self.common_folders) >= 20:
                        # 如果已经有20个文件夹记录，删除最早添加的一个
                        oldest_folder = self.common_folders.pop(0)
                    self.save_common_folders()

                # 更新播放列表和 current_index
                self.update_file_list_threaded()

                # 检查是否需要更新 current_index
                if self.current_index >= len(self.video_files):
                    self.current_index = len(self.video_files) - 1

                # 播放下一个视频
                self.play_selected_video()

                # 关闭常用文件夹选择窗口
                if self.move_to_window:
                    self.move_to_window.destroy()

            except Exception as e:
                messagebox.showerror("移动失败", f"移动视频时出错:\n{str(e)}")

            if self.folder_selection_count[destination_folder] % 5 == 0:
                self.sort_folders_by_selection_count()

    def move_whole_folder(self, destination_folder=None):
        if destination_folder is None:
            destination_folder = filedialog.askdirectory()

        if destination_folder:
            # 记录选择次数
            if destination_folder in self.folder_selection_count:
                self.folder_selection_count[destination_folder] += 1
            else:
                self.folder_selection_count[destination_folder] = 1

            current_video_path = os.path.join(self.folder_path, self.video_files[self.current_index])
            original_folder_name = os.path.basename(self.folder_path)
            new_video_path = os.path.join(destination_folder, original_folder_name)

            try:
                # 停止播放
                self.stop_video()

                # 关闭当前播放的视频文件
                self.player.set_media(None)
                self.player.stop()

                # 获取源文件夹的父文件夹
                source_parent_folder = os.path.dirname(self.folder_path)

                #清理文件夹
                self.clean_folder()

                # 移动整个文件夹
                shutil.move(self.folder_path, destination_folder)

                # 将self.folder_path设定为源文件夹的父文件夹
                self.folder_path = source_parent_folder

                messagebox.showinfo("移动成功", f"整个文件夹已成功移动到:\n{new_video_path}")

                # 清除播放列表
                self.video_files.pop(self.current_index)

                # 记录常用文件夹
                if destination_folder not in self.common_folders:
                    self.common_folders.append(destination_folder)
                    if len(self.common_folders) >= 20:
                        # 如果已经有20个文件夹记录，删除最早添加的一个
                        oldest_folder = self.common_folders.pop(0)
                    self.save_common_folders()

                # 更新播放列表和 current_index
                self.update_file_list_threaded()

                # 检查是否需要更新 current_index
                if self.current_index >= len(self.video_files):
                    self.current_index = len(self.video_files) - 1

                # 播放下一个视频
                #self.open_folder(source_parent_folder)  # 打开源文件夹的父文件夹

                # 关闭常用文件夹选择窗口
                if self.move_to_window:
                    self.move_to_window.destroy()

            except Exception as e:
                messagebox.showerror("移动失败", f"移动视频时出错:\n{str(e)}")

            if self.folder_selection_count[destination_folder] % 5 == 0:
                self.sort_folders_by_selection_count()

        self.open_folder(self.folder_path)

    def sort_folders_by_selection_count(self):
        # 按照选择次数对文件夹排序
        sorted_folders = sorted(self.folder_selection_count.keys(), key=lambda x: self.folder_selection_count[x],
                                reverse=True)

        # 更新常用文件夹列表
        self.common_folders = sorted_folders

        # 更新播放列表和 current_index
        self.update_file_list_threaded()

        # 检查是否需要更新 current_index
        if self.current_index >= len(self.video_files):
            self.current_index = len(self.video_files) - 1

        # 播放下一个视频
        self.play_selected_video()

        # 关闭常用文件夹选择窗口
        if self.move_to_window:
            self.move_to_window.destroy()
    def save_common_folders(self):
        with open('common_folders.json', 'w') as file:
            json.dump(self.common_folders, file)

    def load_common_folders(self):
        try:
            with open('common_folders.json', 'r') as file:
                self.common_folders = json.load(file)
        except FileNotFoundError:
            pass


    def generate_thumbnail(self, video_path):
        thumbnail_size = (150, 100)
        video_capture = cv2.VideoCapture(video_path)

        # 设置视频的当前帧为第300帧
        #target_frame = 900
        #video_capture.set(cv2.CAP_PROP_POS_FRAMES, target_frame)

        _, frame = video_capture.read()
        video_capture.release()

        if frame is not None:
            frame = cv2.resize(frame, thumbnail_size)
            thumbnail = Image.fromarray(cv2.cvtColor(frame, cv2.COLOR_BGR2RGB))
            return ImageTk.PhotoImage(thumbnail)
        else:
            # 处理视频帧读取失败的情况
            return None

    def favorite_video(self):
        current_video_file = self.video_files[self.current_index]
        self.favorite_status[current_video_file] = not self.favorite_status.get(current_video_file, False)
        self.file_states[current_video_file] = {'收藏状态': '藏' if self.favorite_status[current_video_file] else '-'}
        self.save_states_to_csv()
        if self.favorite_status[current_video_file]:
            messagebox.showinfo("收藏", "视频已收藏")
        else:
            messagebox.showinfo("取消收藏", "视频已取消收藏")

    def save_states_to_csv(self):
        csv_file_path = os.path.join(self.folder_path, "file_states.csv")

        with open(csv_file_path, mode='w', newline='', encoding='utf-8') as file:
            fieldnames = ['文件名'] + list(self.file_states[self.video_files[0]].keys())
            writer = csv.DictWriter(file, fieldnames=fieldnames)

            writer.writeheader()
            for video_file, state in self.file_states.items():
                row = {'文件名': video_file}
                row.update(state)
                writer.writerow(row)

    def delete_folder(self):
        # 在这里实现删除文件夹的逻辑
        if self.video_files:
            confirmation = messagebox.askyesno("确认删除", "确认要删除当前文件夹吗？")
            if confirmation:
                # 停止播放
                self.stop_video()
                # 获取源文件夹的父文件夹
                source_parent_folder = os.path.dirname(self.folder_path)

                print(f"Deleting folder: {self.folder_path}")
                shutil.rmtree(self.folder_path)  # 递归删除文件夹及其内容
                print("Folder deleted")

                # 记录被删除的文件夹路径到指定文件中
                with open("d:/run/deleted.txt", "a") as f:
                    f.write(self.folder_path + "\n")

                # 将self.folder_path设定为源文件夹的父文件夹
                self.folder_path = source_parent_folder

                # 在这里可以添加其他相应的逻辑，如清空界面、更新状态等
                self.video_files = []
                self.current_index = 0
        self.open_folder(self.folder_path)

    def clean_folder(self):
        if self.folder_path:
            self.delete_non_media_files_recursive(self.folder_path)
            self.delete_empty_folders_recursive(self.folder_path)

    def delete_non_media_files_recursive(self, folder_path):
        media_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.heic', '.mp4', '.avi', '.mkv', '.mov', '.ts',
                            '.mpg', '.mpeg', '.wmv']

        for root, dirs, files in os.walk(folder_path, topdown=False):
            for file in files:
                file_path = os.path.join(root, file)
                _, file_extension = os.path.splitext(file)
                mime_type, _ = mimetypes.guess_type(file_path)

                print(f"File: {file_path}, MIME Type: {mime_type}, Extension: {file_extension}")

                file_size = os.path.getsize(file_path)
                if file_extension and file_extension.lower() not in media_extensions or file_size < 40 * 1024:
                    #if mime_type and not mime_type.startswith(('image/', 'video/')):
                        os.remove(file_path)
                        print(f"已删除文件: {file_path}")

    def delete_empty_folders_recursive(self, folder_path):
        for root, dirs, files in os.walk(folder_path, topdown=False):
            for folder in dirs:
                folder_path = os.path.join(root, folder)
                if not os.listdir(folder_path):  # 如果文件夹为空
                    os.rmdir(folder_path)
                    print(f"已删除空文件夹: {folder_path}")

    def seek_backward(self):
        # 后退 10 秒
        current_time = self.player.get_time()
        new_time = max(current_time - 10000, 0)  # Ensure the time is non-negative
        self.player.set_time(new_time)

    def seek_forward(self):
        # 前进 10 秒
        current_time = self.player.get_time()
        new_time = current_time + 10000  # 10 seconds in milliseconds
        self.player.set_time(new_time)

    def rename_video(self):
        if self.folder_path and self.video_files:
            current_video_path = self.video_files[self.current_index]

            # 获取文件夹名
            folder_name = os.path.basename(self.folder_path)

            # 构建新文件名
            new_file_name = f"{folder_name}_{os.path.basename(current_video_path)}"

            # 构建新文件路径
            new_video_path = os.path.join(os.path.dirname(current_video_path), new_file_name)

            try:
                # 停止播放
                self.stop_video()

                # 关闭当前播放的视频文件
                self.player.set_media(None)
                self.player.stop()

                # 更名文件
                os.rename(current_video_path, new_video_path)

                messagebox.showinfo("更名成功", f"文件已成功更名为:\n{new_file_name}")

                # 更新播放列表和 current_index
                self.update_file_list_threaded()

                # 播放下一个视频
                self.play_selected_video()

            except Exception as e:
                messagebox.showerror("更名失败", f"更名时出错:\n{str(e)}")

        else:
            messagebox.showwarning("无效操作", "请先打开一个文件夹并选择一个视频。")

    def on_closing(self):
        self.stop_video()
        self.master.destroy()


if __name__ == "__main__":
    root = tk.Tk()
    player = VideoPlayer(root)
    root.mainloop()