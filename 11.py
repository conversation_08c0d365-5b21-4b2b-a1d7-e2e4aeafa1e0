import tkinter as tk
from tkinter import simpledialog, messagebox
from tkinter import ttk
from webdav3.client import Client
import os
import json

# 登录信息的文件路径
LOGIN_INFO_FILE = 'webdav_logins.json'


# 创建主窗口
class WebDAVBrowser(tk.Tk):
    def __init__(self):
        super().__init__()
        self.title("WebDAV 文件浏览器")
        self.geometry("600x400")

        # 登录信息
        self.webdav_hostname = None
        self.webdav_login = None
        self.webdav_password = None

        # 初始化登录信息
        self.logins = self.load_logins()

        # 初始化界面
        self.create_widgets()

    def create_widgets(self):
        # 创建选择已保存站点的下拉框
        self.saved_sites_label = tk.Label(self, text="已保存的站点:")
        self.saved_sites_label.pack(pady=5)

        self.saved_sites_combo = ttk.Combobox(self, state="readonly")
        self.saved_sites_combo['values'] = list(self.logins.keys())
        self.saved_sites_combo.pack(pady=5)

        self.saved_sites_combo.bind("<<ComboboxSelected>>", self.use_saved_login)

        # 创建输入 WebDAV 地址的按钮
        self.login_button = tk.Button(self, text="输入新的 WebDAV 地址和登录信息", command=self.get_webdav_info)
        self.login_button.pack(pady=10)

        # 文件列表框
        self.file_listbox = tk.Listbox(self, width=80, height=20)
        self.file_listbox.pack(pady=10)
        self.file_listbox.bind('<Double-1>', self.on_double_click)

        # 返回上一层按钮
        self.back_button = tk.Button(self, text="返回上一层", command=self.go_back, state=tk.DISABLED)
        self.back_button.pack(pady=5)

        # 选择文件路径的按钮
        self.select_button = tk.Button(self, text="确定", command=self.confirm_selection, state=tk.DISABLED)
        self.select_button.pack(pady=5)

        self.selected_path = tk.StringVar()
        self.selected_label = tk.Label(self, textvariable=self.selected_path)
        self.selected_label.pack(pady=5)

    def load_logins(self):
        # 加载保存的登录信息
        if os.path.exists(LOGIN_INFO_FILE):
            with open(LOGIN_INFO_FILE, 'r') as f:
                return json.load(f)
        return {}

    def save_login_info(self, hostname, username, password):
        # 保存登录信息
        self.logins[hostname] = {"username": username, "password": password}
        with open(LOGIN_INFO_FILE, 'w') as f:
            json.dump(self.logins, f)
        # 更新下拉框
        self.saved_sites_combo['values'] = list(self.logins.keys())

    def get_webdav_info(self):
        # 输入 WebDAV 地址
        self.webdav_hostname = simpledialog.askstring("输入 WebDAV 地址", "请输入 WebDAV 服务器地址:")
        self.webdav_login = simpledialog.askstring("输入用户名", "请输入用户名:")
        self.webdav_password = simpledialog.askstring("输入密码", "请输入密码:", show="*")

        # 如果地址和登录信息不为空，则连接 WebDAV
        if self.webdav_hostname and self.webdav_login and self.webdav_password:
            self.connect_webdav()

    def use_saved_login(self, event):
        # 使用保存的登录信息
        selected_site = self.saved_sites_combo.get()
        if selected_site in self.logins:
            login_info = self.logins[selected_site]
            self.webdav_hostname = selected_site
            self.webdav_login = login_info['username']
            self.webdav_password = login_info['password']
            self.connect_webdav()

    def connect_webdav(self):
        try:
            # 配置 WebDAV 服务器
            options = {
                'webdav_hostname': self.webdav_hostname,
                'webdav_login': self.webdav_login,
                'webdav_password': self.webdav_password
            }

            self.client = Client(options)

            # 列出根目录的文件
            self.browse_directory('/')

            # 保存成功的登录信息
            self.save_login_info(self.webdav_hostname, self.webdav_login, self.webdav_password)

        except Exception as e:
            messagebox.showerror("错误", f"无法连接 WebDAV 服务器: {str(e)}")

    def browse_directory(self, path):
        try:
            # 列出远程目录中的文件
            files = self.client.list(path)

            # 清空文件列表框
            self.file_listbox.delete(0, tk.END)

            # 显示文件列表
            for file in files:
                self.file_listbox.insert(tk.END, file)

            # 保存当前路径
            self.current_path = path

            # 激活选择按钮和返回按钮
            self.select_button.config(state=tk.NORMAL)
            if path != '/':  # 非根目录才启用返回上一层按钮
                self.back_button.config(state=tk.NORMAL)
            else:
                self.back_button.config(state=tk.DISABLED)

        except Exception as e:
            messagebox.showerror("错误", f"无法浏览目录: {str(e)}")

    def on_double_click(self, event):
        # 双击文件夹或文件，进行导航或选择
        selected_file = self.file_listbox.get(self.file_listbox.curselection())

        # 如果选择的是文件夹，则进入该文件夹
        new_path = f"{self.current_path}/{selected_file}".replace("//", "/")
        if self.client.is_dir(new_path):
            self.browse_directory(new_path)
        else:
            self.selected_path.set(f"选择的文件路径: {new_path}")

    def go_back(self):
        # 返回上一层
        if self.current_path != '/':
            parent_path = os.path.dirname(self.current_path.rstrip('/'))
            if not parent_path:
                parent_path = '/'
            self.browse_directory(parent_path)

    def confirm_selection(self):
        # 点击确定按钮，递归查找所有子文件夹中的视频文件
        selected_file = self.file_listbox.get(self.file_listbox.curselection())
        selected_full_path = f"{self.current_path}/{selected_file}".replace("//", "/")


        # 检查是否是文件夹
        if self.client.is_dir(selected_full_path):
            # 获取所有子文件夹中的视频文件
            video_files = self.get_all_video_files(selected_full_path)
            if video_files:
                # 显示视频文件路径
                video_paths = "\n".join(video_files)
                self.selected_path.set(f"视频文件路径:\n{video_paths}")
                messagebox.showinfo("文件选择", f"文件夹及其子文件夹中的视频文件:\n{video_paths}")
            else:
                messagebox.showinfo("文件选择", "该文件夹及其子文件夹中没有视频文件")
        else:
            # 显示选择的文件路径
            self.selected_path.set(f"选择的文件路径: {selected_full_path}")
            messagebox.showinfo("提示", f"选择的文件路径:\n{selected_full_path}")

    def get_all_video_files(self, folder_path):
        try:
            # 递归获取文件夹及子文件夹中的所有文件
            all_files = []
            video_extensions = ['.mp4', '.mkv', '.avi', '.mov', '.flv']

            # 获取当前文件夹中的所有文件和子文件夹
            files = self.client.list(folder_path)

            for file in files:
                # 这里检查 file 是否已经包含在 folder_path 中，避免重复拼接
                if folder_path.endswith(file):
                    # 如果 folder_path 已经包含 file，则直接使用 folder_path
                    full_file_path = folder_path
                else:
                    # 正常拼接路径，确保不会重复
                    full_file_path = os.path.join(folder_path, file).replace("//", "/")

                if self.client.is_dir(full_file_path):
                    # 递归获取子文件夹中的文件
                    all_files.extend(self.get_all_video_files(full_file_path))
                else:
                    # 如果是视频文件，则添加到列表
                    if os.path.splitext(file)[1].lower() in video_extensions:
                        all_files.append(full_file_path)

            return all_files
        except Exception as e:
            messagebox.showerror("错误", f"无法读取文件夹: {folder_path}\n原因: {str(e)}")
            return []


# 运行程序
if __name__ == "__main__":
    app = WebDAVBrowser()
    app.mainloop()
