{"cells": [{"cell_type": "code", "execution_count": 1, "id": "68f72f87", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["apples     3\n", "oranges    2\n", "bananas    1\n", "Name: A, dtype: int64\n", "apples     3\n", "oranges    2\n", "bananas    1\n", "Name: A, dtype: int64\n"]}], "source": ["import pandas as pd\n", "\n", "data = {'apples': [3, 2, 0, 1], 'oranges': [2, 4, 6, 8], 'bananas': [1, 3, 5, 7]}\n", "index = ['A', 'B', 'C', 'D']\n", "df = pd.DataFrame(data, index=index)\n", "\n", "# 使用.loc访问行\n", "print(df.loc['A'])\n", "\n", "# 使用.iloc访问行\n", "print(df.iloc[0])"]}, {"cell_type": "code", "execution_count": null, "id": "a785bb0d", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}