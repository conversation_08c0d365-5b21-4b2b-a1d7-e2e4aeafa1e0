import os
import json
import tkinter as tk
from tkinter import filedialog, messagebox, Listbox, Frame, ttk
from tkinter import scrolledtext  # 用于显示详细结果
import shutil

# 创建主窗口
root = tk.Tk()
root.title("关键字查找工具")
root.geometry("1200x800")  # 改为更大的窗口尺寸

keywords = []
config_file = "search_config.json"  # 保存配置的文件

# 保存配置
def save_config(keyword_folder, search_folder):
    config = {
        "keyword_folder": keyword_folder,
        "search_folder": search_folder
    }
    try:
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False)
    except Exception as e:
        print(f"保存配置失败: {str(e)}")

# 加载配置
def load_config():
    try:
        if os.path.exists(config_file):
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
                return config.get("keyword_folder"), config.get("search_folder")
    except Exception as e:
        print(f"加载配置失败: {str(e)}")
    return None, None

def extract_keywords_from_folders(folder_path):
    """从文件夹名称中提取关键字"""
    global keywords
    keywords = []
    folder_names = {}
    # 定义标点符号，不包括下划线和空格
    punctuation = '.,()[]{}!@#$%^&*+=<>?/\\'
    
    for entry in os.scandir(folder_path):
        if entry.is_dir() and entry.name.lower() not in ('t', 'temp', 't1', 't2'):
            # 将文件夹名称按空格分隔，保持原始格式
            folder_keywords = [k for k in entry.name.split() if not all(c in punctuation for c in k)]
            for keyword in folder_keywords:
                if keyword.lower() not in folder_names:
                    folder_names[keyword.lower()] = []
                folder_names[keyword.lower()].append(entry.name)
                if keyword not in keywords:
                    keywords.append(keyword)

# 选择关键字文件夹的函数
def select_keyword_folder():
    folder_path = filedialog.askdirectory(title="选择关键字文件夹")
    if folder_path:
        extract_keywords_from_folders(folder_path)
        keyword_path_label.config(text=f"当前关键字文件夹: {folder_path}")
        save_config(folder_path, folder_path_label.cget("text").replace("当前文件夹: ", ""))

# 选择搜索文件夹的函数
def select_folder():
    folder_path = filedialog.askdirectory(title="选择文件夹")
    if folder_path:
        folder_path_label.config(text=f"当前待整理文件夹: {folder_path}")
        save_config(keyword_path_label.cget("text").replace("当前关键字文件夹: ", ""), folder_path)

# 开始搜索
def start_search():
    folder_path = folder_path_label.cget("text").replace("当前待整理文件夹: ", "")
    if not folder_path or not os.path.exists(folder_path):
        messagebox.showwarning("警告", "请先选择有效的文件夹！")
        return
    if not keywords:
        messagebox.showwarning("警告", "请先加载关键字文件！")
        return
    
    list_files_and_folders(folder_path)

# 列出包含关键字的文件和文件夹
def list_files_and_folders(folder_path):
    if not keywords:
        messagebox.showwarning("警告", "请先加载关键字文件！")
        return

    # 使用字典来存储每个关键字对应的结果
    result_by_keyword = {keyword: {'folders': {}, 'files': []} for keyword in keywords}
    
    # 创建进度条窗口
    progress_window = tk.Toplevel(root)
    progress_window.title("搜索进度")
    progress_window.geometry("300x150")
    
    progress_label = tk.Label(progress_window, text="正在搜索...")
    progress_label.pack(pady=10)
    
    progress_bar = ttk.Progressbar(progress_window, length=200, mode='determinate')
    progress_bar.pack(pady=10)
    
    # 首先收集所有匹配的路径
    all_matches = []
    file_matches = []
    
    # 计算总文件数
    total_items = 0
    for root_dir, dirs, files in os.walk(folder_path):
        # 跳过特定文件夹
        if 'lowre' in root_dir.lower() or 'lowrespic' in root_dir.lower():
            continue
        total_items += len(dirs) + len(files)
    
    current_items = 0
    
    # 遍历收集匹配项
    for root_dir, dirs, files in os.walk(folder_path):
        # 跳过特定文件夹
        if 'lowre' in root_dir.lower() or 'lowrespic' in root_dir.lower():
            continue
            
        current_items += len(dirs) + len(files)
        progress = (current_items / total_items) * 100
        progress_bar['value'] = progress
        progress_label.config(text=f"正在搜索: {os.path.basename(root_dir)}")
        progress_window.update()

        # 检查当前文件夹是否匹配任何关键字
        for keyword in keywords:
            base_name = os.path.basename(root_dir)
            if smart_word_match(base_name, keyword):
                all_matches.append((root_dir, keyword))
                break
        
        # 检查文件名
        for file in files:
            for keyword in keywords:
                if smart_word_match(os.path.splitext(file)[0], keyword):
                    file_path = os.path.join(root_dir, file)
                    file_matches.append((file_path, keyword))
                    break

    # 过滤掉被父文件夹包含的路径
    filtered_matches = []
    for path, keyword in all_matches:
        is_subpath = False
        for other_path, _ in all_matches:
            if path != other_path and path.startswith(other_path + os.sep):
                is_subpath = True
                break
        if not is_subpath:
            filtered_matches.append((path, keyword))

    # 过滤掉在匹配文件夹内的文件
    filtered_files = []
    for file_path, keyword in file_matches:
        is_in_matched_folder = False
        for folder_path, _ in filtered_matches:
            if file_path.startswith(folder_path + os.sep):
                is_in_matched_folder = True
                break
        if not is_in_matched_folder:
            filtered_files.append((file_path, keyword))

    # 将过滤后的结果添加到结果字典
    for path, keyword in filtered_matches:
        result_by_keyword[keyword]['folders'][path] = {
            'subfolders': [],
            'files': []
        }
    
    # 添加文件结果
    for file_path, keyword in filtered_files:
        result_by_keyword[keyword]['files'].append(file_path)

    progress_window.destroy()
    display_results(result_by_keyword)

# 在界面中显示结果
def display_results(result_by_keyword):
    for widget in result_frame.winfo_children():
        widget.destroy()

    # 创建滚动框架
    canvas = tk.Canvas(result_frame)
    v_scrollbar = ttk.Scrollbar(result_frame, orient="vertical", command=canvas.yview)
    h_scrollbar = ttk.Scrollbar(result_frame, orient="horizontal", command=canvas.xview)
    scrollable_frame = ttk.Frame(canvas)

    scrollable_frame.bind(
        "<Configure>",
        lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
    )

    canvas.configure(
        yscrollcommand=v_scrollbar.set,
        xscrollcommand=h_scrollbar.set,
        width=1160,
        height=600
    )

    canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")

    total_folders = 0
    total_files = 0

    # 按关键字显示结果
    for keyword in keywords:
        folders = result_by_keyword[keyword]['folders']
        files = result_by_keyword[keyword]['files']
        
        if folders or files:  # 如果有任何匹配结果
            keyword_frame = ttk.LabelFrame(scrollable_frame, text=f"关键字 「{keyword}」 的搜索结果")
            keyword_frame.pack(fill="x", padx=5, pady=5)
            
            # 显示文件夹
            if folders:
                folder_header = ttk.Label(
                    keyword_frame,
                    text="[文件夹]",
                    font=("Arial", 10, "bold")
                )
                folder_header.pack(anchor="w", padx=5, pady=(5,0))
                
            for main_folder in folders:
                folder_frame = ttk.Frame(keyword_frame)
                folder_frame.pack(fill="x", padx=5, pady=2)
                
                path_frame = ttk.Frame(folder_frame)
                path_frame.pack(side="left", fill="x", expand=True)
                
                # 显示完整路径
                folder_name = os.path.basename(main_folder)
                folder_path = main_folder[:-len(folder_name)]
                
                full_path_label = ttk.Label(
                    path_frame,
                    text=f"📁 {folder_path}{folder_name}",
                    wraplength=800,  # 减小wraplength以确保按钮可见
                    justify="left"
                )
                full_path_label.pack(anchor="w")
                
                move_btn = ttk.Button(folder_frame, text=f"移动到「{keyword}」文件夹")
                move_btn.configure(command=lambda f=main_folder, k=keyword, b=move_btn: move_item(f, k, b))
                move_btn.pack(side="right", padx=5)
            
            # 显示独立的文件
            if files:
                file_header = ttk.Label(
                    keyword_frame,
                    text="[文件]",
                    font=("Arial", 10, "bold")
                )
                file_header.pack(anchor="w", padx=5, pady=(15,0))
                
            for file_path in files:
                file_frame = ttk.Frame(keyword_frame)
                file_frame.pack(fill="x", padx=5, pady=2)
                
                path_frame = ttk.Frame(file_frame)
                path_frame.pack(side="left", fill="x", expand=True)
                
                # 显示完整路径
                file_name = os.path.basename(file_path)
                file_dir = file_path[:-len(file_name)]
                
                full_path_label = ttk.Label(
                    path_frame,
                    text=f"📄 {file_dir}{file_name}",
                    wraplength=800,  # 减小wraplength以确保按钮可见
                    justify="left"
                )
                full_path_label.pack(anchor="w")
                
                move_btn = ttk.Button(file_frame, text=f"移动到「{keyword}」文件夹")
                move_btn.configure(command=lambda f=file_path, k=keyword, b=move_btn: move_item(f, k, b))
                move_btn.pack(side="right", padx=5)

    # 添加结果统计
    stats_frame = ttk.Frame(scrollable_frame)
    stats_frame.pack(fill="x", padx=5, pady=10)
    stats_label = ttk.Label(
        stats_frame, 
        text=f"搜索完成！总计找到 {total_folders} 个文件夹，{total_files} 个文件。"
    )
    stats_label.pack()

    # 布局滚动条和画布
    h_scrollbar.pack(side="bottom", fill="x")
    v_scrollbar.pack(side="right", fill="y")
    canvas.pack(side="left", fill="both", expand=True, padx=5)

    # 绑定鼠标滚轮事件
    def _on_mousewheel(event):
        canvas.yview_scroll(int(-1*(event.delta/120)), "units")
    
    def _on_shift_mousewheel(event):
        canvas.xview_scroll(int(-1*(event.delta/120)), "units")

    canvas.bind_all("<MouseWheel>", _on_mousewheel)
    canvas.bind_all("<Shift-MouseWheel>", _on_shift_mousewheel)

# 创建路径显示标签
path_frame = Frame(root)
path_frame.pack(pady=5, fill=tk.X, padx=10)

keyword_path_label = tk.Label(path_frame, text="当前关键字文件夹: 未选择", anchor="w", wraplength=1160)  # 添加自动换行
keyword_path_label.pack(fill=tk.X)

folder_path_label = tk.Label(path_frame, text="当前待整理文件夹: 未选择", anchor="w", wraplength=1160)  # 修改初始显示文本
folder_path_label.pack(fill=tk.X)

# 创建结果显示区域
result_frame = Frame(root)
result_frame.pack(pady=10, fill=tk.BOTH, expand=True)  # 允许结果区域填充和扩展

# 创建按钮
button_frame = Frame(root)
button_frame.pack(pady=20)

load_keywords_button = tk.Button(button_frame, text="选择关键字文件夹", command=select_keyword_folder)
load_keywords_button.pack(side=tk.LEFT, padx=10)

select_folder_button = tk.Button(button_frame, text="选择搜索文件夹", command=select_folder)
select_folder_button.pack(side=tk.LEFT, padx=10)

start_search_button = tk.Button(button_frame, text="开始搜索", command=start_search)
start_search_button.pack(side=tk.LEFT, padx=10)

# 加载上次的配置
last_keyword_folder, last_folder = load_config()
if last_keyword_folder and os.path.exists(last_keyword_folder):
    extract_keywords_from_folders(last_keyword_folder)
    keyword_path_label.config(text=f"当前关键字文件夹: {last_keyword_folder}")
if last_folder and os.path.exists(last_folder):
    folder_path_label.config(text=f"当前待整理文件夹: {last_folder}")  # 修改这里的显示文本

def get_folder_info(folder_path):
    """获取文件夹的详细信息"""
    total_size = 0
    file_count = 0
    file_list = []
    
    for root, _, files in os.walk(folder_path):
        for file in files:
            file_path = os.path.join(root, file)
            try:
                size = os.path.getsize(file_path)
                total_size += size
                file_count += 1
                file_list.append((file, size))
            except Exception:
                continue
    
    return {
        'total_size': total_size,
        'file_count': file_count,
        'files': file_list
    }

def format_size(size):
    """格式化文件大小显示"""
    for unit in ['B', 'KB', 'MB', 'GB']:
        if size < 1024:
            return f"{size:.2f} {unit}"
        size /= 1024
    return f"{size:.2f} TB"

def move_item(item_path, keyword, button):
    """移动文件或文件夹到对应的关键字子文件夹中"""
    source_folder = keyword_path_label.cget("text").replace("当前关键字文件夹: ", "")
    if not source_folder or not os.path.exists(source_folder):
        messagebox.showerror("错误", "源文件夹不存在！")
        return
    
    # 查找包含关键字的子文件夹
    target_subfolder = None
    for entry in os.scandir(source_folder):
        if entry.is_dir() and keyword in entry.name:
            target_subfolder = entry.path
            break
    
    if not target_subfolder:
        messagebox.showerror("错误", f"在源文件夹中未找到包含关键字「{keyword}」的子文件夹！")
        return
        
    try:
        # 获取目标路径
        target_path = os.path.join(target_subfolder, os.path.basename(item_path))
        
        # 检查目标路径是否已存在
        if os.path.exists(target_path):
            # 准备比较信息
            if os.path.isfile(item_path):
                # 文件比较
                src_size = os.path.getsize(item_path)
                dst_size = os.path.getsize(target_path)
                
                compare_msg = (
                    f"源文件: {format_size(src_size)}\n"
                    f"目标文件: {format_size(dst_size)}\n"
                    f"大小差异: {format_size(abs(src_size - dst_size))}\n"
                    f"\n是否覆盖？"
                )
            else:
                # 文件夹比较
                src_info = get_folder_info(item_path)
                dst_info = get_folder_info(target_path)
                
                compare_msg = (
                    f"源文件夹: {format_size(src_info['total_size'])}，{src_info['file_count']} 个文件\n"
                    f"目标文件夹: {format_size(dst_info['total_size'])}，{dst_info['file_count']} 个文件\n"
                    f"大小差异: {format_size(abs(src_info['total_size'] - dst_info['total_size']))}\n"
                    f"文件数差异: {abs(src_info['file_count'] - dst_info['file_count'])} 个\n"
                    f"\n是否覆盖？"
                )
            
            if not messagebox.askyesno("确认覆盖", 
                f"目标路径已存在：\n{target_path}\n\n"
                f"{compare_msg}"
            ):
                return
                
        # 执行移动操作
        shutil.move(item_path, target_path)
        
        # 禁用按钮并改变文本
        button.configure(state='disabled', text="已移动")
        
    except Exception as e:
        messagebox.showerror("错误", f"移动失败：{str(e)}")

def is_english_word(text):
    """判断是否为英文单词（允许包含数字和下划线）"""
    # 检查是否包含非ASCII字符（如中文、日文等）
    if any(ord(c) > 127 for c in text):
        return False
    # 检查是否只包含英文字母、数字和下划线
    return all(c.isalnum() or c == '_' for c in text) and any(c.isalpha() for c in text)

def smart_word_match(text, keyword):
    """智能匹配关键字
    - 对于英文关键字：如果关键字本身是英文，则需要匹配完整单词（下划线连接的视为一个单词）
    - 对于中文/其他字符：直接包含即可
    """
    if is_english_word(keyword):
        # 只使用空格分割文本，保持下划线连接的完整性
        words = []
        for part in text.split():
            # 处理可能的连续英文单词（没有空格分隔的情况）
            if not any(c.isalpha() for c in part):
                continue

            # 提取所有连续的英文字母数字下划线组合
            current = ''
            for c in part:
                if c.isascii() and (c.isalnum() or c == '_'):
                    current += c
                else:
                    if current and any(c.isalpha() for c in current):
                        words.append(current)
                    current = ''
            if current and any(c.isalpha() for c in current):
                words.append(current)

        # 对于英文关键字，需要完整匹配单词
        return keyword.lower() in [w.lower() for w in words]
    else:
        # 对于中文/其他字符，直接包含即可（不区分大小写）
        return keyword.lower() in text.lower()

# 运行主循环
root.mainloop()
