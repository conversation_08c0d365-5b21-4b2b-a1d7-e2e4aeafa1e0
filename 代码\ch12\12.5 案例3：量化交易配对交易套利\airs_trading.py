import pandas as pd
import numpy as np

# 假设我们有两只股票的价格数据
stock1_prices = [10.05, 10.07, 10.06, 10.08, 10.10, 10.12, 10.11]
stock2_prices = [8.02, 8.01, 8.04, 8.06, 8.05, 8.03, 8.07]

# 将价格数据转换为DataFrame
data = pd.DataFrame({'stock1': stock1_prices, 'stock2': stock2_prices})

# 计算两只股票的价格差异（spread）
spread = data['stock1'] - data['stock2']

# 计算价格差异的均值和标准差
mean_spread = spread.mean()
std_spread = spread.std()

# 定义配对交易策略的信号
threshold = 1.5  # 设置价格差异的阈值
entry_zscore = 1.0  # 设置进入交易的z-score阈值
exit_zscore = 0.0  # 设置退出交易的z-score阈值

# 定义一个函数来执行配对交易策略
def pairs_trading_strategy(data, mean_spread, std_spread, threshold, entry_zscore, exit_zscore):
    # 计算当前价格差异的z-score
    current_spread = data['stock1'] - data['stock2']
    zscore = (current_spread - mean_spread) / std_spread
    
    # 获取最新的zscore值
    latest_zscore = zscore.iloc[-1]
    
    # 判断是否满足进入交易条件
    if latest_zscore > entry_zscore and np.abs(latest_zscore) > threshold:
        # 执行买入stock1、卖空stock2的交易操作
        print("进入交易：买入 stock1，卖空 stock2")
        
    # 判断是否满足退出交易条件
    if np.abs(latest_zscore) < exit_zscore:
        # 执行平仓交易操作
        print("退出交易：平仓")

# 遍历每个时间点，执行配对交易策略
for timestamp, row in data.iterrows():
    pairs_trading_strategy(data.loc[:timestamp], mean_spread, std_spread, threshold, entry_zscore, exit_zscore)
