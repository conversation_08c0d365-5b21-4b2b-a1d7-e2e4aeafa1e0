{"cells": [{"cell_type": "code", "execution_count": 3, "id": "259129d9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["皮尔逊相关系数： 0.41099746826339323\n"]}], "source": ["import pandas as pd\n", "import numpy as np\n", "\n", "# 从CSV文件读取数据\n", "df = pd.read_csv('data/stock_data（相关性）.csv')\n", "\n", "# 转换日期列为日期类型\n", "df['Date'] = pd.to_datetime(df['Date'])\n", "\n", "# 将日期列设为索引\n", "df.set_index('Date', inplace=True)\n", "\n", "# 计算相关系数\n", "correlation_matrix = np.corrcoef(df['Stock_A'], df['Stock_B'])\n", "\n", "# 提取相关系数\n", "correlation = correlation_matrix[0, 1]\n", "\n", "print('皮尔逊相关系数：', correlation)"]}, {"cell_type": "code", "execution_count": null, "id": "c67aa97b", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}