{"cells": [{"cell_type": "code", "execution_count": 1, "id": "6557ef39", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[[1. 0. 0.]\n", " [0. 1. 0.]\n", " [0. 0. 1.]]\n", "float64\n", "[[1. 0. 0.]\n", " [0. 1. 0.]\n", " [0. 0. 1.]]\n", "float64\n"]}], "source": ["import numpy as np\n", "\n", "a = np.identity(3)\n", "print(a)\n", "print(a.dtype)\n", "\n", "b = np.eye(3)\n", "print(b)\n", "print(b.dtype)"]}, {"cell_type": "code", "execution_count": null, "id": "ac4c6eda", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}