import tkinter as tk
from tkinter import filedialog
import os
import shutil


def open_folder():
    folder_path.set(filedialog.askdirectory())
    folder_content.set('\n'.join([folder_path.get() + '/' + name for name in os.listdir(folder_path.get())]))


def search_keyword():
    keyword = keyword_entry.get()
    search_result_paths = [line for line in folder_content.get().split('\n') if keyword in line]
    result_text.delete('1.0', tk.END)
    result_text.insert(tk.END, '\n'.join(search_result_paths))
    return search_result_paths


def move_files():
    keyword = keyword_entry.get()
    new_folder_path = os.path.join(folder_path.get(), keyword)
    os.makedirs(new_folder_path, exist_ok=True)

    search_result_paths = search_keyword()

    for item in search_result_paths:
        item_path = item.split('/')[-1]
        shutil.move(item, os.path.join(new_folder_path, item_path))

    result_text.delete('1.0', tk.<PERSON>ND)
    result_text.insert(tk.END, f"Files moved to: {new_folder_path}")


root = tk.Tk()

folder_path = tk.StringVar()
folder_content = tk.StringVar()

open_button = tk.Button(root, text="Open Folder", command=open_folder)
open_button.pack()

folder_label = tk.Label(root, textvariable=folder_path)
folder_label.pack()

keyword_entry = tk.Entry(root)
keyword_entry.pack()

search_button = tk.Button(root, text="Search Keyword", command=search_keyword)
search_button.pack()

move_button = tk.Button(root, text="Move", command=move_files)
move_button.pack()

result_text = tk.Text(root)
result_text.pack()

root.mainloop()
