{"cells": [{"cell_type": "code", "execution_count": 3, "id": "5521a9e8", "metadata": {}, "outputs": [{"data": {"text/plain": ["1"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["fobj = open('test1.txt', 'w+', encoding='utf-8') \n", "\n", "fobj.write('大家好')\n", "fname1 =r'C:\\Users\\<USER>\\OneDrive\\书\\北大\\AI时代Python量化交易实战：ChatGPT让量化交易插上翅膀\\代码\\ch2\\\\test1.txt'\n", "fobj = open(fname1, 'a+', encoding='utf-8')\n", "fobj.write('！')\n", "fobj.close()"]}, {"cell_type": "code", "execution_count": null, "id": "11b0db6f", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}