{"cells": [{"cell_type": "code", "execution_count": 2, "id": "bfa97add", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>HDate</th>\n", "      <th>Open</th>\n", "      <th>High</th>\n", "      <th>Low</th>\n", "      <th>Close</th>\n", "      <th>Volume</th>\n", "      <th>Symbol</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2023-01-22</td>\n", "      <td>177.3000</td>\n", "      <td>177.7800</td>\n", "      <td>176.6016</td>\n", "      <td>177.00</td>\n", "      <td>27052000</td>\n", "      <td>AAPL</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2023-01-23</td>\n", "      <td>177.3000</td>\n", "      <td>179.4400</td>\n", "      <td>176.8200</td>\n", "      <td>177.04</td>\n", "      <td>32395870</td>\n", "      <td>AAPL</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2023-01-24</td>\n", "      <td>177.2500</td>\n", "      <td>177.3000</td>\n", "      <td>173.2000</td>\n", "      <td>174.22</td>\n", "      <td>51368540</td>\n", "      <td>AAPL</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2023-01-25</td>\n", "      <td>174.5050</td>\n", "      <td>174.9500</td>\n", "      <td>170.5300</td>\n", "      <td>171.11</td>\n", "      <td>41438280</td>\n", "      <td>AAPL</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2023-01-26</td>\n", "      <td>172.0000</td>\n", "      <td>172.0000</td>\n", "      <td>170.0600</td>\n", "      <td>171.51</td>\n", "      <td>39075250</td>\n", "      <td>AAPL</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>58</th>\n", "      <td>2023-04-16</td>\n", "      <td>175.0301</td>\n", "      <td>176.1900</td>\n", "      <td>174.8301</td>\n", "      <td>175.82</td>\n", "      <td>21561320</td>\n", "      <td>AAPL</td>\n", "    </tr>\n", "    <tr>\n", "      <th>59</th>\n", "      <td>2023-04-17</td>\n", "      <td>176.4900</td>\n", "      <td>178.9365</td>\n", "      <td>176.4100</td>\n", "      <td>178.24</td>\n", "      <td>26575010</td>\n", "      <td>AAPL</td>\n", "    </tr>\n", "    <tr>\n", "      <th>60</th>\n", "      <td>2023-04-18</td>\n", "      <td>177.8100</td>\n", "      <td>178.8200</td>\n", "      <td>176.8800</td>\n", "      <td>177.84</td>\n", "      <td>20544600</td>\n", "      <td>AAPL</td>\n", "    </tr>\n", "    <tr>\n", "      <th>61</th>\n", "      <td>2023-04-19</td>\n", "      <td>174.9500</td>\n", "      <td>175.3900</td>\n", "      <td>172.6600</td>\n", "      <td>172.80</td>\n", "      <td>34693280</td>\n", "      <td>AAPL</td>\n", "    </tr>\n", "    <tr>\n", "      <th>62</th>\n", "      <td>2023-04-20</td>\n", "      <td>170.5950</td>\n", "      <td>171.2184</td>\n", "      <td>165.4300</td>\n", "      <td>165.72</td>\n", "      <td>65270950</td>\n", "      <td>AAPL</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>63 rows × 7 columns</p>\n", "</div>"], "text/plain": ["         HDate      Open      High       Low   Close    Volume Symbol\n", "0   2023-01-22  177.3000  177.7800  176.6016  177.00  27052000   AAPL\n", "1   2023-01-23  177.3000  179.4400  176.8200  177.04  32395870   AAPL\n", "2   2023-01-24  177.2500  177.3000  173.2000  174.22  51368540   AAPL\n", "3   2023-01-25  174.5050  174.9500  170.5300  171.11  41438280   AAPL\n", "4   2023-01-26  172.0000  172.0000  170.0600  171.51  39075250   AAPL\n", "..         ...       ...       ...       ...     ...       ...    ...\n", "58  2023-04-16  175.0301  176.1900  174.8301  175.82  21561320   AAPL\n", "59  2023-04-17  176.4900  178.9365  176.4100  178.24  26575010   AAPL\n", "60  2023-04-18  177.8100  178.8200  176.8800  177.84  20544600   AAPL\n", "61  2023-04-19  174.9500  175.3900  172.6600  172.80  34693280   AAPL\n", "62  2023-04-20  170.5950  171.2184  165.4300  165.72  65270950   AAPL\n", "\n", "[63 rows x 7 columns]"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "\n", "# 从SQL数据库读取数据并转换为DataFrame对象\n", "import sqlite3\n", "conn = sqlite3.connect('data/NASDAQ_DB.db')\n", "# 准备SQL语句HistoricalQuote表保存股票历史数据\n", "query = 'SELECT * FROM HistoricalQuote'\n", "data_sql = pd.read_sql(query, conn)\n", "data_sql "]}, {"cell_type": "code", "execution_count": null, "id": "d441f55f", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}