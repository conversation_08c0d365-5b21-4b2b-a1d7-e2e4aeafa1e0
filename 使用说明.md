# HTML解码链接提取器使用说明

## 功能描述
这个工具用于从HTML文件中提取特定格式的解码链接，并汇总包含链接的行及其上下文（上一行和下一行）。

## 支持的链接格式
工具能够识别以下格式的解码链接：

1. **showfilesbot格式**：
   - `showfilesbot_1V_D1t7H5U2y0i7z6p8W1h6`
   - `showfilesbot_1P_q1o7s5u2w0q4n9h2D5R3`
   - `showfilesbot_7V_X1w7G38253t6y9u3t7V6`
   - `showfilesbot_9P_1V_y1D764N772A9e4I3j0s5`

2. **grp格式**：
   - `VT6AjWM77I5v_Xl62uqZV0tU=_grp`
   - `CUbkYSmly4nY_wky2uqZV0tU=_grp`

3. **filepan_bot格式**：
   - `@filepan_bot:_78P_115V_eaeT1VzuCB3x`

## 文件说明

### 1. html_link_extractor_gui.py（图形界面版 - 推荐）
最易用的图形界面版本，特点：
- 🖱️ 图形界面操作，无需命令行
- 📁 通过对话框选择文件或文件夹
- 📊 实时显示提取进度和结果
- 💾 一键保存结果到文件
- 🎨 美观的界面设计
- 适合所有用户使用

### 2. html_link_extractor.py（完整版）
功能最全面的命令行版本，支持：
- 命令行参数和图形界面双模式
- 处理单个文件或整个目录
- 多种编码格式支持
- 详细的统计信息
- 自定义输出文件名

### 3. extract_links_simple.py（简化版）
简单易用的命令行版本，特点：
- 自动处理当前目录下的所有HTML文件
- 直接运行即可使用
- 结果保存为中文文件名
- 适合快速批量处理

## 使用方法

### 方法一：使用图形界面版（强烈推荐）
1. 双击运行 `html_link_extractor_gui.py`
2. 在界面中点击"选择文件"选择单个HTML文件，或点击"选择文件夹"选择包含HTML文件的文件夹
3. 点击"开始提取"开始提取解码链接
4. 查看提取结果，点击"保存结果"将结果保存到文件
5. 可以点击"清空结果"清除当前结果重新开始

**图形界面特点：**
- 🎯 直观易用，无需记忆命令
- 📁 文件选择对话框，方便选择文件
- ⚡ 多线程处理，界面不会卡顿
- 📊 实时显示处理进度和统计信息
- 💾 一键保存结果

### 方法二：使用简化命令行版
1. 将 `extract_links_simple.py` 放在包含HTML文件的目录中
2. 双击运行或在命令行中执行：
   ```bash
   python extract_links_simple.py
   ```
3. 程序会自动处理当前目录下的所有HTML文件
4. 结果保存在 `解码链接提取结果.txt` 文件中

### 方法三：使用完整命令行版
1. 启动图形界面模式（默认）：
   ```bash
   python html_link_extractor.py
   # 或
   python html_link_extractor.py --gui
   ```

2. 处理指定的HTML文件：
   ```bash
   python html_link_extractor.py path/to/file.html
   ```

3. 处理指定目录：
   ```bash
   python html_link_extractor.py path/to/directory
   ```

4. 自定义输出文件名：
   ```bash
   python html_link_extractor.py -o my_results.txt path/to/file.html
   ```

5. 只显示结果不保存文件：
   ```bash
   python html_link_extractor.py --no-save path/to/file.html
   ```

## 输出格式
程序会提取HTML中的纯文本内容，并输出包含链接的三行文本：

```
匹配项 1:
您的文件码已生成，点击复制：
showfilesbot_1V_D1t7H5U2y0i7z6p8W1h6 通用解码器：ShowFilesBot
牢梦
------------------------------------------------------------
```

**特点：**
- 自动从HTML中提取纯文本，去除HTML标签
- 显示包含链接的行及其上下文（共三行）
- 精简显示，专注于文本内容本身

## 编码支持
程序支持多种文件编码格式：
- UTF-8
- GBK
- GB2312
- ANSI
- Latin-1

程序会自动尝试这些编码格式，直到成功读取文件。

## 注意事项
1. 确保Python环境已安装（推荐Python 3.6+）
2. HTML文件应该是有效的文本文件
3. 程序会递归处理子目录中的HTML文件（完整版）
4. 大文件处理可能需要一些时间
5. 结果文件使用UTF-8编码保存

## 故障排除
1. **文件读取失败**：检查文件是否存在且有读取权限
2. **编码错误**：程序会自动尝试多种编码，通常能解决编码问题
3. **没有找到链接**：检查HTML文件中是否确实包含目标格式的链接
4. **程序运行缓慢**：大量文件或大文件会影响处理速度，这是正常现象

## 示例输出
运行程序后，你会看到类似以下的输出：

```
HTML解码链接提取器
========================================
找到 3 个HTML文件

正在处理: messages.html
  找到 5 个匹配项

正在处理: debug_page.html
  找到 2 个匹配项

总共找到 7 个解码链接
============================================================

统计信息:
总链接数: 7
涉及文件数: 2

结果已保存到: 解码链接提取结果.txt
```
