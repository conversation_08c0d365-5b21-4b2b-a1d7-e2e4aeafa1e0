{"cells": [{"cell_type": "code", "execution_count": null, "id": "3381dc12", "metadata": {}, "outputs": [], "source": ["import random\n", "import time\n", "import logging\n", "import sys\n", "\n", "# 配置日志记录\n", "logging.basicConfig(filename='trading.log', level=logging.INFO,\n", "                    format='%(asctime)s - %(levelname)s - %(message)s')\n", "\n", "# 创建控制台输出处理器\n", "console_handler = logging.StreamHandler(sys.stdout)\n", "console_handler.setLevel(logging.INFO)\n", "console_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')\n", "console_handler.setFormatter(console_formatter)\n", "\n", "# 将控制台输出处理器添加到日志记录器\n", "logger = logging.getLogger()\n", "logger.addHandler(console_handler)\n", "\n", "def get_price(symbol):\n", "    # 模拟获取标的的最新报价\n", "    return random.uniform(100, 200)\n", "\n", "def trade(threshold):\n", "    while True:\n", "        # 获取标的1和标的2的最新价格\n", "        price1 = get_price(\"AAPL\")\n", "        price2 = get_price(\"GOOGL\")\n", "        \n", "        # 计算价差\n", "        spread = price1 - price2\n", "        \n", "        # 判断价差是否超过阈值\n", "        if abs(spread) > threshold:\n", "            # 根据价差变化方向下单\n", "            if spread > 0:\n", "                buy(\"AAPL\", price1)\n", "                sell(\"GOOGL\", price2)\n", "            else:\n", "                sell(\"AAPL\", price1)\n", "                buy(\"GOOGL\", price2)\n", "        \n", "        # 控制交易频率\n", "        time.sleep(1)\n", "\n", "def buy(symbol, price):\n", "    # 下买单逻辑...\n", "    logging.info(\"买入 %s - 价格: %.2f\", symbol, price)\n", "\n", "def sell(symbol, price):\n", "    # 下卖单逻辑...\n", "    logging.info(\"卖出 %s - 价格: %.2f\", symbol, price)\n", "\n", "# 示例使用\n", "threshold = 5  # 设置价差变化阈值\n", "trade(threshold)\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}