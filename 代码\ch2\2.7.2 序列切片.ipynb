{"cells": [{"cell_type": "code", "execution_count": 2, "id": "cd6afa31", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["a[1:3] =  el\n", "a[:3] =  Hel\n", "a[0:] =  Hello\n", "a[0:5] =  Hello\n", "a[:] =  Hello\n", "a[1:-1] =  ell\n", "a[1:5] =  ello\n", "a[1:5:2] =  el\n", "a[0:3] =  Hel\n", "a[0:3:2] =  Hl\n", "a[0:3:3] =  H\n", "a[::-1] =  olleH\n"]}], "source": ["a = 'Hello'\n", "print('a[1:3] = ', a[1:3])  # el\n", "print('a[:3] = ', a[:3])  # Hel\t\n", "\n", "print('a[0:] = ', a[0:])  # Hello\n", "\n", "print('a[0:5] = ', a[0:5])  # Hello\n", "print('a[:] = ', a[:])  # Hello\n", "print('a[1:-1] = ', a[1:-1])  # ell\t\n", "\n", "print('a[1:5] = ', a[1:5])  # ello\n", "print('a[1:5:2] = ', a[1:5:2])  # el\n", "print('a[0:3] = ', a[0:3])  # Hel\n", "print('a[0:3:2] = ', a[0:3:2])  # Hl\n", "print('a[0:3:3] = ', a[0:3:3])  # H\n", "print('a[::-1] = ', a[::-1])  # olleH"]}, {"cell_type": "code", "execution_count": null, "id": "70866aac", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}