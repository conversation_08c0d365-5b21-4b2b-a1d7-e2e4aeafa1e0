import os
from tkinter import Tk, filedialog, messagebox, StringVar, ttk, Canvas, Text
from deep_translator import GoogleTranslator
import http.client
import hashlib
import urllib
import random
import json
import time
import sys
import logging
import re

# 添加配置文件路径
config_path = r'Z:\work'
sys.path.append(config_path)

try:
    from config import (
        BAIDU_APP_ID,
        BAIDU_SECRET_KEY,
        YOUDAO_APP_KEY,
        YOUDAO_APP_SECRET
    )
except ImportError:
    print(f"错误：无法从 {config_path} 加载配置文件")
    print("请确保config.py文件存在且包含必要的API密钥")
    sys.exit(1)

def google_translate(text):
    try:
        translator = GoogleTranslator(source='auto', target='zh-CN')
        # 添加重试机制
        max_retries = 3
        for attempt in range(max_retries):
            try:
                return translator.translate(text)
            except Exception as e:
                if attempt == max_retries - 1:
                    raise Exception(f"Google翻译失败: {str(e)}")
                time.sleep(1)
        return None
    except Exception as e:
        raise Exception(f"Google翻译服务错误: {str(e)}")

def baidu_translate(text):
    appid = BAIDU_APP_ID
    secretKey = BAIDU_SECRET_KEY
    httpClient = None
    salt = random.randint(32768, 65536)
    sign = appid + text + str(salt) + secretKey
    sign = hashlib.md5(sign.encode()).hexdigest()
    url = '/api/trans/vip/translate'
    
    # 修改语言参数，使用source_lang参数
    params = {
        'q': text,
        'from': 'auto',  # 改为自动检测源语言
        'to': 'zh',      # 目标语言为中文
        'appid': appid,
        'salt': salt,
        'sign': sign
    }
    
    try:
        httpClient = http.client.HTTPConnection('api.fanyi.baidu.com')
        httpClient.request('GET', url + '?' + urllib.parse.urlencode(params))
        response = httpClient.getresponse()
        result_json = response.read().decode('utf-8')
        result = json.loads(result_json)
        
        # 详细的错误处理
        if 'error_code' in result:
            error_codes = {
                '54003': '访问频率受限，请稍后再试',
                '54001': 'API密钥错误',
                '52001': '请求超时，请重试',
                '52002': '系统错误，请重试',
                '52003': '未授权用户',
                '54004': '账户余额不足',
                '54005': '长query请求频繁',
                '58000': '客户端IP非法',
                '58001': '译文语言方向不支持',
                '58002': '服务当前已关闭'
            }
            error_msg = error_codes.get(result['error_code'], '未知错误')
            raise Exception(f"{error_msg} (错误代码: {result['error_code']})")
        
        if 'trans_result' not in result or not result['trans_result']:
            raise Exception("未获取到翻译结果")
            
        return result['trans_result'][0]['dst']
        
    except json.JSONDecodeError:
        raise Exception("解析翻译结果失败")
    except Exception as e:
        raise Exception(f"百度翻译失败: {str(e)}")
    finally:
        if httpClient:
            httpClient.close()

def youdao_translate(text, source_lang):
    app_key = YOUDAO_APP_KEY
    app_secret = YOUDAO_APP_SECRET
    salt = str(round(time.time() * 1000))
    curtime = str(int(time.time()))
    input_text = text
    if len(input_text) > 20:
        input_len = len(input_text)
        input_text = input_text[:10] + str(input_len) + input_text[-10:]
    sign_str = app_key + input_text + salt + curtime + app_secret
    sign = hashlib.sha256(sign_str.encode('utf-8')).hexdigest()

    url = 'https://openapi.youdao.com/api'
    params = {
        'q': text,
        'from': source_lang['youdao'],
        'to': 'zh-CHS',
        'appKey': app_key,
        'salt': salt,
        'sign': sign,
        'signType': 'v3',
        'curtime': curtime,
    }
    
    try:
        response = urllib.request.urlopen(url + '?' + urllib.parse.urlencode(params))
        result = json.loads(response.read().decode('utf-8'))
        
        if 'errorCode' in result and result['errorCode'] != '0':
            error_codes = {
                '411': '访问过于频繁',
                '401': 'API账户不合法',
                '101': '缺少必填参数',
                '102': '不支持的语言类型',
                '103': '翻译文本过长',
                '108': '应用ID无效',
                '110': '无效的签名',
                '111': '无效的公钥',
                '112': '服务已关闭'
            }
            error_msg = error_codes.get(result['errorCode'], '未知错误')
            if result['errorCode'] == '411':
                time.sleep(3)  # 访问频繁时特别处理
            raise Exception(f"{error_msg} (错误代码: {result['errorCode']})")
        
        if 'translation' not in result or not result['translation']:
            raise Exception("未获取到翻译结果")
            
        return result['translation'][0]
    except Exception as e:
        raise Exception(f"翻译出错: {str(e)}")

def create_selection_panel():
    """创建选择面板"""
    root = Tk()
    root.title("翻译设置")
    root.geometry("300x400")
    root.resizable(False, False)
    
    # 语言选择
    lang_frame = ttk.LabelFrame(root, text="选择源语言", padding="10")
    lang_frame.pack(fill="x", padx=10, pady=5)
    
    lang_var = StringVar(value="ja")  # 默认选择日语
    languages = [
        ("日语", "ja"),
        ("韩语", "ko"),
        ("英语", "en"),
        ("法语", "fr")
    ]
    
    for lang_name, lang_code in languages:
        ttk.Radiobutton(
            lang_frame, 
            text=lang_name,
            value=lang_code,
            variable=lang_var
        ).pack(anchor="w", pady=2)
    
    # API选择
    api_frame = ttk.LabelFrame(root, text="选择翻译API", padding="10")
    api_frame.pack(fill="x", padx=10, pady=5)
    
    api_var = StringVar(value="1")  # 默认选择Google翻译
    apis = [
        ("Google翻译", "1"),
        ("百度翻译", "2"),
        ("有道翻译", "3")
    ]
    
    for api_name, api_code in apis:
        ttk.Radiobutton(
            api_frame, 
            text=api_name,
            value=api_code,
            variable=api_var
        ).pack(anchor="w", pady=2)
    
    # 确认按钮
    def confirm():
        root.result = {
            'lang_code': lang_var.get(),
            'api_code': api_var.get()
        }
        root.destroy()
    
    ttk.Button(
        root, 
        text="确认",
        command=confirm
    ).pack(pady=20)
    
    # 窗口居中
    root.update_idletasks()
    width = root.winfo_width()
    height = root.winfo_height()
    x = (root.winfo_screenwidth() // 2) - (width // 2)
    y = (root.winfo_screenheight() // 2) - (height // 2)
    root.geometry(f'+{x}+{y}')
    
    root.result = None
    root.mainloop()
    return root.result

def select_source_language():
    """使用选择面板选择源语言"""
    languages = {
        'ko': {'name': '韩语', 'google': 'ko', 'baidu': 'kor', 'youdao': 'ko'},
        'ja': {'name': '日语', 'google': 'ja', 'baidu': 'jp', 'youdao': 'ja'},
        'en': {'name': '英语', 'google': 'en', 'baidu': 'en', 'youdao': 'en'},
        'fr': {'name': '法语', 'google': 'fr', 'baidu': 'fra', 'youdao': 'fr'}
    }
    
    result = create_selection_panel()
    if result is None:
        sys.exit(0)
    
    return languages[result['lang_code']], result['api_code']

def split_text(text):
    """智能分割文本，保留分隔符和不需要翻译的部分"""
    parts = []
    current_part = ''
    
    def is_cjk(char):
        """判断是否是中日韩文字"""
        return any([
            '\u4e00' <= char <= '\u9fff',  # 中文
            '\u3040' <= char <= '\u309f',  # 平假名
            '\u30a0' <= char <= '\u30ff',  # 片假名
            '\uac00' <= char <= '\ud7af'   # 韩文
        ])
    
    def is_separator(char):
        """判断是否是分隔符"""
        return char.isspace() or char in ',.，。!！?？;；-_'
    
    def is_name_pattern(text, pos):
        """判断是否是人名模式（通常是最后一部分且有空格分隔）"""
        # 检查前面是否有空格
        if pos == 0 or not text[pos-1].isspace():
            return False
        # 检查是否是最后一组文字
        remaining = text[pos:].strip()
        return all(is_cjk(c) for c in remaining if not is_separator(c))
    
    def is_code_pattern(text, pos):
        """判断是否是番号模式 (如 ABC-123)"""
        if pos + 8 >= len(text):  # 番号通常不会太长
            return False
        code_part = text[pos:pos+8]
        if '-' not in code_part:
            return False
        parts = code_part.split('-', 1)
        return (parts[0].isalpha() and 
                len(parts[0]) <= 5 and 
                any(c.isdigit() for c in parts[1]))
    
    i = 0
    while i < len(text):
        char = text[i]
        
        # 处理番号模式
        if char.isalpha() and i + 2 < len(text) and is_code_pattern(text, i):
            if current_part:
                if current_part.strip():
                    parts.append({'text': current_part.strip(), 'type': 'translate'})
                current_part = ''
            
            code = ''
            while i < len(text) and (text[i].isalnum() or text[i] == '-'):
                code += text[i]
                i += 1
            parts.append({'text': code, 'type': 'keep'})
            continue
        
        # 处理分隔符
        if is_separator(char):
            if current_part:
                if current_part.strip():
                    parts.append({'text': current_part.strip(), 'type': 'translate'})
                current_part = ''
            # 检查是否是人名前的空格，如果是则标记为必需的分隔符
            if char.isspace() and i + 1 < len(text) and is_name_pattern(text, i + 1):
                parts.append({'text': char, 'type': 'name_separator'})
            else:
                parts.append({'text': char, 'type': 'separator'})
        # 处理其他不需要翻译的字符
        elif char.isdigit() or (char.isascii() and not is_cjk(char)):
            if current_part:
                if current_part.strip():
                    parts.append({'text': current_part.strip(), 'type': 'translate'})
                current_part = ''
            parts.append({'text': char, 'type': 'keep'})
        else:
            current_part += char
        i += 1
    
    # 处理最后一部分
    if current_part and current_part.strip():
        parts.append({'text': current_part.strip(), 'type': 'translate'})
    
    return parts

def post_process_translation(text):
    """对翻译结果进行后处理"""
    # 替换所有中文和日文标点为指定格式
    replacements = {
        # 中文标点
        '。': ' ',  # 句号
        '，': ' ',  # 逗号
        '、': ' ',  # 顿号
        '！': ' ',  # 感叹号
        '？': ' ',  # 问号
        '；': ' ',  # 分号
        '：': ' ',  # 冒号
        '（': ' ',  # 左括号
        '）': ' ',  # 右括号
        '【': ' ',  # 方括号
        '】': ' ',
        '《': ' ',  # 书名号
        '》': ' ',
        '〈': ' ',  # 尖括号
        '〉': ' ',
        '"': '',   # 引号
        '"': '',
        ''': '',
        ''': '',
        '～': '-',  # 波浪线转连字符
        '·': ' ',  # 间隔号
        '…': ' ',  # 省略号
        '━': '-',  # 破折号
        '─': '-',
        '—': '-',
        '＿': '_',  # 下划线
        
        # 日文标点
        '。': ' ',  # 句号
        '、': ' ',  # 顿号
        '！': ' ',  # 感叹号
        '？': ' ',  # 问号
        '：': ' ',  # 冒号
        '；': ' ',  # 分号
        '（': ' ',  # 括号
        '）': ' ',
        '「': '',   # 引号
        '」': '',
        '『': '',
        '』': '',
        '【': ' ',  # 方括号
        '】': ' ',
        '《': ' ',  # 书名号
        '》': ' ',
        '〈': ' ',  # 尖括号
        '〉': ' ',
        '・': ' ',  # 中点
        '…': ' ',  # 省略号
        '─': '-',  # 破折号
        '━': '-',
        '〜': '-',  # 波浪线
        
        # 全角符号转半角
        '，': ' ',
        '！': ' ',
        '？': ' ',
        '：': ' ',
        '；': ' ',
        '＂': '"',
        '＇': "'",
        '｀': '`',
        '＠': '@',
        '＃': '#',
        '＄': '$',
        '％': '%',
        '＾': '^',
        '＆': '&',
        '＊': '*',
        '（': ' ',
        '）': ' ',
        '＿': '_',
        '＋': '+',
        '＝': '=',
        '｜': '|',
        '＼': '\\',
        '｛': '{',
        '｝': '}',
        '［': '[',
        '］': ']',
        '＜': '<',
        '＞': '>',
        '／': '/',
        '？': '?'
    }
    
    for old, new in replacements.items():
        text = text.replace(old, new)
    
    # 处理连续的空格
    text = ' '.join(filter(None, text.split()))
    
    # 处理数字序列前的空格，将其替换为横杠
    words = text.split()
    for i in range(1, len(words)):
        if words[i].isdigit():  # 如果是纯数字
            words[i] = '-' + words[i]  # 在数字前添加横杠
    
    # 重新组合文本
    text = ' '.join(words)
    
    # 处理可能遗留的连续标点
    text = text.replace('--', '-')
    text = text.replace('- -', '-')
    text = text.replace('  ', ' ')
    
    return text.strip()

def translate_text(text, api_choice, source_lang):
    """根据选择的API和源语言进行翻译"""
    max_retries = 3
    retry_delay = 2
    
    # 分割文本
    parts = split_text(text)
    result = []
    
    for part in parts:
        if part['type'] == 'translate':
            # 翻译需要翻译的部分
            for attempt in range(max_retries):
                try:
                    if api_choice == '1':  # Google翻译
                        translator = GoogleTranslator(source=source_lang['google'], target='zh-CN')
                        translated_text = post_process_translation(translator.translate(part['text']))
                        result.append(translated_text)
                        time.sleep(1)  # 添加延迟避免频率限制
                        break
                    elif api_choice == '2':  # 百度翻译
                        # 修改百度翻译的调用方式
                        translated_text = post_process_translation(baidu_translate(part['text']))
                        result.append(translated_text)
                        time.sleep(2)  # 百度翻译需要更长的延迟
                        break
                    else:  # 有道翻译
                        translated_text = post_process_translation(youdao_translate(part['text'], source_lang))
                        result.append(translated_text)
                        time.sleep(1)
                        break
                except Exception as e:
                    if '54003' in str(e):  # 访问频率限制错误
                        wait_time = retry_delay * (attempt + 1)
                        print(f"\n访问过于频繁，等待{wait_time}秒后重试...")
                        time.sleep(wait_time)
                    elif attempt < max_retries - 1:
                        print(f"\n第{attempt + 1}次翻译失败，{retry_delay}秒后重试...")
                        time.sleep(retry_delay)
                    else:
                        raise Exception(f"翻译失败: {str(e)}")
        else:
            # 保持原样的部分直接添加
            result.append(part['text'])
    
    # 组合结果并确保空格正确
    final_result = ''.join(result)
    final_result = ' '.join(filter(None, final_result.split()))
    
    return final_result

def sanitize_filename(filename):
    """清理文件名中的非法字符"""
    # 检查文件名是否为None或空
    if not filename:
        return '未命名'
        
    # Windows文件名中不允许的字符
    illegal_chars = r'<>:"/\|?*'
    # 替换非法字符为下划线
    for char in illegal_chars:
        filename = filename.replace(char, '_')
    
    # 处理Windows保留文件名
    reserved_names = {
        'CON', 'PRN', 'AUX', 'NUL',
        'COM1', 'COM2', 'COM3', 'COM4',
        'LPT1', 'LPT2', 'LPT3', 'LPT4'
    }
    
    # 如果文件名是保留名称，添加下划线前缀
    name_without_ext = filename.split('.')[0].upper()
    if name_without_ext in reserved_names:
        filename = '_' + filename
    
    # 去除首尾空格和点
    filename = filename.strip('. ')
    
    # 如果文件名为空，返回默认名称
    if not filename:
        filename = '未命名'
    
    return filename

def setup_logging():
    """设置日志"""
    log_dir = r'Z:\work\logs'
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    log_file = os.path.join(log_dir, f'translation_{time.strftime("%Y%m%d_%H%M%S")}.log')
    
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler()  # 同时输出到控制台
        ]
    )
    return log_file

def update_nfo_title(nfo_path, new_title):
    """更新NFO文件中的title字段"""
    try:
        # 读取NFO文件内容
        with open(nfo_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 使用正则表达式替换title标签内容
        new_content = re.sub(
            r'<title>.*?</title>',
            f'<title>{new_title}</title>',
            content,
            flags=re.DOTALL
        )
        
        # 写回文件
        with open(nfo_path, 'w', encoding='utf-8') as f:
            f.write(new_content)
            
        return True
    except Exception as e:
        logging.error(f"更新NFO文件失败: {str(e)}")
        return False

def rename_folder_contents(folder_path, new_folder_name):
    """重命名文件夹内的文件"""
    try:
        base_name = os.path.basename(new_folder_name)
        
        # 遍历文件夹中的所有文件
        for filename in os.listdir(folder_path):
            old_path = os.path.join(folder_path, filename)
            if os.path.isfile(old_path):
                # 获取文件扩展名
                _, ext = os.path.splitext(filename)
                
                # 检查是否有特殊标记（如 -landscape, -poster, -cd1 等）
                special_mark = ''
                # 检查常见的特殊标记
                if any(mark in filename.lower() for mark in [
                    '-poster', '-landscape', '-fanart', '-thumb', '-backdrop',
                    '-cd1', '-cd2', '-cd3', '-cd4'
                ]):
                    # 提取完整的标记（包括连字符）
                    mark_match = re.search(
                        r'(-(?:poster|landscape|fanart|thumb|backdrop|cd[1-4]))(?:\.|$)', 
                        filename.lower()
                    )
                    if mark_match:
                        special_mark = mark_match.group(1)
                
                # 构建新文件名
                new_filename = f"{base_name}{special_mark}{ext}"
                new_path = os.path.join(folder_path, new_filename)
                
                # 如果目标文件已存在，添加数字后缀
                counter = 1
                while os.path.exists(new_path):
                    name_without_ext = f"{base_name}{special_mark}_{counter}"
                    new_filename = f"{name_without_ext}{ext}"
                    new_path = os.path.join(folder_path, new_filename)
                    counter += 1
                
                # 重命名文件
                os.rename(old_path, new_path)
                
                # 如果是NFO文件，更新其中的title字段
                if ext.lower() == '.nfo':
                    update_nfo_title(new_path, base_name)
                    
        return True
    except Exception as e:
        logging.error(f"重命名文件夹内容失败: {str(e)}")
        return False

def rename_folder(folder_path, original_name, translated_name):
    """安全地重命名文件夹及其内容，返回(是否成功, 新名称, 错误信息)"""
    original_path = os.path.join(folder_path, original_name)
    new_path = os.path.join(folder_path, translated_name)
    
    try:
        # 检查源文件夹是否存在
        if not os.path.exists(original_path):
            return False, None, f"源文件夹不存在: {original_path}"
        
        # 检查是否是文件夹
        if not os.path.isdir(original_path):
            return False, None, f"不是文件夹: {original_path}"
        
        # 处理重名情况
        final_name = translated_name
        counter = 1
        while os.path.exists(new_path):
            final_name = f"{translated_name}_{counter}"
            new_path = os.path.join(folder_path, final_name)
            counter += 1
        
        # 首先重命名文件夹内的内容
        if not rename_folder_contents(original_path, final_name):
            return False, None, "重命名文件夹内容失败"
        
        # 执行文件夹重命名
        os.rename(original_path, new_path)
        
        # 验证重命名结果
        if not os.path.exists(new_path):
            return False, None, "重命名后无法找到新文件夹"
        
        return True, final_name, None
        
    except Exception as e:
        return False, None, str(e)

def show_translation_dialog(original_text, translated_text, api_choice, source_lang):
    """显示翻译结果对话框"""
    dialog = Tk()
    dialog.title("翻译结果")
    dialog.geometry("600x300")
    
    # 创建主框架
    main_frame = ttk.Frame(dialog, padding="10")
    main_frame.pack(fill="both", expand=True)
    
    # 原文框架
    original_frame = ttk.LabelFrame(main_frame, text="原文", padding="5")
    original_frame.pack(side="left", fill="both", expand=True, padx=(0, 5))
    
    original_text_widget = ttk.Entry(original_frame)
    original_text_widget.insert(0, original_text)
    original_text_widget.configure(state="readonly")
    original_text_widget.pack(fill="x", padx=5, pady=5)
    
    # 翻译结果框架
    translated_frame = ttk.LabelFrame(main_frame, text="翻译结果", padding="5")
    translated_frame.pack(side="right", fill="both", expand=True, padx=(5, 0))
    
    translated_text_widget = ttk.Entry(translated_frame)
    translated_text_widget.insert(0, translated_text)
    translated_text_widget.pack(fill="x", padx=5, pady=5)
    
    # 按钮框架
    button_frame = ttk.Frame(dialog, padding="10")
    button_frame.pack(fill="x", pady=(0, 10))
    
    def retranslate():
        try:
            new_text = translate_text(original_text, api_choice, source_lang)
            translated_text_widget.delete(0, "end")
            translated_text_widget.insert(0, new_text)
        except Exception as e:
            messagebox.showerror("翻译错误", str(e))
    
    def confirm():
        dialog.result = {
            'action': 'confirm',
            'text': translated_text_widget.get()
        }
        dialog.destroy()
    
    def cancel():
        dialog.result = {'action': 'cancel'}
        dialog.destroy()
    
    # 添加按钮
    ttk.Button(button_frame, text="重新翻译", command=retranslate).pack(side="left", padx=5)
    ttk.Button(button_frame, text="确认", command=confirm).pack(side="right", padx=5)
    ttk.Button(button_frame, text="取消", command=cancel).pack(side="right", padx=5)
    
    # 窗口居中
    dialog.update_idletasks()
    width = dialog.winfo_width()
    height = dialog.winfo_height()
    x = (dialog.winfo_screenwidth() // 2) - (width // 2)
    y = (dialog.winfo_screenheight() // 2) - (height // 2)
    dialog.geometry(f'+{x}+{y}')
    
    dialog.result = None
    dialog.mainloop()
    return dialog.result

def show_batch_translation_dialog(translations):
    """显示批量翻译结果对话框"""
    dialog = Tk()
    dialog.title("批量翻译结果")
    dialog.geometry("1200x800")  # 增加窗口大小
    
    # 创建主框架
    main_frame = ttk.Frame(dialog, padding="10")
    main_frame.pack(fill="both", expand=True)
    
    # 创建滚动区域
    canvas = Canvas(main_frame)
    scrollbar = ttk.Scrollbar(main_frame, orient="vertical", command=canvas.yview)
    scrollable_frame = ttk.Frame(canvas)
    
    scrollable_frame.bind(
        "<Configure>",
        lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
    )
    
    canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
    canvas.configure(yscrollcommand=scrollbar.set)
    
    # 存储所有翻译结果的Entry控件
    translation_entries = []
    
    # 添加每个翻译项
    for i, item in enumerate(translations):
        # 创建每个项目的框架
        item_frame = ttk.Frame(scrollable_frame)
        item_frame.pack(fill="x", pady=5)
        
        # 序号标签
        ttk.Label(item_frame, text=f"{i+1}.").pack(side="left", padx=(0, 5))
        
        # 原文（只读）
        original_frame = ttk.LabelFrame(item_frame, text="原文")
        original_frame.pack(side="left", fill="x", expand=True, padx=5)
        
        # 使用Text替代Entry以显示更长的文本
        original_text = Text(original_frame, height=3, width=60)  # 增加宽度和高度
        original_text.insert('1.0', item['original'])
        original_text.configure(state="disabled")  # 设为只读
        original_text.pack(fill="x", padx=5, pady=2)
        
        # 翻译结果（可编辑）
        translated_frame = ttk.LabelFrame(item_frame, text="翻译结果")
        translated_frame.pack(side="left", fill="x", expand=True, padx=5)
        
        # 使用Text替代Entry
        translated_text = Text(translated_frame, height=3, width=60)  # 增加宽度和高度
        translated_text.insert('1.0', item['translated'])
        translated_text.pack(fill="x", padx=5, pady=2)
        
        translation_entries.append({
            'original': item['original'],
            'entry': translated_text  # 现在存储Text控件
        })
        
        # 重新翻译按钮
        def make_retranslate(original, text_widget, api_choice, source_lang):
            def retranslate():
                try:
                    new_text = translate_text(original, api_choice, source_lang)
                    text_widget.delete('1.0', 'end')
                    text_widget.insert('1.0', new_text)
                except Exception as e:
                    messagebox.showerror("翻译错误", str(e))
            return retranslate
        
        ttk.Button(
            item_frame, 
            text="重译",
            command=make_retranslate(
                item['original'], 
                translated_text,
                item['api_choice'],
                item['source_lang']
            )
        ).pack(side="left", padx=5)
    
    # 布局滚动区域
    canvas.pack(side="left", fill="both", expand=True)
    scrollbar.pack(side="right", fill="y")
    
    # 底部按钮框架
    button_frame = ttk.Frame(dialog, padding="10")
    button_frame.pack(fill="x", pady=(0, 10))
    
    def confirm():
        dialog.result = {
            'action': 'confirm',
            'translations': [
                {
                    'original': item['original'],
                    'translated': item['entry'].get('1.0', 'end-1c')  # 获取Text控件的内容
                }
                for item in translation_entries
            ]
        }
        dialog.destroy()
    
    def cancel():
        dialog.result = {'action': 'cancel'}
        dialog.destroy()
    
    ttk.Button(button_frame, text="确认", command=confirm).pack(side="right", padx=5)
    ttk.Button(button_frame, text="取消", command=cancel).pack(side="right", padx=5)
    
    # 绑定鼠标滚轮
    def _on_mousewheel(event):
        canvas.yview_scroll(int(-1*(event.delta/120)), "units")
    canvas.bind_all("<MouseWheel>", _on_mousewheel)
    
    # 窗口居中
    dialog.update_idletasks()
    width = dialog.winfo_width()
    height = dialog.winfo_height()
    x = (dialog.winfo_screenwidth() // 2) - (width // 2)
    y = (dialog.winfo_screenheight() // 2) - (height // 2)
    dialog.geometry(f'+{x}+{y}')
    
    dialog.result = None
    dialog.mainloop()
    return dialog.result

def show_unified_translation_dialog(folder_path):
    """显示统一的翻译界面"""
    dialog = Tk()
    dialog.title("文件夹批量翻译")
    dialog.geometry("1200x900")  # 增加高度以容纳设置面板
    
    # 创建主框架
    main_frame = ttk.Frame(dialog, padding="10")
    main_frame.pack(fill="both", expand=True)
    
    # 设置面板
    settings_frame = ttk.LabelFrame(main_frame, text="翻译设置", padding="10")
    settings_frame.pack(fill="x", pady=(0, 10))
    
    # 语言选择
    lang_frame = ttk.Frame(settings_frame)
    lang_frame.pack(side="left", padx=20)
    ttk.Label(lang_frame, text="源语言:").pack(side="left")
    
    lang_var = StringVar(value="ja")
    languages = [
        ("日语", "ja"),
        ("韩语", "ko"),
        ("英语", "en"),
        ("法语", "fr")
    ]
    
    for lang_name, lang_code in languages:
        ttk.Radiobutton(
            lang_frame, 
            text=lang_name,
            value=lang_code,
            variable=lang_var
        ).pack(side="left", padx=5)
    
    # API选择
    api_frame = ttk.Frame(settings_frame)
    api_frame.pack(side="left", padx=20)
    ttk.Label(api_frame, text="翻译API:").pack(side="left")
    
    api_var = StringVar(value="1")  # 默认选择 Google 翻译
    apis = [
        ("Google翻译", "1"),
        ("百度翻译", "2"),
        ("有道翻译", "3")
    ]
    
    for api_name, api_code in apis:
        ttk.Radiobutton(
            api_frame, 
            text=api_name,
            value=api_code,
            variable=api_var
        ).pack(side="left", padx=5)
    
    # 创建滚动区域
    scroll_frame = ttk.Frame(main_frame)
    scroll_frame.pack(fill="both", expand=True)
    
    canvas = Canvas(scroll_frame)
    scrollbar = ttk.Scrollbar(scroll_frame, orient="vertical", command=canvas.yview)
    scrollable_frame = ttk.Frame(canvas)
    
    scrollable_frame.bind(
        "<Configure>",
        lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
    )
    
    canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
    canvas.configure(yscrollcommand=scrollbar.set)
    
    # 存储翻译条目
    translation_entries = []
    
    def add_translation_entries(subdirs):
        # 清除现有条目
        for widget in scrollable_frame.winfo_children():
            widget.destroy()
        translation_entries.clear()
        
        # 添加新条目
        for i, subdir in enumerate(subdirs, 1):
            item_frame = ttk.Frame(scrollable_frame)
            item_frame.pack(fill="x", pady=5)
            
            # 序号标签
            ttk.Label(item_frame, text=f"{i}.").pack(side="left", padx=(0, 5))
            
            # 原文（只读）
            original_frame = ttk.LabelFrame(item_frame, text="原文")
            original_frame.pack(side="left", fill="x", expand=True, padx=5)
            
            original_text = Text(original_frame, height=3, width=50)
            original_text.insert('1.0', subdir)
            original_text.configure(state="disabled")
            original_text.pack(fill="x", padx=5, pady=2)
            
            # 翻译结果（可编辑）
            translated_frame = ttk.LabelFrame(item_frame, text="翻译结果")
            translated_frame.pack(side="left", fill="x", expand=True, padx=5)
            
            translated_text = Text(translated_frame, height=3, width=50)
            translated_text.pack(fill="x", padx=5, pady=2)
            
            translation_entries.append({
                'original': subdir,
                'entry': translated_text
            })
            
            # 翻译按钮
            def make_translate(original, text_widget):
                def translate():
                    try:
                        # 获取当前设置
                        selected_lang = lang_var.get()
                        selected_api = api_var.get()
                        
                        # 构建语言配置
                        source_lang = {
                            'ja': {'name': '日语', 'google': 'ja', 'baidu': 'jp', 'youdao': 'ja'},
                            'ko': {'name': '韩语', 'google': 'ko', 'baidu': 'kor', 'youdao': 'ko'},
                            'en': {'name': '英语', 'google': 'en', 'baidu': 'en', 'youdao': 'en'},
                            'fr': {'name': '法语', 'google': 'fr', 'baidu': 'fra', 'youdao': 'fr'}
                        }[selected_lang]
                        
                        # 添加进度提示
                        text_widget.delete('1.0', 'end')
                        text_widget.insert('1.0', '正在翻译...')
                        text_widget.update()
                        
                        new_text = translate_text(original, selected_api, source_lang)
                        text_widget.delete('1.0', 'end')
                        text_widget.insert('1.0', new_text)
                    except Exception as e:
                        text_widget.delete('1.0', 'end')  # 清除"正在翻译..."
                        messagebox.showerror("翻译错误", str(e))
                return translate
            
            ttk.Button(
                item_frame,
                text="翻译",
                command=make_translate(subdir, translated_text)
            ).pack(side="left", padx=5)
    
    # 翻译全部按钮
    def translate_all():
        total = len(translation_entries)
        for i, item in enumerate(translation_entries, 1):
            try:
                selected_lang = lang_var.get()
                selected_api = api_var.get()
                source_lang = {
                    'ja': {'name': '日语', 'google': 'ja', 'baidu': 'jp', 'youdao': 'ja'},
                    'ko': {'name': '韩语', 'google': 'ko', 'baidu': 'kor', 'youdao': 'ko'},
                    'en': {'name': '英语', 'google': 'en', 'baidu': 'en', 'youdao': 'en'},
                    'fr': {'name': '法语', 'google': 'fr', 'baidu': 'fra', 'youdao': 'fr'}
                }[selected_lang]
                
                text_widget = item['entry']
                if not text_widget.winfo_exists():  # 检查控件是否还存在
                    continue
                    
                # 添加进度提示
                text_widget.delete('1.0', 'end')
                text_widget.insert('1.0', f'正在翻译... ({i}/{total})')
                text_widget.update()
                
                new_text = translate_text(item['original'], selected_api, source_lang)
                
                if text_widget.winfo_exists():  # 再次检查控件是否存在
                    text_widget.delete('1.0', 'end')
                    text_widget.insert('1.0', new_text)
                
                time.sleep(1)  # 避免API调用过于频繁
                
            except Exception as e:
                try:
                    if 'text_widget' in locals() and text_widget.winfo_exists():
                        text_widget.delete('1.0', 'end')  # 清除进度提示
                except:
                    pass  # 忽略清除文本时的错误
                    
                error_msg = f"翻译 '{item['original']}' 失败: {str(e)}"
                messagebox.showerror("翻译错误", error_msg)
                if not messagebox.askyesno("继续", "是否继续翻译其他项目?"):
                    break
    
    # 添加翻译全部按钮
    ttk.Button(
        settings_frame,
        text="翻译全部",
        command=translate_all
    ).pack(side="right", padx=5)
    
    # 布局滚动区域
    canvas.pack(side="left", fill="both", expand=True)
    scrollbar.pack(side="right", fill="y")
    
    # 底部按钮框架
    button_frame = ttk.Frame(dialog, padding="10")
    button_frame.pack(fill="x", pady=(0, 10))
    
    def confirm():
        # 过滤掉未翻译的项目
        valid_translations = [
            {
                'original': item['original'],
                'translated': item['entry'].get('1.0', 'end-1c').strip()
            }
            for item in translation_entries
            if item['entry'].get('1.0', 'end-1c').strip()  # 只包含有翻译内容的项目
        ]
        
        # 如果没有任何有效翻译，显示提示
        if not valid_translations:
            messagebox.showwarning("警告", "没有任何已翻译的项目，操作取消")
            return
            
        dialog.result = {
            'action': 'confirm',
            'translations': valid_translations
        }
        dialog.destroy()
    
    def cancel():
        dialog.result = {'action': 'cancel'}
        dialog.destroy()
    
    ttk.Button(button_frame, text="确认", command=confirm).pack(side="right", padx=5)
    ttk.Button(button_frame, text="取消", command=cancel).pack(side="right", padx=5)
    
    # 绑定鼠标滚轮
    def _on_mousewheel(event):
        canvas.yview_scroll(int(-1*(event.delta/120)), "units")
    canvas.bind_all("<MouseWheel>", _on_mousewheel)
    
    # 获取并显示文件夹列表
    subdirs = [d for d in os.listdir(folder_path) if os.path.isdir(os.path.join(folder_path, d))]
    add_translation_entries(subdirs)
    
    # 窗口居中
    dialog.update_idletasks()
    width = dialog.winfo_width()
    height = dialog.winfo_height()
    x = (dialog.winfo_screenwidth() // 2) - (width // 2)
    y = (dialog.winfo_screenheight() // 2) - (height // 2)
    dialog.geometry(f'+{x}+{y}')
    
    dialog.result = None
    dialog.mainloop()
    return dialog.result

def select_folder_and_translate():
    # 选择文件夹
    root = Tk()
    root.attributes('-topmost', True)
    root.withdraw()
    folder_path = filedialog.askdirectory()
    root.destroy()
    
    if folder_path:
        # 显示统一的翻译界面
        result = show_unified_translation_dialog(folder_path)
        
        if result and result['action'] == 'confirm':
            # 记录操作结果
            success_count = 0
            skip_count = 0
            error_count = 0
            error_messages = []
            
            # 执行批量重命名
            for item in result['translations']:
                try:
                    # 如果翻译结果为空，跳过该项
                    if not item['translated'].strip():
                        skip_count += 1
                        continue
                        
                    final_translated_name = sanitize_filename(item['translated'])
                    success, final_name, error = rename_folder(
                        folder_path, 
                        item['original'], 
                        final_translated_name
                    )
                    
                    if success:
                        success_count += 1
                    else:
                        error_count += 1
                        error_messages.append(f"重命名失败 '{item['original']}': {error}")
                except Exception as e:
                    error_count += 1
                    error_messages.append(f"重命名失败 '{item['original']}': {str(e)}")
            
            # 显示详细的操作结果
            result_message = f"翻译任务已完成\n\n"
            result_message += f"成功: {success_count} 个\n"
            result_message += f"跳过: {skip_count} 个\n"
            result_message += f"失败: {error_count} 个\n"
            
            if error_messages:
                result_message += "\n错误详情:\n" + "\n".join(error_messages)
            
            messagebox.showinfo("完成", result_message)

if __name__ == "__main__":
    select_folder_and_translate()
