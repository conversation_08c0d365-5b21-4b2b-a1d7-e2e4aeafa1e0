{"cells": [{"cell_type": "code", "execution_count": 24, "id": "8cc75998", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 20 entries, 0 to 19\n", "Data columns (total 6 columns):\n", " #   Column  Non-Null Count  Dtype  \n", "---  ------  --------------  -----  \n", " 0   Date    20 non-null     object \n", " 1   Close   20 non-null     float64\n", " 2   Volume  20 non-null     int64  \n", " 3   Open    20 non-null     object \n", " 4   High    20 non-null     object \n", " 5   Low     20 non-null     object \n", "dtypes: float64(1), int64(1), object(4)\n", "memory usage: 1.1+ KB\n"]}], "source": ["import pandas as pd\n", "# 读取股票数据\n", "data = pd.read_csv('data/AAPL.csv', \n", "\t\tconverters={'Close': lambda x: float(x.replace('$', ''))}) \n", "# 输出数据集的基本信息\n", "data.info()"]}, {"cell_type": "code", "execution_count": 25, "id": "45c17026", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["均值=181.34,中位数=180.95\n", "标准差=3.92,方差=15.39\n", "偏度=-0.38,峰度=-0.61\n"]}], "source": ["# 计算收盘价的统计特征\n", "mean = data['Close'].mean()\n", "median = data['Close'].median()\n", "std = data['Close'].std()\n", "var = data['Close'].var()\n", "skew = data['Close'].skew()\n", "kurt = data['Close'].kurtosis()\n", "print('均值=%.2f,中位数=%.2f' % (mean, median))\n", "print('标准差=%.2f,方差=%.2f' % (std, var))\n", "print('偏度=%.2f,峰度=%.2f' % (skew, kurt))"]}, {"cell_type": "code", "execution_count": 28, "id": "0dd41f0a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["第一四分位数=178.86,第二四分位数=180.95,第三四分位数=184.20\n"]}], "source": ["# 计算百分位数\n", "Q1 = data['Close'].quantile(0.25)\n", "Q2 = data['Close'].quantile(0.5)\n", "Q3 = data['Close'].quantile(0.75)\n", "print('第一四分位数=%.2f,第二四分位数=%.2f,第三四分位数=%.2f' % (Q1, Q2, Q3))"]}, {"cell_type": "code", "execution_count": 29, "id": "ba148cd8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["涨8天,平均上涨幅度为0.42%\n", "下跌11天,平均下跌幅度为-0.99%\n"]}], "source": ["# 价格趋势分析\n", "# 计算涨跌天数和平均涨跌幅度\n", "data['Return'] = data['Close'].pct_change()\n", "Up_days = len(data.loc[data['Return'] > 0])\n", "Down_days = len(data.loc[data['Return'] < 0])\n", "Avg_Up = data.loc[data['Return'] > 0, 'Return'].mean()\n", "Avg_Down = data.loc[data['Return'] < 0, 'Return'].mean()\n", "print('涨%d天,平均上涨幅度为%0.2f%%' % (Up_days, Avg_Up*100))\n", "print('下跌%d天,平均下跌幅度为%0.2f%%' % (Down_days, Avg_Down*100))"]}, {"cell_type": "code", "execution_count": null, "id": "8834c670", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}