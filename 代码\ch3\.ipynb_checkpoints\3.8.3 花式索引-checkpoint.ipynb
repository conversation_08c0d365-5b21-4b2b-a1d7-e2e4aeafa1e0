{"cells": [{"cell_type": "code", "execution_count": 1, "id": "5788f8a6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["原始数组： [1 2 3 4 5]\n", "花式索引： [1, 3]\n", "选择的元素： [2 4]\n"]}], "source": ["import numpy as np\n", "\n", "arr = np.array([1, 2, 3, 4, 5])\n", "\n", "# 使用花式索引选择指定位置的元素\n", "indices = [1, 3]\n", "selected_arr = arr[indices]\n", "\n", "print(\"原始数组：\", arr)\n", "print(\"花式索引：\", indices)\n", "print(\"选择的元素：\", selected_arr)\n"]}, {"cell_type": "code", "execution_count": null, "id": "dde245a1", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}