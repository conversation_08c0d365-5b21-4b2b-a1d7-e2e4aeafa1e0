{"cells": [{"cell_type": "code", "execution_count": null, "id": "118d9c9e", "metadata": {}, "outputs": [], "source": ["from selenium import webdriver\n", "from selenium.webdriver.common.by import By\n", "\n", "driver = webdriver.Firefox()\n", "driver.get('http://q.stock.sohu.com/cn/600519/lshq.shtml')\n", "table_element = driver.find_element(By.ID, 'BIZ_hq_historySearch')\n", "print(table_element.text)\n", "driver.quit()"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}