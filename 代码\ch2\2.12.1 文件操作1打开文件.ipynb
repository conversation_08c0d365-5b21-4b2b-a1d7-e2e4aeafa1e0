{"cells": [{"cell_type": "code", "execution_count": 5, "id": "6cc1d001", "metadata": {}, "outputs": [{"data": {"text/plain": ["1"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["fobj = open('test1.txt', 'w+', encoding='utf-8') \n", "fobj.write('大家好')\n", "\n", "fname1 =r'C:\\Users\\<USER>\\OneDrive\\书\\北大\\AI时代Python量化交易实战：ChatGPT让量化交易插上翅膀\\代码\\ch2\\\\test1.txt'\n", "fobj = open(fname1, 'a+', encoding='utf-8') \n", "\n", "fobj.write('！')\n"]}, {"cell_type": "code", "execution_count": null, "id": "27ddf85f", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "3ed3ab31", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}