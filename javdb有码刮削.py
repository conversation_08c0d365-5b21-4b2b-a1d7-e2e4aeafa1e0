import shutil
import time
from bs4 import BeautifulSoup
import webbrowser
import os
import re
import tkinter as tk
from tkinter import filedialog
from PIL import Image
import subprocess
from selenium.webdriver.chrome.options import Options
import uuid
import hashlib
import json
import requests
import sys
sys.path.append(r'Z:\work')  # 添加配置文件路径
from config import YOUDAO_APP_KEY, YOUDAO_APP_SECRET  # 导入有道API配置
from tkinter import ttk
from tkinter import messagebox

# 添加在文件开头的常量定义
DEFAULT_SOURCE_PATH = r"V:\youma\t2"
DEFAULT_TARGET_PATH = r"V:\youma\=m="
CONFIG_FILE = "javdb_config.json"

class PathSelector:
    def __init__(self):
        self.window = tk.Tk()
        self.window.title("JavDB路径选择器")
        self.window.geometry("800x400")
        
        # 设置窗口样式
        style = ttk.Style()
        style.configure('TLabe<PERSON>rame', padding=10)
        style.configure('TButton', padding=5)
        
        # 主容器
        main_frame = ttk.Frame(self.window, padding="20")
        main_frame.pack(fill="both", expand=True)
        
        # 加载保存的路径
        self.load_paths()
        
        # 源路径框架
        source_frame = ttk.LabelFrame(main_frame, text="源文件夹路径", padding="10")
        source_frame.pack(fill="x", padx=10, pady=10)
        
        self.source_path_var = tk.StringVar(value=self.source_path)
        source_entry = ttk.Entry(source_frame, textvariable=self.source_path_var, width=80)
        source_entry.pack(side="left", padx=5, fill="x", expand=True)
        
        source_button = ttk.Button(source_frame, text="浏览", command=self.select_source)
        source_button.pack(side="left", padx=5)
        
        # 目标路径框架
        target_frame = ttk.LabelFrame(main_frame, text="目标文件夹路径", padding="10")
        target_frame.pack(fill="x", padx=10, pady=10)
        
        self.target_path_var = tk.StringVar(value=self.target_path)
        target_entry = ttk.Entry(target_frame, textvariable=self.target_path_var, width=80)
        target_entry.pack(side="left", padx=5, fill="x", expand=True)
        
        target_button = ttk.Button(target_frame, text="浏览", command=self.select_target)
        target_button.pack(side="left", padx=5)
        
        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(pady=20)
        
        # 开始处理按钮
        start_button = ttk.Button(button_frame, text="开始处理", command=self.confirm, style='Accent.TButton')
        start_button.pack(side="left", padx=10)
        
        # 退出按钮
        exit_button = ttk.Button(button_frame, text="退出程序", command=self.exit_program)
        exit_button.pack(side="left", padx=10)
        
        # 设置窗口关闭按钮行为
        self.window.protocol("WM_DELETE_WINDOW", self.exit_program)
        
        # 运行状态标志
        self.running = True
    
    def exit_program(self):
        """退出程序"""
        self.running = False
        self.window.quit()
        self.window.destroy()
    
    def confirm(self):
        """确认并开始处理"""
        self.source_path = self.source_path_var.get()
        self.target_path = self.target_path_var.get()
        
        if not os.path.exists(self.source_path):
            messagebox.showerror("错误", "源文件夹路径不存在！")
            return
        
        if not os.path.exists(self.target_path):
            if messagebox.askyesno("提示", "目标文件夹不存在，是否创建？"):
                os.makedirs(self.target_path)
            else:
                return
        
        self.save_paths()
        self.window.quit()
        self.window.destroy()
    
    def run(self):
        """运行窗口并返回结果"""
        self.window.mainloop()
        if self.running:
            return self.source_path, self.target_path
        return None, None
    
    def load_paths(self):
        try:
            with open(CONFIG_FILE, 'r') as f:
                config = json.load(f)
                self.source_path = config.get('source_path', DEFAULT_SOURCE_PATH)
                self.target_path = config.get('target_path', DEFAULT_TARGET_PATH)
        except FileNotFoundError:
            self.source_path = DEFAULT_SOURCE_PATH
            self.target_path = DEFAULT_TARGET_PATH
            self.save_paths()
    
    def save_paths(self):
        config = {
            'source_path': self.source_path,
            'target_path': self.target_path
        }
        with open(CONFIG_FILE, 'w') as f:
            json.dump(config, f)
    
    def select_source(self):
        path = filedialog.askdirectory(initialdir=self.source_path)
        if path:
            self.source_path = path
            self.source_path_var.set(path)
    
    def select_target(self):
        path = filedialog.askdirectory(initialdir=self.target_path)
        if path:
            self.target_path = path
            self.target_path_var.set(path)

def extract_number(file_name):
    pattern = re.compile(
        r"([a-zA-Z]{2}-\d{4})|"  # 匹配形式为ab-1234，不区分大小写
        r"([a-zA-Z]{2}-\d{3})|"  # 匹配形式为ab-123，不区分大小写
        r"([a-zA-Z]{3}-\d{3})|"  # 匹配形式为abc-123，不区分大小写
        r"([a-zA-Z]{4}-\d{4})|"  # 匹配形式为abcd-1234，不区分大小写
        r"([a-zA-Z]{4}-\d{3})|"  # 匹配形式为abcd-123，不区分大小写
        r"([a-zA-Z]{5}-\d{3})|",  # 匹配形式为abcde-123，不区分大小写
        re.IGNORECASE
    )
    match = pattern.search(file_name)
    if match:
        return match.group()
    return None


def select_folder(prompt="请选择文件夹"):
    """
    弹出对话框选择文件夹
    prompt: 显示的提示信息
    """
    root = tk.Tk()
    root.withdraw()  # 隐藏根窗口
    folder_selected = filedialog.askdirectory(title=prompt)  # 弹出对话框选择文件夹
    if not folder_selected:
        print(f"{prompt}操作被取消")
        return None
    return folder_selected


def extract_info_from_html(file_name):
    with open(file_name, 'r', encoding='utf-8') as file:
        soup = BeautifulSoup(file, 'html.parser')

        # 提取番号
        h2_tag = soup.find('h2', class_='title is-4')
        strong_tags = h2_tag.find_all('strong')
        if len(strong_tags) >= 1:
            # 提取第一个<strong>标签内的文本并转换为大写
            code = strong_tags[0].text.strip()
            code = code.split()[0].upper()  # 转换为大写
            # 如果番号不为空，则提取影片名称
            if code:
                original_title = strong_tags[1].text.strip() if len(strong_tags) >= 2 else ""
                # 确保番号和标题之间只有一个空格
                title = f"{code} {original_title}".replace("/", " ")
                title = title.replace(":", " ")
                # 确保不会出现多个空格
                title = ' '.join(title.split())
        
        print("提取的番号", code)  # 输出番号
        # 提取关键词
        keywords = []
        links = soup.find_all('a', href=True)
        for link in links:
            if '/tags' in link['href']:
                # 获取链接的文本内容作为关键词
                keyword = link.text.strip()
                keywords.append(keyword)

                # 提取演员名字
                actor_name = None
                strong_tags = soup.find_all('strong')
                for tag in strong_tags:
                    if tag.text.strip() == '演員:':
                        next_a_tag = tag.find_next('a', href=True)
                        if next_a_tag:
                            actor_name = next_a_tag.text.strip()

        print("影片名称: {}, 关键词: {}, 演员名字: {}".format(title, keywords, actor_name))

        # 提取发行日期
        release_date = None
        strong_tags = soup.find_all('strong')
        for tag in strong_tags:
            if tag.text.strip() == '日期:':
                next_span_tag = tag.find_next('span', class_='value')
                if next_span_tag:
                    release_date = next_span_tag.text.strip()

        print("发行日期:", release_date)

        # 提取时长
        duration = None
        strong_tags = soup.find_all('strong')
        for tag in strong_tags:
            if tag.text.strip() == '時長:':
                next_span_tag = tag.find_next('span', class_='value')
                if next_span_tag:
                    duration = next_span_tag.text.strip()
        print("时长:", duration)

        # 提取片商信息
        producer = None
        strong_tags = soup.find_all('strong')
        for tag in strong_tags:
            if tag.text.strip() == '片商:':
                next_span_tag = tag.find_next('span', class_='value')
                if next_span_tag and next_span_tag.a:
                    producer = next_span_tag.a.text.strip()
        print("片商:", producer)

        return title, keywords, actor_name, release_date, duration, producer, original_title


def create_nfo_content(title, original_title, actor_name, release_date, duration, producer, keywords):
    # 排除的关键字列表
    excluded_keywords = ["無碼", "類別", "有碼", "歐美", "FC2", "動漫", "獨佔動畫", "企畫", "單體作品",  "企劃物", "1080p", "60fps", "HEYZO"]

    # 生成标签
    genre_tags = "\n".join([
        f"<genre>{keyword.strip()}</genre>" for keyword in keywords
        if keyword.strip() and
           keyword.strip() not in excluded_keywords and
           not any(char.isdigit() for char in keyword.strip()) and
           "HEYZO" not in keyword.strip()
    ])

    # 构造 NFO 文件内容
    nfo_content = f"""
<movie>
    <title>{title}</title>
    <originaltitle>{original_title}</originaltitle>
    <actor>
        <name>{actor_name}</name>
        <type>Actor</type>
    </actor>
    <releasedate>{release_date}</releasedate>
    <runtime>{duration}</runtime>
    <studio>{producer}</studio>
    {genre_tags}
</movie>
"""
    return nfo_content


def create_movie_folder(folder_path, movie_name):
    movie_folder_path = os.path.join(folder_path, movie_name)
    if not os.path.exists(movie_folder_path):
        os.makedirs(movie_folder_path)
    return movie_folder_path


def retry_on_ssl_error(max_retries=3, delay=5):
    def decorator(func):
        def wrapper(*args, **kwargs):
            for attempt in range(max_retries):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    if "SSL error" in str(e) and attempt < max_retries - 1:
                        print(f"SSL错误，{delay}秒后重试...")
                        time.sleep(delay)
                        continue
                    raise
            return func(*args, **kwargs)
        return wrapper
    return decorator


def encrypt(signStr):
    """计算有道API签名"""
    hash_algorithm = hashlib.sha256()
    hash_algorithm.update(signStr.encode('utf-8'))
    return hash_algorithm.hexdigest()


def translate_to_chinese(text):
    """使用有道API将文本翻译成中文，保留原文的空格格式"""
    try:
        # 找到最后一个空格的位置
        last_space_index = text.rfind(' ')
        
        if last_space_index != -1:
            # 分割文本为两部分
            part1 = text[:last_space_index]
            part2 = text[last_space_index + 1:]
            
            # 去除多余空格后分别翻译
            part1_translated = translate_text(part1)
            part2_translated = translate_text(part2)
            
            # 组合翻译后的文本并清理文件名
            translated_text = f"{part1_translated} {part2_translated}"
            translated_text = sanitize_filename(translated_text)
        else:
            # 如果没有空格，直接翻译整个文本并清理
            translated_text = translate_text(text)
            translated_text = sanitize_filename(translated_text)
        
        return translated_text
    except Exception as e:
        print(f"翻译出错: {str(e)}")
        return sanitize_filename(text)  # 即使翻译失败也清理文件名


def translate_text(text):
    """调用有道API翻译文本"""
    try:
        youdao_url = 'https://openapi.youdao.com/api'
        curtime = str(int(time.time()))
        salt = str(uuid.uuid1())
        
        sign_str = YOUDAO_APP_KEY + truncate(text) + salt + curtime + YOUDAO_APP_SECRET
        sign = encrypt(sign_str)
        
        data = {
            'q': text,
            'from': 'ja',
            'to': 'zh-CHS',
            'appKey': YOUDAO_APP_KEY,
            'salt': salt,
            'sign': sign,
            'signType': 'v3',
            'curtime': curtime,
        }
        
        headers = {'Content-Type': 'application/x-www-form-urlencoded'}
        response = requests.post(youdao_url, data=data, headers=headers)
        result = response.json()
        
        if result.get('translation'):
            return result['translation'][0]
        else:
            print(f"翻译失败: {result.get('errorCode')}")
            return text
            
    except Exception as e:
        print(f"翻译出错: {str(e)}")
        return text


def truncate(q):
    """截取字符串"""
    if q is None:
        return None
    size = len(q)
    return q if size <= 20 else q[0:10] + str(size) + q[size - 10:size]


def sanitize_filename(filename):
    """清理文件名中的非法字符"""
    # 替换Windows文件系统中的非法字符，包括中文问号
    illegal_chars = r'[<>:"/\\|?*？]'  # 添加了中文问号 ？
    sanitized = re.sub(illegal_chars, '', filename)
    # 确保不会出现多个空格
    sanitized = ' '.join(sanitized.split())
    return sanitized.strip()


def main():
    while True:
        # 显示路径选择器
        selector = PathSelector()
        source_folder, target_folder = selector.run()
        
        if source_folder is None or target_folder is None:
            print("程序已退出")
            break
            
        print("选择的源文件夹:", source_folder)
        print("选择的目标文件夹:", target_folder)
        
        video_files = [f for f in os.listdir(source_folder) if f.endswith((".mp4", ".mkv", ".avi", ".wmv", ".mpg",
                                                                         ".mpeg", ".iso", ".m2ts"))]
        if video_files:
            print("找到的视频文件:")
            for file_name in video_files:
                try:
                    number = extract_number(file_name)
                    if number:
                        # 将番号转换为大写
                        number = number.upper()
                        
                        file_path = 'd:\\tt\\page_source.html'
                        if os.path.exists(file_path):
                            os.remove(file_path)
                        url = f"https://javdb.com/search?q={number}&f=all"
                        try:
                            chrome_options = Options()
                            chrome_options.add_argument('--ignore-certificate-errors')  # 忽略证书错误
                            chrome_options.add_argument('--ignore-ssl-errors')  # 忽略 SSL 错误
                            chrome_options.add_argument('--disable-gpu')
                            chrome_options.add_argument('--no-sandbox')
                            chrome_options.add_argument('--disable-dev-shm-usage')
                            chrome_options.add_argument('--disable-blink-features=AutomationControlled')
                            
                            # 如果使用代理，可以添加代理设置
                            # chrome_options.add_argument('--proxy-server=http://your_proxy:port')
                            
                            browser_path = "C:/Program Files/Waterfox/waterfox.exe"
                            subprocess.run([
                                browser_path, 
                                url,
                                "--ignore-certificate-errors",  # 命令行参数也添加忽略证书错误
                                "--ignore-ssl-errors"
                            ])
                        except Exception as e:
                            print(f"打开浏览器时出现异常: {e}")
                            continue
                        print("文件名: {}, 文件番号: {}".format(file_name, number))

                        time.sleep(10)  # 暂停10秒等待页面加载
                        title, keywords, actor, release_date, duration, producer, original_title = extract_info_from_html(
                            'd:\\tt\\page_source.html')

                        # 保存原始标题并翻译新标题
                        original_title = title
                        # 分离番号和标题部分
                        title_parts = title.split(' ', 1)
                        if len(title_parts) > 1:
                            number_part = title_parts[0].upper()  # 确保番号大写
                            title_part = title_parts[1]
                            # 翻译标题部分
                            translated_title = translate_to_chinese(title_part)
                            # 重新组合，确保只有一个空格
                            title = f"{number_part} {translated_title}"
                        else:
                            title = translate_to_chinese(title)

                        # 从原始标题中提取番号进行验证
                        extracted_number = extract_number(original_title)

                        # 将提取的番号和文件名中的番号转换为小写
                        extracted_number_lower = extracted_number.lower()
                        number_lower = number.lower()

                        # 检查转换后的番号是否一致
                        if extracted_number_lower != number_lower:
                            print(f"文件 {file_name} 的番号与提取的番号不一致，跳过该文件。")
                            continue

                        # 创建Emby格式的nfo内容
                        nfo_content = create_nfo_content(
                            title=title,  # 翻译后的标题
                            original_title=original_title,  # 原始标题
                            actor_name=actor,
                            release_date=release_date,
                            duration=duration,
                            producer=producer,
                            keywords=keywords
                        )

                        # 创建以影片名命名的文件夹
                        movie_folder_path = create_movie_folder(target_folder, title)
                        # 检测是否包含特定的cd标记，并添加到文件名
                        cd_tag = ""
                        for cd in ['cd1', 'cd2', 'cd3', 'cd4']:
                            if cd in file_name.lower():
                                cd_tag = f"-{cd}"
                                break

                        # 在创建新文件名时添加清理步骤
                        title = sanitize_filename(title)
                        title_with_cd = sanitize_filename(f"{title}{cd_tag}")
                        new_video_name = f"{title_with_cd}{os.path.splitext(file_name)[1]}"

                        original_video_path = os.path.join(source_folder, file_name)
                        new_video_path = os.path.join(movie_folder_path, new_video_name)
                        os.rename(original_video_path, new_video_path)

                        # 在d:\\tt\\目录下查找所有jpg文件
                        temp_dir = 'd:\\tt\\'
                        jpg_files = [f for f in os.listdir(temp_dir) if f.endswith('.jpg')]

                        for jpg_file in jpg_files:
                            # 从图片文件名中提取番号
                            image_number = extract_number(jpg_file)
                            
                            # 将两个番号转换为小写进行比对
                            if image_number and image_number.lower() == number.lower():
                                jpg_file_path = os.path.join(temp_dir, jpg_file)
                                try:
                                    # 打开原始图片
                                    original_image = Image.open(jpg_file_path)

                                    # 计算裁剪区域，以右上角为基点裁剪
                                    width, height = original_image.size
                                    new_width = 360
                                    new_height = 536
                                    right = width
                                    top = 0
                                    left = right - new_width
                                    bottom = top + new_height

                                    # 裁剪图片
                                    cropped_image = original_image.crop((left, top, right, bottom))

                                    # 在保存图片文件时也使用清理后的文件名
                                    new_jpg_path = os.path.join(movie_folder_path, f"{sanitize_filename(title_with_cd)}-poster.jpg")
                                    cropped_image.save(new_jpg_path)

                                    # 移动原始jpg文件作为fanart
                                    fanart_path = os.path.join(movie_folder_path, f"{sanitize_filename(title_with_cd)}-fanart.jpg")
                                    shutil.move(jpg_file_path, fanart_path)
                                    
                                    print(f"图片处理完成: poster={new_jpg_path}, fanart={fanart_path}")
                                    break  # 找到匹配的图片后退出循环
                                    
                                except Exception as e:
                                    print(f"处理图片时出错: {e}")
                                    continue
                            
                        if not any(extract_number(f).lower() == number.lower() for f in jpg_files if extract_number(f)):
                            print(f"未找到匹配的图片文件，番号: {number}")

                        # 写入信息到.nfo文件
                        nfo_file_path = os.path.join(movie_folder_path, f"{sanitize_filename(title_with_cd)}.nfo")
                        with open(nfo_file_path, 'w', encoding='utf-8') as nfo_file:
                            nfo_file.write(nfo_content)

                    else:
                        print("文件名: {}, 未找到番号".format(file_name))
                except Exception as e:
                    print(f"处理文件 {file_name} 时出现异常: {e}")
                    continue
        else:
            print("在所选文件夹中未找到视频文件。")


if __name__ == "__main__":
    main()
