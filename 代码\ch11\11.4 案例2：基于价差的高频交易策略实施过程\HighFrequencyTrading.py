import random
import time
import logging
import sys

# 配置日志记录
logging.basicConfig(filename='trading.log', level=logging.INFO,
                    format='%(asctime)s - %(levelname)s - %(message)s')

# 创建控制台输出处理器
console_handler = logging.StreamHandler(sys.stdout)
console_handler.setLevel(logging.INFO)
console_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
console_handler.setFormatter(console_formatter)

# 将控制台输出处理器添加到日志记录器
logger = logging.getLogger()
logger.addHandler(console_handler)

def get_price(symbol):
    # 模拟获取标的的最新报价
    return random.uniform(100, 200)

def trade(threshold):
    while True:
        # 获取标的1和标的2的最新价格
        price1 = get_price("AAPL")
        price2 = get_price("GOOGL")
        
        # 计算价差
        spread = price1 - price2
        
        # 判断价差是否超过阈值
        if abs(spread) > threshold:
            # 根据价差变化方向下单
            if spread > 0:
                buy("AAPL", price1)
                sell("GOOGL", price2)
            else:
                sell("AAPL", price1)
                buy("GOOGL", price2)
        
        # 控制交易频率
        time.sleep(1)

def buy(symbol, price):
    # 下买单逻辑...
    logging.info("买入 %s - 价格: %.2f", symbol, price)

def sell(symbol, price):
    # 下卖单逻辑...
    logging.info("卖出 %s - 价格: %.2f", symbol, price)

# 示例使用
threshold = 5  # 设置价差变化阈值
trade(threshold)
