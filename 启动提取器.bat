@echo off
chcp 65001 >nul
title HTML解码链接提取器

echo.
echo ========================================
echo       HTML解码链接提取器
echo ========================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

REM 检查GUI版本文件是否存在
if exist "html_link_extractor_gui.py" (
    echo 启动图形界面版本...
    python html_link_extractor_gui.py
) else if exist "html_link_extractor.py" (
    echo 启动完整版本（图形界面模式）...
    python html_link_extractor.py --gui
) else if exist "extract_links_simple.py" (
    echo 启动简化版本...
    python extract_links_simple.py
) else (
    echo 错误: 未找到提取器程序文件
    echo 请确保以下文件之一存在:
    echo - html_link_extractor_gui.py
    echo - html_link_extractor.py  
    echo - extract_links_simple.py
)

echo.
pause
