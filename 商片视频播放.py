import tkinter as tk
from tkinter import filedialog, messagebox, ttk
import vlc
import os
import json

class VideoPlayer:
    def __init__(self, master):
        self.master = master
        self.master.title("Jiang's 视频播放器")

        # 检查并创建日志文件目录
        log_directory = 'Z:/work/'
        os.makedirs(log_directory, exist_ok=True)

        self.folder_selection_count = {}

        # 创建一个VLC播放器实例
        self.Instance = vlc.Instance("--no-xlib")
        self.player = self.Instance.media_player_new()


        self.time_label = tk.Label(self.master, text="00:00 / 00:00")
        self.time_label.pack(side=tk.BOTTOM, padx=10)

        # 文件列表和当前索引
        self.video_files = []
        self.current_index = 0

        # 文件夹路径
        self.folder_path = ""

        # 是否循环播放
        self.loop_play = True

        # 常用文件夹记录
        self.common_folders = []
        self.load_common_folders()  # 在初始化时加载常用文件夹记录

        # 常用文件夹选择窗口
        self.move_to_window = None

        # 排序方式
        self.sort_method = tk.StringVar(value="按日期")  # 默认按时间排序
        self.sort_reverse = tk.BooleanVar(value=False)  # 默认升序

        # 创建GUI元素
        self.create_widgets()

        # 创建定时器更新进度条
        self.update_progress()

        # 加载上次播放位置
        self.load_last_position()

        # 绑定窗口关闭事件
        self.master.protocol("WM_DELETE_WINDOW", self.on_closing)


    def create_widgets(self):
        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_slider = tk.Scale(self.master, variable=self.progress_var, from_=0, to=100, orient=tk.HORIZONTAL,
                                        command=self.set_progress)
        self.progress_slider.pack(side=tk.BOTTOM, fill=tk.X, padx=10, pady=5)


        # 文件列表
        self.file_listbox = tk.Listbox(self.master, selectmode=tk.SINGLE)
        self.file_listbox.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=10, pady=5)
        self.file_listbox.bind('<ButtonRelease-1>', self.listbox_click)

        # 视频播放窗口
        self.video_frame = tk.Frame(self.master)
        self.video_frame.pack(side=tk.TOP, fill=tk.BOTH, expand=True)

        # 将vlc播放器窗口嵌入到tkinter Frame中
        self.player.set_hwnd(self.video_frame.winfo_id())

        # 控制按钮
        self.controls_frame = tk.Frame(self.master)
        self.controls_frame.pack(side=tk.TOP, fill=tk.BOTH, expand=False, padx=0)

        self.create_control_buttons()

        # 时间标签
        self.time_label.pack(side=tk.BOTTOM, padx=10)

        # 调整主窗口的大小和位置
        screen_width = self.master.winfo_screenwidth()
        screen_height = self.master.winfo_screenheight()
        window_width = int(screen_width * 0.8)  # 设置为屏幕宽度的 80%
        window_height = int(screen_height * 3 / 4)  # 设置为屏幕高度的 3/4
        window_x = int(screen_width * 0.1)  # 在屏幕左侧留出 10% 的空间
        window_y = int(screen_height / 8)  # 在屏幕上方留有 1/8 的空间
        self.master.geometry(f"{window_width}x{window_height}+{window_x}+{window_y}")

        # 监听鼠标点击事件
        self.progress_slider.bind("<Button-1>", self.mouse_click_callback)

    def mouse_click_callback(self, event):
        # 获取鼠标点击的位置
        x = event.x

        # 计算鼠标点击位置在进度条上的百分比（这里的系数决定了跳转的幅度）
        jump_coefficient = 0.10  # 调整这个系数以控制跳转的幅度
        progress_percentage = x / self.progress_slider.winfo_width() * (1 + jump_coefficient)

        # 将百分比转换为秒数
        duration = self.player.get_length()
        position = int(progress_percentage * duration)

        # 设置播放器的时间
        self.player.set_time(position)

    def set_progress(self, value):
        # 将进度百分比转换为秒数
        duration = self.player.get_length()
        position = int(float(value) * duration / 100)
        self.player.set_time(position)

    def create_control_buttons(self):
        # 播放按钮
        self.play_button = tk.Button(self.controls_frame, text="播放", command=self.play_video)
        self.play_button.pack(side=tk.LEFT, padx=10)

        # 暂停按钮
        self.pause_button = tk.Button(self.controls_frame, text="暂停", command=self.pause_video)
        self.pause_button.pack(side=tk.LEFT, padx=10)

        # 停止按钮
        self.stop_button = tk.Button(self.controls_frame, text="停止", command=self.stop_video)
        self.stop_button.pack(side=tk.LEFT, padx=10)

        # 上一个文件按钮
        self.prev_button = tk.Button(self.controls_frame, text="上一个文件", command=self.play_prev)
        self.prev_button.pack(side=tk.LEFT, padx=10)

        # 下一个文件按钮
        self.next_button = tk.Button(self.controls_frame, text="下一个文件", command=self.play_next)
        self.next_button.pack(side=tk.LEFT, padx=10)

        # 后退10秒按钮
        self.backward_button = tk.Button(self.controls_frame, text="后退10秒", command=self.backward_10_seconds)
        self.backward_button.pack(side=tk.LEFT, padx=10)

        # 前进10秒按钮
        self.forward_button = tk.Button(self.controls_frame, text="前进10秒", command=self.forward_10_seconds)
        self.forward_button.pack(side=tk.LEFT, padx=10)

        # 前进1/4按钮
        self.forward_1_4_button = tk.Button(self.controls_frame, text="前进1/4", command=self.forward_1_4)
        self.forward_1_4_button.pack(side=tk.LEFT, padx=10)

        # 移动到按钮
        self.move_to_button = tk.Button(self.controls_frame, text="移动到", command=self.show_move_to_panel)
        self.move_to_button.pack(side=tk.LEFT, padx=10)

        # 删除按钮
        self.delete_button = tk.Button(self.controls_frame, text="删除", command=self.delete_video)
        self.delete_button.pack(side=tk.LEFT, padx=10)

        # 选择文件夹按钮
        self.select_button = tk.Button(self.controls_frame, text="选择文件夹", command=self.open_folder)
        self.select_button.pack(side=tk.LEFT, padx=10)

        # 排序方式选择框
        sort_frame = tk.Frame(self.controls_frame)
        sort_frame.pack(side=tk.LEFT, padx=10)
        
        # 排序方式下拉框
        sort_methods = ["按名称", "按日期"]
        sort_menu = tk.OptionMenu(sort_frame, self.sort_method, *sort_methods, 
                                command=self.on_sort_method_change)
        sort_menu.pack(side=tk.LEFT)
        
        # 排序方向复选框
        sort_reverse_check = tk.Checkbutton(sort_frame, text="倒序", variable=self.sort_reverse,
                                          command=self.on_sort_method_change)
        sort_reverse_check.pack(side=tk.LEFT)

    def play_video(self):
        if hasattr(self, 'media'):
            self.player.set_media(self.media)

        if self.player.get_media():
            self.player.play()

    def pause_video(self):
        self.player.pause()

    def stop_video(self):
        self.player.stop()

    def open_folder(self):
        self.folder_path = filedialog.askdirectory()
        if self.folder_path:
            # 使用 os.walk 遍历根目录和所有子目录，收集所有视频文件
            self.video_files = []
            for root, _, files in os.walk(self.folder_path):
                for f in files:
                    if f.lower().endswith(('.mp4', '.avi', '.mkv', '.mov', '.ts', '.wmv')):
                        self.video_files.append(os.path.join(root, f))

            # 对所有文件进行统一排序
            if self.sort_method.get() == "按名称":
                self.video_files.sort(reverse=self.sort_reverse.get())
            else:  # 按日期
                self.video_files.sort(key=lambda x: os.path.getmtime(x), reverse=self.sort_reverse.get())

            # 尝试定位到上次播放的文件
            last_position = self.load_last_position()
            if last_position:
                for i, video_file in enumerate(self.video_files):
                    if video_file == last_position:  # 使用完整路径比较
                        self.current_index = i
                        break

            self.update_file_list()  # 更新列表框中的文件列表
            if self.video_files:
                self.play_selected_video()
                # 确保列表选中项与当前播放文件一致
                self.file_listbox.selection_clear(0, tk.END)
                self.file_listbox.selection_set(self.current_index)
                self.file_listbox.see(self.current_index)
            else:
                print("在选定的文件夹中未找到视频文件。")

    def play_next(self):
        if self.video_files:
            # 保存当前索引以检查是否到达最后一个文件
            current_index_before = self.current_index
            self.current_index = (self.current_index + 1) % len(self.video_files)

            # 如果当前索引为0且之前索引不为0，表示已经播放到最后一个文件
            if self.current_index == 0 and current_index_before != 0:
                if not self.loop_play:
                    self.stop_video()
                return

            self.play_selected_video()
            self.update_file_list()  # 更新文件列表，确保正确高亮正在播放的文件

    def play_prev(self):
        if self.video_files:
            self.current_index = (self.current_index - 1) % len(self.video_files)
            self.play_selected_video()

    def play_selected_video(self):
        video_path = os.path.join(self.folder_path, self.video_files[self.current_index])
        self.media = self.Instance.media_new(video_path)
        self.play_video()

    def delete_video(self):
        if self.video_files:
            confirmation = messagebox.askyesno("确认删除", "确认要删除当前视频文件吗？")
            if confirmation:
                # 停止播放
                self.stop_video()

                video_path = os.path.join(self.folder_path, self.video_files[self.current_index])
                video_dir = os.path.dirname(video_path)

                # 记录删除操作到日志文件
                self.log_deletion(video_path)

                os.remove(video_path)
                self.video_files.pop(self.current_index)

                # 检查子文件夹是否还有其他视频文件
                if video_dir != self.folder_path:  # 确保是子文件夹
                    try:
                        # 检查文件夹中是否还有其他视频文件
                        has_other_videos = False
                        for file in os.listdir(video_dir):
                            if file.lower().endswith(('.mp4', '.avi', '.mkv', '.mov', '.ts', '.wmv')):
                                has_other_videos = True
                                break
                        
                        if not has_other_videos:
                            # 删除文件夹中的所有文件
                            for file in os.listdir(video_dir):
                                file_path = os.path.join(video_dir, file)
                                try:
                                    if os.path.isfile(file_path):
                                        os.remove(file_path)
                                    elif os.path.isdir(file_path):
                                        import shutil
                                        shutil.rmtree(file_path)
                                except Exception as e:
                                    print(f"删除文件 {file_path} 时出错: {str(e)}")
                            
                            # 删除空文件夹
                            os.rmdir(video_dir)
                            messagebox.showinfo("删除成功", f"视频文件及其空文件夹已删除")
                    except Exception as e:
                        messagebox.showerror("删除失败", f"删除文件夹时出错: {str(e)}")

                # 如果删除后列表为空，停止播放
                if not self.video_files:
                    self.stop_video()
                else:
                    # 如果删除的是最后一个文件，播放前一个文件
                    if self.current_index == len(self.video_files):
                        self.current_index -= 1
                    self.play_selected_video()

                # 更新播放列表
                self.update_file_list()

    def log_deletion(self, video_path):
        log_file_path = 'Z:/work/deletion_index.txt'
        with open(log_file_path, 'a', encoding="utf-8") as log_file:
            log_file.write(f"Deleted: {video_path}\n")

    def forward_10_seconds(self):
        current_time = self.player.get_time()
        new_time = current_time + 10000  # 10 seconds in milliseconds
        self.player.set_time(new_time)

    def backward_10_seconds(self):
        current_time = self.player.get_time()
        new_time = max(current_time - 10000, 0)  # Ensure the time is non-negative
        self.player.set_time(new_time)

    def forward_1_4(self):
        current_time = self.player.get_time()
        total_duration = self.player.get_length()

        if total_duration > 0:
            # 预留5分钟（300秒 = 300000毫秒）
            buffer_time = 300000

            # 检查是否在最后5分钟区域内
            remaining_time = total_duration - current_time
            is_in_buffer_zone = remaining_time <= buffer_time

            if is_in_buffer_zone:
                # 在最后5分钟区域内，逐渐减少跳跃幅度
                if remaining_time > 180000:  # 剩余时间大于3分钟
                    # 跳跃30秒
                    jump_time = 30000
                elif remaining_time > 120000:  # 剩余时间大于2分钟
                    # 跳跃20秒
                    jump_time = 20000
                elif remaining_time > 60000:   # 剩余时间大于1分钟
                    # 跳跃15秒
                    jump_time = 15000
                else:  # 剩余时间小于1分钟
                    # 跳跃10秒
                    jump_time = 10000

                # 确保不会跳过结束位置，至少保留10秒
                max_allowed_time = total_duration - 10000
                forward_time = min(max_allowed_time, current_time + jump_time)
            else:
                # 不在最后5分钟区域内，正常跳转1/8
                jump_time = total_duration // 8
                # 确保跳跃后不会进入最后5分钟的缓冲区
                max_allowed_time = total_duration - buffer_time
                forward_time = min(max_allowed_time, current_time + jump_time)

            # 设置新的播放时间
            self.player.set_time(forward_time)

    def update_progress(self):
        if self.player.get_length() > 0:
            position = self.player.get_time()
            duration = self.player.get_length()

            # 更新进度条
            progress_percentage = (position / duration) * 100
            self.progress_var.set(progress_percentage)

            # 更新时间标签
            current_time_str = self.format_time(position)
            total_time_str = self.format_time(duration)
            time_label_text = f"{current_time_str} / {total_time_str}"
            self.time_label.config(text=time_label_text)

        self.master.after(100, self.update_progress)

    def format_time(self, milliseconds):
        seconds, milliseconds = divmod(milliseconds, 1000)
        minutes, seconds = divmod(seconds, 60)
        hours, minutes = divmod(minutes, 60)
        return f"{hours:02}:{minutes:02}:{seconds:02}"


    def update_file_list(self):
        self.file_listbox.delete(0, tk.END)  # 清空列表

        for i, video_file in enumerate(self.video_files):
            self.file_listbox.insert(tk.END, video_file)
            if i == self.current_index:
                self.file_listbox.selection_set(i)  # 高亮当前正在播放的文件
                self.file_listbox.see(i)  # 确保当前文件在可视区域内

    def listbox_click(self, event):
        selected_index = self.file_listbox.curselection()
        if selected_index:
            self.current_index = selected_index[0]
            self.play_selected_video()

    def show_move_to_panel(self):
        self.move_to_window = tk.Toplevel(self.master)
        self.move_to_window.title("移动到")

        # 获取主窗口的位置和大小
        master_x = self.master.winfo_x()
        master_y = self.master.winfo_y()
        master_width = self.master.winfo_width()
        master_height = self.master.winfo_height()

        # 计算移动到窗口的位置
        window_x = master_x + master_width // 4
        window_y = master_y + master_height // 4

        # 设置窗口位置
        self.move_to_window.geometry(f"+{window_x}+{window_y}")

        # 添加常用文件夹按钮
        for folder in self.common_folders:
            folder_button = tk.Button(self.move_to_window, text=folder, command=lambda f=folder: self.move_to_folder(f))
            folder_button.pack(side=tk.TOP, padx=10, pady=5)

        # 添加其他选项按钮
        other_button = tk.Button(self.move_to_window, text="其他", command=self.move_to_folder)
        other_button.pack(side=tk.TOP, padx=10, pady=5)

    def move_to_folder(self, destination_folder=None):
        if destination_folder is None:
            destination_folder = filedialog.askdirectory()

        if destination_folder:
            # 记录选择次数
            if destination_folder in self.folder_selection_count:
                self.folder_selection_count[destination_folder] += 1
            else:
                self.folder_selection_count[destination_folder] = 1

            current_video_path = os.path.join(self.folder_path, self.video_files[self.current_index])
            new_video_path = os.path.join(destination_folder, os.path.basename(current_video_path))

            try:
                # 停止播放
                self.stop_video()

                # 关闭当前播放的视频文件
                self.player.set_media(None)
                self.player.stop()

                # 移动文件
                os.rename(current_video_path, new_video_path)
                messagebox.showinfo("移动成功", f"视频已成功移动到:\n{new_video_path}")

                 # 清除播放列表
                self.video_files.pop(self.current_index)

                # 记录常用文件夹
                if destination_folder not in self.common_folders:
                    self.common_folders.append(destination_folder)
                    self.save_common_folders()

                # 更新播放列表和 current_index
                self.update_file_list()

                # 检查是否需要更新 current_index
                if self.current_index >= len(self.video_files):
                    self.current_index = len(self.video_files) - 1

                # 播放下一个视频
                self.play_selected_video()

                # 关闭常用文件夹选择窗口
                if self.move_to_window:
                    self.move_to_window.destroy()

            except Exception as e:
                messagebox.showerror("移动失败", f"移动视频时出错:\n{str(e)}")

            if self.folder_selection_count[destination_folder] % 5 == 0:
                self.sort_folders_by_selection_count()

    def close_message_box(self):
        if self.message_box:
            self.message_box.destroy()

    def sort_folders_by_selection_count(self):
        # 按照选择次数对文件夹排序
        sorted_folders = sorted(self.folder_selection_count.keys(), key=lambda x: self.folder_selection_count[x],
                                reverse=True)

        # 更新常用文件夹列表
        self.common_folders = sorted_folders

        # 更新播放列表和 current_index
        self.update_file_list()

        # 检查是否需要更新 current_index
        if self.current_index >= len(self.video_files):
            self.current_index = len(self.video_files) - 1

        # 播放下一个视频
        self.play_selected_video()

        # 关闭常用文件夹选择窗口
        if self.move_to_window:
            self.move_to_window.destroy()
    def save_common_folders(self):
        with open('common_folders.json', 'w') as file:
            json.dump(self.common_folders, file)

    def load_common_folders(self):
        try:
            with open('common_folders.json', 'r') as file:
                self.common_folders = json.load(file)
        except FileNotFoundError:
            pass

    def save_last_position(self):
        if self.video_files and self.current_index < len(self.video_files):
            current_video = os.path.join(self.folder_path, self.video_files[self.current_index])
            last_position_path = os.path.join(self.folder_path, 'last_position.json')
            with open(last_position_path, 'w', encoding='utf-8') as f:
                json.dump({'last_file': current_video}, f)

    def load_last_position(self):
        if not self.folder_path:  # 如果还没有选择文件夹，返回None
            return None
        try:
            last_position_path = os.path.join(self.folder_path, 'last_position.json')
            with open(last_position_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                return data.get('last_file')
        except (FileNotFoundError, json.JSONDecodeError):
            return None

    def on_closing(self):
        self.save_last_position()
        self.master.destroy()

    def on_sort_method_change(self, *args):
        """当排序方式改变时直接对当前列表进行排序"""
        if self.video_files:
            # 保存当前播放的文件的完整路径
            current_file = self.video_files[self.current_index] if self.video_files else None
            
            # 根据排序方式对文件进行排序
            if self.sort_method.get() == "按名称":
                self.video_files.sort(reverse=self.sort_reverse.get())
            else:  # 按日期
                self.video_files.sort(key=lambda x: os.path.getmtime(x), reverse=self.sort_reverse.get())
            
            # 找到当前文件在新排序中的位置
            if current_file:
                for i, file in enumerate(self.video_files):
                    if file == current_file:
                        self.current_index = i
                        break
            
            # 更新文件列表显示
            self.update_file_list()
            
            # 确保列表选中项与当前播放文件一致
            self.file_listbox.selection_clear(0, tk.END)
            self.file_listbox.selection_set(self.current_index)
            self.file_listbox.see(self.current_index)
            
            # 继续播放当前文件
            self.play_selected_video()


if __name__ == "__main__":
    root = tk.Tk()
    player = VideoPlayer(root)
    root.mainloop()