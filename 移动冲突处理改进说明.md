# 图片预览管理器 - 移动冲突处理功能改进

## 改进概述

在原有的图片预览管理器基础上，增强了文件和文件夹移动时的冲突处理功能，特别是添加了**重命名选项**，让用户在遇到同名冲突时有更多的处理选择。

## 主要改进内容

### 1. 统一的冲突处理对话框

**改进前：**
- 文件移动冲突：有完整的重命名选项
- 文件夹移动冲突：只有简单的是/否确认
- 文件移动到上级文件夹冲突：只有简单的是/否确认

**改进后：**
- 所有移动冲突场景都使用统一的对话框
- 支持文件和文件夹的重命名处理
- 提供一致的用户体验

### 2. 新增自动重命名功能

**核心特性：**
- 点击"自动重命名"按钮，系统自动生成不冲突的名称
- 命名规则：在原名称后添加 `-1`, `-2`, `-3` 等数字后缀
- 自动检测直到找到不冲突的名称

**示例：**
```
原文件名: 测试图片.jpg
目标文件夹已存在: 测试图片.jpg, 测试图片-1.jpg
自动生成: 测试图片-2.jpg

原文件夹名: 我的文件夹
目标位置已存在: 我的文件夹, 我的文件夹-1
自动生成: 我的文件夹-2
```

### 3. 完整的冲突处理选项

新的冲突对话框提供以下选项：

#### 文件冲突处理：
- **覆盖** - 覆盖目标位置的同名文件
- **全部覆盖** - 对后续所有冲突文件都执行覆盖
- **跳过** - 跳过当前文件，不进行移动
- **全部跳过** - 跳过所有冲突文件
- **重命名** - 手动输入新的文件名
- **自动重命名** - 系统自动生成不冲突的文件名
- **取消** - 取消整个移动操作

#### 文件夹冲突处理：
- **覆盖** - 覆盖目标位置的同名文件夹
- **跳过** - 跳过当前文件夹，不进行移动
- **重命名** - 手动输入新的文件夹名
- **自动重命名** - 系统自动生成不冲突的文件夹名
- **取消** - 取消整个移动操作

### 4. 适用场景

改进后的冲突处理功能适用于以下场景：

1. **批量文件移动** - 选择多个文件移动到目标文件夹
2. **文件夹移动** - 通过右键菜单移动文件夹
3. **文件移动到上级** - 将当前文件夹中的文件移动到上级文件夹
4. **单个文件移动** - 移动单个文件到指定位置

## 技术实现细节

### 1. 统一的对话框函数

```python
def show_overwrite_dialog(self, file_name, is_folder=False):
    """显示文件/文件夹覆盖确认对话框，返回用户选择和新文件名"""
    # 支持文件和文件夹两种模式
    # 返回用户选择和新名称
```

### 2. 自动重命名算法

```python
def _generate_unique_filename(self, target_dir, filename):
    """生成唯一的文件名，避免冲突"""
    name, ext = os.path.splitext(filename)
    counter = 1
    while True:
        new_filename = f"{name}-{counter}{ext}"
        if not os.path.exists(os.path.join(target_dir, new_filename)):
            return new_filename
        counter += 1
```

### 3. 冲突处理逻辑

在所有移动操作中统一处理冲突：
- 检测目标位置是否存在同名项
- 显示冲突对话框让用户选择处理方式
- 根据用户选择执行相应操作
- 支持批量操作的"全部"选项

## 用户体验改进

### 1. 更直观的界面
- 清晰的冲突提示信息
- 分组的操作按钮
- 实时的文件名预览

### 2. 更灵活的选择
- 手动重命名：完全自定义新名称
- 自动重命名：一键解决冲突
- 批量处理：减少重复操作

### 3. 更安全的操作
- 明确的确认提示
- 可撤销的操作选择
- 详细的操作结果反馈

## 测试验证

提供了完整的测试脚本：
- `测试移动冲突.py` - 创建测试环境验证功能
- `演示自动重命名.py` - 演示自动重命名算法

## 总结

通过这次改进，图片预览管理器的文件移动功能变得更加完善和用户友好：

1. **统一性** - 所有移动场景都有一致的冲突处理体验
2. **便利性** - 自动重命名功能大大减少了用户的手动操作
3. **灵活性** - 提供多种冲突处理选择，满足不同需求
4. **安全性** - 清晰的确认机制，避免意外的文件覆盖

这些改进让用户在管理大量图片文件时更加高效和安全。
