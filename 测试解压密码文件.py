# -*- coding: utf-8 -*-
"""
测试解压密码文件功能
"""
import os

def create_test_password_files():
    """创建测试用的解压密码文件"""
    # 在当前目录创建测试文件夹
    test_dir = os.path.join(os.getcwd(), "测试解压密码文件")
    
    if not os.path.exists(test_dir):
        os.makedirs(test_dir)
        print(f"创建测试目录：{test_dir}")
    else:
        print(f"测试目录已存在：{test_dir}")
    
    # 创建各种解压密码文件
    password_files = [
        # 标准格式
        ("解压密码.txt", "密码：test123456"),
        ("解压密码.TXT", "解压密码：mypassword"),
        
        # 其他格式
        ("密码.txt", "password:secret123"),
        ("password_extract.txt", "Extract Password: admin2023"),
        ("unzip_password.txt", "unzip password: unlock789"),
        
        # 无密码指示
        ("无密码文件.txt", "密码：无"),
        
        # 直接密码（无前缀）
        ("直接密码.txt", "directpass456"),
        
        # 包含注释的文件
        ("带注释密码.txt", """# 这是解压密码文件
# 请使用下面的密码解压
密码：comment123
# 备注：这个密码很重要"""),
    ]
    
    created_files = []
    
    for filename, content in password_files:
        file_path = os.path.join(test_dir, filename)
        
        # 如果文件已存在，跳过
        if os.path.exists(file_path):
            print(f"⏭️ 文件已存在，跳过：{filename}")
            continue
            
        try:
            # 使用ANSI编码保存
            with open(file_path, 'w', encoding='ansi') as f:
                f.write(content)
                
            created_files.append(file_path)
            print(f"✅ 创建文件：{filename}")
            
        except UnicodeEncodeError:
            # 如果ANSI编码失败，使用UTF-8
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                created_files.append(file_path)
                print(f"✅ 创建文件（UTF-8）：{filename}")
            except Exception as e:
                print(f"❌ 创建文件失败：{filename} - {e}")
        except Exception as e:
            print(f"❌ 创建文件失败：{filename} - {e}")
    
    return test_dir, created_files

def test_password_reading():
    """测试密码读取功能"""
    print("\n=== 测试密码读取功能 ===")
    
    try:
        # 导入解压缩模块
        import sys
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        from 解压缩 import get_extract_password, read_extract_password_from_file
        
        test_dir, created_files = create_test_password_files()
        
        # 测试每个密码文件
        for file_path in created_files:
            filename = os.path.basename(file_path)
            print(f"\n测试文件：{filename}")
            
            try:
                password = read_extract_password_from_file(file_path)
                if password is None:
                    print(f"  结果：无密码指示")
                elif password:
                    print(f"  结果：找到密码 - {password}")
                else:
                    print(f"  结果：未找到有效密码")
            except Exception as e:
                print(f"  错误：{e}")
        
        # 测试目录搜索功能
        print(f"\n=== 测试目录搜索功能 ===")
        password = get_extract_password(test_dir, test_dir)
        if password:
            print(f"目录搜索结果：找到密码 - {password}")
        else:
            print("目录搜索结果：未找到密码")
            
        return test_dir
        
    except ImportError as e:
        print(f"❌ 导入模块失败：{e}")
        return None
    except Exception as e:
        print(f"❌ 测试失败：{e}")
        return None

def show_file_contents(test_dir):
    """显示创建的文件内容"""
    print(f"\n=== 创建的文件内容 ===")
    
    for filename in os.listdir(test_dir):
        file_path = os.path.join(test_dir, filename)
        if os.path.isfile(file_path):
            print(f"\n文件：{filename}")
            try:
                # 尝试多种编码读取
                for encoding in ['ansi', 'utf-8', 'gbk']:
                    try:
                        with open(file_path, 'r', encoding=encoding) as f:
                            content = f.read()
                            print(f"  内容（{encoding}）：{content}")
                            break
                    except UnicodeDecodeError:
                        continue
            except Exception as e:
                print(f"  读取失败：{e}")

if __name__ == "__main__":
    print("=== 解压密码文件功能测试 ===")
    
    # 创建测试文件并测试
    test_dir = test_password_reading()
    
    if test_dir:
        # 显示文件内容
        show_file_contents(test_dir)
        
        print(f"\n=== 测试说明 ===")
        print(f"测试目录：{test_dir}")
        print(f"\n现在可以在GUI程序中选择此目录进行测试")
        print(f"程序会按以下顺序查找密码：")
        print(f"1. 解压密码.txt 等文件 ← 新增，优先级最高")
        print(f"2. password.txt 文件")
        print(f"3. 密码字典 passdict.txt")
        print(f"4. 无密码解压")
        
        print(f"\n支持的解压密码文件名：")
        print(f"- 解压密码.txt / 解压密码.TXT")
        print(f"- 密码.txt / 密码.TXT")
        print(f"- password_extract.txt")
        print(f"- extract_password.txt")
        print(f"- unzip_password.txt")
        
        print(f"\n支持的密码格式：")
        print(f"- 密码：your_password")
        print(f"- 解压密码：your_password")
        print(f"- password:your_password")
        print(f"- Extract Password:your_password")
        print(f"- 直接写密码（无前缀）")
    
    print("\n测试完成！")
