{"cells": [{"cell_type": "code", "execution_count": 2, "id": "e1d6ca73", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Close</th>\n", "      <th>Volume</th>\n", "      <th>Open</th>\n", "      <th>High</th>\n", "      <th>Low</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2023-06-23</th>\n", "      <td>186.68</td>\n", "      <td>53117000</td>\n", "      <td>185.550</td>\n", "      <td>187.560</td>\n", "      <td>185.0100</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-06-22</th>\n", "      <td>187.00</td>\n", "      <td>51245330</td>\n", "      <td>183.740</td>\n", "      <td>187.045</td>\n", "      <td>183.6700</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-06-21</th>\n", "      <td>183.96</td>\n", "      <td>49515700</td>\n", "      <td>184.900</td>\n", "      <td>185.410</td>\n", "      <td>182.5901</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-06-20</th>\n", "      <td>185.01</td>\n", "      <td>49799090</td>\n", "      <td>184.410</td>\n", "      <td>186.100</td>\n", "      <td>184.4100</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-06-16</th>\n", "      <td>184.92</td>\n", "      <td>101256200</td>\n", "      <td>186.730</td>\n", "      <td>186.990</td>\n", "      <td>184.2700</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-06-15</th>\n", "      <td>186.01</td>\n", "      <td>65433170</td>\n", "      <td>183.960</td>\n", "      <td>186.520</td>\n", "      <td>183.7800</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-06-14</th>\n", "      <td>183.95</td>\n", "      <td>57462880</td>\n", "      <td>183.370</td>\n", "      <td>184.390</td>\n", "      <td>182.0200</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-06-13</th>\n", "      <td>183.31</td>\n", "      <td>54929130</td>\n", "      <td>182.800</td>\n", "      <td>184.150</td>\n", "      <td>182.4400</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-06-12</th>\n", "      <td>183.79</td>\n", "      <td>54755000</td>\n", "      <td>181.270</td>\n", "      <td>183.890</td>\n", "      <td>180.9700</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-06-09</th>\n", "      <td>180.96</td>\n", "      <td>48899970</td>\n", "      <td>181.500</td>\n", "      <td>182.230</td>\n", "      <td>180.6300</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-06-08</th>\n", "      <td>180.57</td>\n", "      <td>50214880</td>\n", "      <td>177.895</td>\n", "      <td>180.840</td>\n", "      <td>177.4600</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-06-07</th>\n", "      <td>177.82</td>\n", "      <td>61944620</td>\n", "      <td>178.440</td>\n", "      <td>181.210</td>\n", "      <td>177.3200</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-06-06</th>\n", "      <td>179.21</td>\n", "      <td>64848370</td>\n", "      <td>179.965</td>\n", "      <td>180.120</td>\n", "      <td>177.4300</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-06-05</th>\n", "      <td>179.58</td>\n", "      <td>121946500</td>\n", "      <td>182.630</td>\n", "      <td>184.951</td>\n", "      <td>178.0350</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-06-02</th>\n", "      <td>180.95</td>\n", "      <td>61996910</td>\n", "      <td>181.030</td>\n", "      <td>181.780</td>\n", "      <td>179.2600</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-06-01</th>\n", "      <td>180.09</td>\n", "      <td>68901810</td>\n", "      <td>177.700</td>\n", "      <td>180.120</td>\n", "      <td>176.9306</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-05-31</th>\n", "      <td>177.25</td>\n", "      <td>99625290</td>\n", "      <td>177.325</td>\n", "      <td>179.350</td>\n", "      <td>176.7600</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-05-30</th>\n", "      <td>177.30</td>\n", "      <td>55964400</td>\n", "      <td>176.960</td>\n", "      <td>178.990</td>\n", "      <td>176.5700</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-05-26</th>\n", "      <td>175.43</td>\n", "      <td>54834980</td>\n", "      <td>173.320</td>\n", "      <td>175.770</td>\n", "      <td>173.1100</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-05-25</th>\n", "      <td>172.99</td>\n", "      <td>56058260</td>\n", "      <td>172.410</td>\n", "      <td>173.895</td>\n", "      <td>171.6900</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["             Close     Volume     Open     High       Low\n", "Date                                                     \n", "2023-06-23  186.68   53117000  185.550  187.560  185.0100\n", "2023-06-22  187.00   51245330  183.740  187.045  183.6700\n", "2023-06-21  183.96   49515700  184.900  185.410  182.5901\n", "2023-06-20  185.01   49799090  184.410  186.100  184.4100\n", "2023-06-16  184.92  101256200  186.730  186.990  184.2700\n", "2023-06-15  186.01   65433170  183.960  186.520  183.7800\n", "2023-06-14  183.95   57462880  183.370  184.390  182.0200\n", "2023-06-13  183.31   54929130  182.800  184.150  182.4400\n", "2023-06-12  183.79   54755000  181.270  183.890  180.9700\n", "2023-06-09  180.96   48899970  181.500  182.230  180.6300\n", "2023-06-08  180.57   50214880  177.895  180.840  177.4600\n", "2023-06-07  177.82   61944620  178.440  181.210  177.3200\n", "2023-06-06  179.21   64848370  179.965  180.120  177.4300\n", "2023-06-05  179.58  121946500  182.630  184.951  178.0350\n", "2023-06-02  180.95   61996910  181.030  181.780  179.2600\n", "2023-06-01  180.09   68901810  177.700  180.120  176.9306\n", "2023-05-31  177.25   99625290  177.325  179.350  176.7600\n", "2023-05-30  177.30   55964400  176.960  178.990  176.5700\n", "2023-05-26  175.43   54834980  173.320  175.770  173.1100\n", "2023-05-25  172.99   56058260  172.410  173.895  171.6900"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["import matplotlib.pyplot as plt\n", "import mplfinance as mpf\n", "import pandas as pd\n", "\n", "plt.rcParams['font.family'] = ['SimHei']  # 设置中文字体\n", "plt.rcParams['axes.unicode_minus'] = False  # 设置负号显示\n", "\n", "# 读取股票数据\n", "df = pd.read_csv('data/AAPL.csv', index_col='Date', parse_dates=True)\n", "# 清洗数据\n", "df['Close'] = df['Close'].str.replace('$', '').astype(float)\n", "df['Open'] = df['Open'].str.replace('$', '').astype(float)\n", "df['High'] = df['High'].str.replace('$', '').astype(float)\n", "df['Low'] = df['Low'].str.replace('$', '').astype(float)\n", "\n", "df"]}, {"cell_type": "markdown", "id": "85a554e0", "metadata": {}, "source": ["###  绘制K线图和移动平均线图"]}, {"cell_type": "code", "execution_count": 6, "id": "79c48812", "metadata": {}, "outputs": [{"ename": "ImportError", "evalue": "Failed to import any of the following Qt binding modules: PyQt6, PySide6, PyQt5, PySide2", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mImportError\u001b[0m                               Traceback (most recent call last)", "Cell \u001b[1;32mIn[6], line 1\u001b[0m\n\u001b[1;32m----> 1\u001b[0m \u001b[43mget_ipython\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mrun_line_magic\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mmatplotlib\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mqt\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[0;32m      3\u001b[0m ma5 \u001b[38;5;241m=\u001b[39m df[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mMA5\u001b[39m\u001b[38;5;124m'\u001b[39m] \u001b[38;5;241m=\u001b[39m df[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mClose\u001b[39m\u001b[38;5;124m'\u001b[39m]\u001b[38;5;241m.\u001b[39mrolling(\u001b[38;5;241m5\u001b[39m,min_periods\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m1\u001b[39m)\u001b[38;5;241m.\u001b[39mmean()\n\u001b[0;32m      4\u001b[0m ma20 \u001b[38;5;241m=\u001b[39m df[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mMA20\u001b[39m\u001b[38;5;124m'\u001b[39m] \u001b[38;5;241m=\u001b[39m df[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mClose\u001b[39m\u001b[38;5;124m'\u001b[39m]\u001b[38;5;241m.\u001b[39mrolling(\u001b[38;5;241m20\u001b[39m,min_periods\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m1\u001b[39m)\u001b[38;5;241m.\u001b[39mmean()\n", "File \u001b[1;32m~\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\IPython\\core\\interactiveshell.py:2417\u001b[0m, in \u001b[0;36mInteractiveShell.run_line_magic\u001b[1;34m(self, magic_name, line, _stack_depth)\u001b[0m\n\u001b[0;32m   2415\u001b[0m     kwargs[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mlocal_ns\u001b[39m\u001b[38;5;124m'\u001b[39m] \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mget_local_scope(stack_depth)\n\u001b[0;32m   2416\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mbuiltin_trap:\n\u001b[1;32m-> 2417\u001b[0m     result \u001b[38;5;241m=\u001b[39m \u001b[43mfn\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m   2419\u001b[0m \u001b[38;5;66;03m# The code below prevents the output from being displayed\u001b[39;00m\n\u001b[0;32m   2420\u001b[0m \u001b[38;5;66;03m# when using magics with decodator @output_can_be_silenced\u001b[39;00m\n\u001b[0;32m   2421\u001b[0m \u001b[38;5;66;03m# when the last Python token in the expression is a ';'.\u001b[39;00m\n\u001b[0;32m   2422\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mgetattr\u001b[39m(fn, magic\u001b[38;5;241m.\u001b[39mMAGIC_OUTPUT_CAN_BE_SILENCED, \u001b[38;5;28;01mFalse\u001b[39;00m):\n", "File \u001b[1;32m~\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\IPython\\core\\magics\\pylab.py:99\u001b[0m, in \u001b[0;36mPylabMagics.matplotlib\u001b[1;34m(self, line)\u001b[0m\n\u001b[0;32m     97\u001b[0m     \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mAvailable matplotlib backends: \u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;241m%\u001b[39m backends_list)\n\u001b[0;32m     98\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m---> 99\u001b[0m     gui, backend \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mshell\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43menable_matplotlib\u001b[49m\u001b[43m(\u001b[49m\u001b[43margs\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mgui\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mlower\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mif\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;28;43misinstance\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43margs\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mgui\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mstr\u001b[39;49m\u001b[43m)\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01melse\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43margs\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mgui\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    100\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_show_matplotlib_backend(args\u001b[38;5;241m.\u001b[39mgui, backend)\n", "File \u001b[1;32m~\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\IPython\\core\\interactiveshell.py:3603\u001b[0m, in \u001b[0;36mInteractiveShell.enable_matplotlib\u001b[1;34m(self, gui)\u001b[0m\n\u001b[0;32m   3599\u001b[0m         \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mWarning: Cannot change to a different GUI toolkit: \u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[38;5;124m.\u001b[39m\u001b[38;5;124m'\u001b[39m\n\u001b[0;32m   3600\u001b[0m                 \u001b[38;5;124m'\u001b[39m\u001b[38;5;124m Using \u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[38;5;124m instead.\u001b[39m\u001b[38;5;124m'\u001b[39m \u001b[38;5;241m%\u001b[39m (gui, \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mpylab_gui_select))\n\u001b[0;32m   3601\u001b[0m         gui, backend \u001b[38;5;241m=\u001b[39m pt\u001b[38;5;241m.\u001b[39mfind_gui_and_backend(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mpylab_gui_select)\n\u001b[1;32m-> 3603\u001b[0m \u001b[43mpt\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mactivate_matplotlib\u001b[49m\u001b[43m(\u001b[49m\u001b[43mbackend\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m   3604\u001b[0m configure_inline_support(\u001b[38;5;28mself\u001b[39m, backend)\n\u001b[0;32m   3606\u001b[0m \u001b[38;5;66;03m# Now we must activate the gui pylab wants to use, and fix %run to take\u001b[39;00m\n\u001b[0;32m   3607\u001b[0m \u001b[38;5;66;03m# plot updates into account\u001b[39;00m\n", "File \u001b[1;32m~\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\IPython\\core\\pylabtools.py:360\u001b[0m, in \u001b[0;36mactivate_matplotlib\u001b[1;34m(backend)\u001b[0m\n\u001b[0;32m    355\u001b[0m \u001b[38;5;66;03m# Due to circular imports, pyplot may be only partially initialised\u001b[39;00m\n\u001b[0;32m    356\u001b[0m \u001b[38;5;66;03m# when this function runs.\u001b[39;00m\n\u001b[0;32m    357\u001b[0m \u001b[38;5;66;03m# So avoid needing matplotlib attribute-lookup to access pyplot.\u001b[39;00m\n\u001b[0;32m    358\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mmatplotlib\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m pyplot \u001b[38;5;28;01mas\u001b[39;00m plt\n\u001b[1;32m--> 360\u001b[0m \u001b[43mplt\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mswitch_backend\u001b[49m\u001b[43m(\u001b[49m\u001b[43mbackend\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    362\u001b[0m plt\u001b[38;5;241m.\u001b[39mshow\u001b[38;5;241m.\u001b[39m_needmain \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mFalse\u001b[39;00m\n\u001b[0;32m    363\u001b[0m \u001b[38;5;66;03m# We need to detect at runtime whether show() is called by the user.\u001b[39;00m\n\u001b[0;32m    364\u001b[0m \u001b[38;5;66;03m# For this, we wrap it into a decorator which adds a 'called' flag.\u001b[39;00m\n", "File \u001b[1;32m~\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\matplotlib\\pyplot.py:271\u001b[0m, in \u001b[0;36mswitch_backend\u001b[1;34m(newbackend)\u001b[0m\n\u001b[0;32m    268\u001b[0m \u001b[38;5;66;03m# have to escape the switch on access logic\u001b[39;00m\n\u001b[0;32m    269\u001b[0m old_backend \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mdict\u001b[39m\u001b[38;5;241m.\u001b[39m\u001b[38;5;21m__getitem__\u001b[39m(rcParams, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mbackend\u001b[39m\u001b[38;5;124m'\u001b[39m)\n\u001b[1;32m--> 271\u001b[0m backend_mod \u001b[38;5;241m=\u001b[39m \u001b[43mimportlib\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mimport_module\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m    272\u001b[0m \u001b[43m    \u001b[49m\u001b[43mcbook\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_backend_module_name\u001b[49m\u001b[43m(\u001b[49m\u001b[43mnewbackend\u001b[49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    274\u001b[0m required_framework \u001b[38;5;241m=\u001b[39m _get_required_interactive_framework(backend_mod)\n\u001b[0;32m    275\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m required_framework \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n", "File \u001b[1;32m~\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\importlib\\__init__.py:126\u001b[0m, in \u001b[0;36mimport_module\u001b[1;34m(name, package)\u001b[0m\n\u001b[0;32m    124\u001b[0m             \u001b[38;5;28;01<PERSON><PERSON>\u001b[39;00m\n\u001b[0;32m    125\u001b[0m         level \u001b[38;5;241m+\u001b[39m\u001b[38;5;241m=\u001b[39m \u001b[38;5;241m1\u001b[39m\n\u001b[1;32m--> 126\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43m_bootstrap\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_gcd_import\u001b[49m\u001b[43m(\u001b[49m\u001b[43mname\u001b[49m\u001b[43m[\u001b[49m\u001b[43mlevel\u001b[49m\u001b[43m:\u001b[49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mpackage\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mlevel\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32m<frozen importlib._bootstrap>:1206\u001b[0m, in \u001b[0;36m_gcd_import\u001b[1;34m(name, package, level)\u001b[0m\n", "File \u001b[1;32m<frozen importlib._bootstrap>:1178\u001b[0m, in \u001b[0;36m_find_and_load\u001b[1;34m(name, import_)\u001b[0m\n", "File \u001b[1;32m<frozen importlib._bootstrap>:1149\u001b[0m, in \u001b[0;36m_find_and_load_unlocked\u001b[1;34m(name, import_)\u001b[0m\n", "File \u001b[1;32m<frozen importlib._bootstrap>:690\u001b[0m, in \u001b[0;36m_load_unlocked\u001b[1;34m(spec)\u001b[0m\n", "File \u001b[1;32m<frozen importlib._bootstrap_external>:940\u001b[0m, in \u001b[0;36mexec_module\u001b[1;34m(self, module)\u001b[0m\n", "File \u001b[1;32m<frozen importlib._bootstrap>:241\u001b[0m, in \u001b[0;36m_call_with_frames_removed\u001b[1;34m(f, *args, **kwds)\u001b[0m\n", "File \u001b[1;32m~\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\matplotlib\\backends\\backend_qt5agg.py:7\u001b[0m\n\u001b[0;32m      4\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m backends\n\u001b[0;32m      6\u001b[0m backends\u001b[38;5;241m.\u001b[39m_QT_FORCE_QT5_BINDING \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mTrue\u001b[39;00m\n\u001b[1;32m----> 7\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mbackend_qtagg\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m (    \u001b[38;5;66;03m# noqa: F401, E402 # pylint: disable=W0611\u001b[39;00m\n\u001b[0;32m      8\u001b[0m     _BackendQTAgg, FigureCanvasQTAgg, FigureManagerQT, NavigationToolbar2QT,\n\u001b[0;32m      9\u001b[0m     FigureCanvasAgg, FigureCanvasQT)\n\u001b[0;32m     12\u001b[0m \u001b[38;5;129m@_BackendQTAgg\u001b[39m\u001b[38;5;241m.\u001b[39mexport\n\u001b[0;32m     13\u001b[0m \u001b[38;5;28;01mclass\u001b[39;00m \u001b[38;5;21;01m_BackendQT5Agg\u001b[39;00m(_BackendQTAgg):\n\u001b[0;32m     14\u001b[0m     \u001b[38;5;28;01mpass\u001b[39;00m\n", "File \u001b[1;32m~\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\matplotlib\\backends\\backend_qtagg.py:9\u001b[0m\n\u001b[0;32m      5\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;21;01mctypes\u001b[39;00m\n\u001b[0;32m      7\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mmatplotlib\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mtransforms\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m Bbox\n\u001b[1;32m----> 9\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mqt_compat\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m QT_API, _enum\n\u001b[0;32m     10\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mbackend_agg\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m FigureCanvasAgg\n\u001b[0;32m     11\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mbackend_qt\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m QtCore, QtGui, _BackendQT, FigureCanvasQT\n", "File \u001b[1;32m~\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\matplotlib\\backends\\qt_compat.py:135\u001b[0m\n\u001b[0;32m    133\u001b[0m         \u001b[38;5;28;01<PERSON><PERSON>\u001b[39;00m\n\u001b[0;32m    134\u001b[0m     \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m--> 135\u001b[0m         \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mImportError\u001b[39;00m(\n\u001b[0;32m    136\u001b[0m             \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mFailed to import any of the following Qt binding modules: \u001b[39m\u001b[38;5;132;01m{}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m    137\u001b[0m             \u001b[38;5;241m.\u001b[39mformat(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m, \u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;241m.\u001b[39mjoin(_ETS\u001b[38;5;241m.\u001b[39mvalues())))\n\u001b[0;32m    138\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:  \u001b[38;5;66;03m# We should not get there.\u001b[39;00m\n\u001b[0;32m    139\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mAssertionError\u001b[39;00m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mUnexpected QT_API: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mQT_API\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m)\n", "\u001b[1;31mImportError\u001b[0m: Failed to import any of the following Qt binding modules: PyQt6, PySide6, PyQt5, PySide2"]}], "source": ["ma5 = df['MA5'] = df['Close'].rolling(5,min_periods=1).mean()\n", "ma20 = df['MA20'] = df['Close'].rolling(20,min_periods=1).mean()\n", "# 添加移动平均线参数\n", "ap0 = [  \n", "    mpf.make_addplot(ma5,color=\"b\", width=1.5),\n", "    mpf.make_addplot(ma20,color=\"y\", width=1.5),\n", "]\n", "\n", "market_colors = mpf.make_marketcolors(up='red', down='green', )\n", "\n", "\n", "\n", "my_style = mpf.make_mpf_style(marketcolors=market_colors)\n", "# 绘制K线图\n", "mpf.plot(df, type='candle', \n", "         figratio=(10,4),\n", "         mav=(10, 20),\n", "         show_nontrading=True,\n", "         addplot = ap0,\n", "         style=my_style)\n", "\n", "mpf.show()"]}, {"cell_type": "code", "execution_count": null, "id": "a244d97a", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}