import os
import shutil
import xml.etree.ElementTree as ET
import tkinter as tk
from tkinter import filedialog

def scan_folder(folder_path, target_folder):
    # 遍历文件夹中的所有文件
    for root, dirs, files in os.walk(folder_path):
        for file in files:
            if file.endswith('.nfo'):
                file_path = os.path.join(root, file)
                try:
                    # 解析NFO文件
                    tree = ET.parse(file_path)
                    root_element = tree.getroot()
                    
                    # 查找genre元素
                    genre = root_element.find('genre')
                    
                    # 如果没有找到genre元素，移动所在的子文件夹
                    if genre is None:
                        print(f"没有genre的子文件夹: {root}")
                        # 直接移动到目标文件夹
                        target_path = os.path.join(target_folder, os.path.basename(root))
                        try:
                            # 移动文件夹
                            shutil.move(root, target_path)
                        except FileNotFoundError:
                            print(f"找不到文件夹，跳过: {root}")
                        except shutil.Error as e:
                            print(f"移动文件夹时出错，跳过: {root}")
                            print(f"错误信息: {e}")
                except ET.ParseError:
                    print(f"无法解析文件，跳过: {file_path}")
                except FileNotFoundError:
                    print(f"找不到文件，跳过: {file_path}")

def select_folder_and_scan():
    # 创建一个隐藏的主窗口
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口

    # 打开文件夹选择对话框
    source_folder = filedialog.askdirectory(title="选择要扫描的文件夹")
    target_folder = filedialog.askdirectory(title="选择目标文件夹")
    
    if source_folder and target_folder:
        # 移除创建't'文件夹的代码
        scan_folder(source_folder, target_folder)

# 确保调用该函数
if __name__ == "__main__":
    select_folder_and_scan()
