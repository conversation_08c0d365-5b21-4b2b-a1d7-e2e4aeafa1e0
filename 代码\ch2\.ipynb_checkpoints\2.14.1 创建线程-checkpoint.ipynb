{"cells": [{"cell_type": "code", "execution_count": 2, "id": "776946d1", "metadata": {}, "outputs": [], "source": ["import threading\n", "import time\n", "\n", "\n", "# 线程体函数\n", "def thread_body():\n", "    # 当前线程对象\n", "    t = threading.current_thread()\n", "    for n in range(5):\n", "        # 当前线程名\n", "        print('第{0}次执行线程{1}'.format(n, t.name))\n", "        # 线程休眠\n", "        time.sleep(1)\n", "    print('线程{0}执行完成！'.format(t.name))\n", "\n", "\n", "# 创建线程对象t1\n", "t1 = threading.Thread(target=thread_body)\n", "# 启动线程t1\n", "t1.start()\n", "\n", "# 创建线程对象t2\n", "t2 = threading.Thread(target=thread_body, name='MyThread')\n", "# 启动线程t2\n", "t2.start()\n"]}, {"cell_type": "code", "execution_count": null, "id": "90732d44", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}