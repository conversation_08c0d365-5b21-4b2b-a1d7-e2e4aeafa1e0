{"cells": [{"cell_type": "code", "execution_count": null, "id": "ecb0df9e", "metadata": {}, "outputs": [], "source": ["# 通过元素之间用逗号分隔创建元组\n", "T1 = 21, 32, 43, 45\n", "T2 = (21, 32, 43, 45)\n", "\n", "print('T1：', T1) \n", "print('T2：', T2)\n", "\n", "print('T1数据类型是：', type(T1))\n", "\n", "T3 = ['Hello', 'World', 1, 2, 3] \n", "# 通过tuple函数创建元组\n", "T4 = tuple([21, 32, 43, 45]) "]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}