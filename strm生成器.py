import os
import re
import urllib.parse
import tkinter as tk
from tkinter import filedialog
from tkinter import ttk
import configparser
import pypinyin
import time


# 读取上次保存的路径
def load_paths(config_file="config.ini"):
    # 设置默认路径
    default_input_drive = "Q:/"
    default_output_drive = "R:/"
    
    # 直接返回默认路径
    return default_input_drive, default_output_drive


# 保存路径到配置文件
def save_paths(input_path, output_path, config_file="config.ini"):
    config = configparser.ConfigParser()
    config['Paths'] = {
        'input_path': input_path,
        'output_path': output_path
    }

    with open(config_file, 'w') as configfile:
        config.write(configfile)

# 获取视频文件列表
def get_video_files(directory):
    video_files = []
    video_extensions = ('.mp4', '.avi', '.mkv', '.mov', '.ts')

    for root, _, files in os.walk(directory):
        for file in files:
            if file.endswith(video_extensions):
                full_path = os.path.join(root, file)
                video_files.append(full_path)
                print(f"Found video file: {full_path}")
                time.sleep(1)  # 添加1秒延时以避免风控

    return video_files

# 创建 .strm 文件
def create_strm_files(video_files, output_base_folder):
    base_url = "http://192.168.2.41:5244/d"
    total_files = len(video_files)
    print(f"\n开始处理，共发现 {total_files} 个视频文件")

    for index, full_path in enumerate(video_files, 1):
        print(f"\n处理第 {index}/{total_files} 个文件:")
        print(f"源文件路径: {full_path}")
        
        full_path_unix = full_path.replace("\\", "/")
        print(f"转换后的路径: {full_path_unix}")

        path_without_drive = full_path_unix.split(":/", 1)[-1]
        path_without_drive = path_without_drive.replace("CloudDrive", "")
        path_without_drive = path_without_drive.replace("WebDAV", "")
        print(f"处理后的路径: {path_without_drive}")

        folders = path_without_drive.split('/')
        print(f"目录结构: {folders}")

        new_folders = folders[:-1]
        print(f"目标目录结构: {new_folders}")

        if not new_folders:
            print("警告: 无法确定目标目录，跳过此文件")
            continue

        # 修改URL生成逻辑
        relative_path = "/".join(folders)
        encoded_path = urllib.parse.quote(relative_path)
        encoded_path_replaced = encoded_path.replace('%EF%BF%BD', 'GBP')
        # 确保路径以/开头
        if not encoded_path_replaced.startswith('/'):
            encoded_path_replaced = '/' + encoded_path_replaced
        url = base_url + encoded_path_replaced
        print(f"生成的URL: {url}")

        last_folder = new_folders[-1]
        second_last_folder = new_folders[-2] if len(new_folders) > 1 else ""

        if re.match(r'^(s\d{1,2}|season\s?\d{1,2})$', last_folder, re.IGNORECASE):
            parent_folder = os.path.join(output_base_folder, second_last_folder)
            if not os.path.exists(parent_folder):
                os.makedirs(parent_folder)
                print(f"创建父目录: {parent_folder}")

            output_folder = os.path.join(parent_folder, last_folder)
            if not os.path.exists(output_folder):
                os.makedirs(output_folder)
                print(f"创建目标目录: {output_folder}")
        else:
            output_folder = os.path.join(output_base_folder, last_folder)
            if not os.path.exists(output_folder):
                os.makedirs(output_folder)
                print(f"创建目标目录: {output_folder}")

        strm_file_name = os.path.splitext(os.path.basename(full_path_unix))[0] + ".strm"
        strm_file_path = os.path.join(output_folder, strm_file_name)

        with open(strm_file_path, 'w') as f:
            f.write(url)

        print(f"已创建 .strm 文件: {strm_file_path}")

    print(f"\n处理完成！共处理 {total_files} 个文件")

# 自定义文件夹选择窗口
class FolderSelector(tk.Toplevel):
    def __init__(self, master=None, initialdir="", title="选择文件夹"):
        super().__init__(master)
        self.title(title)

        self.current_dir = initialdir or os.getcwd()
        self.folder_list = tk.Listbox(self, selectmode=tk.EXTENDED, width=200, height=40)
        self.folder_list.pack(padx=10, pady=10)

        self.list_folders(self.current_dir)

        self.folder_list.bind("<<ListboxSelect>>", self.update_selected_colors)

        # 选择按钮
        btn_frame = tk.Frame(self)
        btn_frame.pack(pady=5)
        select_button = tk.Button(btn_frame, text="选择", command=self.select_folders)
        select_button.pack(side=tk.LEFT, padx=5)

        cancel_button = tk.Button(btn_frame, text="取消", command=self.cancel)
        cancel_button.pack(side=tk.LEFT, padx=5)

        # 切换目录、返回上一层的按钮
        navigation_frame = tk.Frame(self)
        navigation_frame.pack(pady=5)
        back_button = tk.Button(navigation_frame, text="返回上一级", command=self.go_up)
        back_button.pack(side=tk.LEFT, padx=5)

        switch_button = tk.Button(navigation_frame, text="切换目录", command=self.switch_directory)
        switch_button.pack(side=tk.LEFT, padx=5)

        self.selected_folders = []

    # 列出当前目录下的子文件夹，并按拼音排序
    def list_folders(self, directory):
        self.folder_list.delete(0, tk.END)  # 清空列表
        folders = [f for f in os.listdir(directory) if os.path.isdir(os.path.join(directory, f))]
        folders_sorted = sorted(folders, key=lambda f: pypinyin.lazy_pinyin(f))
        for folder in folders_sorted:
            self.folder_list.insert(tk.END, os.path.join(directory, folder))

    # 更新选中的文件夹的颜色
    def update_selected_colors(self, event=None):
        for idx in range(self.folder_list.size()):
            self.folder_list.itemconfig(idx, bg="white")  # 重置背景颜色
        for idx in self.folder_list.curselection():
            self.folder_list.itemconfig(idx, bg="green")  # 选中的标记为绿色

    # 返回上一层目录
    def go_up(self):
        self.current_dir = os.path.dirname(self.current_dir)
        self.list_folders(self.current_dir)

    # 切换到用户选择的目录
    def switch_directory(self):
        new_directory = filedialog.askdirectory(initialdir=self.current_dir, title="切换目录")
        if new_directory:
            self.current_dir = new_directory
            self.list_folders(self.current_dir)

    # 确定选择的文件夹
    def select_folders(self):
        self.selected_folders = [self.folder_list.get(idx) for idx in self.folder_list.curselection()]
        self.destroy()

    # 取消选择
    def cancel(self):
        self.selected_folders = []
        self.destroy()

# 主界面
class MainApp:
    def __init__(self, root):
        self.root = root
        self.root.title("多选文件夹并标识")

        # 读取上次的路径
        self.input_path, self.output_path = load_paths()

        self.input_label = tk.Label(root, text="输入文件夹: ")
        self.input_label.pack(pady=10)

        self.select_button = tk.Button(root, text="选择输入文件夹", command=self.select_input_folders)
        self.select_button.pack(pady=10)

        self.output_label = tk.Label(root, text="输出文件夹: ")
        self.output_label.pack(pady=10)

        self.output_button = tk.Button(root, text="选择输出文件夹", command=self.select_output_folder)
        self.output_button.pack(pady=10)

        self.start_button = tk.Button(root, text="生成 .strm 文件", command=self.start_process)
        self.start_button.pack(pady=10)

    def select_input_folders(self):
        folder_selected = filedialog.askdirectory(initialdir=self.input_path, title="选择输入文件夹")
        if folder_selected:
            self.input_path = folder_selected
            self.input_label.config(text="输入文件夹: " + self.input_path)
            save_paths(self.input_path, self.output_path)  # 保存路径

    def select_output_folder(self):
        folder_selected = filedialog.askdirectory(initialdir=self.output_path, title="选择输出文件夹")
        if folder_selected:
            self.output_path = folder_selected
            self.output_label.config(text="输出文件夹: " + self.output_path)
            save_paths(self.input_path, self.output_path)  # 保存路径

    def start_process(self):
        if self.input_path and self.output_path:
            video_files = get_video_files(self.input_path)
            create_strm_files(video_files, self.output_path)
            print(f".strm files created in {self.output_path}")
        else:
            print("请先选择输入和输出路径！")


def main():
    root = tk.Tk()
    app = MainApp(root)
    root.mainloop()


if __name__ == "__main__":
    main()
