<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>随机邮箱生成器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 500px;
            text-align: center;
        }

        .title {
            font-size: 2.5rem;
            font-weight: 700;
            color: #333;
            margin-bottom: 10px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .subtitle {
            color: #666;
            margin-bottom: 40px;
            font-size: 1.1rem;
        }

        .input-group {
            margin-bottom: 30px;
            text-align: left;
        }

        .input-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
            font-size: 1rem;
        }

        .input-field {
            width: 100%;
            padding: 15px 20px;
            border: 2px solid #e1e5e9;
            border-radius: 12px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: #fff;
        }

        .input-field:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .generate-btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 40px;
            border-radius: 12px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 30px;
            width: 100%;
        }

        .generate-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        }

        .generate-btn:active {
            transform: translateY(0);
        }

        .result-container {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 20px;
            margin-top: 20px;
            border: 2px dashed #e1e5e9;
            transition: all 0.3s ease;
        }

        .result-container.has-result {
            border-color: #667eea;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.05), rgba(118, 75, 162, 0.05));
        }

        .result-email {
            font-size: 1.3rem;
            font-weight: 600;
            color: #333;
            word-break: break-all;
            margin-bottom: 15px;
        }

        .copy-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            font-size: 0.9rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .copy-btn:hover {
            background: #218838;
            transform: translateY(-1px);
        }

        .placeholder-text {
            color: #999;
            font-style: italic;
        }

        .length-info {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
            border-radius: 8px;
            padding: 12px 16px;
            text-align: center;
        }

        .random-info {
            color: #667eea;
            font-size: 0.9rem;
            font-weight: 500;
        }

        .tab-container {
            display: flex;
            margin-bottom: 30px;
            background: #f8f9fa;
            border-radius: 12px;
            padding: 4px;
        }

        .tab {
            flex: 1;
            padding: 12px 20px;
            text-align: center;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 600;
            color: #666;
        }

        .tab.active {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            box-shadow: 0 2px 10px rgba(102, 126, 234, 0.3);
        }

        .tab:hover:not(.active) {
            color: #667eea;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .verification-container {
            text-align: left;
        }

        .email-list {
            max-height: 300px;
            overflow-y: auto;
            border: 2px solid #e1e5e9;
            border-radius: 12px;
            background: #fff;
        }

        .email-item {
            padding: 15px;
            border-bottom: 1px solid #e1e5e9;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .email-item:hover {
            background: #f8f9fa;
        }

        .email-item:last-child {
            border-bottom: none;
        }

        .email-subject {
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }

        .email-from {
            font-size: 0.9rem;
            color: #666;
            margin-bottom: 5px;
        }

        .email-time {
            font-size: 0.8rem;
            color: #999;
        }

        .verification-code {
            background: linear-gradient(135deg, rgba(40, 167, 69, 0.1), rgba(40, 167, 69, 0.05));
            border: 2px solid #28a745;
            border-radius: 8px;
            padding: 10px 15px;
            font-size: 1.2rem;
            font-weight: 700;
            color: #28a745;
            text-align: center;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
        }

        .refresh-btn {
            background: #17a2b8;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            font-size: 0.9rem;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-left: 10px;
        }

        .refresh-btn:hover {
            background: #138496;
            transform: translateY(-1px);
        }

        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }

        .no-emails {
            text-align: center;
            padding: 40px 20px;
            color: #999;
            font-style: italic;
        }

        @media (max-width: 480px) {
            .container {
                padding: 30px 20px;
            }
            
            .title {
                font-size: 2rem;
            }
            

        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">📧 邮箱工具箱</h1>
        <p class="subtitle">生成随机邮箱地址，获取验证码，保护您的隐私</p>

        <!-- 功能切换标签 -->
        <div class="tab-container">
            <div class="tab active" data-tab="generator">🎲 生成邮箱</div>
            <div class="tab" data-tab="verification">📨 获取验证码</div>
        </div>

        <!-- 邮箱生成器标签页 -->
        <div class="tab-content active" id="generator">
            <div class="input-group">
                <label class="input-label" for="emailSuffix">邮箱后缀</label>
                <input
                    type="text"
                    id="emailSuffix"
                    class="input-field"
                    placeholder="例如: @gmail.com, @outlook.com"
                    value="@mytaki.xyz"
                >
            </div>

            <div class="input-group">
                <label class="input-label">前缀长度（自动随机：7-9位）</label>
                <div class="length-info">
                    <span class="random-info">✨ 每次生成时会自动随机选择7-9位长度</span>
                </div>
            </div>

            <button class="generate-btn" onclick="generateEmail()">
                🎲 生成随机邮箱
            </button>

            <div class="result-container" id="resultContainer">
                <div class="placeholder-text">点击上方按钮生成随机邮箱</div>
            </div>
        </div>

        <!-- 验证码获取标签页 -->
        <div class="tab-content" id="verification">
            <div class="verification-container">
                <div class="input-group">
                    <label class="input-label" for="verificationEmail">临时邮箱地址</label>
                    <input
                        type="text"
                        id="verificationEmail"
                        class="input-field"
                        placeholder="输入临时邮箱地址"
                        value="<EMAIL>"
                    >
                </div>

                <button class="generate-btn" onclick="fetchEmails()">
                    📨 获取邮件
                </button>
                <button class="refresh-btn" onclick="fetchEmails()">
                    🔄 刷新
                </button>

                <div class="result-container" id="emailListContainer">
                    <div class="placeholder-text">点击上方按钮获取邮件列表</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 标签页切换
        document.querySelectorAll('.tab').forEach(tab => {
            tab.addEventListener('click', function() {
                const tabName = this.dataset.tab;

                // 切换标签样式
                document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
                this.classList.add('active');

                // 切换内容
                document.querySelectorAll('.tab-content').forEach(content => {
                    content.classList.remove('active');
                });
                document.getElementById(tabName).classList.add('active');
            });
        });

        // 随机生成7-9位长度
        function getRandomLength() {
            return Math.floor(Math.random() * 3) + 7; // 生成7, 8, 9中的随机数
        }

        // 生成随机字符串
        function generateRandomString(length) {
            const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
            let result = '';
            
            // 确保至少包含一个字母和一个数字
            const letters = 'abcdefghijklmnopqrstuvwxyz';
            const numbers = '0123456789';
            
            // 添加至少一个字母
            result += letters.charAt(Math.floor(Math.random() * letters.length));
            
            // 添加至少一个数字
            result += numbers.charAt(Math.floor(Math.random() * numbers.length));
            
            // 填充剩余位数
            for (let i = 2; i < length; i++) {
                result += chars.charAt(Math.floor(Math.random() * chars.length));
            }
            
            // 打乱字符顺序
            return result.split('').sort(() => Math.random() - 0.5).join('');
        }

        // 生成邮箱
        function generateEmail() {
            const suffixInput = document.getElementById('emailSuffix');
            const resultContainer = document.getElementById('resultContainer');

            let suffix = suffixInput.value.trim();

            // 验证后缀格式
            if (!suffix) {
                alert('请输入邮箱后缀！');
                return;
            }

            if (!suffix.startsWith('@')) {
                suffix = '@' + suffix;
            }

            // 随机生成7-9位长度的前缀
            const randomLength = getRandomLength();
            const prefix = generateRandomString(randomLength);
            const email = prefix + suffix;
            
            // 显示结果
            resultContainer.classList.add('has-result');
            resultContainer.innerHTML = `
                <div class="result-email">${email}</div>
                <div style="color: #666; font-size: 0.9rem; margin-bottom: 10px;">
                    前缀长度: ${randomLength}位
                </div>
                <button class="copy-btn" onclick="copyToClipboard('${email}')">
                    📋 复制邮箱
                </button>
            `;
        }

        // 复制到剪贴板
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(() => {
                const copyBtn = document.querySelector('.copy-btn');
                const originalText = copyBtn.innerHTML;
                copyBtn.innerHTML = '✅ 已复制';
                copyBtn.style.background = '#28a745';
                
                setTimeout(() => {
                    copyBtn.innerHTML = originalText;
                    copyBtn.style.background = '#28a745';
                }, 2000);
            }).catch(() => {
                // 备用复制方法
                const textArea = document.createElement('textarea');
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                
                alert('邮箱已复制到剪贴板！');
            });
        }

        // 获取邮件列表
        async function fetchEmails() {
            const emailInput = document.getElementById('verificationEmail');
            const emailListContainer = document.getElementById('emailListContainer');
            const email = emailInput.value.trim();

            if (!email) {
                alert('请输入邮箱地址！');
                return;
            }

            // 显示加载状态
            emailListContainer.innerHTML = '<div class="loading">📡 正在获取邮件...</div>';

            try {
                // 这里使用模拟数据，实际使用时需要替换为真实的API
                await simulateEmailFetch(email, emailListContainer);
            } catch (error) {
                emailListContainer.innerHTML = `
                    <div class="no-emails">
                        ❌ 获取邮件失败: ${error.message}<br>
                        <small>请检查邮箱地址是否正确</small>
                    </div>
                `;
            }
        }

        // 模拟邮件获取（实际使用时需要替换为真实API调用）
        async function simulateEmailFetch(email, container) {
            // 模拟网络延迟
            await new Promise(resolve => setTimeout(resolve, 1500));

            // 模拟邮件数据
            const mockEmails = [
                {
                    subject: "验证您的账户",
                    from: "<EMAIL>",
                    time: "2分钟前",
                    content: "您的验证码是: 123456",
                    verificationCode: "123456"
                },
                {
                    subject: "登录验证码",
                    from: "<EMAIL>",
                    time: "5分钟前",
                    content: "验证码: 789012，请在10分钟内使用",
                    verificationCode: "789012"
                },
                {
                    subject: "注册确认",
                    from: "<EMAIL>",
                    time: "10分钟前",
                    content: "欢迎注册！验证码: 456789",
                    verificationCode: "456789"
                }
            ];

            if (mockEmails.length === 0) {
                container.innerHTML = '<div class="no-emails">📭 暂无邮件</div>';
                return;
            }

            let emailsHtml = '<div class="email-list">';
            mockEmails.forEach((email, index) => {
                emailsHtml += `
                    <div class="email-item" onclick="showEmailDetail(${index})">
                        <div class="email-subject">${email.subject}</div>
                        <div class="email-from">来自: ${email.from}</div>
                        <div class="email-time">${email.time}</div>
                        ${email.verificationCode ? `<div class="verification-code">${email.verificationCode}</div>` : ''}
                    </div>
                `;
            });
            emailsHtml += '</div>';

            container.innerHTML = emailsHtml;

            // 存储邮件数据供详情查看使用
            window.currentEmails = mockEmails;
        }

        // 显示邮件详情
        function showEmailDetail(index) {
            const email = window.currentEmails[index];
            if (email.verificationCode) {
                copyToClipboard(email.verificationCode);
                alert(`验证码 ${email.verificationCode} 已复制到剪贴板！`);
            }
        }

        // 回车键生成
        document.getElementById('emailSuffix').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                generateEmail();
            }
        });

        document.getElementById('verificationEmail').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                fetchEmails();
            }
        });
    </script>
</body>
</html>
