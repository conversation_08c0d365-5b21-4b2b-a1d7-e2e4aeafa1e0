#!/usr/bin/env python3
# -*- coding: utf-8 -*-

# 测试_grp格式链接的识别
from html_link_extractor_gui import HTMLLinkExtractor

def test_grp_links():
    """测试各种_grp格式链接是否能正确识别"""
    
    extractor = HTMLLinkExtractor()
    
    # 测试各种_grp格式的链接
    test_cases = [
        # 您提到的问题链接
        "7QIRho5TaTna_STi2uqZV0tU=_grp",
        # 其他可能的格式
        "VT6AjWM77I5v_Xl62uqZV0tU=_grp",
        "CUbkYSmly4nY_wky2uqZV0tU=_grp",
        "ABC123=_grp",
        "test_link_123=_grp",
        "simple=_grp",
        # 包含多个下划线的
        "complex_test_link_with_underscores=_grp",
        # 混合测试文本
        "这里有一个链接：7QIRho5TaTna_STi2uqZV0tU=_grp 还有其他内容",
        "多个链接：ABC123=_grp 和 test_link_456=_grp 在同一行"
    ]
    
    print("测试_grp格式链接识别:")
    print("=" * 60)
    
    for i, test_text in enumerate(test_cases, 1):
        print(f"\n测试 {i}: {test_text}")
        
        extracted_links = extractor.extract_links_from_text(test_text)
        
        if extracted_links:
            print(f"✅ 识别到链接: {extracted_links}")
        else:
            print(f"❌ 未识别到链接")
    
    # 特别测试您提到的问题链接
    print(f"\n" + "="*60)
    print("特别测试问题链接:")
    problem_link = "7QIRho5TaTna_STi2uqZV0tU=_grp"
    result = extractor.extract_links_from_text(problem_link)
    
    if result and problem_link in result:
        print(f"✅ 问题链接 '{problem_link}' 现在可以正确识别了！")
    else:
        print(f"❌ 问题链接 '{problem_link}' 仍然无法识别")
        print(f"识别结果: {result}")
    
    # 测试正则表达式模式
    print(f"\n" + "="*60)
    print("正则表达式模式测试:")
    import re
    
    grp_pattern = r'[A-Za-z0-9_]+=_grp'
    compiled_pattern = re.compile(grp_pattern, re.IGNORECASE)
    
    test_link = "7QIRho5TaTna_STi2uqZV0tU=_grp"
    match = compiled_pattern.search(test_link)
    
    if match:
        print(f"✅ 正则表达式匹配成功: {match.group()}")
    else:
        print(f"❌ 正则表达式匹配失败")
    
    print(f"使用的模式: {grp_pattern}")

if __name__ == "__main__":
    test_grp_links()
