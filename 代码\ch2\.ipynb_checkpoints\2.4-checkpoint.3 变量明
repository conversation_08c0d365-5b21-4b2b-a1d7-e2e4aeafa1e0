{"cells": [{"cell_type": "code", "execution_count": 2, "id": "b3171168", "metadata": {}, "outputs": [], "source": ["_hello = \"HelloWorld\"\n", "score_for_student = 0.0\n", "y = 20\n", "y = True"]}, {"cell_type": "code", "execution_count": null, "id": "14de6e72", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}