{"cells": [{"cell_type": "code", "execution_count": 1, "id": "2a7134d0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["数组的维度数： 2\n", "数组的形状： (2, 3)\n", "数组中元素的总数： 6\n", "数组中元素的数据类型： int32\n", "数组中每个元素的字节大小： 4\n", "数组占用的总字节数： 24\n"]}], "source": ["import numpy as np\n", "\n", "arr = np.array([[1, 2, 3], [4, 5, 6]])\n", "\n", "print(\"数组的维度数：\", arr.ndim)\n", "print(\"数组的形状：\", arr.shape)\n", "print(\"数组中元素的总数：\", arr.size)\n", "print(\"数组中元素的数据类型：\", arr.dtype)\n", "print(\"数组中每个元素的字节大小：\", arr.itemsize)\n", "print(\"数组占用的总字节数：\", arr.nbytes)\n"]}, {"cell_type": "code", "execution_count": null, "id": "0ff4d0e2", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}