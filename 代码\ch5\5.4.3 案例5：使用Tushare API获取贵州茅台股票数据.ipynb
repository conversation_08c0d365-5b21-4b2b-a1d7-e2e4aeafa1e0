{"cells": [{"cell_type": "code", "execution_count": 3, "id": "cad403af", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ts_code</th>\n", "      <th>trade_date</th>\n", "      <th>open</th>\n", "      <th>high</th>\n", "      <th>low</th>\n", "      <th>close</th>\n", "      <th>pre_close</th>\n", "      <th>change</th>\n", "      <th>pct_chg</th>\n", "      <th>vol</th>\n", "      <th>amount</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>600519.SH</td>\n", "      <td>20241105</td>\n", "      <td>1540.00</td>\n", "      <td>1577.70</td>\n", "      <td>1536.01</td>\n", "      <td>1576.99</td>\n", "      <td>1548.20</td>\n", "      <td>28.79</td>\n", "      <td>1.8596</td>\n", "      <td>45640.47</td>\n", "      <td>7.134443e+06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>600519.SH</td>\n", "      <td>20241104</td>\n", "      <td>1544.93</td>\n", "      <td>1550.00</td>\n", "      <td>1528.06</td>\n", "      <td>1548.20</td>\n", "      <td>1533.75</td>\n", "      <td>14.45</td>\n", "      <td>0.9421</td>\n", "      <td>29092.92</td>\n", "      <td>4.481762e+06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>600519.SH</td>\n", "      <td>20241101</td>\n", "      <td>1521.00</td>\n", "      <td>1545.13</td>\n", "      <td>1520.80</td>\n", "      <td>1533.75</td>\n", "      <td>1527.79</td>\n", "      <td>5.96</td>\n", "      <td>0.3901</td>\n", "      <td>32283.92</td>\n", "      <td>4.954302e+06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>600519.SH</td>\n", "      <td>20241031</td>\n", "      <td>1532.15</td>\n", "      <td>1540.00</td>\n", "      <td>1520.00</td>\n", "      <td>1527.79</td>\n", "      <td>1532.00</td>\n", "      <td>-4.21</td>\n", "      <td>-0.2748</td>\n", "      <td>29957.35</td>\n", "      <td>4.575545e+06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>600519.SH</td>\n", "      <td>20241030</td>\n", "      <td>1525.00</td>\n", "      <td>1551.19</td>\n", "      <td>1523.41</td>\n", "      <td>1532.00</td>\n", "      <td>1531.62</td>\n", "      <td>0.38</td>\n", "      <td>0.0248</td>\n", "      <td>32005.74</td>\n", "      <td>4.913176e+06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>600519.SH</td>\n", "      <td>20241029</td>\n", "      <td>1560.00</td>\n", "      <td>1580.00</td>\n", "      <td>1530.00</td>\n", "      <td>1531.62</td>\n", "      <td>1565.00</td>\n", "      <td>-33.38</td>\n", "      <td>-2.1329</td>\n", "      <td>41165.78</td>\n", "      <td>6.368105e+06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>600519.SH</td>\n", "      <td>20241028</td>\n", "      <td>1560.00</td>\n", "      <td>1565.00</td>\n", "      <td>1522.23</td>\n", "      <td>1565.00</td>\n", "      <td>1558.85</td>\n", "      <td>6.15</td>\n", "      <td>0.3945</td>\n", "      <td>39121.40</td>\n", "      <td>6.064454e+06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>600519.SH</td>\n", "      <td>20241025</td>\n", "      <td>1547.00</td>\n", "      <td>1574.79</td>\n", "      <td>1546.01</td>\n", "      <td>1558.85</td>\n", "      <td>1552.20</td>\n", "      <td>6.65</td>\n", "      <td>0.4284</td>\n", "      <td>27745.08</td>\n", "      <td>4.328271e+06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>600519.SH</td>\n", "      <td>20241024</td>\n", "      <td>1561.00</td>\n", "      <td>1575.00</td>\n", "      <td>1545.01</td>\n", "      <td>1552.20</td>\n", "      <td>1567.50</td>\n", "      <td>-15.30</td>\n", "      <td>-0.9761</td>\n", "      <td>23922.80</td>\n", "      <td>3.723973e+06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>600519.SH</td>\n", "      <td>20241023</td>\n", "      <td>1544.11</td>\n", "      <td>1576.95</td>\n", "      <td>1532.00</td>\n", "      <td>1567.50</td>\n", "      <td>1543.00</td>\n", "      <td>24.50</td>\n", "      <td>1.5878</td>\n", "      <td>40088.46</td>\n", "      <td>6.259600e+06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>600519.SH</td>\n", "      <td>20241022</td>\n", "      <td>1526.56</td>\n", "      <td>1560.18</td>\n", "      <td>1524.20</td>\n", "      <td>1543.00</td>\n", "      <td>1528.80</td>\n", "      <td>14.20</td>\n", "      <td>0.9288</td>\n", "      <td>36269.13</td>\n", "      <td>5.607826e+06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>600519.SH</td>\n", "      <td>20241021</td>\n", "      <td>1541.21</td>\n", "      <td>1549.97</td>\n", "      <td>1521.21</td>\n", "      <td>1528.80</td>\n", "      <td>1541.00</td>\n", "      <td>-12.20</td>\n", "      <td>-0.7917</td>\n", "      <td>42007.90</td>\n", "      <td>6.433918e+06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>600519.SH</td>\n", "      <td>20241018</td>\n", "      <td>1493.00</td>\n", "      <td>1567.84</td>\n", "      <td>1478.96</td>\n", "      <td>1541.00</td>\n", "      <td>1494.60</td>\n", "      <td>46.40</td>\n", "      <td>3.1045</td>\n", "      <td>68619.30</td>\n", "      <td>1.041804e+07</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>600519.SH</td>\n", "      <td>20241017</td>\n", "      <td>1537.00</td>\n", "      <td>1537.66</td>\n", "      <td>1493.41</td>\n", "      <td>1494.60</td>\n", "      <td>1522.50</td>\n", "      <td>-27.90</td>\n", "      <td>-1.8325</td>\n", "      <td>39459.55</td>\n", "      <td>5.964779e+06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>600519.SH</td>\n", "      <td>20241016</td>\n", "      <td>1540.88</td>\n", "      <td>1549.80</td>\n", "      <td>1510.00</td>\n", "      <td>1522.50</td>\n", "      <td>1554.25</td>\n", "      <td>-31.75</td>\n", "      <td>-2.0428</td>\n", "      <td>46664.40</td>\n", "      <td>7.140170e+06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>600519.SH</td>\n", "      <td>20241015</td>\n", "      <td>1599.00</td>\n", "      <td>1600.99</td>\n", "      <td>1553.01</td>\n", "      <td>1554.25</td>\n", "      <td>1601.99</td>\n", "      <td>-47.74</td>\n", "      <td>-2.9800</td>\n", "      <td>43571.43</td>\n", "      <td>6.884142e+06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>600519.SH</td>\n", "      <td>20241014</td>\n", "      <td>1613.00</td>\n", "      <td>1620.63</td>\n", "      <td>1581.17</td>\n", "      <td>1601.99</td>\n", "      <td>1604.99</td>\n", "      <td>-3.00</td>\n", "      <td>-0.1869</td>\n", "      <td>44812.02</td>\n", "      <td>7.181351e+06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>600519.SH</td>\n", "      <td>20241011</td>\n", "      <td>1615.40</td>\n", "      <td>1639.96</td>\n", "      <td>1590.00</td>\n", "      <td>1604.99</td>\n", "      <td>1640.00</td>\n", "      <td>-35.01</td>\n", "      <td>-2.1348</td>\n", "      <td>41914.71</td>\n", "      <td>6.764983e+06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>600519.SH</td>\n", "      <td>20241010</td>\n", "      <td>1622.26</td>\n", "      <td>1668.08</td>\n", "      <td>1591.00</td>\n", "      <td>1640.00</td>\n", "      <td>1595.15</td>\n", "      <td>44.85</td>\n", "      <td>2.8116</td>\n", "      <td>74603.42</td>\n", "      <td>1.219896e+07</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>600519.SH</td>\n", "      <td>20241009</td>\n", "      <td>1700.00</td>\n", "      <td>1700.00</td>\n", "      <td>1590.00</td>\n", "      <td>1595.15</td>\n", "      <td>1723.00</td>\n", "      <td>-127.85</td>\n", "      <td>-7.4202</td>\n", "      <td>120196.84</td>\n", "      <td>1.970843e+07</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>600519.SH</td>\n", "      <td>20241008</td>\n", "      <td>1910.00</td>\n", "      <td>1910.00</td>\n", "      <td>1680.18</td>\n", "      <td>1723.00</td>\n", "      <td>1748.00</td>\n", "      <td>-25.00</td>\n", "      <td>-1.4302</td>\n", "      <td>194709.47</td>\n", "      <td>3.511408e+07</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      ts_code trade_date     open     high      low    close  pre_close  \\\n", "0   600519.SH   20241105  1540.00  1577.70  1536.01  1576.99    1548.20   \n", "1   600519.SH   20241104  1544.93  1550.00  1528.06  1548.20    1533.75   \n", "2   600519.SH   20241101  1521.00  1545.13  1520.80  1533.75    1527.79   \n", "3   600519.SH   20241031  1532.15  1540.00  1520.00  1527.79    1532.00   \n", "4   600519.SH   20241030  1525.00  1551.19  1523.41  1532.00    1531.62   \n", "5   600519.SH   20241029  1560.00  1580.00  1530.00  1531.62    1565.00   \n", "6   600519.SH   20241028  1560.00  1565.00  1522.23  1565.00    1558.85   \n", "7   600519.SH   20241025  1547.00  1574.79  1546.01  1558.85    1552.20   \n", "8   600519.SH   20241024  1561.00  1575.00  1545.01  1552.20    1567.50   \n", "9   600519.SH   20241023  1544.11  1576.95  1532.00  1567.50    1543.00   \n", "10  600519.SH   20241022  1526.56  1560.18  1524.20  1543.00    1528.80   \n", "11  600519.SH   20241021  1541.21  1549.97  1521.21  1528.80    1541.00   \n", "12  600519.SH   20241018  1493.00  1567.84  1478.96  1541.00    1494.60   \n", "13  600519.SH   20241017  1537.00  1537.66  1493.41  1494.60    1522.50   \n", "14  600519.SH   20241016  1540.88  1549.80  1510.00  1522.50    1554.25   \n", "15  600519.SH   20241015  1599.00  1600.99  1553.01  1554.25    1601.99   \n", "16  600519.SH   20241014  1613.00  1620.63  1581.17  1601.99    1604.99   \n", "17  600519.SH   20241011  1615.40  1639.96  1590.00  1604.99    1640.00   \n", "18  600519.SH   20241010  1622.26  1668.08  1591.00  1640.00    1595.15   \n", "19  600519.SH   20241009  1700.00  1700.00  1590.00  1595.15    1723.00   \n", "20  600519.SH   20241008  1910.00  1910.00  1680.18  1723.00    1748.00   \n", "\n", "    change  pct_chg        vol        amount  \n", "0    28.79   1.8596   45640.47  7.134443e+06  \n", "1    14.45   0.9421   29092.92  4.481762e+06  \n", "2     5.96   0.3901   32283.92  4.954302e+06  \n", "3    -4.21  -0.2748   29957.35  4.575545e+06  \n", "4     0.38   0.0248   32005.74  4.913176e+06  \n", "5   -33.38  -2.1329   41165.78  6.368105e+06  \n", "6     6.15   0.3945   39121.40  6.064454e+06  \n", "7     6.65   0.4284   27745.08  4.328271e+06  \n", "8   -15.30  -0.9761   23922.80  3.723973e+06  \n", "9    24.50   1.5878   40088.46  6.259600e+06  \n", "10   14.20   0.9288   36269.13  5.607826e+06  \n", "11  -12.20  -0.7917   42007.90  6.433918e+06  \n", "12   46.40   3.1045   68619.30  1.041804e+07  \n", "13  -27.90  -1.8325   39459.55  5.964779e+06  \n", "14  -31.75  -2.0428   46664.40  7.140170e+06  \n", "15  -47.74  -2.9800   43571.43  6.884142e+06  \n", "16   -3.00  -0.1869   44812.02  7.181351e+06  \n", "17  -35.01  -2.1348   41914.71  6.764983e+06  \n", "18   44.85   2.8116   74603.42  1.219896e+07  \n", "19 -127.85  -7.4202  120196.84  1.970843e+07  \n", "20  -25.00  -1.4302  194709.47  3.511408e+07  "]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["# 导入tushare\n", "import tushare as ts\n", "\n", "# 初始化pro接口\n", "pro = ts.pro_api('d86e533e66dc79830ba10a52a021103be88fa767f4a779f33f4c572b')\n", "df = pro.daily(ts_code='600519.SH', start_date='20241008', end_date='20241105')\n", "df"]}, {"cell_type": "code", "execution_count": null, "id": "10d4cb72-2bab-4b1d-9b8a-836fe0e0e176", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.6"}}, "nbformat": 4, "nbformat_minor": 5}