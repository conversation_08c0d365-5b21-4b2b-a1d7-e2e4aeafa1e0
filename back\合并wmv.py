import os
import tkinter as tk
from tkinter import filedialog
from tkinter import messagebox
from multiprocessing import Process
import subprocess

import cv2


def select_file(entry_widget):
    file_path = filedialog.askopenfilename(filetypes=[("WMV files", "*.wmv")])
    entry_widget.delete(0, tk.END)
    entry_widget.insert(0, file_path)
    check_codec(file_path)


def select_output_path():
    output_path = filedialog.asksaveasfilename(initialdir=os.path.dirname(entry.get()), defaultextension=".wmv",
                                               filetypes=[("WMV files", "*.wmv")])
    if not output_path:  # 如果用户点击取消，则不进行后续操作
        return
    if not output_path.endswith(".wmv"):  # 如果输出路径没有指定后缀名，则手动添加 ".wmv" 后缀
        output_path += ".wmv"
    entry_output.delete(0, tk.END)
    entry_output.insert(0, output_path)


def get_codec_opencv(file_path):
    try:
        cap = cv2.VideoCapture(file_path)
        codec = int(cap.get(cv2.CAP_PROP_FOURCC))
        codec_str = chr(codec & 0xFF) + chr((codec & 0xFF00) >> 8) + chr((codec & 0xFF0000) >> 16) + chr(
            (codec & 0xFF000000) >> 24)
        cap.release()
        return codec_str
    except Exception as e:
        return None


def check_codec(file_path):
    codec = get_codec_opencv(file_path)
    if codec:
        label_codec.config(text=f"Codec: {codec}")
    else:
        label_codec.config(text="Codec: Unknown")


def merge_wmv_videos():
    video1_path = entry.get()
    video2_path = entry2.get()
    output_path = entry_output.get()

    if not video1_path or not video2_path:
        messagebox.showerror("Error", "Please select input files.")
        return

    if not output_path:  # 如果输出路径为空，则使用输入路径并添加 .wmv 后缀
        output_path = os.path.join(os.path.dirname(video1_path), "output_merged.wmv")

    try:
        merge_wmv_videos_ffmpeg(video1_path, video2_path, output_path)
        messagebox.showinfo("Success", "Videos merged successfully.")
    except Exception as e:
        messagebox.showerror("Error", f"An error occurred: {str(e)}")


def merge_wmv_videos_ffmpeg(video1_path, video2_path, output_path):
    # 使用 FFmpeg 命令行工具合并视频并指定码率
    cmd = [
        "ffmpeg",
        "-i", video1_path,
        "-i", video2_path,
        "-filter_complex", "[0:v:0][删除扩展名中的汉字:v:0]concat=n=2:v=删除扩展名中的汉字:a=0[outv]",
        "-map", "[outv]",
        "-c:v", "libx264",  # 使用 H.264 编码器
        "-crf", "23",  # 设置视频质量
        output_path
    ]
    subprocess.run(cmd, check=True)


# 创建主窗口
root = tk.Tk()
root.title("WMV Video Merger")

# 创建选择输入文件的按钮和输入框
tk.Label(root, text="Select Video 删除扩展名中的汉字:").grid(row=0, column=0, padx=5, pady=5)
entry = tk.Entry(root, width=50)
entry.grid(row=0, column=1, padx=5, pady=5)
tk.Button(root, text="Browse", command=lambda: select_file(entry)).grid(row=0, column=2, padx=5, pady=5)

tk.Label(root, text="Select Video 2:").grid(row=1, column=0, padx=5, pady=5)
entry2 = tk.Entry(root, width=50)
entry2.grid(row=1, column=1, padx=5, pady=5)
tk.Button(root, text="Browse", command=lambda: select_file(entry2)).grid(row=1, column=2, padx=5, pady=5)

# 创建选择输出文件路径的按钮和输入框
tk.Label(root, text="Output Path:").grid(row=2, column=0, padx=5, pady=5)
entry_output = tk.Entry(root, width=50)
entry_output.grid(row=2, column=1, padx=5, pady=5)
tk.Button(root, text="Browse", command=select_output_path).grid(row=2, column=2, padx=5, pady=5)

# 创建显示编码器信息的标签
label_codec = tk.Label(root, text="Codec: ")
label_codec.grid(row=3, column=1, padx=5, pady=5)

# 创建合并按钮
tk.Button(root, text="Merge Videos", command=merge_wmv_videos).grid(row=4, column=1, padx=5, pady=5)

root.mainloop()
