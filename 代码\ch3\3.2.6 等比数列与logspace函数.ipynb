{"cells": [{"cell_type": "code", "execution_count": 1, "id": "e688a0ff", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[1.e+00 1.e+01 1.e+02 1.e+03 1.e+04 1.e+05 1.e+06 1.e+07 1.e+08 1.e+09]\n", "[1.e+00 1.e+01 1.e+02 1.e+03 1.e+04 1.e+05 1.e+06 1.e+07 1.e+08 1.e+09]\n", "[  1.   2.   4.   8.  16.  32.  64. 128. 256. 512.]\n"]}], "source": ["import numpy as np\n", "a = np.logspace(0, 9, 10)\n", "print(a)\n", "b = np.logspace(0, 10, 10, endpoint=False) \n", "print(b)\n", "c = np.logspace(0, 9, 10, base=2)\n", "print(c)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}