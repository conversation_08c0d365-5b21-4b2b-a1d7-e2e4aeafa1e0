{"cells": [{"cell_type": "code", "execution_count": 1, "id": "623b06cb", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["解析完成。 [{'Date': '10/04/2022', 'Open': '225.64', 'High': '227.49', 'Low': '223.89', 'Close': '227.01', 'Volume': '34,755,550'}, {'Date': '10/03/2022', 'Open': '218.43', 'High': '220.96', 'Low': '215.132', 'Close': '220.82', 'Volume': '30,352,690'}, {'Date': '10/02/2022', 'Open': '223.06', 'High': '223.58', 'Low': '217.93', 'Close': '218.96', 'Volume': '35,767,260'}, {'Date': '10/01/2022', 'Open': '225.07', 'High': '228.22', 'Low': '224.2', 'Close': '224.59', 'Volume': '36,187,160'}, {'Date': '09/30/2022', 'Open': '220.9', 'High': '224.58', 'Low': '220.79', 'Close': '223.97', 'Volume': '26,318,580'}, {'Date': '09/27/2022', 'Open': '220.54', 'High': '220.96', 'Low': '217.2814', 'Close': '218.82', 'Volume': '25,361,290'}, {'Date': '09/26/2022', 'Open': '220', 'High': '220.94', 'Low': '218.83', 'Close': '219.89', 'Volume': '19,088,310'}, {'Date': '09/25/2022', 'Open': '218.55', 'High': '221.5', 'Low': '217.1402', 'Close': '221.03', 'Volume': '22,481,010'}, {'Date': '09/24/2022', 'Open': '221.03', 'High': '222.49', 'Low': '217.19', 'Close': '217.68', 'Volume': '31,434,370'}, {'Date': '09/23/2022', 'Open': '218.95', 'High': '219.84', 'Low': '217.65', 'Close': '218.72', 'Volume': '19,419,650'}, {'Date': '09/20/2022', 'Open': '221.38', 'High': '222.56', 'Low': '217.473', 'Close': '217.73', 'Volume': '57,977,090'}, {'Date': '09/19/2022', 'Open': '222.01', 'High': '223.76', 'Low': '220.37', 'Close': '220.96', 'Volume': '22,187,880'}, {'Date': '09/18/2022', 'Open': '221.06', 'High': '222.85', 'Low': '219.44', 'Close': '222.77', 'Volume': '25,643,090'}, {'Date': '09/17/2022', 'Open': '219.96', 'High': '220.82', 'Low': '219.12', 'Close': '220.7', 'Volume': '18,386,470'}, {'Date': '09/16/2022', 'Open': '217.73', 'High': '220.13', 'Low': '217.56', 'Close': '219.9', 'Volume': '21,158,140'}, {'Date': '09/13/2022', 'Open': '220', 'High': '220.79', 'Low': '217.02', 'Close': '218.75', 'Volume': '39,763,300'}, {'Date': '09/12/2022', 'Open': '224.8', 'High': '226.42', 'Low': '222.86', 'Close': '223.085', 'Volume': '32,226,670'}, {'Date': '09/11/2022', 'Open': '218.07', 'High': '223.71', 'Low': '217.73', 'Close': '223.59', 'Volume': '44,289,650'}]\n"]}], "source": ["import urllib.request\n", "from bs4 import BeautifulSoup\n", "\n", "# url = 'https://www.nasdaq.com/symbol/aapl/historical#.UWdnJBDMhHk'\n", "# 换成自己到路径\n", "url = \"file:///C:/Users/<USER>/OneDrive/书/北大/AI时代Python量化交易实战：ChatGPT让量化交易插上翅膀/代码/ch5/data/nasdaq-Apple1.html\"\n", "\n", "req = urllib.request.Request(url)\n", "\n", "with urllib.request.urlopen(req) as response:\n", "    data = response.read()\n", "    html_data = data.decode()\n", "    \n", "    sp = BeautifulSoup(html_data, 'html.parser')\n", "\n", "    # 返回<tbody>标签元素\n", "    tbody = sp.find('tbody')\n", "    # 返回<tbody>标签下所有的<tr>元素\n", "    trlist = tbody.select('tr')\n", "\n", "    data = []\n", "\n", "    for tr in trlist:\n", "        fields = {}\n", "        # 获得交易日期<th>元素\n", "        th = tr.find('th')\n", "        fields['Date'] = th.text  # 日期\n", "        # 获得tr下的所有<td>元素\n", "        tds = tr.select('td')\n", "        fields['Open'] = tds[0].text  # 开盘\n", "        fields['High'] = tds[1].text  # 最高\n", "        fields['Low'] = tds[2].text  # 最低\n", "        fields['Close'] = tds[3].text  # 收盘\n", "        fields['Volume'] = tds[4].text  # 成交量\n", "\n", "        data.append(fields)\n", "\n", "print(\"解析完成。\", data)\n"]}, {"cell_type": "code", "execution_count": 3, "id": "64291835", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["CSV文件已生成：data.csv\n"]}], "source": ["import csv\n", "keys = data[0].keys()\n", "\n", "f = 'data/纳斯达克股票数据.csv'\n", "# 将数据写入CSV文件\n", "with open(f, 'w', newline='') as csvfile:\n", "    writer = csv.DictWriter(csvfile, fieldnames=keys)\n", "\n", "    # 写入表头\n", "    writer.writeheader()\n", "\n", "    # 写入数据\n", "    writer.writerows(data)\n", "\n", "print(\"CSV文件已生成：data.csv\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "28302bc3", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}