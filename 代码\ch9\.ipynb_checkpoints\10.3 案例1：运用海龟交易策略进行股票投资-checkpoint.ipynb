{"cells": [{"cell_type": "code", "execution_count": null, "id": "ef7c1cc5", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "\n", "# 读取股票历史交易数据\n", "df = pd.read_csv('stock_data.csv')\n", "\n", "# 设置移动平均线窗口期\n", "ma_short_window = 20\n", "ma_long_window = 50\n", "\n", "# 计算移动平均线\n", "df['MA_Short'] = df['Close'].rolling(window=ma_short_window).mean()\n", "df['MA_Long'] = df['Close'].rolling(window=ma_long_window).mean()\n", "\n", "# 初始资金和交易单位\n", "initial_capital = 1000000\n", "unit_size = 100\n", "\n", "# 定义海龟交易策略规则\n", "def turtle_trading_strategy(df):\n", "    # 确定买入和卖出信号\n", "    df['Buy_Signal'] = (df['Close'] > df['MA_Long']) & (df['Close'].shift(1) < df['MA_Long'].shift(1))\n", "    df['Sell_Signal'] = (df['Close'] < df['MA_Long']) & (df['Close'].shift(1) > df['MA_Long'].shift(1))\n", "    \n", "    # 计算持有头寸和资金曲线\n", "    df['Position'] = 0\n", "    df.loc[df['Buy_Signal'], 'Position'] = unit_size\n", "    df.loc[df['Sell_Signal'], 'Position'] = -unit_size\n", "    df['Portfolio_Value'] = df['Position'] * df['Close'].shift(-1)\n", "    df['Portfolio_Value'].fillna(0, inplace=True)\n", "    \n", "    # 计算每日盈亏和累计盈亏\n", "    df['Daily_Return'] = df['Portfolio_Value'].pct_change()\n", "    df['Cumulative_Return'] = (df['Daily_Return'] + 1).cumprod()\n", "    \n", "    # 计算累计收益率\n", "    cumulative_returns = df['Cumulative_Return'].values\n", "    total_trades = df[df['Position'] != 0]['Position'].count()\n", "    total_profit = cumulative_returns[-1] * initial_capital - initial_capital\n", "    average_return = total_profit / total_trades\n", "    \n", "    return df, cumulative_returns, total_profit, average_return\n", "\n", "# 实施海龟交易策略\n", "df, cumulative_returns, total_profit, average_return = turtle_trading_strategy(df)\n", "\n", "# 打印结果\n", "print(f\"总交易次数：{df[df['Position'] != 0]['Position'].count()}\")\n", "print(f\"总盈利：{total_profit:.2f}元\")\n", "print(f\"平均收益：{average_return:.2f}元/交易\")\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}