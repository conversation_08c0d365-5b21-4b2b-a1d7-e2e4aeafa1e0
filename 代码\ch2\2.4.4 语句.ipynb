{"cells": [{"cell_type": "code", "execution_count": 2, "id": "3a40110d", "metadata": {}, "outputs": [], "source": ["_hello = \"HelloWorld\"\n", "score_for_student = 0.0;  # 没有错误发生\n", "y = 20\n", "\n", "name1 = \"张三\";name2 = \"李四\"\n", "# 链式赋值语句\n", "a = b = c = 10"]}, {"cell_type": "code", "execution_count": null, "id": "e61399d5", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}