from http.server import BaseHTTPRequestHandler, HTTPServer
import urllib.parse
import os

# HTTPRequestHandler class
class HTTPRequestHandler(BaseHTTPRequestHandler):

    # GET
    def do_GET(self):
        # 解析请求路径，提取标题
        parsed_path = urllib.parse.urlparse(self.path)
        folder_name = os.path.basename(parsed_path.path)
        # 去掉前导空格并解码
        decoded_folder_name = urllib.parse.unquote(folder_name)#.lstrip()

        # 创建文件夹
        try:
            os.makedirs("d:/BaiduYunDownload/" + decoded_folder_name)
            self.send_response(200)
            self.end_headers()
            self.wfile.write(bytes("Folder created successfully: " + decoded_folder_name, "utf-8"))
        except Exception as e:
            self.send_response(500)
            self.end_headers()
            self.wfile.write(bytes("Failed to create folder: " + str(e), "utf-8"))

    # POST
    def do_POST(self):
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')  # 允许所有域的请求
        self.end_headers()

        print("Received POST request...")
        # 使用GET请求中解析得到的文件夹名
        folder_name = self.path.split('/')[-2]
        # 去掉前导空格并解码
        decoded_folder_name = urllib.parse.unquote(folder_name)#.lstrip()
        print(decoded_folder_name)

        # 读取请求体中的密码文本
        content_length = int(self.headers['Content-Length'])
        password_text = self.rfile.read(content_length).decode('utf-8')
        if password_text:
            print("Password received:", password_text)
        else:
            print("No password received")

        # 确保文件夹路径正确
        folder_path = "d:/BaiduYunDownload/" + decoded_folder_name
        print("Folder path:", folder_path)

        # 确保文件夹存在，如果不存在则创建
        if not os.path.exists(folder_path):
            os.makedirs(folder_path)
            print("Folder created successfully")

        # 确保密码文本不为空
        if password_text:
            print("Password received:", password_text)
        else:
            print("No password received")

        # 将密码保存到文件中
        try:
            # 确保子文件夹路径正确
            #folder_path_with_subfolder = os.path.join(folder_path, decoded_folder_name)
            if not os.path.exists(folder_path):
                os.makedirs(folder_path)

            with open(os.path.join(folder_path, "password.txt"), 'w') as f:
                f.write(password_text)
            print("Password saved to file successfully")
        except Exception as e:
            print("Failed to save password to file:", e)


def run(server_class=HTTPServer, handler_class=HTTPRequestHandler, port=8000):
    server_address = ('', port)
    httpd = server_class(server_address, handler_class)
    print('Starting server...')
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        pass
    httpd.server_close()
    print('Stopping server...')

if __name__ == "__main__":
    run()
