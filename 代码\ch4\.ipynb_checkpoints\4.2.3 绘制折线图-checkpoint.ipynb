{"cells": [{"cell_type": "code", "execution_count": 2, "id": "e174ba66", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_11924\\1907584761.py:17: UserWarning: Glyph 36724 (\\N{CJK UNIFIED IDEOGRAPH-8F74}) missing from current font.\n", "  plt.savefig('折线图', dpi=72)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_11924\\1907584761.py:17: UserWarning: Glyph 32472 (\\N{CJK UNIFIED IDEOGRAPH-7ED8}) missing from current font.\n", "  plt.savefig('折线图', dpi=72)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_11924\\1907584761.py:17: UserWarning: Glyph 21046 (\\N{CJK UNIFIED IDEOGRAPH-5236}) missing from current font.\n", "  plt.savefig('折线图', dpi=72)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_11924\\1907584761.py:17: UserWarning: Glyph 25240 (\\N{CJK UNIFIED IDEOGRAPH-6298}) missing from current font.\n", "  plt.savefig('折线图', dpi=72)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_11924\\1907584761.py:17: UserWarning: Glyph 32447 (\\N{CJK UNIFIED IDEOGRAPH-7EBF}) missing from current font.\n", "  plt.savefig('折线图', dpi=72)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_11924\\1907584761.py:17: UserWarning: Glyph 22270 (\\N{CJK UNIFIED IDEOGRAPH-56FE}) missing from current font.\n", "  plt.savefig('折线图', dpi=72)\n"]}, {"data": {"image/png": "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**************************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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "\n", "plt.rcParams['font.family'] = ['SimHei'] # 设置中文字体\n", "plt.rcParams['axes.unicode_minus'] = False # 设置负号显示\n", "\n", "x = [-5, -4, 2, 1]  # x轴坐标数据 \n", "y = [7, 8, 9, 10]  # y轴坐标数据 \n", "# 绘制线段\n", "plt.plot(x, y, 'b', label='线1', linewidth=2)\n", "\n", "plt.title('绘制折线图')  # 添加图表标题 \n", "\n", "plt.ylabel('y轴')  # 添加y轴标题\n", "plt.xlabel('x轴')  # 添加x轴标题\n", "\n", "plt.legend()  # 设置图例\n", "# 以分辨率 72 来保存图片\n", "plt.savefig('折线图', dpi=72) \n", "\n", "plt.show()  # 显示图形 "]}, {"cell_type": "code", "execution_count": null, "id": "5fadb596", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}