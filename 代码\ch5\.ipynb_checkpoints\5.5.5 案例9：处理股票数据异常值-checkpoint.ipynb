{"cells": [{"cell_type": "code", "execution_count": 4, "id": "097b6729", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["存在异常值：成交量为负数\n", "           日期   开盘价  收盘价   最低价   最高价       成交量\n", "2  2022-01-03  54.5  NaN  53.8  56.0 -500000.0\n", "处理后的数据：\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>日期</th>\n", "      <th>开盘价</th>\n", "      <th>收盘价</th>\n", "      <th>最低价</th>\n", "      <th>最高价</th>\n", "      <th>成交量</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2022-01-01</td>\n", "      <td>50.2</td>\n", "      <td>51.5</td>\n", "      <td>49.8</td>\n", "      <td>52.1</td>\n", "      <td>1000000.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2022-01-02</td>\n", "      <td>52.0</td>\n", "      <td>53.2</td>\n", "      <td>51.5</td>\n", "      <td>54.0</td>\n", "      <td>1200000.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2022-01-03</td>\n", "      <td>54.5</td>\n", "      <td>NaN</td>\n", "      <td>53.8</td>\n", "      <td>56.0</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2022-01-04</td>\n", "      <td>55.2</td>\n", "      <td>53.8</td>\n", "      <td>52.5</td>\n", "      <td>55.5</td>\n", "      <td>900000.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2022-01-05</td>\n", "      <td>54.0</td>\n", "      <td>53.2</td>\n", "      <td>52.1</td>\n", "      <td>54.8</td>\n", "      <td>1100000.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>2022-01-06</td>\n", "      <td>53.5</td>\n", "      <td>52.7</td>\n", "      <td>51.8</td>\n", "      <td>54.2</td>\n", "      <td>950000.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>2022-01-07</td>\n", "      <td>52.8</td>\n", "      <td>54.3</td>\n", "      <td>52.4</td>\n", "      <td>55.1</td>\n", "      <td>800000.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>2022-01-08</td>\n", "      <td>54.2</td>\n", "      <td>55.6</td>\n", "      <td>NaN</td>\n", "      <td>56.2</td>\n", "      <td>750000.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>2022-01-09</td>\n", "      <td>55.7</td>\n", "      <td>56.2</td>\n", "      <td>54.9</td>\n", "      <td>56.5</td>\n", "      <td>850000.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>2022-01-10</td>\n", "      <td>56.0</td>\n", "      <td>55.5</td>\n", "      <td>54.7</td>\n", "      <td>56.8</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["           日期   开盘价   收盘价   最低价   最高价        成交量\n", "0  2022-01-01  50.2  51.5  49.8  52.1  1000000.0\n", "1  2022-01-02  52.0  53.2  51.5  54.0  1200000.0\n", "2  2022-01-03  54.5   NaN  53.8  56.0        NaN\n", "3  2022-01-04  55.2  53.8  52.5  55.5   900000.0\n", "4  2022-01-05  54.0  53.2  52.1  54.8  1100000.0\n", "5  2022-01-06  53.5  52.7  51.8  54.2   950000.0\n", "6  2022-01-07  52.8  54.3  52.4  55.1   800000.0\n", "7  2022-01-08  54.2  55.6   NaN  56.2   750000.0\n", "8  2022-01-09  55.7  56.2  54.9  56.5   850000.0\n", "9  2022-01-10  56.0  55.5  54.7  56.8        NaN"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "# 读取数据文件\n", "df = pd.read_csv('data/股票数据Test.csv')\n", "\n", "# 检查异常值\n", "negative_volume = df[df['成交量'] < 0]\n", "if not negative_volume.empty:\n", "    print(\"存在异常值：成交量为负数\")\n", "    print(negative_volume)\n", "\n", "# 将成交量为负数的异常值置为NaN或其他合适的值\n", "df.loc[df['成交量'] < 0, '成交量'] = None\n", "\n", "# 查看处理后的数据\n", "print(\"处理后的数据：\")\n", "df"]}, {"cell_type": "code", "execution_count": null, "id": "85cb0a5d", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}