{"cells": [{"cell_type": "code", "execution_count": 2, "id": "44d7e506", "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAjwAAAHECAYAAAA9JvBzAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjcuMSwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy/bCgiHAAAACXBIWXMAAA9hAAAPYQGoP6dpAABNQ0lEQVR4nO3deVxUVeM/8M8w7CK7yKIhIiq4pmJYbpQ+poKm9hiolZlpLmlmZubjnktqmJbLE64ZamXl8hSahpoaZCIuiLtoKJAgODNsw3Z+f/Djfh2HXXCG2+f9es3r1T3nLud0gfvx3nPPKIQQAkREREQyZmLoBhARERHVNQYeIiIikj0GHiIiIpI9Bh4iIiKSPQYeIiIikj0GHiIiIpI9Bh4iIiKSPQYeIiIikj0GHiIiIpI9Bh4iAgDk5ubi8uXLKC4ulsquXLmCxMREA7bKOGRkZOD8+fMoKChAYWEhsrKyDN0ko5GWlqbzM0NkrBh4iAysqKgIhYWFOp/y6oqKiqS64uJi5OXloTrfDlNUVITs7Owy686fPw9fX1+d469YsQK+vr5IT0+vVp+++uorvPnmm7h//361tktISMDly5f1yv/8888qbZ+ZmYlZs2bh/Pnz1TpuZU6dOoUOHTogJSUF8+fPR/fu3ZGcnFylbb/44gs0a9YMGRkZtdqmqrh16xZOnTqlV75+/Xqo1epytxNCID8/H9nZ2cjMzMTt27dx9uxZREVFYevWrZg3bx5CQ0Ph7e0NFxcXLF26tC67QVQrTA3dAKJ/unbt2uHSpUs6ZSkpKXB1dYWTkxNUKpVU7ufnh4sXLwIArl69Cl9f3xods6CgAKamur/+VlZWAABzc3MAJYEqMjISI0eOhLOzc6X7zMvLQ2pqKgBgx44dSE5OhkajgUaj0VmvSZMmescutXr1auzbtw+JiYmwtLQEANy8eRNdu3bFF198gUmTJlXYhpSUFCxbtgz9+/evtL3VYW1tDQCwsLDA+++/j0OHDqF37944ceIEXFxcKtzWw8MDt2/fxtatW/Hee+9V6XgtWrRAQUEBHBwcyl3n5s2bmD59OubNm1fuOgsXLsQvv/yC8+fPw9HREQBw7do1TJw4EcnJyVi0aFGZ24WGhuKbb76Rls3NzVFcXIynnnoK7du3h4eHBzp27Ij+/fvDxcUFdnZ2VeoXkSEx8BAZmIWFBWbNmoWPPvoIf/31F9q0aQMzMzMAQH5+PsLDwxESEoIlS5bgl19+kbZr1aoVVCoV0tPTYWJScrP2s88+w9WrV7Fu3TrcuXMHPXr0QHR0NFxdXQEAhYWFaNy4sRQ4vvjiC+Tm5mLGjBnSPkr98ssvSE5OxtSpU3XKCwoKkJ+fD2traygUCqn81KlT6NWrl866Xl5eev29ceMGmjdvrldeUFCAffv2YfLkyVLYAUruMpmamuK7777DxIkTdY75qNLAZWZmVuadolJCCGi1Wvj6+sLCwqLc9UqVhsHi4mLY29tj//79WLlypc62BQUFKCgokMJRqeDgYAQGBuqVAyXnIycnB7a2tnrH69atGzp37lxum/773/9K7SrPihUrsGfPHkybNg3btm0DAGzbtg1ubm6YNWtWudt98cUXWLZsGRwcHGBjY4N79+6hbdu2aNCgAXbs2AErKysUFxdDoVBUeD6IjAkDD5GBmZiYwNzcHDY2NtJF0dzcHAUFBdBqtWjSpAlsbGykOy+lFAoFzp8/jx49eujt8+Gg0a1bN526vLw86b9PnTqFgoKCMtsVHh4OAOjQoUOZ9YmJiWjWrJm0XHrxvXbtGlq0aIH//Oc/aNSoEd544w3Y2toiPT0djRo10gkzD/v++++RmZmJcePGSWXXr1/Hpk2bsGvXLixcuBDz58/HggULytweAO7evQsTExMEBweXuw5QEk5yc3Nx6dIleHt7S+Vubm5SaCqLu7u7znJYWJj0CPDkyZMIDAyEmZmZXngEgN9//x3vvvuuTplWq0WrVq3KDGcNGzas8M7aoz8PD4uPj4eVlRWUSiU++OADtGrVCrdu3YIQAlu2bMHo0aNx7949FBUVIS8vD61bt4ZSqZS2d3Z2lo59//59DBw4EJ07d5bCzoMHDxASEoLi4mJ8+eWXOj8HRMaKgYfIwBQKBf766y+cOHFCutgqFArs2bMHCoVC+le+QqHAnTt3cPXqVZiZmcHLy0sKGRqNBjY2Npg/fz7i4+Oxe/du3Lp1C15eXkhKSkKTJk1w9OhRPP/88zp3JczNzcu8OF+9ehV79uzBzp070bt3bwDA4cOHMXnyZMTHxyMvLw8eHh4AgKysLOmO1MNefvllLF++HLa2tnjjjTekchMTE2k8UmlbhBBYsWIFunbtikaNGgEoubv1+uuvY8iQIRg2bBj8/PzQrVs3WFpalnt34ubNm2jXrh3Onj1bnVMgUSgU+PTTTzFixAid8sLCQjRt2hSRkZHo2LEjhBAoLCxEfn6+tE63bt1w9+5dWFlZQaFQYObMmXj11VfRtm1baZ28vDy8//77mDhxIvz8/JCVlQWtVqvXjoKCAkRGRuL3338vt61//fVXmdsWFhaiXbt2FfZz6dKlOuNu0tLS9MJVamoqfv75Z8ydOxdDhw7FqlWroFQq8ccffyAkJARqtRrz589HkyZNKjwWkdEQRGRQHTt2FI0aNRJt2rQRLVu2FADEb7/9JmxtbcW4ceOk9X799VfRsGFDAUB0795dCCHEqVOnBABx584dkZmZKWbOnCkGDRokMjMzxblz5wQAcfHiRZGZmSn2798vAIji4mJpn2+++aZ4/fXXhRBCXLhwQZT+SXjttdeEg4ODGD9+vLR+eHi4aNeunU7bCwoKBIAafaZOnSrt56uvvhIARK9evYQQQuTl5YmhQ4eKVq1aCZVKJa0XFRUlrK2txZAhQ8Tdu3f1/l++9tpr4qWXXqrxufDw8BDh4eFl1llbW4v//e9/QgghUlJSxC+//FLufqZPny4AiClTpuiUX7p0SbRo0ULY2NiIHTt26NQVFRUJtVotCgsLq9Xm/Px8kZ2drVOWnZ0tioqKdMp69eolFi1apHfMnJwcnZ+JUps2bSr33A0cOFCkpqZWq51EhsY7PEQGVlxcjIkTJ2L+/PnSXZn27dtjxowZmDFjhrTe888/D5VKBa1WKz1+KP0X/qP/yn54sGubNm106rRabbmPlYCSx1zbt2+XHn1MmDABHTp0wNWrV/XG5JiamuLs2bOwtLTEvn37sGLFCvz+++9IT09HSkqKzp2Gv/76Cy+88AIuXLgApVIpjVtJTU3FBx98gKZNmwIAkpKSEBoaijt37uD48eM641sCAwPx66+/YvDgwfDx8cHZs2fh4+Mj1V+9ehVPP/00Hjx4UP7/8IfabmNjo1NmYmKC1NRUnUdM9vb2cHV1hZubG/7++28AwHfffYd3330Xv/32G5577jlp3fz8fEyaNAkbN27EggULMHfuXJ39t27dGr/88gs++OADjBgxAlFRUVizZg2srKyQlJSEli1bwszMDDY2NnptK0tWVhY0Gg1CQ0OxceNGqbys8UJlMTExKXcc0JAhQ+Dj44OWLVuisLAQw4cPR2xsLJYvX44pU6Zg//792LVrF7Zs2VLh4zUiY8HAQ2RgZc1holQqcebMGQwaNEjvcVGHDh2wePFiAED37t1x+/ZtWFlZlflY6WFarRbFxcUVhh2gZDD0pk2b8Prrr2P58uWIiopChw4dEBcXh+eff15v/dIxPocPH0bfvn3RokULxMXFYdSoUQgODsbixYvRqlUr6ZV6Hx8fncdq48ePh5mZGSZPnoyff/4Z9+7dg6mpKU6ePImRI0di0qRJ+Pe//w0AGD58OLy8vHDx4kVERkbqhB2gJPDExMRg/fr1FfYRAPr27aszCLzUnDlzMGfOHGl52rRpCAsLQ/PmzXH16lUAwK5du9ClSxc8++yz0nr5+fkIDAzEn3/+iS1btkChUOC9995DWFiYtE56ejo6deqEzz//HK1bt8ZPP/0EjUYDKysreHp6SgF29erVVZpuwNfXF/369at0vYqUTm/waEhycHBAjx49cOLECYwcORLW1taIiYlBx44dpXW+/fZb5OTk4Lvvviv3zTsiY8F5eIgMrKioCBkZGbh+/Tpu374tlVtZWeG3335Dly5d0KVLF3Tq1AmRkZF6gaVVq1bw9vZGixYtKvx4e3vj5ZdfrrQ9dnZ20pibIUOGYPfu3cjOzsbx48cRGBhY5jb/+c9/cPjwYUyfPh0A8O9//xuXLl2CSqWCUqnE4sWLERwcDBcXF723osLCwvDVV19JF9zOnTvj6NGj8PDwwM2bN3XGGOXk5ECr1cLZ2RmvvvqqXjvu378PIUSln3/9619lvkGWm5uLbdu2SesFBATAyckJQMmdsri4OERHR+P333/Hxx9/rPOGkrm5OcLDwxEZGYnRo0cjOzsbX3zxhc7EjZ9++imAkjtVixYtQnR0dJmvtc+cOROZmZlo1qwZmjVrBrVajY8//lhabtasGb7//nt89913etv27NkTDg4O0sDj0s/JkycxZ84c6c2q0o9SqcRTTz2lsw+1Wo0DBw5gxIgR6N27N/r374/Y2Fi0a9cOKpUKycnJ8PX1xTvvvIM9e/Zg7Nix1ZoPisggDPIgjYgkTZo00RsjodFoRGpqqjA1NRWXLl0SQghx9OhRYWJiIm7duiWEKBnnkp+fX+3jFRQUiLy8PFFYWCi6dOkiWrduLYKCgqR2POzSpUtCoVCIiRMnCk9PzzLHegghxK5du8Tq1avLPeaJEyfEhx9+KM6dO1fuOp9//rk0hqeUi4uLOHHihLQ8ePBgMX369Cr0smJt27YVH3/8sV65ubm52L17t7Ts5+cnNmzYIIQQIiIiQjRq1Ej06dNHBAYGVnoMrVYrnnrqKTFs2DAhhBAxMTHC3NxcbN26tdJtGzZsKNzc3IS3t7fw9vYW7u7uQqlUSsve3t7CxsZGjB8/Xm/bpKQkkZSUJJKTk0VKSopISUkRp0+fFkqlUlhbW4tTp05J5SkpKeL27dvixo0bOvs4dOiQ9LNoZmYmXFxchLW1tVAoFMLExETY2toKDw8P4ePjI/z8/AQAMWPGjEr7RWRIvAdJZGAZGRnYunUrXn/9dWkMDwA0btwYw4cPx/Lly7F582asW7cOQ4cOhaenJwBg0qRJ2LRpU42O+eabb+KLL75AVlYWvLy8EBwcjAkTJmDgwIE667Vu3RrPPvss1q1bh+XLl5c758qhQ4ewadMmvTl7HrVs2TIAJWNPGjRoUGk7NRpNpRP7ASWvyKtUqnLfOgNKHjmZm5ujdevWuHv3rjRmqNS9e/eQn5+vM4meWq2W3l564YUXkJ6ejqNHjyI2NrbSNpmbm2PTpk3417/+hRUrVmD16tUYMWIEXn/99Uq3zcvLwyuvvIJWrVoBKHnV/5tvvsHbb78trRMREaEzK3apst6aWrBgAby9veHq6op3330Xhw4dqnCcT+/evXH06FG4urrC0dERDRo0gKWlJcaNG4fCwkJs3bpVZ/2FCxeiU6dOlfaLyJAYeIgM6O7du8jJySnz8QoALFmyBH5+fmjcuDF+/PFHnDlzRqpbu3Yt1q9fDzMzM+Tm5iI7O1u6OIeEhMDU1BRff/01gP+72AMlrzwXFxfDwsIC58+fl8b+xMfH6x3/wYMH0vdGNWzYsNx+WFlZoVevXtizZw+Sk5PRpk0bZGZmAgAmTpyI5s2b4/3338fBgwcREhJS6TgioGTW5Ly8PLi5uVW67qJFixAREQELC4tyA09xcTE6deqEn3/+GZmZmXqBp/TrK0rHBeXn5+Pvv/+WHmmlpqbC1NQUAQEBaN++PYCSoFXeuQOAPn36IDQ0FB988AFatGhRpbFFALB9+3adR0QPHjyAUqnUCTMzZ86s0vw369evx5dffon//e9/aNeuHZ5++mnpXJVOLfCo0n6OGTMG48aN05tQEgDi4uKwcOFCfPnll3qDs4mMEQMPkQH9+eefUCgU5U7u5+npiXHjxmHZsmV46623dOZ0KR0Lk5mZieHDh+Pvv/9GdHS03p0TlUqFgIAA9OrVC3PnztWZPK+igc6JiYl46aWX8ODBAyxevBjvvPMO7t+/j5kzZ+oNUFUqlTA1NYW9vb0UkOzt7QGU3OmwtLSEvb291LaHJ7krz/79++Ht7V2lt5U2b96MzZs3V7oeAOkNrEcDz4kTJ+Dk5AQPDw9MnToV8fHxKCgoQMuWLXHx4kUMGDAAAQEBOHnyJA4fPow+ffpg+PDh6N+/PxYuXKh3nIyMDCxevBi7du3C888/j2PHjuHVV1/F3LlzK5wnZ+3atVi8eLE0nw9QMrZIpVLhP//5j7Re6YzXKSkpZe4nKysLc+bMweeff47w8HDp6zaioqLQt29ftG7dGtOmTcPUqVOlUFequLgYo0aNwi+//IJhw4aVuf/CwkKcPXsWTz/9NL755hudt9WIjBEHLRMZ0A8//ICOHTtKj1FKX6cuvdDt3bsXX375Jfz9/bF582ZMmTIFf/31F4CSxx7r169HmzZtkJKSgm3btkmB4uEvGbW2tsa0adOwf/9+tGjRArNnzy5zduXSbVQqFZYvX4727dujQYMGiImJwUcffYSvvvoKS5YsQYsWLbBw4UKoVCrcuXMHFy9exIMHD5Cbm4vr16/j1q1bAEpmSb5+/To0Go00KLv04nz58mWcO3euzOMDJRPhLVq0qEqPf6rrwoULAHQf/eTl5WHbtm0YMmQITE1NoVQqYW1tjS1btuD333/Hc889h549eyIqKgrjxo1DaGgoIiMjER8frzMIOzk5GXv27MGYMWPg6emJffv2Yf/+/fj1119x9OhRXLt2De3bt0eXLl0wa9Ys/PDDD9J3o5V69dVXER8fj2vXrkn/D7dt2wZXV1dp+fr167h58yauXbum17/ExEQsXLgQrVu3xo8//ohDhw5hzJgxUn27du3wxx9/ICAgAIsWLULjxo3Rp08f6UtlMzIyMGDAAJw4cQLHjx/H0KFDpW0LCwuln01/f3/ExcWhRYsW6N27t3Q3kchoGXoQEdE/1aVLl4SpqalYvny5EEKIESNGCHd3d2Fvby9+/fVXMWTIEKFQKMTChQuFEEJ8/fXXwsHBQSgUCjF8+HChVqtFv379xIcffijy8vKEEEJcv35ddOnSRZibm4vZs2frHC87O1t88MEHZQ50FaJkUC0A8fXXXws7Ozsxb948UVBQoLPOzZs3xciRI8WECROEEEK88847wtraWtjb2wsnJ6dKP46OjsLGxkZYWFgIc3NznX1/8sknonv37iI1NVX4+/sLHx8fvQn1BgwYoDNhYVVdvHhRTJw4UYwZM0Y0atRIBAQE6NQvXrxYmJqaiosXL0plhYWFYtiwYUKpVIq5c+dKE/k9ePBAdOzYURrU++effwohhNi+fbtU5u/vLzZv3qz3/6+4uFjs2bNHDB48WJibmwsA4q233pLq+/fvL5ycnETz5s11Big/9dRTwtHRUafM29tbNGvWTDg4OIj169dL+xgzZoxwdHQUCxYsEGq1usL/L7t37xbdu3cXH330kVS2du1a4e3tLa5fvy6Vbd68WYwbN044OjrqTaaYk5Mjhg0bJo4cOVLhsYgMjYGHyEC0Wq0IDw8XGRkZQggh1qxZI6ZOnSr+/PNPsXr1auHt7S0OHTqks016erp47733dN4ketTkyZPFunXrhFarrXHbsrKyarxtTS1evFh06dJFXLt2TTzzzDPi8uXLeuu88MIL5Qa2ihQWFooOHTqIZ599Vrz77rsiOTlZp76goEAcPnxYb7t9+/aJY8eO6ZVnZ2eLNWvWiM8//1wqy8/PF1988YW4evVqldqUk5Mjfv31V3Hnzp1q9qZi2dnZlQadRz369l1ubq7O8o4dO4Svr68YM2aMuHnz5mO3kcgQFEJw8gQiYyOEQH5+fpW+yZuIiCrHwENERESyx0HLREREJHsMPERERCR7nIcHJXNOJCcno2HDhuXOJEtERETGRQgBjUYDd3f3cicdLcXAg5K5Mx6dhIyIiIjqh6SkpDK/VuVhDDz4vynzk5KSYGtra+DWEBERUVWo1Wo0bdq0wq++KcXAg/+b1dbW1paBh4iIqJ6pynAUDlomIiIi2WPgISIiItlj4CEiIiLZY+AhIiIi2WPgISIiItlj4CEiIiLZY+AhIiIi2WPgISIiItlj4CEiIiLZ40zLREREVGeKigVOJWbgniYPLg0t0dXLEUqTJ/9F3Qa7w7N37140b94cpqam6NixIy5dugQAiI+Ph7+/PxwcHDBjxgwIIaRtjh07Bl9fXzg7OyMsLExnf7t374anpyfc3d2xc+fOJ9oXIiIi0ncgPgXdP4lCaHgMpu46i9DwGHT/JAoH4lOeeFsMEnhu3LiBN954A8uWLcPdu3fRsmVLjB07FlqtFsHBwejcuTNOnz6NhIQEbN26FQCQlpaGQYMGITQ0FNHR0YiIiMCRI0cAlISkkSNHYs6cOTh48CDmzp2LK1euGKJrREREhJKwM+HrM0hR5emUp6ryMOHrM0889Bgk8Fy6dAnLli3D8OHD0bhxY0yYMAFxcXGIjIyESqVCWFgYvL29sWTJEmzatAkAEBERAXd3d8yZMwc+Pj6YO3euVLdx40YEBgZi7NixaNeuHSZPnozt27cbomtERET/eEXFAgv2J0CUUVdatmB/AoqKy1qjbhgk8AQFBWHcuHHS8pUrV+Dj44Nz584hICAA1tbWAID27dsjISEBAHDu3DkEBgZK34jatWtXxMbGSnXPP/+8tL+H68qi1WqhVqt1PkRERFQ7TiVm6N3ZeZgAkKLKw6nEjCfWJoO/pZWfn49PP/0Ub7/9NtRqNby8vKQ6hUIBpVKJzMxMvTpbW1skJycDQIV1ZVm6dCns7OykT9OmTeugZ0RERP9M9zTlh52arFcbDB545s2bhwYNGmDs2LEwNTWFhYWFTr2lpSVycnL06krLAVRYV5ZZs2ZBpVJJn6SkpFruFRER0T+XS0PLWl2vNhj0tfSoqCisXbsWMTExMDMzg6OjI+Lj43XW0Wg0MDc3h6OjI9LS0vTKAVRYVxYLCwu9YEVERES1o6uXI9zsLJGqyitzHI8CgKtdySvqT4rB7vAkJiYiNDQUa9euhZ+fHwDA398f0dHROutotVo4Ojrq1cXFxcHDw6PM7R6uIyIioidLaaLAvOCSa/ujM+6ULs8L9nui8/EYJPDk5uYiKCgIgwcPxpAhQ5CVlYWsrCz06NEDarUaW7ZsAQAsWbIEffr0gVKpxKBBg3Dy5EkcPnwYBQUFWL58Ofr16wcAGDZsGHbt2oULFy4gKysLa9askeqIiIjoyXuxrRvWj+oEVzvdx1audpZYP6oTXmzr9kTboxAPz+z3hOzduxcvvfSSXnliYiLOnz+P0NBQWFlZwcTEBEePHpXuAG3YsAFTpkyBjY0N7O3tER0djcaNGwMAZs+ejZUrV8LS0hI+Pj44fvw4rKysqtQetVoNOzs7qFQq2Nra1lo/iYiI/unqcqbl6ly/DRJ4KpOamorY2FgEBATAyclJpy4xMRGXL19Gjx49YGNjo1OXkJCAu3fvolevXhWO4XkUAw8REVH9U+8Dz5PGwENERFT/VOf6bfDX0omIiIjqGgMPERERyR4DDxEREckeAw8RERHJHgMPERERyR4DDxEREckeAw8RERHJHgMPERERyR4DDxEREckeAw8RERHJHgMPERERyR4DDxEREckeAw8RERHJHgMPERERyR4DDxEREckeAw8RERHJHgMPERERyR4DDxEREckeAw8RERHJHgMPERERyR4DDxEREckeAw8RERHJHgMPERERyR4DDxEREckeAw8RERHJHgMPERERyR4DDxEREckeAw8RERHJHgMPERERyR4DDxEREckeAw8RERHJHgMPERERyR4DDxEREckeAw8RERHJHgMPERERyR4DDxEREckeAw8RERHJHgMPERERyR4DDxEREckeAw8RERHJHgMPERERyR4DDxEREckeAw8RERHJHgMPERERyR4DDxEREckeAw8RERHJHgMPERERyR4DDxEREckeAw8RERHJHgMPERERyR4DDxEREckeAw8RERHJHgMPERERyR4DDxEREcmeqaEbQEREVJ6iYoFTiRm4p8mDS0NLdPVyhNJEYehmUT1k0Ds86enp8PLywq1bt6SyjRs3omnTprC2tkbv3r1x8+ZNqS4+Ph7+/v5wcHDAjBkzIISQ6o4dOwZfX184OzsjLCzsSXaDiIjqwIH4FHT/JAqh4TGYuussQsNj0P2TKByITzF006geMljgSU9PR1BQkE7YuXHjBhYuXIi9e/fi8uXL8Pb2xujRowEAWq0WwcHB6Ny5M06fPo2EhARs3boVAJCWloZBgwYhNDQU0dHRiIiIwJEjR558p4iIqFYciE/BhK/PIEWVp1OeqsrDhK/PMPRQtRks8ISEhGDEiBE6ZXFxcQgICECnTp3w1FNPYcyYMbh+/ToAIDIyEiqVCmFhYfD29saSJUuwadMmAEBERATc3d0xZ84c+Pj4YO7cuVIdERHVL0XFAgv2J0CUUVdatmB/AoqKy1qDqGwGCzzh4eGYMmWKTpmfnx+ioqJw9uxZqFQqrFu3Dn379gUAnDt3DgEBAbC2tgYAtG/fHgkJCVJdYGAgFIqS57pdu3ZFbGxsucfWarVQq9U6HyIiMg6nEjP07uw8TABIUeXhVGLGk2sU1XsGG7Ts5eWlV+bn54eXX34ZTz/9tLTOH3/8AQBQq9U62ygUCiiVSmRmZkKtVsPPz0+qs7W1RXJycrnHXrp0KRYsWFBbXSEiolp0T1N+2KnJekSAkb2WfurUKezfvx8xMTF48OABQkNDMWDAAAghYGpqCgsLC531LS0tkZOTo1dXWl6eWbNmQaVSSZ+kpKQ66xMREVWPS0PLWl2PCDCywLNz506EhITgmWeegZ2dHT7++GPcuHED586dg6OjI9LS0nTW12g0MDc316srLS+PhYUFbG1tdT5ERGQcuno5ws3OEuW9fK4A4GZX8oo6UVUZVeApLi7GvXv3pGWNRoOcnBwUFRXB398f0dHRUl1iYiK0Wi0cHR316uLi4uDh4fFE205ERLVDaaLAvOCSYQqPhp7S5XnBfpyPh6rFqAJPjx498MMPP2DVqlXYsWMHXnrpJbi6uqJ9+/bo2bMn1Go1tmzZAgBYsmQJ+vTpA6VSiUGDBuHkyZM4fPgwCgoKsHz5cvTr18/AvSEiopp6sa0b1o/qBFc73cdWrnaWWD+qE15s62agllF9ZVQzLQ8bNgyXLl3CZ599hpSUFLRt2xY//vgjzMzMAJRMShgaGooZM2bAxMQER48eBQA4Oztj1apVGDBgAGxsbGBvby/N0UNERPXTi23d0NfPlTMtU61QiIenK64HUlNTERsbi4CAADg5OenUJSYm4vLly+jRowdsbGyqvE+1Wg07OzuoVCqO5yEiIqonqnP9rneBpy4w8BAREdU/1bl+G9UYHiIiIqK6wMBDREREssfAQ0RERLLHwENERESyx8BDREREssfAQ0RERLLHwENERESyx8BDREREssfAQ0RERLLHwENERESyx8BDREREssfAQ0RERLLHwENERESyx8BDREREssfAQ0RERLLHwENERESyx8BDREREssfAQ0RERLLHwENERESyx8BDREREssfAQ0RERLLHwENERESyx8BDREREssfAQ0RERLLHwENERESyx8BDREREssfAQ0RERLLHwENERESyx8BDREREssfAQ0RERLLHwENERESyx8BDREREssfAQ0RERLLHwENERESyx8BDREREssfAQ0RERLLHwENERESyx8BDREREssfAQ0RERLLHwENERESyx8BDREREssfAQ0RERLLHwENERESyx8BDREREssfAQ0RERLLHwENERESyx8BDREREssfAQ0RERLLHwENERESyx8BDREREssfAQ0RERLLHwENERESyx8BDREREssfAQ0RERLLHwENERESyx8BDREREsmfQwJOeng4vLy/cunVLr27mzJkIDg7WKYuPj4e/vz8cHBwwY8YMCCGkumPHjsHX1xfOzs4ICwur66YTERFRPWKwwJOeno6goKAyw8758+exbt06rF69WirTarUIDg5G586dcfr0aSQkJGDr1q0AgLS0NAwaNAihoaGIjo5GREQEjhw58oR6QkRERMbOYIEnJCQEI0aM0CsvLi7GuHHjMG3aNDRv3lwqj4yMhEqlQlhYGLy9vbFkyRJs2rQJABAREQF3d3fMmTMHPj4+mDt3rlRXFq1WC7VarfMhIiIi+TJY4AkPD8eUKVP0yjds2IALFy6gWbNm2LdvH/Lz8wEA586dQ0BAAKytrQEA7du3R0JCglQXGBgIhUIBAOjatStiY2PLPfbSpUthZ2cnfZo2bVrb3SMiIiIjYrDA4+XlpVeWlZWFefPmoXnz5rh9+zZWrVqF7t27Izc3F2q1WmcbhUIBpVKJzMxMvTpbW1skJyeXe+xZs2ZBpVJJn6SkpNrtHBERERkVU0M34GE//PADsrOzceTIETg7O6OwsBDt2rXD9u3bYWpqCgsLC531LS0tkZOTo1dXWl4eCwsLvX0RERGRfBnVa+l37txBQEAAnJ2dAQCmpqZo3749rl+/DkdHR6Slpemsr9FoYG5urldXWk5EREQEGFngadKkCXJzc3XKbt++DQ8PD/j7+yM6OloqT0xMhFarhaOjo15dXFwcPDw8nli7iYiIyLgZVeAZOHAgEhISsGHDBty5cwdr1qzBuXPnMHToUPTs2RNqtRpbtmwBACxZsgR9+vSBUqnEoEGDcPLkSRw+fBgFBQVYvnw5+vXrZ+DeEBERkbEwqjE8Tk5O+Pnnn/H+++/jvffeg5ubG7799lvpLaqNGzciNDQUM2bMgImJCY4ePQoAcHZ2xqpVqzBgwADY2NjA3t5emqOHiIiISCEenq64HkhNTUVsbCwCAgLg5OSkU5eYmIjLly+jR48esLGxqfI+1Wo17OzsoFKpYGtrW9tNJiIiojpQnet3vQs8dYGBh4iIqP6pzvXbqMbwEBEREdUFBh4iIiKSPQYeIiIikj0GHiIiIpI9Bh4iIiKSPQYeIiIikj0GHiIiIpK9GgWeUaNGlVu3aNGiGjeGiIiIqC5UO/AIIXDjxg1kZWUhLS0N9+/fx/379/H9998jPz8fkZGRddFOIiIiohqrUuAxMTGBlZUVzM3NsWjRIjg6OmLz5s3w9PSEq6srRo8ejWXLlqGoqAgNGjSo6zYTERERVUuVAk+vXr3g7++P48ePQwgBpVIJANi5cye6dOmCjh07wszMDFZWVnXaWCIiIqKaqNYjLYVCUaUyIiIiImPyWG9pJSUlITc3F+np6cjNzUVCQgK0Wm1ttY2IiIioVpjWdEMhBFavXo2///4b9+7dg0ajweuvv47U1NTabB8RERHRY6vxHR6FQoGVK1eiTZs2GDNmDNq2bYs///wTrVu3rs32ERERET22x554UKFQcBwPERERGbUqPdJKTU2FEAI//fQTTEz+LyNFR0cjIyMDGRkZAEoecxEREREZmyrd4QkICEDv3r2RnJyMDh06ICsrC02aNMHZs2fRvHlzNGzYEPn5+bh37x5DDxERERkdhahBQunUqRPOnDmjU3bnzh24urrimWeeQWxsbK018ElQq9Wws7ODSqWCra2toZtDREREVVCd63eN3tJ65ZVX9MqaNGkCANiyZUtNdklERERUZ2p0h0dueIeHSJ6KigVOJWbgniYPLg0t0dXLEUoTvmRBJBfVuX5X+S2trKws9OzZE8XFxQBKJh18VH5+PiZPnozMzMxqNpmIqHYdiE9B90+iEBoeg6m7ziI0PAbdP4nCgfgUQzeNiAygyoHHysoKp0+fhomJCY4dO4Y2bdogOTlZZ50bN25g/fr1UKvVtd5QIqKqOhCfgglfn0GKKk+nPFWVhwlfn2HoIfoHqnLgUSqVsLGxQUZGBkaNGoVJkybB3d1dZ53ff/8dXl5e8PT0rPWGEhFVRVGxwIL9CSjrWX1p2YL9CSgq/sc/zSf6R6nWxINCCLzxxhvw9fVFXl4eQkNDdR5f7d27F6GhobXeSCKiqjqVmKF3Z+dhAkCKKg+nEjOeXKOIyOCqPdPyxIkTsX37drz55pvIz89Hu3bt8Mcff+DKlSuIiorCxIkT66KdRERVck9TftipyXpEJA+VvpZeXFyMvXv34v79+1AoFOjXrx8AoHHjxvj+++/xzTffYODAgWjUqBGmT58ONze3Om80EVF5XBpa1up6RCQPld7hiYiIwMsvv4zffvutzPp+/frB19cX165dw+jRo2u7fURE1dLVyxFudpYo7+VzBQA3u5JX1Inon6PSwBMcHIwrV67gq6++ghACDx48QHFxMfLz87Fu3Tq0bt0aTZo0wbRp0zBp0qQn0WYionIpTRSYF+wHAHqhp3R5XrAf5+Mh+oep9JGWvb097O3tpeXRo0fDyckJffv2xdq1a7Fu3ToMHToUhYWFaNmyJY4dO4ZevXrVZZuJiCr0Yls3rB/VCQv2J+gMYHa1s8S8YD+82JaP3on+aar11RIKhQJz5szBiy++CE9PT8THx0OhKPlXkqmpKSZNmoSwsDAGHiIyuBfbuqGvnytnWiYiANX8agknJyfcv38fsbGx6N27NzZu3KjzvVrXrl1DmzZtcO/ePZ27QsaOXy1BRERU/9TJV0toNBpoNBoAQOfOnbFy5Uq9iQd9fHxgb2+Pc+fO1aDZRERERHWj1r489K+//oJSqYSZmRlcXFxqY5dPDO/wEBER1T91cocnJycHQ4YMAQA8ePBAr3706NF46qmn0K9fP2RkcAZTIiIiMh5VCjzTpk3DjBkzcPjwYQDAgAED0Lt3b8TExEjrREVF4auvvsL58+eRnZ1dN60lIiIiqoFKA094eDgOHjyI2bNnw8zMDACwfv16dO3aFQMHDsSwYcNw584d7N+/H1OnTsXs2bPRtGnTOm84ERERUVVVOobn/v37yM/Ph5ubG1xcXHDv3j2p7vfff8eYMWNw69Yt2NvbY/PmzRgwYECdN7q2cQwPERFR/VOd63el8/Ds3bsXJ0+ehBACarUaffv2RWpqKv766y9YW1ujW7dueO211/DZZ5+hsLCw1jpBREREVFsqDTwNGjSAp6cnrKysYGFhgenTp8PDwwNNmjSBg4ODtF7fvn0xYMAAREZGokuXLnXaaCIiIqLqqNZr6Q8/0lqyZAkcHR3h5OQER0dHNG3aFDExMfj4449x9uxZWFtb11mjaxsfaREREdU/tfpIqzzz5s3Da6+9Bo1Gg/v37+P06dP46KOPEBQUBK1WW68CDxEREclbtQLPozeDNm3aJP33559/jv/97384ePBg7bSMiIiIqJZU+8tDy/pvAAgMDESPHj1qp1VEREREtahagScjIwNKpRJAyd0eS0tLNGjQAO7u7mjbti1eeukltG3bFqamNX5SRkRERFTrqjVoOSMjAwqFQrq7o9VqkZmZiWvXruHEiRPYuXMnGjZsiDNnzsDCwqLOGl3bOGiZiIio/qnO9bvWvjwUAPLz8xEZGYnBgwfX1i6fCAYeIiKi+qdOvjy0KszNzetd2CEiIiL5q9XAQ0RERGSMGHiIiIhI9hh4iIiISPYYeIiIiEj2GHiIiIhI9hh4iIiISPYYeIiIiEj2DBp40tPT4eXlhVu3bpVZ/+KLL2Lr1q3S8rFjx+Dr6wtnZ2eEhYXprLt79254enrC3d0dO3furMNWExERUX1jsMCTnp6OoKCgcsNORESEzjevp6WlYdCgQQgNDUV0dDQiIiJw5MgRAEB8fDxGjhyJOXPm4ODBg5g7dy6uXLnyJLpBRERE9YDBAk9ISAhGjBhRZl1GRgamT5+OVq1aSWURERFwd3fHnDlz4OPjg7lz52LTpk0AgI0bNyIwMBBjx45Fu3btMHnyZGzfvv2J9IOIiIiMn8ECT3h4OKZMmVJm3fTp0zFkyBAEBARIZefOnUNgYKD0xaVdu3ZFbGysVPf8889L6z5cVxatVgu1Wq3zISIiIvkyWODx8vIqs/zIkSP49ddfsXz5cp1ytVqts42trS2Sk5MrrSvL0qVLYWdnJ32aNm36OF0hIiIiI2dUb2nl5eVh/PjxWL9+PRo2bKhTZ2pqCgsLC2nZ0tISOTk5ldaVZdasWVCpVNInKSmplntCRERExsTU0A142KJFi+Dv74+BAwfq1Tk6OiItLU1a1mg0MDc3r7SuLBYWFjoBiYiIiOTNqALPjh07kJaWBnt7ewBATk4Ovv32W5w6dQr+/v7YsWOHtG5cXBw8PDwAAP7+/oiOjsabb76pV0dERERkVIHn+PHjKCwslJbff/99BAQEYPTo0QCASZMm4fDhw+jVqxeWL1+Ofv36AQCGDRuG5557DlOnToWXlxfWrFmDUaNGGaILREREZISMKvA0adJEZ9nGxgbOzs5wdnYGAKxatQoDBgyAjY0N7O3tpUkJO3TogKlTp6JLly6wtLSEj48PJk6c+KSbT0REREZKIYQQhm5EdSQmJuLy5cvo0aMHbGxsdOoSEhJw9+5d9OrVq8IxPI9Sq9Wws7ODSqWCra1tbTeZiIiI6kB1rt/1LvDUBQYeIiKi+qc612+jei2diIiIqC4w8BAREZHsMfAQERGR7DHwEBERkewx8BAREZHsMfAQERGR7DHwEBERkewx8BAREZHsMfAQERGR7DHwEBERkewx8BAREZHsMfAQERGR7DHwEBERkewx8BAREZHsMfAQERGR7DHwEBERkewx8BAREZHsMfAQERGR7DHwEBERkewx8BAREZHsMfAQERGR7DHwEBERkewx8BAREZHsMfAQERGR7DHwEBERkewx8BAREZHsMfAQERGR7DHwEBERkewx8BAREZHsMfAQERGR7DHwEBERkewx8BAREZHsMfAQERGR7DHwEBERkewx8BAREZHsMfAQERGR7DHwEBERkewx8BAREZHsMfAQERGR7DHwEBERkewx8BAREZHsMfAQERGR7DHwEBERkewx8BAREZHsMfAQERGR7DHwEBERkewx8BAREZHsMfAQERGR7DHwEBERkewx8BAREZHsMfAQERGR7DHwEBERkewx8BAREZHsMfAQERGR7JkaugFExqqoWOBUYgbuafLg0tASXb0coTRRGLpZRERUAwa9w5Oeng4vLy/cunVLKtu7dy+aN28OU1NTdOzYEZcuXZLq4uPj4e/vDwcHB8yYMQNCCKnu2LFj8PX1hbOzM8LCwp5kN0iGDsSnoPsnUQgNj8HUXWcRGh6D7p9E4UB8iqGbRkRENWCwwJOeno6goCCdsHPjxg288cYbWLZsGe7evYuWLVti7NixAACtVovg4GB07twZp0+fRkJCArZu3QoASEtLw6BBgxAaGoro6GhERETgyJEjBugVycGB+BRM+PoMUlR5OuWpqjxM+PoMQw8RUT1ksMATEhKCESNG6JRdunQJy5Ytw/Dhw9G4cWNMmDABcXFxAIDIyEioVCqEhYXB29sbS5YswaZNmwAAERERcHd3x5w5c+Dj44O5c+dKdUTVUVQssGB/AkQZdaVlC/YnoKi4rDWIiMhYGSzwhIeHY8qUKTplQUFBGDdunLR85coV+Pj4AADOnTuHgIAAWFtbAwDat2+PhIQEqS4wMBAKRcn4iq5duyI2NrbcY2u1WqjVap0PEQCcSszQu7PzMAEgRZWHU4kZT65RRET02AwWeLy8vCqsz8/Px6effoq3334bAKBWq3W2USgUUCqVyMzM1KuztbVFcnJyufteunQp7OzspE/Tpk0fszckF/c05YedmqxHRETGwWhfS583bx4aNGggjeExNTWFhYWFzjqWlpbIycnRqystL8+sWbOgUqmkT1JSUt10guodl4aWtboeEREZB6N8LT0qKgpr165FTEwMzMzMAACOjo6Ij4/XWU+j0cDc3ByOjo5IS0vTKy+PhYWFXngiAoCuXo5ws7NEqiqvzHE8CgCudiWvqBMRUf1hdHd4EhMTERoairVr18LPz08q9/f3R3R0tM56Wq0Wjo6OenVxcXHw8PB4ou0meVCaKDAvuOTn7tEZd0qX5wX7cT4eIqJ6xqgCT25uLoKCgjB48GAMGTIEWVlZyMrKghACPXv2hFqtxpYtWwAAS5YsQZ8+faBUKjFo0CCcPHkShw8fRkFBAZYvX45+/foZuDdUX73Y1g3rR3WCq53uYytXO0usH9UJL7Z1M1DLiIiophTi4dn7DNEAhQKJiYlo1qwZ9u7di5deeklvndL6ffv2ITQ0FFZWVjAxMcHRo0elu0AbNmzAlClTYGNjA3t7e0RHR6Nx48ZVaoNarYadnR1UKhVsbW1rs3tUj3GmZSIi41ad67fBA091paamIjY2FgEBAXByctKpS0xMxOXLl9GjRw/Y2NhUeZ8MPERERPWPrANPXWDgISIiqn+qc/02qjE8RERERHWBgYeIiIhkj4GHiIiIZI+Bh4iIiGSPgYeIiIhkj4GHiIiIZI+Bh4iIiGSPgYeIiIhkj4GHiIiIZI+Bh4iIiGSPgYeIiIhkj4GHiIiIZI+Bh4iIiGSPgYeIiIhkj4GHiIiIZI+Bh4iIiGSPgYeIiIhkj4GHiIiIZI+Bh4iIiGSPgYeIiIhkj4GHiIiIZI+Bh4iIiGSPgYeIiIhkj4GHiIiIZI+Bh4iIiGSPgYeIiIhkj4GHiIiIZI+Bh4iIiGSPgYeIiIhkj4GHiIiIZI+Bh4iIiGSPgYeIiIhkj4GHiIiIZI+Bh4iIiGSPgYeIiIhkj4GHiIiIZI+Bh4iIiGSPgYeIiIhkj4GHiIiIZI+Bh4iIiGSPgYeIiIhkj4GHiIiIZI+Bh4iIiGSPgYeIiIhkj4GHiIiIZI+Bh4iIiGSPgYeIiIhkj4GHiIiIZI+Bh4iIiGSPgYeIiIhkj4GHiIiIZI+Bh4iIiGSPgYeIiIhkj4GHiIiIZM/U0A2Qs6JigVOJGbinyYNLQ0t09XKE0kRh6GYRERH94xj0Dk96ejq8vLxw69YtqSw+Ph7+/v5wcHDAjBkzIISQ6o4dOwZfX184OzsjLCxMZ1+7d++Gp6cn3N3dsXPnzifVhXIdiE9B90+iEBoeg6m7ziI0PAbdP4nCgfgUQzeNiIjoH8dggSc9PR1BQUE6YUer1SI4OBidO3fG6dOnkZCQgK1btwIA0tLSMGjQIISGhiI6OhoRERE4cuQIgJKQNHLkSMyZMwcHDx7E3LlzceXKFQP0qsSB+BRM+PoMUlR5OuWpqjxM+PoMQw8REdETZrDAExISghEjRuiURUZGQqVSISwsDN7e3liyZAk2bdoEAIiIiIC7uzvmzJkDHx8fzJ07V6rbuHEjAgMDMXbsWLRr1w6TJ0/G9u3bn3ifgJLHWAv2J0CUUVdatmB/AoqKy1qDiIiI6oLBAk94eDimTJmiU3bu3DkEBATA2toaANC+fXskJCRIdYGBgVAoSsbAdO3aFbGxsVLd888/L+3n4bqyaLVaqNVqnU9tOZWYoXdn52ECQIoqD6cSM2rtmERERFQxgwUeLy8vvTK1Wq1TrlAooFQqkZmZqVdna2uL5OTkMrd7uK4sS5cuhZ2dnfRp2rRpbXQJAHBPU37Yqcl6RERE9PiM6rV0U1NTWFhY6JRZWloiJydHr660vKztHq4ry6xZs6BSqaRPUlJSrfXBpaFlra5HREREj8+oXkt3dHREfHy8TplGo4G5uTkcHR2RlpamV166XXl1ZbGwsNALVrWlq5cj3OwskarKK3McjwKAq13JK+pERET0ZBjVHR5/f39ER0dLy4mJidBqtXB0dNSri4uLg4eHR5nbPVz3pClNFJgX7AegJNw8rHR5XrAf5+MhIiJ6gowq8PTs2RNqtRpbtmwBACxZsgR9+vSBUqnEoEGDcPLkSRw+fBgFBQVYvnw5+vXrBwAYNmwYdu3ahQsXLiArKwtr1qyR6gzhxbZuWD+qE1ztdB9budpZYv2oTnixrZuBWkZERPTPpBAPz+xniAYoFEhMTESzZs0AAPv27UNoaCisrKxgYmKCo0ePws+v5I7Jhg0bMGXKFNjY2MDe3h7R0dFo3LgxAGD27NlYuXIlLC0t4ePjg+PHj8PKyqpKbVCr1bCzs4NKpYKtrW2t9Y0zLRMREdWd6ly/DR54ypKamorY2FgEBATAyclJpy4xMRGXL19Gjx49YGNjo1OXkJCAu3fvolevXhWO4XlUXQUeIiIiqjv1PvA8aQw8RERE9U91rt9GNYaHiIiIqC4w8BAREZHsMfAQERGR7DHwEBERkewx8BAREZHsMfAQERGR7DHwEBERkewx8BAREZHsGdW3pRtK6dyLarXawC0hIiKiqiq9bldlDmUGHgAajQYA0LRpUwO3hIiIiKpLo9HAzs6uwnX41RIAiouLkZycjIYNG0KhqN0v91Sr1WjatCmSkpJk+bUV7F/9J/c+sn/1n9z7KPf+AXXXRyEENBoN3N3dYWJS8Sgd3uEBYGJigiZNmtTpMWxtbWX7gwywf3Ig9z6yf/Wf3Pso9/4BddPHyu7slOKgZSIiIpI9Bh4iIiKSPQaeOmZhYYF58+bBwsLC0E2pE+xf/Sf3PrJ/9Z/c+yj3/gHG0UcOWiYiIiLZ4x0eIiIikj0GHiIiIpI9Bh4iIiKSPQYeIiIjkJ6eDi8vL9y6datK5Xv37kXz5s1hamqKjh074tKlS1U6zqBBg6BQKKRPnz59aqkHVJ1zuHXrVp3zUPrZunVrpcdp3769zjZjx46t3Y7IFANPFZX1AxsfHw9/f384ODhgxowZOt/lcezYMfj6+sLZ2RlhYWFVPs7atWvRuHFjNG/eHFFRUbXZhQpVt39ffvkl3NzcYGZmhl69eiElJaVKxzHUL2p1+1fTi8Lu3bvh6ekJd3d37Ny5s7a7UaHq9HH+/Pll/rE9evRohccQQsDe3l5nm48//rgOe/V/qhsIKjq/FTHEOUxPT0dQUFCZfSur/MaNG3jjjTewbNky3L17Fy1btqzy79Lp06dx4cIFZGZmIjMzE3v37q2lXlSuuudwypQpOj9rLVq0qNJxavr393FU9xyOGDFCOgeZmZlISkqCs7MzevToUeFxcnJycOPGDdy7d0/a9vPPP6/l3pStrPNUUfA2ut9BQZVKS0sTzzzzjAAgEhMThRBC5OXliWbNmonx48eL69eviwEDBojNmzcLIYS4d++esLW1FQsWLBBXr14VnTp1ElFRUZUe58CBA8LS0lLs2bNHnDx5Unh5eYn09PS67JoQovr9O378uHBxcRGHDh0SSUlJokePHmLEiBGVHic7O1tYW1uLe/fuiczMTJGZmSlycnLqsmtCiOr3Twgh3NzcxIULF6R2ZmVlVXqcCxcuCHNzcxEeHi7Onz8vWrRoIS5fvlxX3dJR3T7m5uZKfcvMzBRnz54VjRo1Eg8ePKjwOFeuXBGenp462+bl5dV198rsX0XllZ3f8hjqHL7wwgti9erVev0or3z//v3iv//9r7QcFRUlrKysKj3OnTt3hKura202vcqqew6FEKJbt27ip59+kn7W1Gp1pcep6d/fx1Xdc/ioxYsXi7feeqvS45w4cUIEBATUQourp6zzdP36deHg4CC++eYbkZqaKv7973+LZ599VghhnL+DDDxVUNYP7I8//igcHBxEdna2EEKIs2fPiueee04IIcSqVatE69atRXFxsRBCiD179oiRI0dWepzBgweL8ePHS8vvvvuuCA8Pr+Xe6Ktu/zZv3ix+/PFHafvNmzcLX1/fSo9jqF/U6vavpheFqVOnin79+knLn332mZg9e/bjd6AKqtvHR7311lti8eLFlR7n66+/FiEhIbXW7qqq7sWkOn1/mKHO4c2bN4UQQq8f5ZU/av369aJ9+/aVHueHH34QjRo1Eh4eHsLa2lq88sorIiMj47HaXlXVPYcFBQXC1tZWaDSaah2npn9/H9fjnMPc3Fzh4uJS4TkuFRYWJpo0aSKcnZ2FnZ2dePvtt5/IPzrKOk8VBW9j/B1k4KmCsn5g58+fL/r37y+tU1xcLBwcHIQQQowePVpMmDBBqktOThatW7eu9DjNmjUT33zzjbS8Y8cO8fbbb9dGFypU3f49aubMmWLQoEGVHsdQv6jV7V9NLwq9e/cWn3zyibT8+++/ixdffLEWe1K+xzmHd+/eFc7OzlW6sEyZMkU0b95c2NnZiUaNGonZs2dLF5a6VN2LSXV+fh9myHMoRPkXxYoullqtVrRo0UKsW7eu0v0vWbJE9OnTR5w9e1acP39edO3aVecfWXWpuufwzJkzwsbGRnh7ewtLS0vRr18/cfv27UqPU9O/v7WlJudw06ZNIjg4uEr7Hz9+vAgJCRGXL18WMTExwtvbWyxduvQxWlw1VQluDwdvY/wd5BieKvDy8tIrU6vVOuUKhQJKpRKZmZl6dba2tkhOTq70ODXd7nFVt38Py8jIwH//+1+8/fbblR7nypUr6N69O06cOIGDBw/i0KFDWLVq1eN3oBLV7d/ly5fRoUMH/PTTT4iJiUFiYiJmzZpV6XEMdf6AxzuHGzZsQGhoKGxsbCo9ztWrVxEcHIy4uDjs2LEDGzZswDfffPP4HahEWf2rqLyqfa9suyd5Dmtq3rx5aNCgQZXG8MyaNQuHDh1Chw4d0K5dO6xYsQK7d+9+Aq2s/jlMSEhAq1atsH37dpw/fx6mpqYYN25cpcepj+dww4YNVfobWrruzp070apVKzzzzDOYO3fuEzmH5Z2nUvn5+fj000+lfhjj7yC/Lb2GTE1N9abItrS0RE5Ojl5daXl191nV7epCRf1zcHCQyiZNmoRnn30W/fv3r3SfGzZs0FmeO3cu1qxZgw8//LB2Gl0NFfVv1qxZOgFnxYoVGDp0qF77K9unIc9fWe0B9M9hUVERwsPD8euvv1Zpn5GRkdJ/e3l5YcqUKdi9ezdCQkJqr+G1oKo/v5VtZ+hzWJmoqCisXbsWMTExMDMzq/b2Li4uuH//PrRardF9rcHIkSMxcuRIaXndunXw8vKCWq2u8Nu269s5vH79Oq5fv46+ffvWaHsXFxfcvXu3lltVfY8Gb2P8HeQdnhpydHREWlqaTplGo4G5ubleXWl5dfdZ1e3qQkX9K7Vt2zYcOXIEmzdvrtExDPmLWpX+lXr4olCdfRry/JXVHkC/TUeOHIGTkxP8/PxqdAxj+WP7qOqc34q2M/Q5rEhiYiJCQ0Oxdu3aKp+/V155BSdOnJCWo6Oj0bhxY6MLO2VxcXFBcXFxpW+E1qdzCADffvstgoKCqhxYu3XrhqSkJGk5Ojoanp6eddW8KikN3jt27JD6YYy/gww8NeTv74/o6GhpOTExEVqtFo6Ojnp1cXFx8PDwqPY+q7pdXaiof0DJq63vvPMOdu3ahcaNG1dpn8b0i1pR/2p6UTCm81dWex49h0DJH9uhQ4dWaX+5ublo164dcnNzpTJj+GNblqr0vSrbGfoclic3NxdBQUEYPHgwhgwZgqysLGRlZUmv/arVahQUFOht165dO0ybNg0nTpzAnj17MGvWLEyYMOFJN79KZsyYgR07dkjL0dHRMDExQdOmTSvcrr6cw1IHDhxA79699cofPHiAoqIivfI2bdpg/Pjx+OOPP7Bt2zZ8+umnBj2H5QVvo/wdrJWRQP8QeGiwVkFBgWjUqJH0mt3YsWNFUFCQEKLk9T1LS0tx6NAhkZ+fL1588UUxefJkIYQQRUVFIjMzs8yBnnv37hVubm7izp07IjU1VXh4eIjdu3c/mc6Jqvfv77//Fq6urmLRokVCo9FIn1KZmZmisLBQb/9vvvmm6N+/v4iJiRFbt24VDRo0EFu3bq37jv1/Ve3fokWLRJcuXcTx48fFjz/+KBo3bizmz58v7ae8/p09e1Y0aNBAnD9/Xmg0GtGxY0excuXKuu/YQ6rax1JNmzYVv/76q05ZRT+jL7zwghg9erT4888/RVhYmDA1NRVHjx6tm86UAVUcEFpZ3431HFa1f3v27BEA9D6l63h6euq8SVkqPz9fjBkzRjRo0EC4urqKBQsWiIKCgrrpTDmq2sft27cLLy8vcfjwYXHw4EHRsmVLMXr0aKlepVKJ/Px8vf1U9Pf3Sahq/4QQIicnR5ibm4tLly6VuX5cXJxeeWZmpnjppZeElZWV8PT0rNJg9dr0cD9ycnKEn5+feOutt3SuBcXFxUb5O8jAUw2P/sDu3btXWFtbCycnJ9GoUSNx8eJFqW79+vXCzMxMODg4CC8vL5GamiqEECIxMVEAEJmZmXr7Ly4uFqNGjRJWVlbCyspKBAUFPZE3YEpVtX+fffZZmX9sH96Psf+iClF+/yq7KJTXPyGE+Oijj4S5ubmwtbUVnTt3fiLzDD2sOj+j169fF0qlUu/trIp+Rm/fvi0CAwOFhYWFaNWq1RMN5EJU72JSUd+N+RzKXXXO4Ycffijs7OyEo6OjmDJlis58WOWFOiHK//tLj+/h81RZ8Da230HF/z8w1VBqaipiY2MREBAAJycnnbrExERcvnwZPXr0qNIbMKX+/PNPZGdno1evXlAoFLXd5GqpqH9yUBf9S0hIwN27d9GrVy+jGDsg93NYkZr23djOIVVfTf/+Uu0ypt9BBh4iIiKSPQ5aJiIiItlj4CEiIiLZY+AhIiIi2WPgISIiItlj4CGieiU3NxdZWVlVWvfKlSu4dOlSlfc9ffp0XL58WVqOjY3F9u3bq91GIjI+DDxEZNROnDiBp59+WlretWsXWrdurTPjc3kWLlyIzz//vErHuX79OtauXQt7e3up7LPPPsPMmTORl5dX7XYTkXFh4CEio6PVarFx40bk5eXB0tJS52s9IiIiMGfOHFhZWUllBQUFKC4ulpZ9fHzQoEED/PTTT9ixYwfs7e2lj6WlZZnHDA8PR2hoKFxdXQEAFy9exC+//IJBgwbh/fffr6OeEtGTwsBDREansLAQb731lt7Em/Hx8fjtt98we/ZsODs7w8bGBlZWVrCxscGhQ4ek9aytrREREYH79+9jyJAh2LdvHx48eIA7d+6U+Z1omZmZCA8Pl75XLC8vD2+88QbWrl2LL774AufPn8esWbPAacuI6i8GHiIyOqWh5NFwMn/+fCxfvhzp6elIT0/Hq6++ipUrV0Kr1aJPnz7IyclBYWGhFJSUSiVefvllhIeHS2FFoVBACKHzSGzhwoXIzMyEmZkZsrOzMWTIEHTq1Akvv/wyTE1NsXfvXhw6dAj/+te/qjUmiIiMBwMPEdULx44dQ2xsLFasWIEzZ84AAJKSkvDUU08BAM6dO4fGjRvDxcUFFy5cwBtvvAFnZ2e8/vrriIyMRKNGjeDp6QmNRgMnJyfp0dXhw4exefNm+Pv7Iz8/H926dYOPjw9CQkKQnZ0NAEhPT8cnn3wCV1dXfPTRR4b5H0BEj8XU0A0gIqqKhIQErFixAnv27MHRo0fRqVMnnD9/Hu3btwcAdOrUCRqNBpmZmXBzc8ONGzdw5swZJCQk4K233oKVlRVSU1MREBCAW7duSftNTEzEokWLEBUVBXNzc3z11Vfo2LEjXF1dcfLkSXh7e+PAgQOIiYlBREQEioqKDPR/gIgeBwMPEdULEyZMAFDySGr16tUICgqCiYkJPD09pXWKioowdepU9O/fH46OjmjVqhV27tyJcePGYerUqfjmm2/0vkjyrbfeAgBERUUBADp27AgAyM/Ph7u7OwDAzMwMZmZmAEoekxFR/cPAQ0RG59SpUwCA4OBgXLp0Cc7OzlLdwIEDMW7cOMyYMQMhISE626WlpaGoqAhr164FADRt2hSbNm2CVqvFrVu3cP/+faxZs6bK7Xj4TTAiqt8YeIjI6Dg7O+Ptt99GSEgILCws8O6770p1lpaWePnll7Fx40a9OXZcXV2xc+dOHDt2DEqlEnfv3kWTJk2Ql5cHjUaDRo0aYd++fejUqRN++eWXco9/8+ZNuLm51VX3iMgAOGiZiIxOy5YtsX79evTq1Qumprr/Ljtw4AB27NgBV1dXrF69Wmf+HaDkrsyJEydw5coVuLq64tatW9i1axf69++PW7duYdmyZZXeufnyyy8RGBhY6/0iIsNh4CEio1b6OvmDBw8we/ZsvPzyy/jss89w5swZREVFoVu3bvjuu++Qk5MDAHpz95SlrHXy8vIghMCPP/6IDRs24L333pPqiouLOQcPUT3HR1pEZNS0Wi20Wi2mTp2KuLg4HD9+XPqqiejoaKxcuRKrVq3Czz//jMjISAgh0LNnT5iYmECj0aBZs2YoKChAXl4emjVrhtzcXOTk5MDe3h7bt29HcHAwgJJByjk5OVizZg22bt2K5s2b67WBiOovheA/W4ioHigsLIQQQnpbqq4IIap0l4iI6hcGHiIiIpI9juEhIiIi2WPgISIiItlj4CEiIiLZY+AhIiIi2WPgISIiItlj4CEiIiLZY+AhIiIi2WPgISIiItn7f6NQtS5Pm1+PAAAAAElFTkSuQmCC", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "plt.rcParams['font.family'] = ['SimHei'] # 设置中文字体\n", "plt.rcParams['axes.unicode_minus'] = False # 设置负号显示\n", "\n", "# 股票数据\n", "closing_prices = [100, 110, 120, 115, 105]  # 收盘价数据\n", "volume = [1000, 1500, 2000, 1800, 1200]  # 成交量数据\n", "\n", "# 绘制散点图\n", "plt.scatter(closing_prices, volume)\n", "\n", "# 设置图表标题和轴标签\n", "plt.title('股票收盘价与成交量关系')\n", "plt.xlabel('收盘价')\n", "plt.ylabel('成交量')\n", "\n", "# 显示图形\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": null, "id": "bd420664", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}