{"cells": [{"cell_type": "code", "execution_count": 1, "id": "f933b193", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0    3\n", "1    2\n", "2    0\n", "3    1\n", "dtype: int32\n"]}], "source": ["import pandas as pd\n", "import numpy as np\n", "a = np.array([3,2,0,1]) # 创建NumPy数组对象\n", "apples  = pd.Series(a)  # 创建Series对象\n", "print(apples)"]}, {"cell_type": "code", "execution_count": null, "id": "8a4541ba", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}