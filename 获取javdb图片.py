import os
import tkinter as tk
from tkinter import Scrollbar, Canvas
from tkinter import messagebox
from bs4 import BeautifulSoup
import urllib.request
from PIL import Image, ImageTk
from io import BytesIO
import urllib.parse

# 设置输入状态为英文
os.system("chcp 437")

class JavImageCrawler:
    def __init__(self, root):
        self.on_image_configure = None
        self.root = root
        self.root.title("JAV 图片搜索")
        self.root.geometry("1200x800")
        # 输入框部分
        self.input_frame = tk.Frame(self.root)
        self.input_frame.pack(fill=tk.X, padx=10, pady=10)

        self.keyword_label = tk.Label(self.input_frame, text="请输入关键字:")
        self.keyword_label.pack(side=tk.LEFT)

        self.keyword_entry = tk.Entry(self.input_frame)
        self.keyword_entry.pack(side=tk.LEFT, expand=True, fill=tk.X)
        # 设置光标位置到末尾，切换输入为英文
        #self.keyword_entry.icursor(tk.END)

        # 绑定粘贴事件
        self.keyword_entry.bind("<Button-1>", self.paste_clipboard)

        self.search_button = tk.Button(self.input_frame, text="搜索", command=self.search_images)
        self.search_button.pack(side=tk.LEFT)

        # 演员链接及演员名字显示部分

        self.text_frame = tk.Frame(self.root, height=5)
        self.text_frame.pack_propagate(False)
        self.text_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        self.text_label = tk.Label(self.text_frame, text="演员链接及名字")
        self.text_label.pack()

        self.text_scrollbar = Scrollbar(self.text_frame)
        self.text_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        self.text_canvas = Canvas(self.text_frame, yscrollcommand=self.text_scrollbar.set)
        self.text_canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        self.text_scrollbar.config(command=self.text_canvas.yview)

        self.text_inner_frame = tk.Frame(self.text_canvas)
        self.text_canvas.create_window((0, 0), window=self.text_inner_frame, anchor=tk.NW)

        self.text_scrollbar.bind("<Configure>", self.on_text_configure)
        self.text_canvas.bind("<Configure>", self.on_text_configure)


        # 图片显示部分，调整高度为 795 像素
        self.image_frame = tk.Frame(self.root, height=2000)
        self.image_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=1)

        self.image_label = tk.Label(self.image_frame, text="图片显示")
        self.image_label.pack()

        self.image_canvas = tk.Canvas(self.image_frame)
        self.image_canvas.pack(fill=tk.BOTH, expand=True)

        self.image_inner_frame = tk.Frame(self.image_canvas)
        self.image_canvas.create_window((0, 0), window=self.image_inner_frame, anchor=tk.NW)

        #self.image_scroll = Scrollbar(self.image_frame, orient=tk.VERTICAL, command=self.image_canvas.yview)
        #self.image_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        #self.image_canvas.config(yscrollcommand=self.image_scroll.set)

        self.image_inner_frame.bind("<Configure>", self.on_image_configure)

        # 将回车键绑定到搜索按钮
        self.root.bind("<Return>", lambda event: self.search_images())

    def on_text_configure(self, event):
        self.text_canvas.configure(scrollregion=self.text_canvas.bbox("all"))

    def paste_clipboard(self, event):
        # 清空输入框内容
        event.widget.delete(0, tk.END)
        # 获取剪贴板内容并粘贴到点击的输入框
        #clipboard_content = pyperclip.paste()
        #event.widget.insert(tk.END, clipboard_content)
    def search_images(self):
        keyword = self.keyword_entry.get()
        if keyword:
            # 对关键字进行URL编码
            encoded_keyword = urllib.parse.quote(keyword)
            url = f'https://javdb.com/search?q={encoded_keyword}&f=all'
            page = self.get_html_code(url)
            file_name = self.get_first_jpg_file_name(page)
            if file_name:
                new_url = f"https://javdb.com/v/{file_name}"
                page = self.get_html_code(new_url)
                actresses = self.get_actress_links(page)
                if actresses:
                    self.show_actress_links(actresses)
                else:
                    messagebox.showinfo("提示", "未找到演员链接")
            else:
                messagebox.showinfo("提示", "未找到以.jpg结尾的图片链接")
        else:
            messagebox.showinfo("提示", "请输入关键字")

    def get_html_code(self, url):
        headers = {'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/89.0.4389.82 Safari/537.36'}
        url_request = urllib.request.Request(url, headers=headers)
        page = urllib.request.urlopen(url_request).read()
        page = page.decode('utf-8')
        return page

    def get_first_jpg_file_name(self, page):
        soup = BeautifulSoup(page, 'html.parser')
        imgList = soup.find_all('img')
        for img in imgList:
            src = img.get('src')
            if src.endswith('.jpg'):
                file_name = src.split('/')[-1].replace('.jpg', '')
                return file_name
        return ""

    def get_actress_links(self, page):
        actresses = []
        soup = BeautifulSoup(page, 'html.parser')
        panel_blocks = soup.find_all('div', class_='panel-block')
        for panel_block in panel_blocks:
            strong_tag = panel_block.find('strong')
            if strong_tag and strong_tag.text.strip() == '演員:':
                actress_links = panel_block.find_all('a')
                for actress_link in actress_links:
                    actress_name = actress_link.text.strip()
                    actress_href = "https://javdb.com" + actress_link.get('href')
                    actresses.append((actress_name, actress_href))  # 将演员名字和链接作为元组保存到列表中
        return actresses

    def show_actress_links(self, actresses):
        # 清空链接显示部分的内容
        for widget in self.text_inner_frame.winfo_children():
            widget.destroy()

        for i, (actress_name, actress_href) in enumerate(actresses):
            # 创建标签，并设置鼠标指针为手指
            actress_label = tk.Label(self.text_inner_frame, text=f"演员名字: {actress_name}\n演员链接: {actress_href}",
                                     fg="blue", cursor="hand2")
            actress_label.grid(row=i, column=0, sticky="w", padx=10, pady=5)
            # 使用lambda函数，传递当前循环迭代的演员名字和链接
            actress_label.bind("<Button-删除扩展名中的汉字>",
                   lambda event, url=actress_href, name=actress_name: self.on_link_click(event, url, name))


    def on_link_click(self, event, url, actress_name):
        # 获取页面内容和图片链接
        page = self.get_html_code(url)
        image_urls = self.get_image_urls(page)
        # 确保图片数量不超过20个
        if len(image_urls) > 20:
            image_urls = image_urls[:20]
        # 调用 show_images 方法显示图片
        self.show_images(image_urls, actress_name)

    def get_image_urls(self, page):
        image_urls = []
        soup = BeautifulSoup(page, 'html.parser')
        img_tags = soup.find_all('img')
        for img_tag in img_tags:
            src = img_tag.get('src')
            if src.endswith('.jpg'):
                image_urls.append(src)
        return image_urls

    def getImageData(self, url):
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/89.0.4389.82 Safari/537.36'
        }
        request = urllib.request.Request(url, headers=headers)
        image_data = urllib.request.urlopen(request).read()
        return image_data

    def show_images(self, image_urls, actress_name):
        def save_image(image_url):
            directory = "d:/download/"  # 保存图像的目录
            if not os.path.exists(directory):
                os.makedirs(directory)
            filename = os.path.join(directory, f"{actress_name}.jpg")  # 使用演员名作为文件名
            image_data = self.getImageData(image_url)  # 获取图片数据
            with open(filename, 'wb') as f:
                f.write(image_data)  # 将图片数据写入文件
            messagebox.showinfo("Save Image", f"图片保存至 {filename}")  # 提示保存成功

        self.image_inner_frame.destroy()  # 清除原有的图片显示

        self.image_inner_frame = tk.Frame(self.image_canvas)
        self.image_canvas.create_window((0, 0), window=self.image_inner_frame, anchor=tk.NW)

        row = 0
        col = 0
        for image_url in image_urls:
            image_data = self.getImageData(image_url)
            image = Image.open(BytesIO(image_data))
            resized_image = image.resize((300, 200), Image.LANCZOS)  # 调整图片大小
            photo = ImageTk.PhotoImage(resized_image)
            label = tk.Label(self.image_inner_frame, image=photo)
            label.grid(row=row, column=col, padx=5, pady=5)
            label.image = photo  # 保持对PhotoImage对象的引用
            label.bind("<Button-删除扩展名中的汉字>", lambda event, url=image_url: save_image(url))  # 添加点击事件，保存图像
            col += 1
            if col == 5:  # 每行显示5张图片
                col = 0
                row += 1

        def on_configure(event):
            self.image_canvas.configure(scrollregion=self.image_canvas.bbox("all"))

        self.image_inner_frame.bind("<Configure>", on_configure)

        # 添加鼠标滚动事件
        self.image_canvas.bind_all("<MouseWheel>",
                                   lambda event: self.image_canvas.yview_scroll(int(-1 * (event.delta / 120)), "units"))

        def on_configure(event):
            self.image_canvas.configure(scrollregion=self.image_canvas.bbox("all"))

        self.image_inner_frame.bind("<Configure>", on_configure)

        # 添加鼠标滚动事件
        self.image_canvas.bind_all("<MouseWheel>", lambda event: self.image_canvas.yview_scroll(int(-1 * (event.delta / 120)), "units"))


if __name__ == '__main__':
    root = tk.Tk()
    app = JavImageCrawler(root)
    root.mainloop()
