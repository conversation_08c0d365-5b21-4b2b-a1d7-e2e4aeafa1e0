import os
import urllib.parse
import tkinter as tk
from tkinter import filedialog


def get_video_files(directory):
    video_files = []
    video_extensions = ('.mp4', '.avi', '.mkv', '.mov')

    for root, _, files in os.walk(directory):
        for file in files:
            if file.endswith(video_extensions):
                full_path = os.path.join(root, file)
                video_files.append(full_path)
                print(f"Found video file: {full_path}")  # 打印搜集到的视频文件完整路径

    return video_files


def create_strm_files(video_files, input_base_folder, output_base_folder):
    base_url = "http://192.168.2.25:5678/d/"
    
    for full_path in video_files:
        # Compute the relative path from the input base folder
        relative_path = os.path.relpath(full_path, input_base_folder)
        # Encode the relative path to ensure it is URL safe
        encoded_path = urllib.parse.quote(relative_path)
        url = base_url + encoded_path

        # Determine the output folder and create it if necessary
        output_folder = os.path.join(output_base_folder, os.path.dirname(relative_path))
        if not os.path.exists(output_folder):
            os.makedirs(output_folder)
            print(f"Created directory: {output_folder}")

        # Create the .strm file with the encoded URL
        strm_file_name = os.path.splitext(os.path.basename(full_path))[0] + ".strm"
        strm_file_path = os.path.join(output_folder, strm_file_name)

        with open(strm_file_path, 'w') as f:
            f.write(url)

        print(f"Created .strm file: {strm_file_path} with URL: {url}")


def main():
    root = tk.Tk()
    root.withdraw()

    # 第一次选择文件夹
    folder_selected_1 = filedialog.askdirectory(title="选择包含视频文件的文件夹")

    if folder_selected_1:
        video_files = get_video_files(folder_selected_1)

        # 第二次选择文件夹
        folder_selected_2 = filedialog.askdirectory(title="选择创建strm文件的目标文件夹")

        if folder_selected_2:
            create_strm_files(video_files, folder_selected_1, folder_selected_2)
            print(f".strm files created in {folder_selected_2}")


if __name__ == "__main__":
    main()
