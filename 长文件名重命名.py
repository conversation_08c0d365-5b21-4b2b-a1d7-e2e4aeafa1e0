import os
import tkinter as tk
from tkinter import filedialog


def rename_long_filenames(folder_path, max_length=30):
    # 遍历文件夹及其子文件夹
    for root, dirs, files in os.walk(folder_path):
        for file_name in files:
            # 获取文件路径
            full_file_path = os.path.join(root, file_name)
            # 分离文件名和扩展名
            name, ext = os.path.splitext(file_name)
            # 如果文件名超过指定长度，进行重命名
            if len(name) > max_length:
                # 构造新的文件名
                new_name = name[:max_length] + ext
                new_file_path = os.path.join(root, new_name)

                # 检查文件名冲突
                counter = 1
                while os.path.exists(new_file_path):
                    new_name = f"{name[:max_length]}-{counter}{ext}"
                    new_file_path = os.path.join(root, new_name)
                    counter += 1

                # 重命名文件
                os.rename(full_file_path, new_file_path)
                print(f"重命名: {full_file_path} -> {new_file_path}")


def open_folder_dialog():
    # 创建一个临时的Tkinter窗口
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口
    # 打开文件夹选择对话框
    folder_path = filedialog.askdirectory(title="选择文件夹")
    if folder_path:
        rename_long_filenames(folder_path)


if __name__ == "__main__":
    open_folder_dialog()
