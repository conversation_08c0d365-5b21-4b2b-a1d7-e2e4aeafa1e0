{"cells": [{"cell_type": "code", "execution_count": 5, "id": "c57e57af", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[1 2 3]\n"]}], "source": ["import numpy as np\n", "a = np.array([1, 2, 3])\n", "print(a)"]}, {"cell_type": "code", "execution_count": 6, "id": "e9ce7a85", "metadata": {}, "outputs": [{"data": {"text/plain": ["array([1, 2, 3])"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["a"]}, {"cell_type": "code", "execution_count": null, "id": "e7d230ba", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}