import os
import re
import shutil
import requests
from PIL import Image
from selenium import webdriver
from selenium.webdriver.common.by import By
from bs4 import BeautifulSoup
import tkinter as tk
from tkinter import filedialog, messagebox
import logging
from selenium.webdriver.firefox.service import Service
from selenium.webdriver.firefox.options import Options
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC


def clean_title(title):
    """
    清理抓取到的标题，替换不必要的字符，提取 `!` 前的文字（若存在 `!`），
    并限制标题长度不超过 42 个字符。
    """
    # 替换不必要的字符
    cleaned_title = (
        title.replace("：", " ")
        .replace(":", " ")
        .replace("\n", " ")
        .replace("\t", " ")
        .replace("、", " ")
        .replace("?", " ")
        .replace("~", " ")
        .strip()
        .replace("  ", " ")
    )

    # 检查是否存在 `!`，如果存在提取 `!` 前的文字
    if "！" in cleaned_title:
        cleaned_title = cleaned_title.split("!")[0].strip()

    # 限制标题长度不超过 42 个字符
    if len(cleaned_title) > 50:
        cleaned_title = cleaned_title[:50].strip()

    return cleaned_title


def extract_cd_info(file_name):
    """
    提取文件名中的多盘 CD 信息，例如 CD1、CD2 或 Disc1、Disc2 等
    """
    pattern = re.compile(r"(CD\d+|Disc\d+)", re.IGNORECASE)
    match = pattern.search(file_name)
    return match.group() if match else None


def extract_number(file_name):
    """
    从文件名中提取番号
    """
    pattern = re.compile(
        r"(\d{6}[-_]\d{3})|"  # 匹配形式为123456-789或123456_789
        # r"([a-zA-Z]{3}-\d{3})|"  # 匹配形式为abc-123，不区分大小写
        # r"([a-zA-Z]{4}-\d{3})|"  # 匹配形式为abcd-123，不区分大小写
        # r"([a-zA-Z]{5}-\d{4})|"  # 匹配形式为abcde-123，不区分大小写
        r"(heyzo[_ -]?\d{4})|"  # 匹配形式为heyzo-0123，不区分大小写
        r"([nN]\d{4})"  # 匹配形式为n0123或N0123
        r"([a-zA-Z]{2}-\d{3})|"  # 匹配形式为ab-123，不区分大小写
        r"([a-zA-Z]{2}-\d{4})|"  # 匹配形式为ab-1234，不区分大小写
        r"([a-zA-Z]{3}-\d{3})|"  # 匹配形式为abc-123，不区分大小写
        r"([a-zA-Z]{4}-\d{4})|"  # 匹配形式为abcd-1234，不区分大小写
        r"([a-zA-Z]{4}-\d{3})|"  # 匹配形式为abcd-123，不区分大小写
        r"([a-zA-Z]{5}-\d{3})|",  # 匹配形式为abcde-123，不区分大小写
        re.IGNORECASE
    )
    match = pattern.search(file_name)
    return match.group() if match else None


def scrape_javdb(search_url, video_folder):
    """
    从 JavDB 抓取视频信息，使用 Waterfox 浏览器
    """
    # 配置 Waterfox
    firefox_options = Options()
    
    # 添加 SSL 证书相关设置
    firefox_options.set_preference("security.ssl.enable_ocsp_stapling", False)
    firefox_options.set_preference("security.ssl.enable_ocsp_must_staple", False)
    firefox_options.set_preference("security.tls.version.min", 1)
    firefox_options.set_preference("security.tls.version.max", 4)
    firefox_options.accept_insecure_certs = True
    
    # 设置 Waterfox 的路径
    waterfox_path = r"C:\Program Files\Waterfox\waterfox.exe"
    if not os.path.exists(waterfox_path):
        logging.error(f"Waterfox 浏览器未找到: {waterfox_path}")
        raise FileNotFoundError(f"请确认 Waterfox 浏览器已安装在: {waterfox_path}")
    
    firefox_options.binary_location = waterfox_path
    
    # 设置 geckodriver 路径
    geckodriver_path = r"D:\360Downloads\geckodriver.exe"
    if not os.path.exists(geckodriver_path):
        logging.error(f"geckodriver 未找到: {geckodriver_path}")
        raise FileNotFoundError(f"请下载 geckodriver 并放置在: {geckodriver_path}")
    
    service = Service(geckodriver_path)
    
    try:
        # 初始化浏览器
        driver = webdriver.Firefox(service=service, options=firefox_options)
        
        # 设置页面加载超时时间(秒)
        driver.set_page_load_timeout(30)
        
        # 尝试访问网页
        logging.info(f"正在访问: {search_url}")
        driver.get(search_url)
        
        # 等待页面加载完成(最多等待30秒)
        WebDriverWait(driver, 30).until(
            EC.presence_of_element_located((By.TAG_NAME, "body"))
        )
        
        # 检查是否成功加载页面
        if "JavDB" not in driver.title:
            logging.error("页面加载失败，可能需要科学上网")
            raise Exception("无法访问网站，请检查网络连接")
            
        result = {
            "title": None,
            "original_title": None,
            "number": None,
            "img_url": None,
            "keywords": [],
            "actor_name": None,
            "release_date": None,
            "duration": None,
            "producer": None,
            "success": False,
            "error": None,
        }

        try:
            # 查找第一个匹配的链接并跳转
            if driver.current_url.startswith("https://javdb.com/search?q"):
                try:
                    link = driver.find_element(By.CSS_SELECTOR, 'a[href^="/v/"]')
                    href = link.get_attribute('href')
                    #logging.info(f"跳转到详情页: {href}")
                    driver.get(href)
                except Exception as e:
                    result['error'] = f"未找到匹配内容: {str(e)}"
                    return result

            # 使用 BeautifulSoup 解析详情页
            soup = BeautifulSoup(driver.page_source, "html.parser")

            # 提取番号和影片标题
            h2_tag = soup.find('h2', class_='title is-4')
            if h2_tag:
                strong_tags = h2_tag.find_all('strong')
                if len(strong_tags) >= 1:
                    # 提取番号
                    result['number'] = strong_tags[0].text.strip().split()[0]

                    # 提取影片的原始标题
                    if len(strong_tags) >= 2:
                        original_title = strong_tags[1].text.strip()
                        original_title = re.sub(r'\s+', ' ', clean_title(original_title))
                        result['original_title'] = f"{result['number']} {original_title}"

            # 提取封面图片 URL
            img_tag = soup.find('img', class_='video-cover')
            if img_tag:
                result['img_url'] = img_tag['src']

            # 提取关键词
            links = soup.find_all('a', href=True)
            for link in links:
                if '/tags/uncensored' in link['href']:
                    result['keywords'].append(link.text.strip())

            # 提取演员名字
            strong_tags = soup.find_all('strong')
            for tag in strong_tags:
                if tag.text.strip() == '演員:':
                    next_a_tag = tag.find_next('a', href=True)
                    if next_a_tag:
                        result['actor_name'] = next_a_tag.text.strip()
                        break

            # 提取发行日期
            for tag in strong_tags:
                if tag.text.strip() == '日期:':
                    next_span_tag = tag.find_next('span', class_='value')
                    if next_span_tag:
                        result['release_date'] = next_span_tag.text.strip()
                        break

            # 提取时长
            for tag in strong_tags:
                if tag.text.strip() == '時長:':
                    next_span_tag = tag.find_next('span', class_='value')
                    if next_span_tag:
                        result['duration'] = next_span_tag.text.strip()
                        break

            # 提取片商信息
            for tag in strong_tags:
                if tag.text.strip() == '片商:':
                    next_span_tag = tag.find_next('span', class_='value')
                    if next_span_tag and next_span_tag.a:
                        result['producer'] = next_span_tag.a.text.strip()
                        break

            # 根据番号决定最终标题
            if re.match(r"(heyzo|kb\d{4})", result['original_title'].lower()):
                result['title'] = result['original_title']
            else:
                # 如果初始文件夹路径包含youma（不区分大小写），则不加演员名字
                if 'youma' in video_folder.lower():
                    result['title'] = result['original_title']
                else:
                    result['title'] = f"{result['original_title']} {result['actor_name'] or ''}".strip()

            result['success'] = True

        except Exception as e:
            result['error'] = f"抓取失败: {str(e)}"

        finally:
            driver.quit()

        return result

    except Exception as e:
        logging.error(f"抓取失败: {str(e)}")
        return {
            "success": False,
            "error": str(e),
        }


def download_image(img_url, save_path):
    """
    下载图片
    """
    try:
        response = requests.get(img_url, stream=True)
        response.raise_for_status()
        with open(save_path, "wb") as file:
            for chunk in response.iter_content(chunk_size=8192):
                file.write(chunk)
        #print(f"图片已下载: {save_path}")
        return save_path
    except Exception as e:
        logging.error(f"图片下载失败: {e}")
        return None


def crop_image_to_poster(fanart_path, poster_path):
    """
    从 fanart 裁剪出 poster 图片（右上角竖版部分）
    """
    try:
        with Image.open(fanart_path) as img:
            width, height = img.size
            new_width = 360  # 竖版宽度
            left = width - new_width
            top = 0
            right = width
            bottom = height

            # 裁剪
            cropped_image = img.crop((left, top, right, bottom))
            cropped_image.save(poster_path)
            print(f"Poster 图片已保存: {poster_path}")
    except Exception as e:
        logging.error(f"裁剪 poster 图片失败: {e}")


def create_nfo_content(title, actor_name, release_date, duration, producer, keywords):
    """
    根据提供的影片信息生成 NFO 文件内容，符合您提供的格式要求。

    :param title: 影片标题
    :param actor_name: 演员名字
    :param release_date: 发行日期
    :param duration: 时长
    :param producer: 片商
    :param keywords: 关键词列表
    :return: 生成的 NFO 内容字符串
    """
    # 排除的关键字列表
    excluded_keywords = ["無碼", "獨佔動畫", "企劃物", "1080p", "60fps", "HEYZO"]

    # 生成 <genre> 标签
    genre_tags = "\n".join([
        f"    <genre>{keyword.strip()}</genre>" for keyword in keywords
        if keyword.strip() and
           keyword.strip() not in excluded_keywords and
           not any(char.isdigit() for char in keyword.strip()) and
           "HEYZO" not in keyword.strip()
    ])

    # 构造 NFO 文件内容
    nfo_content = f"""
<movie>
    <title>{title}</title>
    <originaltitle>{title}</originaltitle>
    <actor>
        <name>{actor_name}</name>
        <type>Actor</type>
    </actor>
    <releasedate>{release_date}</releasedate>
    <runtime>{duration}</runtime>
    <studio>{producer}</studio>
{genre_tags}
</movie>
"""
    return nfo_content.strip()


def select_folder(prompt):
    """
    弹出对话框选择文件夹
    """
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口
    folder = filedialog.askdirectory(title=prompt)
    if not folder:
        messagebox.showerror("错误", f"您未选择{prompt}，程序即将退出。")
        raise SystemExit
    return folder


def main():
    # 使用对话框选择视频文件夹和目标文件夹
    video_folder = select_folder("请选择存放视频文件的文件夹")
    target_folder = select_folder("请选择目标文件夹")

    # 遍历视频文件
    for file_name in os.listdir(video_folder):
        try:
            # 从文件名中提取番号
            original_number = extract_number(file_name)
            print(file_name, f"提取到的番号：", original_number)
            if not original_number:
                print(f"文件 {file_name} 未发现番号，跳过")
                continue

            # 从网页抓取番号
            search_url = f"https://javdb.com/search?q={original_number}&f=all"
            scrape_result = scrape_javdb(search_url, video_folder)

            if not scrape_result["success"]:
                logging.error(f"抓取失败：跳过文件 {file_name}")
                continue

            # 从抓取结果中提取番号
            scraped_number = scrape_result["number"]
            if not scraped_number:
                logging.error(f"抓取结果中未找到番号：跳过文件 {file_name}")
                continue

            # 对比番号
            if original_number.lower() != scraped_number.lower():
                logging.warning(
                    f"番号不匹配！文件名提取：{original_number}，网页抓取：{scraped_number}，跳过处理 {file_name}")
                continue

            print(f"番号匹配成功：{original_number}")

            # 如果番号匹配，继续处理（文件移动、生成 NFO 等）
            scraped_title = scrape_result["title"]
            print(f"提取的标题：", scraped_title)
            #scraped_number = original_number

            # 检查是否为多 CD 文件
            cd_info = extract_cd_info(file_name)

            # 构造文件夹路径（忽略多盘信息）
            movie_folder_path = os.path.join(target_folder, scraped_title)
            os.makedirs(movie_folder_path, exist_ok=True)

            # 移动视频文件（文件名包含番号和标题信息，并保留多盘信息）
            new_file_name = f"{scraped_title}"
            if cd_info:
                new_file_name = f"{new_file_name} - {cd_info}"
            new_file_name += os.path.splitext(file_name)[-1]
            video_target_path = os.path.join(movie_folder_path, new_file_name)
            shutil.move(os.path.join(video_folder, file_name), video_target_path)
            print(f"视频文件已移动: {video_target_path}")


            # 下载封面图片并生成 poster
            fanart_name = f"{scraped_title}"
            if cd_info:
                fanart_name += f" - {cd_info}"
            fanart_path = os.path.join(movie_folder_path, f"{fanart_name}-fanart.jpg")

            if scrape_result["img_url"]:
                downloaded_fanart_path = download_image(scrape_result["img_url"], fanart_path)
                poster_name = f"{scraped_title}"
                if cd_info:
                    poster_name += f" - {cd_info}"
                poster_path = os.path.join(movie_folder_path, f"{poster_name}-poster.jpg")
                crop_image_to_poster(downloaded_fanart_path, poster_path)

            # 写入信息到 .nfo 文件
            try:
                if cd_info:
                    nfo_file_path = os.path.join(movie_folder_path,
                                                 f"{scraped_title} - {cd_info}.nfo")
                else:
                    nfo_file_path = os.path.join(movie_folder_path, f"{scraped_title}.nfo")

                nfo_content = create_nfo_content(
                    title=scrape_result["title"],
                    actor_name=scrape_result["actor_name"],
                    release_date=scrape_result["release_date"],
                    duration=scrape_result["duration"],
                    producer=scrape_result["producer"],
                    keywords=scrape_result["keywords"],
                )

                with open(nfo_file_path, "w", encoding="utf-8") as file:
                    file.write(nfo_content)
                    print(f"NFO 文件已成功写入：{nfo_file_path}")

            except Exception as e:
                logging.error(f"写入 NFO 文件失败：{e}")


        except Exception as e:
            logging.error(f"处理文件 {file_name} 时发生错误: {e}")


if __name__ == "__main__":
    main()
