{"cells": [{"cell_type": "code", "execution_count": 3, "id": "ec87bfb0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["i =  317\n", "i * i = 100489\n"]}], "source": ["i = 0\n", "\n", "while i * i < 100000:\n", "    i += 1\n", "\n", "print(\"i = \", i)\n", "print(\"i * i =\", (i * i))"]}, {"cell_type": "code", "execution_count": null, "id": "e9722702", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}