import shutil
import subprocess
import time
from bs4 import BeautifulSoup
import webbrowser
import os
import re
import tkinter as tk
from tkinter import filedialog
from PIL import Image

def extract_number(file_name):
    pattern = re.compile(
        r"(\d{6}[-_]\d{3})|"  # 匹配形式为123456-789或123456_789
        #r"([a-zA-Z]{3}-\d{3})|"  # 匹配形式为abc-123，不区分大小写
        #r"(sw-\d{3})|"  # 匹配形式为sw-123，不区分大小写
        #r"(nhtda-\d{3})|"  # 匹配形式为nhtda-123，不区分大小写
        r"(heyzo[_ -]?\d{4})|" # 匹配形式为heyzo-0123，不区分大小写
        r"([nN]\d{4})",  # 匹配形式为n123或N123
        re.IGNORECASE
    )
    match = pattern.search(file_name)
    if match:
        return match.group()
    return None

def select_folder():
    root = tk.Tk()
    root.withdraw()  # 隐藏根窗口
    folder_selected = filedialog.askdirectory()  # 弹出对话框选择文件夹
    return folder_selected

def generate_url(number):
    return 'https://www.javbus.com/{}'.format(number)

def extract_info_from_html(file_name):
    with open(file_name, 'r', encoding='utf-8') as file:
        soup = BeautifulSoup(file, 'html.parser')
        # 提取影片名称
        container_div = soup.find('div', class_='container')
        h3_tag = container_div.find_next('h3')
        original_title = h3_tag.text.strip() if h3_tag else "No file name found"
        # 如果原始标题中包含换行符，则替换为 '-'
        original_title = original_title.replace('\n', '-')
        # 提取关键词
        pre_keywords = soup.find('meta', attrs={'name': 'keywords'})['content']
        keywords = re.sub(r'\b\d{6}-\d{3}\b', '', pre_keywords)
        # 提取演员名字
        actor_span = soup.find('div', id='avatar-waterfall').find('span')  # 找到包含演员名字的span标签
        actor_name = actor_span.text.strip()


        print("影片名称: {}, 关键词: {}, 演员名字: {}".format(original_title, keywords, actor_name))

        # 提取发行日期
        description_meta = soup.find('meta', attrs={'name': 'description'})['content']
        release_date_match = re.search(r'【發行日期】(\d{4}-\d{2}-\d{2})', description_meta)
        release_date = release_date_match.group(1) if release_date_match else "No release date found"

        print("发行日期:", release_date)

        # 提取时长
        description_meta = soup.find('meta', attrs={'name': 'description'})['content']
        duration_match = re.search(r'【長度】(\d+)分鐘', description_meta)
        duration = duration_match.group(1) if duration_match else "No duration found"

        print("提取到的时长信息:", duration)

        # 提取制作商信息
        producer_paragraphs = container_div.find_all('p')
        for paragraph in producer_paragraphs:
            if '製作商:' in paragraph.text:
                producer_element = paragraph.find('a')
                producer = producer_element.text if producer_element else "No producer found"
                break
        else:
            producer = "No producer found"
            # 调整影片名称为原名字 + 演员名
        print("制作商:", producer)
        title = f"{original_title} {actor_name}"

        return title, keywords, actor_name, release_date, duration, producer, original_title


def create_nfo_content(title, actor_name, release_date, duration, producer, keywords):
    # 将关键字以逗号分隔为列表
    keyword_list = keywords.split(',')

    excluded_keywords = ["カリビアンコム", "獨佔動畫", "企劃物", "1080p", "60fps", "HEYZO"]

    genre_tags = "\n".join([f"<genre>{keyword.strip()}</genre>" for keyword in keyword_list if
                            keyword.strip() and keyword.strip() not in excluded_keywords and not any(
                                char.isdigit() for char in keyword.strip()) and "HEYZO" not in keyword.strip()])

    nfo_content = f"""
<movie>
    <title>{title}</title>
    <originaltitle>{title}</originaltitle>
    <actor>
    <name>{actor_name}</name>
    <type>Actor</type>
    </actor>
    <releasedate>{release_date}</releasedate>
    <runtime>{duration}</runtime>
    <studio>{producer}</studio>
    {genre_tags}
</movie>
"""
    return nfo_content


def create_movie_folder(folder_path, movie_name):
    movie_folder_path = os.path.join(folder_path, movie_name)
    if not os.path.exists(movie_folder_path):
        os.makedirs(movie_folder_path)
    return movie_folder_path

def main():
    folder_path = select_folder()  # 选择文件夹
    if folder_path:
        print("选择的文件夹:", folder_path)
        video_files = [f for f in os.listdir(folder_path) if f.endswith((".mp4", ".mkv", ".avi", ".wmv"))]
        if video_files:
            print("找到的视频文件:")
            for file_name in video_files:
                try:
                    number = extract_number(file_name)
                    if number:
                        file_path = 'd:\\tt\\page_source.html'
                        if os.path.exists(file_path):
                            os.remove(file_path)
                        url = generate_url(number)
                        browser_path = "C:/Program Files/Waterfox/waterfox.exe"
                        subprocess.run([browser_path, url])
                        print("文件名: {}, 番号: {}".format(file_name, number))
                        time.sleep(10)  # 暂停10秒
                        title, keywords, actor, release_date, duration, producer, original_title = extract_info_from_html('d:\\tt\\page_source.html')
                        # 从 original_title 中提取番号
                        extracted_number = extract_number(original_title)

                        # 如果提取的番号与文件名的番号不一致，跳到下一个文件
                        if extracted_number != number:
                            print(f"文件 {file_name} 的番号与提取的番号不一致，跳过该文件。")
                            continue
                        # 创建Emby格式的nfo内容
                        nfo_content = create_nfo_content(title, actor, release_date, duration, producer, keywords)

                        # 创建以影片名命名的文件夹
                        movie_folder_path = create_movie_folder('V:\\wuma\\亚洲\\=m=', title)

                        # 检测是否包含特定的cd标记，并添加到文件名
                        cd_tag = ""
                        for cd in ['cd1', 'cd2', 'cd3', 'cd4']:
                            if cd in file_name.lower():
                                cd_tag = f"-{cd}"
                                break

                        # 更新文件名以包含CD标记
                        title_with_cd = f"{title}{cd_tag}"
                        new_video_name = f"{title_with_cd}{os.path.splitext(file_name)[1]}"

                        original_video_path = os.path.join(folder_path, file_name)
                        new_video_path = os.path.join(movie_folder_path, new_video_name)
                        os.rename(original_video_path, new_video_path)

                        # 移动图片和写入nfo文件时也应使用更新后的影片名
                        new_jpg_name = f"{title_with_cd}-poster.jpg"
                        new_jpg_path = os.path.join(movie_folder_path, new_jpg_name)

                        jpg_file_path = os.path.join('d:\\tt\\', f"{original_title}.jpg")
                        if os.path.exists(jpg_file_path):
                            # 打开原始图片
                            original_image = Image.open(jpg_file_path)

                            # 计算裁剪区域，以右上角为基点裁剪
                            width, height = original_image.size
                            new_width = 360
                            new_height = 536
                            right = width
                            top = 0
                            left = right - new_width
                            bottom = top + new_height

                            # 裁剪图片
                            cropped_image = original_image.crop((left, top, right, bottom))

                            # 保存裁剪后的图片
                            cropped_image.save(new_jpg_path)

                            # 移动原始 jpg 文件到影片文件夹
                            shutil.move(jpg_file_path, os.path.join(movie_folder_path, f"{title_with_cd}-fanart.jpg"))

                        # 写入信息到.nfo文件
                        nfo_file_path = os.path.join(movie_folder_path, f"{title_with_cd}.nfo")
                        with open(nfo_file_path, 'w', encoding='utf-8') as nfo_file:
                            nfo_file.write(nfo_content)
                        # 重命名视频文件为影片名（保留原始文件类型后缀）
                        new_video_name = f"{title}{os.path.splitext(file_name)[1]}"
                        original_video_path = os.path.join(folder_path, file_name)
                        new_video_path = os.path.join(movie_folder_path, new_video_name)
                        os.rename(original_video_path, new_video_path)


                    else:
                        print("文件名: {}, 未找到番号".format(file_name))
                except Exception as e:
                    print(f"处理文件 {file_name} 时出现异常: {e}")
                    continue
        else:
            print("在所选文件夹中未找到视频文件。")

if __name__ == "__main__":
    main()
