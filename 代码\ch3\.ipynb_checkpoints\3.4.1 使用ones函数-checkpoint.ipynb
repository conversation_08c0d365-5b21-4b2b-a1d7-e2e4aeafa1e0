{"cells": [{"cell_type": "code", "execution_count": null, "id": "6f0a7e0c", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "a = np.ones((2, 3))\n", "print(a)\n", "print(a.dtype)\n", "b = np.ones((2, 3), dtype='i4')\n", "print(b)\n", "print(b.dtype)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}