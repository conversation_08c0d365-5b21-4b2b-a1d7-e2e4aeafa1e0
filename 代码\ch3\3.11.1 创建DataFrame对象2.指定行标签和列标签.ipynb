{"cells": [{"cell_type": "code", "execution_count": 3, "id": "4cfe0007", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["------df1-------\n", "   apples  oranges  bananas\n", "0       3        0        1\n", "1       2        1        2\n", "2       0        2        1\n", "3       1        3        0\n", "------df2-------\n", "        apples  oranges  bananas\n", "June         3        0        1\n", "Robert       2        1        2\n", "Lily         0        2        1\n", "David        1        3        0\n"]}], "source": ["import pandas as pd\n", "L =[[3,0,1], [2,1,2],  [0,2,1], [1,3,0]]\n", "df1 = pd.DataFrame(L,columns=['apples','oranges','bananas']) # 指定列标签\n", "print(\"------df1-------\")\n", "print(df1)\n", "\n", "df2 = pd.DataFrame(L, \n", "                   columns=['apples','oranges','bananas'],   # 指定列标签\n", "                   index=['June','<PERSON>','<PERSON>','<PERSON>'])   # 指定行标签\n", "print(\"------df2-------\")\n", "print(df2)"]}, {"cell_type": "code", "execution_count": null, "id": "852b0c0a", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}