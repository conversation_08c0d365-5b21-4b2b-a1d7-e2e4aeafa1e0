import os
import csv

def remove_string_in_folder_names(folder_path, string_to_remove):
    folders_renamed = 0
    files_renamed = 0
    skipped_items = 0

    for root, dirs, files in os.walk(folder_path, topdown=False):
        for dir_name in dirs:
            if string_to_remove in dir_name:
                old_dir_path = os.path.join(root, dir_name)
                new_dir_name = dir_name.replace(string_to_remove, "")
                new_dir_path = os.path.join(root, new_dir_name)
                if os.path.exists(new_dir_path):
                    skipped_items += 1
                    continue
                os.rename(old_dir_path, new_dir_path)
                folders_renamed += 1

        for filename in files:
            if string_to_remove in filename:
                old_file_path = os.path.join(root, filename)
                new_filename = filename.replace(string_to_remove, "")
                new_file_path = os.path.join(root, new_filename)
                if os.path.exists(new_file_path):
                    skipped_items += 1
                    continue
                os.rename(old_file_path, new_file_path)
                files_renamed += 1

    return folders_renamed, files_renamed, skipped_items

def load_strings_from_csv(csv_path):
    try:
        with open(csv_path, mode='r', newline='') as file:
            reader = csv.reader(file)
            return [row[0] for row in reader if row]
    except FileNotFoundError:
        return []

def remove_strings_from_folder(folder_path, csv_path='strings.csv'):
    strings = load_strings_from_csv(csv_path)
    for string_to_remove in strings:
        folders_renamed, files_renamed, skipped_items = remove_string_in_folder_names(folder_path, string_to_remove)
        print(f"处理字符串 '{string_to_remove}':\n  文件夹重命名：{folders_renamed}\n  文件重命名：{files_renamed}\n  跳过数量：{skipped_items}")
