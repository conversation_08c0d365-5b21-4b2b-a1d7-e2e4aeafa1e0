{"cells": [{"cell_type": "code", "execution_count": 1, "id": "0893422b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[[9.90263869e+067 8.01304531e+262]\n", " [2.60799828e-310 0.00000000e+000]]\n", "float64\n", "[[9.90263869e+067 8.01304531e+262]\n", " [2.60799828e-310 0.00000000e+000]]\n", "float64\n"]}], "source": ["import numpy as np\n", "e = np.empty([2, 2])\n", "print(e)\n", "print(e.dtype)\n", "\n", "f = np.empty((2, 2), dtype=float)\n", "print(f)\n", "print(f.dtype)\n"]}, {"cell_type": "code", "execution_count": null, "id": "06404435", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}