{"cells": [{"cell_type": "code", "execution_count": 2, "id": "8af5820c", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "\n", "# 准备数据\n", "data = [1, 1, 2, 3, 3, 3, 4, 4, 5, 6, 6, 6, 6, 7, 8, 8, 9, 9, 9, 10]\n", "\n", "# 绘制直方图\n", "plt.hist(data, bins=5, edgecolor='black')\n", "\n", "# 设置标题和坐标轴标签\n", "plt.title('Histogram')\n", "plt.xlabel('Values')\n", "plt.ylabel('Frequency')\n", "\n", "# 显示图形\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": null, "id": "fd3ef3d5", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}