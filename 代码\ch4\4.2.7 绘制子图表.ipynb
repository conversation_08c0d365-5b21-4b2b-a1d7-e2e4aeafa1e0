{"cells": [{"cell_type": "code", "execution_count": 1, "id": "9afbf708", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "import numpy as np\n", "\n", "# 设置中文字体\n", "plt.rcParams['font.family'] = ['SimHei']\n", "plt.rcParams['axes.unicode_minus'] = False\n", "\n", "\n", "def drowsubbar():\n", "    \"\"\"绘制饼状图\"\"\"\n", "\n", "    x1 = [1, 3, 5, 7, 9]  # x1轴坐标数据\n", "    y1 = [5, 2, 7, 8, 2]  # y1轴坐标数据\n", "\n", "    x2 = [2, 4, 6, 8, 10]  # x2轴坐标数据\n", "    y2 = [8, 6, 2, 5, 6]  # y2轴坐标数据\n", "\n", "    # 绘制柱状图\n", "    plt.bar(x1, y1, label='柱状图1')\n", "    plt.bar(x2, y2, label='柱状图2')\n", "\n", "    plt.title('绘制柱状图')  # 添加图表标题\n", "\n", "    plt.ylabel('y轴')  # 添加y轴标题\n", "    plt.xlabel('x轴')  # 添加x轴标题\n", "\n", "    plt.title('绘制散点图')\n", "\n", "\n", "def drowsubpie():\n", "    \"\"\"绘制饼状图\"\"\"\n", "\n", "    # 各种活动标题列表\n", "    activies = ['工作', '睡', '吃', '玩']\n", "    # 各种活动所占时间列表\n", "    slices = [8, 7, 3, 6]\n", "    # 各种活动在饼状图中的颜色列表\n", "    cols = ['c', 'm', 'r', 'b']\n", "\n", "    plt.pie(slices, labels=activies, colors=cols,\n", "            shadow=True, explode=(0, 0.1, 0, 0), autopct='%.1f%%')\n", "\n", "    plt.title('绘制饼状图')\n", "\n", "\n", "def drowsubline():\n", "    \"\"\"绘制折线图\"\"\"\n", "\n", "    x = [5, 4, 2, 1]  # x轴坐标数据\n", "    y = [7, 8, 9, 10]  # y轴坐标数据\n", "\n", "    # 绘制线段\n", "    plt.plot(x, y, 'b', label='线1', linewidth=2)\n", "\n", "    plt.title('绘制折线图')  # 添加图表标题\n", "\n", "    plt.ylabel('y轴')  # 添加y轴标题\n", "    plt.xlabel('x轴')  # 添加x轴标题\n", "\n", "    plt.legend()  # 设置图例\n", "\n", "\n", "def drowssubscatter():\n", "    \"\"\"绘制散点图\"\"\"\n", "\n", "    n = 1024\n", "    x = np.random.normal(0, 1, n)\n", "    y = np.random.normal(0, 1, n)\n", "\n", "    plt.scatter(x, y)\n", "\n", "    plt.title('绘制散点图')\n", "\n", "\n", "plt.subplot(2, 2, 1)  # 替换(221)\n", "drowsubbar()\n", "\n", "plt.subplot(2, 2, 2)  # 替换(222)\n", "drowsubpie()\n", "\n", "plt.subplot(2, 2, 3)  # 替换(223)\n", "drowsubline()\n", "\n", "plt.subplot(2, 2, 4)  # 替换(224)\n", "drowssubscatter()\n", "\n", "plt.tight_layout()  # 调整布局\n", "\n", "plt.show()  # 显示图形\n"]}, {"cell_type": "code", "execution_count": null, "id": "43f24b7e", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}