<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>解码链接测试页面</title>
</head>
<body>
    <div class="message">
        <p>您的文件码已生成，点击复制：</p>
        <p>showfilesbot_1V_D1t7H5U2y0i7z6p8W1h6 通用解码器：ShowFilesBot</p>
        <p>牢梦</p>
    </div>

    <div class="message">
        <p>文件分享链接：</p>
        <p>showfilesbot_1P_q1o7s5u2w0q4n9h2D5R3</p>
        <p>请保存好此链接</p>
    </div>

    <!-- 测试相邻的链接，应该被合并 -->
    <div class="adjacent-links">
        <p>第一个链接：showfilesbot_3V_Adjacent1234567</p>
        <p>中间文本</p>
        <p>第二个链接：showfilesbot_4V_Adjacent7890123</p>
        <p>结束文本</p>
    </div>

    <div class="message">
        <p>下载地址生成完成</p>
        <p>VT6AjWM77I5v_Xl62uqZV0tU=_grp</p>
        <p>有效期24小时</p>
    </div>

    <!-- 测试连续的多个链接，应该被合并成一组 -->
    <div class="continuous-links">
        <p>连续链接测试开始</p>
        <p>链接1：showfilesbot_5V_Continuous111</p>
        <p>链接2：showfilesbot_6V_Continuous222</p>
        <p>链接3：ABC123Continuous=_grp</p>
        <p>连续链接测试结束</p>
    </div>

    <div class="message">
        <p>机器人分享：</p>
        <p>@filepan_bot:_78P_115V_eaeT1VzuCB3x</p>
        <p>点击访问</p>
    </div>

    <div class="message">
        <p>混合内容测试：包含多个链接</p>
        <p>showfilesbot_7V_X1w7G38253t6y9u3t7V6 和 DEF456Mixed=_grp 在同一行</p>
        <p>结束内容</p>
    </div>

    <!-- 测试单行多链接 -->
    <div class="multi-links">
        <p>单行多链接测试：</p>
        <p>链接A：showfilesbot_8V_MultiA111 链接B：showfilesbot_9V_MultiB222 链接C：GHI789Multi=_grp</p>
        <p>测试结束</p>
    </div>
</body>
</html>
