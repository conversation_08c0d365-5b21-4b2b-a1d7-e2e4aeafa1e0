{"cells": [{"cell_type": "code", "execution_count": 1, "id": "931c0d89", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["----范围-------\n", "1 x 1 = 1\n", "2 x 2 = 4\n", "3 x 3 = 9\n", "4 x 4 = 16\n", "5 x 5 = 25\n", "6 x 6 = 36\n", "7 x 7 = 49\n", "8 x 8 = 64\n", "9 x 9 = 81\n", "----字符串-------\n", "H\n", "e\n", "l\n", "l\n", "o\n", "----整数列表-------\n", "Count is : 43\n", "Count is : 32\n", "Count is : 53\n", "Count is : 54\n", "Count is : 75\n", "Count is : 7\n", "Count is : 10\n"]}], "source": ["print(\"----范围-------\")\n", "for num in range(1, 10):  # 使用范围\n", "    print(\"{0} x {0} = {1}\".format(num, num * num))\n", "print(\"----字符串-------\")\n", "#  for语句\n", "for item in 'Hello': \n", "    print(item)\n", "\n", "# 声明整数列表\n", "numbers = [43, 32, 53, 54, 75, 7, 10] \n", "\n", "print(\"----整数列表-------\")\n", "\n", "#  for语句\n", "for item in numbers: \n", "    print(\"Count is : {0}\".format(item))"]}, {"cell_type": "code", "execution_count": null, "id": "e7794982", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}