import shutil
import time
from bs4 import BeautifulSoup
import os
import re
import tkinter as tk
from tkinter import filedialog
from PIL import Image
import subprocess
def extract_number(file_name):
    pattern = re.compile(
        r"(\d{6}[-_]\d{3})|"  # 匹配形式为123456-789或123456_789
        #r"([a-zA-Z]{3}-\d{3})|"  # 匹配形式为abc-123，不区分大小写
        #r"([a-zA-Z]{4}-\d{3})|"  # 匹配形式为abcd-123，不区分大小写
        #r"([a-zA-Z]{5}-\d{4})|"  # 匹配形式为abcde-123，不区分大小写
        r"(kb\d{4})|"  # 匹配形式为sw-123，不区分大小写
        r"(heyzo[_ -]?\d{4})|" # 匹配形式为heyzo-0123，不区分大小写
        r"([nN]\d{4})",  # 匹配形式为n0123或N0123
        re.IGNORECASE
    )
    match = pattern.search(file_name)
    if match:
        return match.group()
    return None

def select_folder():
    root = tk.Tk()
    root.withdraw()  # 隐藏根窗口
    folder_selected = filedialog.askdirectory()  # 弹出对话框选择文件夹
    return folder_selected

def extract_info_from_html(file_name):
    with open(file_name, 'r', encoding='utf-8') as file:
        soup = BeautifulSoup(file, 'html.parser')

        # 提取番号
        h2_tag = soup.find('h2', class_='title is-4')
        strong_tags = h2_tag.find_all('strong')
        if len(strong_tags) >= 1:
            # 提取第一个<strong>标签内的文本
            code = strong_tags[0].text.strip()
            # 番号为第一个<strong>标签内的文本中的空格之前的部分
            code = code.split()[0]
            # 如果番号不为空，则提取影片名称
            if code:
                original_title = strong_tags[1].text.strip() if len(strong_tags) >= 2 else ""
                original_title= (
                    original_title.replace("/", " ")  # 替换非法字符
                    .replace("\\", " ")
                    .replace(":", " ")
                    .replace("*", " ")
                    .replace("?", " ")
                    .replace("\"", " ")
                    .replace("<", " ")
                    .replace(">", " ")
                    .replace("|", " ")
                    .replace("_", " ")  # 替换下划线为单个空格
                    .replace("：", " ")  # 替换全角冒号为单个空格
                    .replace("\n", " ")  # 替换换行符为单个空格
                    .replace("\t", " ")  # 替换制表符为单个空格
                    .replace("~", " ")
                    .strip()  # 去除首尾多余空格
                    .replace("  ", " ")  # 将连续两个空格替换为一个空格
                )

                # 替换所有连续空格为单个空格
                original_title = re.sub(r'\s+', ' ', original_title).strip()
                original_title = f"{code} {original_title}"  # 组合番号和影片名称作为新的片名
        print("提取的番号", code)  # 输出番号
        # 提取关键词
        keywords = []
        links = soup.find_all('a', href=True)
        for link in links:
            if '/tags/uncensored' in link['href']:
                keywords.append(link.text.strip())


        # 提取演员名字
                actor_name = None
                strong_tags = soup.find_all('strong')
                for tag in strong_tags:
                    if tag.text.strip() == '演員:':
                        next_a_tag = tag.find_next('a', href=True)
                        if next_a_tag:
                            actor_name = next_a_tag.text.strip()
        if re.match(r"(heyzo|kb\d{4})", original_title.lower()):
            title = original_title
        else:
            #title = f"{original_title} {actor_name}"
            title = original_title

        print("影片名称: {}, 关键词: {}, 演员名字: {}".format(title, keywords, actor_name))

        # 提取发行日期
        release_date = None
        strong_tags = soup.find_all('strong')
        for tag in strong_tags:
            if tag.text.strip() == '日期:':
                next_span_tag = tag.find_next('span', class_='value')
                if next_span_tag:
                    release_date = next_span_tag.text.strip()

        print("发行日期:", release_date)

        # 提取时长
        duration = None
        strong_tags = soup.find_all('strong')
        for tag in strong_tags:
            if tag.text.strip() == '時長:':
                next_span_tag = tag.find_next('span', class_='value')
                if next_span_tag:
                    duration = next_span_tag.text.strip()
        print("时长:", duration)

                # 提取片商信息
        producer = None
        strong_tags = soup.find_all('strong')
        for tag in strong_tags:
            if tag.text.strip() == '片商:':
               next_span_tag = tag.find_next('span', class_='value')
               if next_span_tag and next_span_tag.a:
                   producer= next_span_tag.a.text.strip()
        print("片商:", producer)


        return title, keywords, actor_name, release_date, duration, producer, original_title


def create_nfo_content(title, actor_name, release_date, duration, producer, keywords):
    # 排除的关键字列表
    excluded_keywords = ["無碼", "獨佔動畫", "企劃物", "1080p", "60fps", "HEYZO"]

    # 生成标签
    genre_tags = "\n".join([
        f"<genre>{keyword.strip()}</genre>" for keyword in keywords
        if keyword.strip() and
           keyword.strip() not in excluded_keywords and
           not any(char.isdigit() for char in keyword.strip()) and
           "HEYZO" not in keyword.strip()
    ])

    # 构造 NFO 文件内容
    nfo_content = f"""
<movie>
    <title>{title}</title>
    <originaltitle>{title}</originaltitle>
    <actor>
        <name>{actor_name}</name>
        <type>Actor</type>
    </actor>
    <releasedate>{release_date}</releasedate>
    <runtime>{duration}</runtime>
    <studio>{producer}</studio>
    {genre_tags}
</movie>
"""
    return nfo_content



def create_movie_folder(folder_path, movie_name):
    movie_folder_path = os.path.join(folder_path, movie_name)
    if not os.path.exists(movie_folder_path):
        os.makedirs(movie_folder_path)
    return movie_folder_path

def main():
    folder_path = select_folder()  # 选择文件夹
    if folder_path:
        print("选择的文件夹:", folder_path)
        video_files = [f for f in os.listdir(folder_path) if f.endswith((".mp4", ".mkv", ".avi", ".wmv"))]
        if video_files:
            print("找到的视频文件:")
            for file_name in video_files:
                try:
                    number = extract_number(file_name)
                    if number:
                        file_path = 'd:\\tt\\page_source.html'
                        if os.path.exists(file_path):
                            os.remove(file_path)
                        url = f"https://javdb.com/search?q={number}&f=all"
                        browser_path = "C:/Program Files/Waterfox/waterfox.exe"
                        #browser_path = "C:\Program Files\Google\Chrome\Application\chrome.exe"
                        subprocess.run([browser_path, url])
                        print("文件名: {}, 文件番号: {}".format(file_name, number))

                        time.sleep(10)  # 暂停10秒等待页面加载
                        title, keywords, actor, release_date, duration, producer, original_title = extract_info_from_html('d:\\tt\\page_source.html')

                        # 从 original_title 中提取番号
                        extracted_number = extract_number(title)

                        # 将提取的番号和文件名中的番号转换为小写
                        extracted_number_lower = extracted_number.lower()
                        number_lower = number.lower()
                        number_lower = number_lower.replace("-", "").replace("_", "").replace(" ", "")
                        extracted_number_lower = extracted_number_lower.replace("-", "").replace("_", "").replace(" ",
                                                                                                                  "")

                        # 检查转换后的番号是否一致
                        if extracted_number_lower != number_lower:
                            print(f"文件 {file_name} 的番号与提取的番号不一致，跳过该文件。")
                            continue

                        # 创建Emby格式的nfo内容
                        nfo_content = create_nfo_content(title, actor, release_date, duration, producer, keywords)

                        # 创建以影片名命名的文件夹
                        #movie_folder_path = create_movie_folder('V:\\wuma\\亚洲\\=m=', title)
                        movie_folder_path = create_movie_folder('V:\\wuma\\亚洲\\=m=', title)

                        # 检测是否包含特定的cd标记，并添加到文件名
                        cd_tag = ""
                        for cd in ['cd1', 'cd2', 'cd3', 'cd4']:
                            if cd in file_name.lower():
                                cd_tag = f"-{cd}"
                                break

                        # 更新文件名以包含CD标记
                        title_with_cd = f"{title}{cd_tag}"
                        new_video_name = f"{title_with_cd}{os.path.splitext(file_name)[1]}"

                        original_video_path = os.path.join(folder_path, file_name)
                        new_video_path = os.path.join(movie_folder_path, new_video_name)
                        os.rename(original_video_path, new_video_path)

                        # 移动图片和写入nfo文件时也应使用更新后的影片名
                        new_jpg_name = f"{title_with_cd}-poster.jpg"
                        new_jpg_path = os.path.join(movie_folder_path, new_jpg_name)

                        jpg_file_path = os.path.join('d:\\tt\\', f"{original_title}.jpg")
                        if os.path.exists(jpg_file_path):
                            # 打开原始图片
                            original_image = Image.open(jpg_file_path)

                            # 计算裁剪区域，以右上角为基点裁剪
                            width, height = original_image.size
                            new_width = 360
                            new_height = height
                            right = width
                            top = 0
                            left = right - new_width
                            bottom = top + new_height

                            # 裁剪图片
                            cropped_image = original_image.crop((left, top, right, bottom))

                            # 保存裁剪后的图片
                            cropped_image.save(new_jpg_path)

                            # 移动原始 jpg 文件到影片文件夹
                            shutil.move(jpg_file_path, os.path.join(movie_folder_path, f"{title_with_cd}-fanart.jpg"))

                        # 写入信息到.nfo文件
                        nfo_file_path = os.path.join(movie_folder_path, f"{title_with_cd}.nfo")
                        with open(nfo_file_path, 'w', encoding='utf-8') as nfo_file:
                            nfo_file.write(nfo_content)

                    else:
                        print("文件名: {}, 未找到番号".format(file_name))
                except Exception as e:
                    print(f"处理文件 {file_name} 时出现异常: {e}")
                    continue
        else:
            print("在所选文件夹中未找到视频文件。")

if __name__ == "__main__":
    main()
