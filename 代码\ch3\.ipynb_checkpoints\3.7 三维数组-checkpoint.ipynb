{"cells": [{"cell_type": "code", "execution_count": 1, "id": "14af8b42", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[[[10 11 12]\n", "  [13 14 15]\n", "  [16 17 18]]\n", "\n", " [[20 21 22]\n", "  [23 24 25]\n", "  [26 27 28]]\n", "\n", " [[30 31 32]\n", "  [33 34 35]\n", "  [36 37 38]]]\n"]}], "source": ["import numpy as np\n", "# 创建三维数组\n", "a3 = np.array([[[10, 11, 12], [13, 14, 15], [16, 17, 18]],\n", "               [[20, 21, 22], [23, 24, 25], [26, 27, 28]],\n", "               [[30, 31, 32], [33, 34, 35], [36, 37, 38]]])\n", "print(a3)"]}, {"cell_type": "code", "execution_count": null, "id": "1ec671c6", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}