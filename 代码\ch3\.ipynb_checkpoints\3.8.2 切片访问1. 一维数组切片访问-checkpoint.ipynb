{"cells": [{"cell_type": "code", "execution_count": 4, "id": "392bab14", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["一维数组切片访问：\n", "[3 4 5]\n", "[1 2 3 4]\n", "[3 4 5 6]\n", "[1 3 5]\n", "[6 5 4 3 2 1]\n"]}], "source": ["import numpy as np\n", "\n", "arr = np.array([1, 2, 3, 4, 5, 6])\n", "\n", "# 切片访问一维数组\n", "print(\"一维数组切片访问：\")\n", "print(arr[2:5])        # 输出：[3, 4, 5]\n", "print(arr[:4])         # 输出：[1, 2, 3, 4]\n", "print(arr[2:])         # 输出：[3, 4, 5, 6]\n", "print(arr[::2])        # 输出：[1, 3, 5]\n", "print(arr[::-1])       # 输出：[6, 5, 4, 3, 2, 1]\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}