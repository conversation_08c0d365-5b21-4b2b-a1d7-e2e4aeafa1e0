{"cells": [{"cell_type": "code", "execution_count": 1, "id": "bd52dfd4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["您真优秀！\n"]}], "source": ["score = 95\n", "\n", "if score >= 85:\n", "    print(\"您真优秀！\")\n", "\n", "if score < 60:\n", "    print(\"您需要加倍努力！\")\n", "\n", "if (score >= 60) and (score < 85):\n", "    print(\"您的成绩还可以，仍需继续努力！\")"]}, {"cell_type": "code", "execution_count": null, "id": "d89806d0", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}