import time
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler

class MyHandler(FileSystemEventHandler):
    def __init__(self, log_file):
        super().__init__()
        self.log_file = log_file

    def on_any_event(self, event):
        if event.is_directory:
            event_type = "创建" if event.event_type == "created" else "删除" if event.event_type == "deleted" else "更名" if event.event_type == "moved" else None
            if event_type:
                with open(self.log_file, "a") as f:
                    if event_type == "更名":
                        f.write(f'文件夹 {event.src_path} 被 {event_type}为 {event.dest_path}\n')
                    else:
                        f.write(f'文件夹 {event.src_path} 被 {event_type}\n')
        else:
            event_type = "创建" if event.event_type == "created" else  "删除" if event.event_type == "deleted" else "更名" if event.event_type == "moved" else None
            if event_type:
                with open(self.log_file, "a") as f:
                    if event_type == "更名":
                        f.write(f'文件 {event.src_path} 被 {event_type}为 {event.dest_path}\n')
                    else:
                        f.write(f'文件 {event.src_path} 被 {event_type}\n')

def monitor_folders(folder_paths, log_files):
    if len(folder_paths) != len(log_files):
        raise ValueError("文件夹路径和日志文件路径数量不匹配")

    observers = []
    for folder_path, log_file in zip(folder_paths, log_files):
        observer = Observer()
        event_handler = MyHandler(log_file)
        observer.schedule(event_handler, folder_path, recursive=True)
        observer.start()
        observers.append(observer)

    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        for observer in observers:
            observer.stop()
        for observer in observers:
            observer.join()

if __name__ == "__main__":
    folder_paths = ["d:/BaiduYunDownload", "d:/tdl_Windows_64bit/downloads", "d:/tt"]  # 添加需要监视的文件夹路径
    log_files = ["d:/temp/百度日志文件.txt", "d:/temp/电报日志文件2.txt", "d:/temp/115日志文件.txt"]  # 对应的日志文件路径
    monitor_folders(folder_paths, log_files)
