{"cells": [{"cell_type": "code", "execution_count": 11, "id": "5fb331f6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["20\n", "True\n", "10.1\n"]}], "source": ["import module1 \n", "from module1 import z   \n", "y = 20\n", "print(y)  # 访问当前模块变量y \n", "print(module1.y)  # 访问module1模块变量y  \n", "print(z)  # 访问module1模块变量z "]}, {"cell_type": "code", "execution_count": null, "id": "6c5d9b45", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}