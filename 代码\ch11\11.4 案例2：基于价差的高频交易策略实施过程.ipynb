{"cells": [{"cell_type": "code", "execution_count": 2, "id": "3f4e6c3e", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2023-07-01 16:09:39,537 - INFO - 买入 AAPL - 价格: 161.46\n", "2023-07-01 16:09:39,537 - INFO - 买入 AAPL - 价格: 161.46\n", "2023-07-01 16:09:39,538 - INFO - 卖出 GOOGL - 价格: 107.53\n", "2023-07-01 16:09:39,538 - INFO - 卖出 GOOGL - 价格: 107.53\n", "2023-07-01 16:09:40,538 - INFO - 卖出 AAPL - 价格: 103.92\n", "2023-07-01 16:09:40,538 - INFO - 卖出 AAPL - 价格: 103.92\n", "2023-07-01 16:09:40,539 - INFO - 买入 GOOGL - 价格: 179.64\n", "2023-07-01 16:09:40,539 - INFO - 买入 GOOGL - 价格: 179.64\n", "2023-07-01 16:09:41,541 - INFO - 买入 AAPL - 价格: 197.32\n", "2023-07-01 16:09:41,541 - INFO - 买入 AAPL - 价格: 197.32\n", "2023-07-01 16:09:41,542 - INFO - 卖出 GOOGL - 价格: 121.78\n", "2023-07-01 16:09:41,542 - INFO - 卖出 GOOGL - 价格: 121.78\n", "2023-07-01 16:09:42,543 - INFO - 卖出 AAPL - 价格: 108.54\n", "2023-07-01 16:09:42,543 - INFO - 卖出 AAPL - 价格: 108.54\n", "2023-07-01 16:09:42,544 - INFO - 买入 GOOGL - 价格: 139.22\n", "2023-07-01 16:09:42,544 - INFO - 买入 GOOGL - 价格: 139.22\n", "2023-07-01 16:09:43,547 - INFO - 买入 AAPL - 价格: 173.48\n", "2023-07-01 16:09:43,547 - INFO - 买入 AAPL - 价格: 173.48\n", "2023-07-01 16:09:43,548 - INFO - 卖出 GOOGL - 价格: 138.31\n", "2023-07-01 16:09:43,548 - INFO - 卖出 GOOGL - 价格: 138.31\n", "2023-07-01 16:09:44,549 - INFO - 买入 AAPL - 价格: 169.80\n", "2023-07-01 16:09:44,549 - INFO - 买入 AAPL - 价格: 169.80\n", "2023-07-01 16:09:44,550 - INFO - 卖出 GOOGL - 价格: 138.46\n", "2023-07-01 16:09:44,550 - INFO - 卖出 GOOGL - 价格: 138.46\n", "2023-07-01 16:09:45,552 - INFO - 卖出 AAPL - 价格: 158.88\n", "2023-07-01 16:09:45,552 - INFO - 卖出 AAPL - 价格: 158.88\n", "2023-07-01 16:09:45,553 - INFO - 买入 GOOGL - 价格: 167.68\n", "2023-07-01 16:09:45,553 - INFO - 买入 GOOGL - 价格: 167.68\n", "2023-07-01 16:09:46,554 - INFO - 买入 AAPL - 价格: 178.65\n", "2023-07-01 16:09:46,554 - INFO - 买入 AAPL - 价格: 178.65\n", "2023-07-01 16:09:46,555 - INFO - 卖出 GOOGL - 价格: 125.70\n", "2023-07-01 16:09:46,555 - INFO - 卖出 GOOGL - 价格: 125.70\n"]}, {"ename": "KeyboardInterrupt", "evalue": "", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mKeyboardInterrupt\u001b[0m                         <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[2], line 56\u001b[0m\n\u001b[0;32m     54\u001b[0m \u001b[38;5;66;03m# 示例使用\u001b[39;00m\n\u001b[0;32m     55\u001b[0m threshold \u001b[38;5;241m=\u001b[39m \u001b[38;5;241m5\u001b[39m  \u001b[38;5;66;03m# 设置价差变化阈值\u001b[39;00m\n\u001b[1;32m---> 56\u001b[0m \u001b[43mtrade\u001b[49m\u001b[43m(\u001b[49m\u001b[43mthreshold\u001b[49m\u001b[43m)\u001b[49m\n", "Cell \u001b[1;32mIn[2], line 44\u001b[0m, in \u001b[0;36mtrade\u001b[1;34m(threshold)\u001b[0m\n\u001b[0;32m     41\u001b[0m         buy(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mGOOGL\u001b[39m\u001b[38;5;124m\"\u001b[39m, price2)\n\u001b[0;32m     43\u001b[0m \u001b[38;5;66;03m# 控制交易频率\u001b[39;00m\n\u001b[1;32m---> 44\u001b[0m time\u001b[38;5;241m.\u001b[39msleep(\u001b[38;5;241m1\u001b[39m)\n", "\u001b[1;31mKeyboardInterrupt\u001b[0m: "]}], "source": ["import random\n", "import time\n", "import logging\n", "import sys\n", "\n", "# 配置日志记录\n", "logging.basicConfig(filename='trading.log', level=logging.INFO,\n", "                    format='%(asctime)s - %(levelname)s - %(message)s')\n", "\n", "# 创建控制台输出处理器\n", "console_handler = logging.StreamHandler(sys.stdout)\n", "console_handler.setLevel(logging.INFO)\n", "console_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')\n", "console_handler.setFormatter(console_formatter)\n", "\n", "# 将控制台输出处理器添加到日志记录器\n", "logger = logging.getLogger()\n", "logger.addHandler(console_handler)\n", "\n", "def get_price(symbol):\n", "    # 模拟获取标的的最新报价\n", "    return random.uniform(100, 200)\n", "\n", "def trade(threshold):\n", "    while True:\n", "        # 获取标的1和标的2的最新价格\n", "        price1 = get_price(\"AAPL\")\n", "        price2 = get_price(\"GOOGL\")\n", "        \n", "        # 计算价差\n", "        spread = price1 - price2\n", "        \n", "        # 判断价差是否超过阈值\n", "        if abs(spread) > threshold:\n", "            # 根据价差变化方向下单\n", "            if spread > 0:\n", "                buy(\"AAPL\", price1)\n", "                sell(\"GOOGL\", price2)\n", "            else:\n", "                sell(\"AAPL\", price1)\n", "                buy(\"GOOGL\", price2)\n", "        \n", "        # 控制交易频率\n", "        time.sleep(1)\n", "\n", "def buy(symbol, price):\n", "    # 下买单逻辑...\n", "    logging.info(\"买入 %s - 价格: %.2f\", symbol, price)\n", "\n", "def sell(symbol, price):\n", "    # 下卖单逻辑...\n", "    logging.info(\"卖出 %s - 价格: %.2f\", symbol, price)\n", "\n", "# 示例使用\n", "threshold = 5  # 设置价差变化阈值\n", "trade(threshold)\n"]}, {"cell_type": "code", "execution_count": null, "id": "e42fc41d", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "f383c9d8", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}