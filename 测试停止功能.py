# -*- coding: utf-8 -*-
"""
测试停止解压功能
"""
import os
import time
import subprocess
import threading

def create_large_test_file():
    """创建一个大的测试文件用于压缩"""
    test_dir = os.path.join(os.getcwd(), "测试停止功能")
    
    if not os.path.exists(test_dir):
        os.makedirs(test_dir)
        print(f"创建测试目录：{test_dir}")
    
    # 创建一个大文件
    large_file = os.path.join(test_dir, "大文件.txt")
    if not os.path.exists(large_file):
        print("创建大文件用于测试...")
        with open(large_file, 'w', encoding='utf-8') as f:
            # 写入大量数据（约100MB）
            for i in range(1000000):
                f.write(f"这是测试数据行 {i:06d} - " + "x" * 80 + "\n")
        print(f"✅ 创建大文件：{large_file}")
    else:
        print(f"⏭️ 大文件已存在：{large_file}")
    
    # 创建压缩包
    zip_file = os.path.join(test_dir, "测试压缩包.zip")
    if not os.path.exists(zip_file):
        print("创建测试压缩包...")
        try:
            # 使用7z创建压缩包
            cmd = [r'C:\Program Files\7-Zip\7z.exe', 'a', zip_file, large_file]
            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode == 0:
                print(f"✅ 创建压缩包：{zip_file}")
            else:
                print(f"❌ 创建压缩包失败：{result.stderr}")
                return None
        except FileNotFoundError:
            print("❌ 未找到7-Zip程序，请确保已安装7-Zip")
            return None
        except Exception as e:
            print(f"❌ 创建压缩包时出错：{e}")
            return None
    else:
        print(f"⏭️ 压缩包已存在：{zip_file}")
    
    # 删除原始大文件，只保留压缩包
    if os.path.exists(large_file):
        os.remove(large_file)
        print("删除原始大文件，保留压缩包用于测试")
    
    return test_dir, zip_file

def test_7z_process_control():
    """测试7z进程控制"""
    print("\n=== 测试7z进程控制 ===")
    
    test_dir, zip_file = create_large_test_file()
    if not zip_file:
        return
    
    print(f"测试文件：{zip_file}")
    
    # 启动解压进程
    extract_dir = os.path.join(test_dir, "解压测试")
    if not os.path.exists(extract_dir):
        os.makedirs(extract_dir)
    
    print("启动7z解压进程...")
    cmd = [r'C:\Program Files\7-Zip\7z.exe', 'x', zip_file, f'-o{extract_dir}', '-aou']
    
    try:
        process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.STDOUT,
                                 universal_newlines=True, creationflags=subprocess.CREATE_NO_WINDOW)
        
        print(f"7z进程已启动，PID: {process.pid}")
        
        # 模拟运行一段时间后停止
        def stop_after_delay():
            time.sleep(3)  # 3秒后停止
            print("\n模拟用户点击停止按钮...")
            
            try:
                print("尝试优雅终止进程...")
                process.terminate()
                
                # 等待进程结束
                try:
                    process.wait(timeout=3)
                    print("✅ 进程已优雅终止")
                except subprocess.TimeoutExpired:
                    print("进程未响应，强制终止...")
                    process.kill()
                    try:
                        process.wait(timeout=2)
                        print("✅ 进程已强制终止")
                    except subprocess.TimeoutExpired:
                        print("❌ 无法终止进程")
                        
            except Exception as e:
                print(f"❌ 终止进程时出错：{e}")
        
        # 启动停止线程
        stop_thread = threading.Thread(target=stop_after_delay)
        stop_thread.daemon = True
        stop_thread.start()
        
        # 监控进程输出
        start_time = time.time()
        while True:
            if process.poll() is not None:
                print(f"进程已结束，返回码：{process.returncode}")
                break
                
            # 读取输出
            try:
                line = process.stdout.readline()
                if line:
                    print(f"7z输出: {line.strip()}")
            except:
                pass
            
            # 防止无限循环
            if time.time() - start_time > 10:
                print("测试超时，强制结束")
                try:
                    process.kill()
                except:
                    pass
                break
                
            time.sleep(0.1)
            
    except FileNotFoundError:
        print("❌ 未找到7-Zip程序")
    except Exception as e:
        print(f"❌ 测试过程出错：{e}")

def show_process_info():
    """显示进程信息"""
    print("\n=== 进程控制说明 ===")
    print("1. terminate() - 发送SIGTERM信号，优雅终止")
    print("2. kill() - 发送SIGKILL信号，强制终止")
    print("3. wait(timeout) - 等待进程结束，带超时")
    print("4. poll() - 检查进程是否结束")
    print("5. CREATE_NO_WINDOW - 不显示命令行窗口")

if __name__ == "__main__":
    print("=== 停止解压功能测试 ===")
    
    # 显示进程控制说明
    show_process_info()
    
    # 测试7z进程控制
    test_7z_process_control()
    
    print("\n=== 测试说明 ===")
    print("1. 程序会创建一个大文件并压缩")
    print("2. 启动7z解压进程")
    print("3. 3秒后模拟用户点击停止")
    print("4. 测试进程终止机制")
    print("\n现在可以在GUI程序中测试停止功能：")
    print("- 选择包含大压缩包的文件夹")
    print("- 点击开始解压")
    print("- 在解压过程中点击停止解压")
    print("- 观察7z进程是否正确终止")
    
    print("\n测试完成！")
