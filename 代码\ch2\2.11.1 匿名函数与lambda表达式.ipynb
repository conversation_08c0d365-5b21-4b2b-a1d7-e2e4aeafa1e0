{"cells": [{"cell_type": "code", "execution_count": 1, "id": "8f0d8cd6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'function'>\n", "10 + 5 = 15\n", "10 - 5 = 5\n"]}], "source": ["def calculate_fun(opr):\n", "    if opr == '+':\n", "        return lambda a, b: (a + b) \n", "    else:\n", "        return lambda a, b: (a - b) \n", "\n", "\n", "\n", "f1 = calculate_fun('+') \n", "f2 = calculate_fun('-') \n", "\n", "print(type(f1))\n", "\n", "print(\"10 + 5 = {0}\".format(f1(10, 5)))\n", "print(\"10 - 5 = {0}\".format(f2(10, 5))) "]}, {"cell_type": "code", "execution_count": null, "id": "279955d0", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}