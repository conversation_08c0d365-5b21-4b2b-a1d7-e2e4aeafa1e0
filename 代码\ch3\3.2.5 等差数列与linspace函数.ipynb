{"cells": [{"cell_type": "code", "execution_count": 2, "id": "1ca08b03", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[ 0.          1.11111111  2.22222222  3.33333333  4.44444444  5.55555556\n", "  6.66666667  7.77777778  8.88888889 10.        ]\n", "[0. 1. 2. 3. 4. 5. 6. 7. 8. 9.]\n", "(array([0., 1., 2., 3., 4., 5., 6., 7., 8., 9.]), 1.0)\n", "步长= 1.0\n"]}], "source": ["import numpy as np\n", "a = np.linspace(0, 10, 10) \n", "print(a)\n", "b = np.linspace(0, 10, 10, endpoint=False) \n", "print(b)\n", "c = np.linspace(0, 10, 10,endpoint=False, retstep=True) \n", "print(c)\n", "mystep = c[1]\n", "print(\"步长=\", mystep)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}