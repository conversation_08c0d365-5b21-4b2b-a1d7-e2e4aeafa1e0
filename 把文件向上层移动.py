import os
import shutil
import json
import datetime
from tkinter import Tk, messagebox
from tkinter.filedialog import askdirectory

# 全局变量用于记录操作
operation_log = []

def get_folder_size(folder_path):
    total_size = 0
    for dirpath, _, filenames in os.walk(folder_path):
        for f in filenames:
            fp = os.path.join(dirpath, f)
            # 跳过符号链接
            if not os.path.islink(fp):
                total_size += os.path.getsize(fp)
    return total_size

def get_unique_filename(target_dir, filename):
    """生成唯一的文件名，如果存在冲突则自动添加数字后缀"""
    base_name, ext = os.path.splitext(filename)
    target_path = os.path.join(target_dir, filename)

    if not os.path.exists(target_path):
        return filename

    counter = 1
    while True:
        new_filename = f"{base_name}-{counter}{ext}"
        new_target_path = os.path.join(target_dir, new_filename)
        if not os.path.exists(new_target_path):
            return new_filename
        counter += 1

def log_operation(operation_type, source_path, target_path, original_name=None):
    """记录操作以便回退"""
    operation = {
        'type': operation_type,  # 'move_file', 'delete_folder', 'rename'
        'source': source_path,
        'target': target_path,
        'original_name': original_name,
        'timestamp': datetime.datetime.now().isoformat()
    }
    operation_log.append(operation)

def save_operation_log(root_folder):
    """保存操作日志到文件"""
    log_file = os.path.join(root_folder, 'file_move_log.json')
    with open(log_file, 'w', encoding='utf-8') as f:
        json.dump(operation_log, f, ensure_ascii=False, indent=2)
    return log_file

def is_image_or_video(filename):
    image_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff'}
    video_extensions = {'.mp4', '.avi', '.mkv', '.mov', '.flv', '.wmv', '.mpg', '.mpeg', '.ts', '.m4v'}
    _, ext = os.path.splitext(filename.lower())
    return ext in image_extensions or ext in video_extensions

def move_files_up_one_level(root_folder):
    moved_files = 0
    renamed_files = 0

    for foldername, subfolders, _ in os.walk(root_folder):
        # 遍历所有子文件夹
        for subfolder in subfolders:
            subfolder_path = os.path.join(foldername, subfolder)
            # 遍历子文件夹中的所有文件
            for file in os.listdir(subfolder_path):
                file_path = os.path.join(subfolder_path, file)
                if os.path.isfile(file_path):  # 确保是文件而不是文件夹
                    # 检查目标位置是否存在同名文件，如果存在则自动重命名
                    unique_filename = get_unique_filename(foldername, file)
                    target_path = os.path.join(foldername, unique_filename)

                    # 记录操作
                    if unique_filename != file:
                        log_operation('rename', file_path, target_path, file)
                        renamed_files += 1
                        print(f"重命名冲突文件: {file} -> {unique_filename}")

                    log_operation('move_file', file_path, target_path)

                    # 将文件移动到上一级文件夹
                    shutil.move(file_path, target_path)
                    moved_files += 1

            # 如果子文件夹现在为空，删除它
            try:
                if not os.listdir(subfolder_path):  # 检查文件夹是否为空
                    log_operation('delete_folder', subfolder_path, None)
                    os.rmdir(subfolder_path)
                    print(f"删除空文件夹: {subfolder_path}")
            except OSError:
                # 如果文件夹不为空（可能还有子文件夹），跳过删除
                pass

    print(f"移动了 {moved_files} 个文件")
    if renamed_files > 0:
        print(f"重命名了 {renamed_files} 个冲突文件")

def clean_empty_folders(root_folder):
    deleted_folders = 0

    for foldername, subfolders, filenames in os.walk(root_folder, topdown=False):
        # 跳过根文件夹
        if foldername == root_folder:
            continue

        # 检查文件夹是否为空或没有图片/视频文件且总大小不超过20MB
        if not filenames and not subfolders:
            log_operation('delete_folder', foldername, None)
            os.rmdir(foldername)
            deleted_folders += 1
            print(f"删除空文件夹: {foldername}")
        elif not any(is_image_or_video(f) for f in filenames):
            folder_size = get_folder_size(foldername)
            if folder_size <= 20 * 1024 * 1024:  # 20MB
                log_operation('delete_folder', foldername, None)
                shutil.rmtree(foldername)
                deleted_folders += 1
                print(f"删除小文件夹: {foldername} (大小: {folder_size/1024/1024:.2f}MB)")

    if deleted_folders > 0:
        print(f"清理了 {deleted_folders} 个文件夹")

def undo_operations(log_file_path):
    """根据日志文件回退操作"""
    if not os.path.exists(log_file_path):
        print("未找到操作日志文件")
        return False

    try:
        with open(log_file_path, 'r', encoding='utf-8') as f:
            operations = json.load(f)

        # 反向执行操作
        success_count = 0
        error_count = 0

        for operation in reversed(operations):
            try:
                if operation['type'] == 'move_file':
                    # 将文件移回原位置
                    source_dir = os.path.dirname(operation['source'])
                    if not os.path.exists(source_dir):
                        os.makedirs(source_dir)
                    shutil.move(operation['target'], operation['source'])
                    success_count += 1

                elif operation['type'] == 'rename':
                    # 如果是重命名操作，恢复原始文件名
                    if operation['original_name']:
                        original_path = os.path.join(
                            os.path.dirname(operation['target']),
                            operation['original_name']
                        )
                        if os.path.exists(operation['target']) and not os.path.exists(original_path):
                            shutil.move(operation['target'], original_path)
                            success_count += 1

                elif operation['type'] == 'delete_folder':
                    # 重新创建被删除的文件夹
                    if not os.path.exists(operation['source']):
                        os.makedirs(operation['source'])
                        success_count += 1

            except Exception as e:
                print(f"回退操作失败: {operation} - 错误: {e}")
                error_count += 1

        print(f"回退完成: 成功 {success_count} 个操作, 失败 {error_count} 个操作")

        # 删除日志文件
        os.remove(log_file_path)
        return True

    except Exception as e:
        print(f"读取日志文件失败: {e}")
        return False

def main():
    # 创建Tkinter根窗口并隐藏它
    root = Tk()
    root.withdraw()

    # 检查是否存在日志文件，如果存在则询问是否要回退
    folder_path = askdirectory(title="请选择一个文件夹")
    if not folder_path:
        print("未选择文件夹")
        return

    log_file_path = os.path.join(folder_path, 'file_move_log.json')

    if os.path.exists(log_file_path):
        # 询问用户是否要回退之前的操作
        choice = messagebox.askyesnocancel(
            "发现操作日志",
            "发现之前的操作日志文件，您想要：\n\n"
            "是 - 回退之前的操作\n"
            "否 - 继续执行新的操作\n"
            "取消 - 退出程序"
        )

        if choice is None:  # 取消
            return
        elif choice:  # 是 - 回退
            if undo_operations(log_file_path):
                messagebox.showinfo("回退完成", "操作已成功回退")
            else:
                messagebox.showerror("回退失败", "回退操作时发生错误")
            return
        # 否 - 继续执行新操作，清空之前的日志
        operation_log.clear()

    try:
        print("开始处理文件...")
        move_files_up_one_level(folder_path)
        clean_empty_folders(folder_path)

        # 保存操作日志
        if operation_log:
            log_file = save_operation_log(folder_path)
            print(f"操作日志已保存到: {log_file}")
            messagebox.showinfo(
                "操作完成",
                f"文件移动和清理完成！\n\n"
                f"操作日志已保存，如需回退请重新运行程序。\n"
                f"日志文件: {os.path.basename(log_file)}"
            )
        else:
            print("没有执行任何操作")
            messagebox.showinfo("操作完成", "没有需要处理的文件")

    except Exception as e:
        error_msg = f"操作过程中发生错误: {e}"
        print(error_msg)
        messagebox.showerror("错误", error_msg)

if __name__ == "__main__":
    main()
