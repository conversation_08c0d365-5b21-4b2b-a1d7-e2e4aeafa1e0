# -*- coding: utf-8 -*-
"""
测试文件后缀规范化功能
"""
import os
import tempfile
import random

def create_test_files():
    """创建测试用的文件"""
    # 创建临时目录
    test_dir = tempfile.mkdtemp()
    print(f"测试目录：{test_dir}")
    
    # 创建各种测试文件
    test_files = [
        # 无扩展名的大文件（模拟）
        ("大文件无扩展名", 150 * 1024 * 1024),  # 150MB
        
        # 扩展名包含汉字
        ("测试文件.txt汉字", 1024),
        ("另一个文件.doc中文", 2048),
        
        # 需要标准化的扩展名
        ("压缩文件.7", 1024),
        ("另一个压缩.7zz", 2048),
        ("旧格式.z", 512),
        
        # 大文件需要重命名
        ("大PDF文件.pdf", 400 * 1024 * 1024),  # 400MB
        ("大文档.docx", 350 * 1024 * 1024),   # 350MB
        ("大文本.txt", 320 * 1024 * 1024),    # 320MB
        
        # 分卷压缩文件（不应该被处理）
        ("压缩包.7z.001", 100 * 1024 * 1024),
        ("压缩包.zip.002", 100 * 1024 * 1024),
        
        # 正常文件（不应该被处理）
        ("正常视频.mp4", 200 * 1024 * 1024),
        ("正常图片.jpg", 5 * 1024 * 1024),
        ("正常文档.pdf", 10 * 1024 * 1024),
    ]
    
    created_files = []
    
    for filename, size in test_files:
        file_path = os.path.join(test_dir, filename)
        try:
            # 创建指定大小的文件
            with open(file_path, 'wb') as f:
                # 写入随机数据来模拟文件大小
                chunk_size = min(1024 * 1024, size)  # 1MB chunks
                remaining = size
                while remaining > 0:
                    write_size = min(chunk_size, remaining)
                    f.write(b'0' * write_size)
                    remaining -= write_size
                    
            created_files.append((file_path, size))
            print(f"✅ 创建文件：{filename} ({size / (1024*1024):.1f}MB)")
            
        except Exception as e:
            print(f"❌ 创建文件失败：{filename} - {e}")
    
    return test_dir, created_files

def analyze_test_files(test_dir):
    """分析测试文件，预测哪些会被处理"""
    print("\n=== 文件分析 ===")
    
    for filename in os.listdir(test_dir):
        file_path = os.path.join(test_dir, filename)
        if os.path.isfile(file_path):
            file_size = os.path.getsize(file_path)
            file_size_mb = file_size / (1024 * 1024)
            
            name, ext = os.path.splitext(filename)
            
            print(f"\n文件：{filename}")
            print(f"  大小：{file_size_mb:.1f}MB")
            print(f"  扩展名：{ext if ext else '(无)'}")
            
            # 预测处理结果
            will_process = False
            action = ""
            
            # 无扩展名且大于100MB
            if not ext and file_size > 100 * 1024 * 1024:
                will_process = True
                action = "添加 .7z 扩展名"
                
            # 扩展名包含汉字
            elif any('\u4e00' <= char <= '\u9fff' for char in ext):
                will_process = True
                new_ext = ''.join(char for char in ext if not ('\u4e00' <= char <= '\u9fff'))
                action = f"移除汉字：{ext} -> {new_ext}"
                
            # 大文件重命名
            elif ext.lower() in ['.pdf', '.doc', '.docx', '.txt', '.gif'] and file_size > 300 * 1024 * 1024:
                will_process = True
                action = f"大文件重命名：{ext} -> .zip"
                
            # 扩展名标准化
            elif ext.lower() in ['.7', '.7zz', '.z']:
                will_process = True
                action = f"标准化：{ext} -> .7z"
                
            if will_process:
                print(f"  🔄 预期处理：{action}")
            else:
                print(f"  ✅ 不需要处理")

def cleanup_test_files(test_dir):
    """清理测试文件"""
    try:
        import shutil
        shutil.rmtree(test_dir)
        print(f"\n✅ 已清理测试目录：{test_dir}")
    except Exception as e:
        print(f"\n❌ 清理测试目录失败：{e}")

if __name__ == "__main__":
    print("=== 文件后缀规范化测试 ===")
    
    # 创建测试文件
    test_dir, created_files = create_test_files()
    
    # 分析测试文件
    analyze_test_files(test_dir)
    
    print(f"\n=== 测试说明 ===")
    print(f"测试目录：{test_dir}")
    print(f"总文件数：{len(created_files)}")
    print(f"\n现在可以在GUI程序中选择此目录进行测试")
    print(f"测试完成后，可以手动删除测试目录")
    
    # 询问是否立即清理
    try:
        response = input("\n是否立即清理测试文件？(y/N): ").strip().lower()
        if response == 'y':
            cleanup_test_files(test_dir)
        else:
            print(f"测试文件保留在：{test_dir}")
    except KeyboardInterrupt:
        print(f"\n测试文件保留在：{test_dir}")
