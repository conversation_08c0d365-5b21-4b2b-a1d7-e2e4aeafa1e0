{"cells": [{"cell_type": "code", "execution_count": 2, "id": "12aec46a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0    3\n", "1    2\n", "2    0\n", "3    1\n", "Name: apples, dtype: int64\n", "0    3\n", "1    2\n", "2    0\n", "3    1\n", "Name: apples, dtype: int64\n"]}], "source": ["import pandas as pd\n", "\n", "data = {'apples': [3, 2, 0, 1], 'oranges': [2, 4, 6, 8], 'bananas': [1, 3, 5, 7]}\n", "df = pd.DataFrame(data)\n", "\n", "# 使用点操作符\n", "print(df.apples)\n", "\n", "# 使用下标操作符 \n", "print(df['apples'])\n"]}, {"cell_type": "code", "execution_count": null, "id": "86b04658", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}