{"cells": [{"cell_type": "code", "execution_count": 8, "id": "c48fe360", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'王五', '李四', '张三'}\n", "b变量数据类型是： <class 'set'>\n"]}], "source": ["# 创建集合对象\n", "a = {'张三', '李四', '王五'}\n", "print(a)\n", "b = set((20, 10, 50, 40, 30)) \n", "print('b变量数据类型是：', type(b)) "]}, {"cell_type": "code", "execution_count": null, "id": "b5fa6ed5", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}