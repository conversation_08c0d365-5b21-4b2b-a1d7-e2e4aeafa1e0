#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试翻页后动画预览功能是否正常
"""

import tkinter as tk
from tkinter import messagebox
import os
import sys

def test_animation_preview():
    """测试动画预览功能"""
    try:
        # 导入主程序
        from 短视频预览播放 import VideoPreviewApp
        
        # 创建测试窗口
        root = tk.Tk()
        root.title("动画预览功能测试")
        root.geometry("800x600")
        
        # 创建应用实例
        app = VideoPreviewApp(root)
        
        # 创建测试说明
        info_text = """
动画预览功能测试说明：

1. 选择一个包含视频文件的文件夹
2. 将鼠标悬停在第一页的视频缩略图上，观察是否有动画预览
3. 点击"下一页"按钮翻页
4. 将鼠标悬停在第二页的视频缩略图上，观察是否有动画预览
5. 如果第二页也能正常显示动画预览，说明修复成功

预期结果：
- 第一页：动画预览正常 ✓
- 翻页后：动画预览也正常 ✓（这是修复的重点）

如果翻页后动画预览丢失，说明还需要进一步修复。
        """
        
        # 创建说明标签
        info_label = tk.Label(root, text=info_text, justify=tk.LEFT, 
                             font=("Arial", 10), bg="lightyellow")
        info_label.pack(fill=tk.X, padx=10, pady=5)
        
        # 运行测试
        root.mainloop()
        
    except Exception as e:
        messagebox.showerror("测试失败", f"测试过程中出现错误：{e}")
        print(f"测试错误: {e}")

if __name__ == "__main__":
    test_animation_preview()
