{"cells": [{"cell_type": "code", "execution_count": 2, "id": "023c241c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[1. 2. 3. 4.]\n", "float64\n"]}], "source": ["import numpy as np\n", "# 使用dtype参数指定数组类型\n", "b = np.array((1, 2, 3, 4), dtype=float)\n", "print(b)\n", "print(b.dtype)"]}, {"cell_type": "code", "execution_count": null, "id": "e6c4c9f2", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}