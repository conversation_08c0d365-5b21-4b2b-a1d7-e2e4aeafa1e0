import tkinter as tk

import pyautogui
import pyperclip
import time
import threading
import os
import pygetwindow as gw

class AddressGenerator:
    def __init__(self, root):
        self.root = root
        self.root.title("地址生成器")

        # 设置窗口大小
        self.root.geometry("500x300")

        # 创建第一个地址输入框
        self.first_label = tk.Label(root, text="起始地址:")
        self.first_label.pack()
        self.first_entry = tk.Entry(root)
        self.first_entry.pack()
        # 绑定点击事件，自动粘贴剪贴板内容
        self.first_entry.bind("<Button-删除扩展名中的汉字>", self.paste_clipboard)

        # 创建第二个地址输入框
        self.second_label = tk.Label(root, text="结束地址:")
        self.second_label.pack()
        self.second_entry = tk.Entry(root)
        self.second_entry.pack()
        # 绑定点击事件，自动粘贴剪贴板内容
        self.second_entry.bind("<Button-删除扩展名中的汉字>", self.paste_clipboard)

        # 创建批量下载按钮
        self.download_button = tk.Button(root, text="批量下载", command=self.generate_addresses)
        self.download_button.pack()

        # 创建地址列表显示框
        self.addresses_text = tk.Text(root, height=10, width=60)
        self.addresses_text.pack()

        # 初始化地址列表
        self.addresses = []

        # 在程序启动时执行特定命令
        os.system("start cmd /K cd /d D:/tdl_Windows_64bit")

    def paste_clipboard(self, event):
        # 清空输入框内容
        event.widget.delete(0, tk.END)
        # 获取剪贴板内容并粘贴到点击的输入框
        clipboard_content = pyperclip.paste()
        event.widget.insert(tk.END, clipboard_content)

    def generate_addresses(self):
        # 获取第一个地址和第二个地址
        first_address = self.first_entry.get()
        second_address = self.second_entry.get()

        # 解析地址中的数字部分
        first_number = int(''.join(filter(str.isdigit, first_address)))
        second_number = int(''.join(filter(str.isdigit, second_address)))

        # 清空地址列表
        self.addresses = []

        # 生成地址列表
        current_number = first_number
        while current_number <= second_number:
            new_address = f".\\tdl dl -u {first_address.replace(str(first_number), str(current_number))} -t 8 -s 524288 -l 4"
            self.addresses.append(new_address)
            current_number += 1

        # 显示地址列表
        self.show_addresses()

        # 开始发送地址到命令行窗口
        self.send_addresses_to_cmd()

    def show_addresses(self):
        # 清空地址列表显示框
        self.addresses_text.delete(1.0, tk.END)

        # 将地址列表显示在地址列表显示框中
        for address in self.addresses:
            self.addresses_text.insert(tk.END, address + "\n")

    def send_addresses_to_cmd(self):
        # 创建线程发送地址到命令行窗口
        thread = threading.Thread(target=self.send_addresses_thread)
        thread.start()

    def send_addresses_thread(self):
        # 等待1秒，确保地址列表显示出来
        time.sleep(1)

        # 激活Powershell窗口
        powershell_window = None
        for window in gw.getAllWindows():
            if "C:\\Windows\\system32\\cmd.exe" in window.title:
                powershell_window = window
                break

        if powershell_window:
            powershell_window.activate()
            for address in self.addresses:
                 pyautogui.typewrite(address)
                 pyautogui.press('enter')
                # os.system(f"echo {address} | C:\\Windows\\system32\\cmd.exe")
                # 每隔15秒发送一次
                #time.sleep(15)


        else:
            print("未找到Windows Powershell窗口！")

# 创建主窗口
root = tk.Tk()

# 创建应用程序实例
app = AddressGenerator(root)

# 运行程序
root.mainloop()
