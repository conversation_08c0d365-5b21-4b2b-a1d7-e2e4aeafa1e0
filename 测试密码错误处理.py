# -*- coding: utf-8 -*-
"""
测试密码错误处理和模糊匹配功能
"""
import os

def create_test_password_files():
    """创建测试用的密码文件"""
    test_dir = os.path.join(os.getcwd(), "测试密码错误处理")
    
    if not os.path.exists(test_dir):
        os.makedirs(test_dir)
        print(f"创建测试目录：{test_dir}")
    else:
        print(f"测试目录已存在：{test_dir}")
    
    # 创建各种密码文件用于测试模糊匹配
    password_files = [
        # 精确匹配文件
        ("解压密码.txt", "解压密码：correct123"),
        ("密码.txt", "密码：wrong456"),
        
        # 模糊匹配文件
        ("压缩包解压密码.txt", "密码：fuzzy789"),
        ("文件密码说明.txt", "解压密码：match999"),
        ("password_info.txt", "password:english123"),
        ("解压说明.txt", "解压密码：readme456"),
        ("unlock_code.txt", "unlock password: unlock789"),
        
        # 无密码指示文件
        ("无密码文件.txt", "密码：无"),
        
        # 错误密码文件（用于测试Wrong password处理）
        ("错误密码.txt", "密码：wrongpassword"),
        
        # 非密码文件（不应该被匹配）
        ("readme.txt", "这是说明文件，不包含密码"),
        ("config.txt", "配置文件内容"),
    ]
    
    created_files = []
    
    for filename, content in password_files:
        file_path = os.path.join(test_dir, filename)
        
        if os.path.exists(file_path):
            print(f"⏭️ 文件已存在，跳过：{filename}")
            continue
            
        try:
            # 使用GBK编码保存
            with open(file_path, 'w', encoding='gbk') as f:
                f.write(content)
                
            created_files.append(file_path)
            print(f"✅ 创建文件：{filename}")
            
        except Exception as e:
            print(f"❌ 创建文件失败：{filename} - {e}")
    
    return test_dir, created_files

def test_fuzzy_matching():
    """测试模糊匹配功能"""
    print("\n=== 测试模糊匹配功能 ===")
    
    try:
        # 导入解压缩模块
        import sys
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        from 解压缩GUI import UnzipGUI
        import tkinter as tk
        
        # 创建GUI实例用于测试
        root = tk.Tk()
        root.withdraw()  # 隐藏主窗口
        app = UnzipGUI(root)
        
        test_dir, created_files = create_test_password_files()
        
        # 测试获取所有解压密码
        print(f"\n测试目录：{test_dir}")
        passwords = app.get_all_extract_passwords(test_dir, test_dir)
        
        print(f"\n找到的密码数量：{len(passwords)}")
        for i, pwd in enumerate(passwords, 1):
            if pwd is None:
                print(f"{i}. 无密码指示")
            else:
                print(f"{i}. {pwd}")
        
        root.destroy()
        return test_dir
        
    except ImportError as e:
        print(f"❌ 导入模块失败：{e}")
        return None
    except Exception as e:
        print(f"❌ 测试失败：{e}")
        return None

def test_wrong_password_patterns():
    """测试Wrong password错误模式"""
    print("\n=== 测试Wrong password错误模式 ===")
    
    # 模拟7z输出的各种错误信息
    test_outputs = [
        "ERROR: Wrong password : archive.zip",
        "Wrong password",
        "ERROR: Wrong password",
        "7-Zip 19.00 : Copyright (c) 1999-2018 Igor Pavlov : 2019-02-21",
        "Extracting archive: test.zip",
        "ERROR: Wrong password : test.zip",
        "Sub items Errors: 1",
        "Archives with Errors: 1",
    ]
    
    print("测试输出行：")
    for i, output in enumerate(test_outputs, 1):
        has_error = "Wrong password" in output or "ERROR: Wrong password" in output
        status = "❌ 检测到密码错误" if has_error else "✅ 正常输出"
        print(f"{i}. {output}")
        print(f"   {status}")

def show_fuzzy_patterns():
    """显示模糊匹配模式"""
    print("\n=== 模糊匹配模式 ===")
    
    exact_patterns = [
        '解压密码.txt', '解压密码.TXT', 
        '密码.txt', '密码.TXT',
        'password_extract.txt', 'extract_password.txt', 'unzip_password.txt'
    ]
    
    fuzzy_patterns = [
        '解压密码', '密码', 'password', 'pass', 'pwd', 
        '解压', 'extract', 'unzip', 'unlock'
    ]
    
    print("精确匹配文件名：")
    for pattern in exact_patterns:
        print(f"  - {pattern}")
    
    print("\n模糊匹配关键词：")
    for pattern in fuzzy_patterns:
        print(f"  - {pattern}")
    
    print("\n匹配示例：")
    test_filenames = [
        "压缩包解压密码.txt",  # 匹配"解压密码"
        "文件密码说明.txt",    # 匹配"密码"
        "password_info.txt",   # 匹配"password"
        "解压说明.txt",        # 匹配"解压"
        "unlock_code.txt",     # 匹配"unlock"
        "readme.txt",          # 不匹配
        "config.ini",          # 不匹配（非txt）
    ]
    
    for filename in test_filenames:
        matched = False
        matched_pattern = ""
        
        if filename.lower().endswith(('.txt', '.text')):
            filename_lower = filename.lower()
            for pattern in fuzzy_patterns:
                if pattern.lower() in filename_lower:
                    matched = True
                    matched_pattern = pattern
                    break
        
        status = f"✅ 匹配 '{matched_pattern}'" if matched else "❌ 不匹配"
        print(f"  {filename} - {status}")

if __name__ == "__main__":
    print("=== 密码错误处理和模糊匹配测试 ===")
    
    # 显示模糊匹配模式
    show_fuzzy_patterns()
    
    # 测试Wrong password错误模式
    test_wrong_password_patterns()
    
    # 测试模糊匹配功能
    test_dir = test_fuzzy_matching()
    
    if test_dir:
        print(f"\n=== 改进说明 ===")
        print(f"1. Wrong password错误处理：")
        print(f"   - 检测到'Wrong password'或'ERROR: Wrong password'时立即终止当前尝试")
        print(f"   - 避免等待超时，快速切换到下一个密码")
        print(f"   - 提高解压效率")
        
        print(f"\n2. 解压密码文件模糊匹配：")
        print(f"   - 精确匹配：解压密码.txt、密码.txt等")
        print(f"   - 模糊匹配：包含'解压密码'、'密码'、'password'等关键词的txt文件")
        print(f"   - 多文件支持：找到多个密码文件时都会尝试")
        print(f"   - 去重处理：相同密码只尝试一次")
        
        print(f"\n3. 密码尝试顺序：")
        print(f"   - 精确匹配的文件优先")
        print(f"   - 模糊匹配的文件其次")
        print(f"   - 每个文件中的密码都会被提取并尝试")
        
        print(f"\n测试目录：{test_dir}")
        print(f"现在可以在GUI程序中测试改进的功能")
    
    print("\n测试完成！")
