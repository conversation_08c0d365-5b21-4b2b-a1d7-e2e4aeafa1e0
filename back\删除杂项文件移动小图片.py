import os
import shutil
from tkinter import Tk
from tkinter.filedialog import askdirectory
from PIL import Image


def check_and_delete_files(folder_path, extensions):
    found_files = False

    # 遍历指定文件夹及其子文件夹
    for root, dirs, files in os.walk(folder_path):
        for filename in files:
            filename_lower = filename.lower()
            if filename_lower.endswith(extensions):
                filepath = os.path.join(root, filename)
                try:
                    print(f"删除文件: {filepath}")  # 打印删除信息
                    os.remove(filepath)
                    found_files = True
                except PermissionError:
                    print(f"无法删除文件 (权限错误): {filepath}")  # 打印权限错误信息
                except Exception as e:
                    print(f"删除文件 {filepath} 时出错：{e}")  # 打印其他错误信息

    return found_files


def move_low_resolution_images(folder_path):
    lowres_folder = os.path.join(folder_path, 'lowrespic')  # 创建 lowrespic 文件夹的路径
    os.makedirs(lowres_folder, exist_ok=True)  # 创建 lowrespic 文件夹（如果不存在）

    found_images = False

    for root, dirs, files in os.walk(folder_path):
        for filename in files:
            filename_lower = filename.lower()
            if filename_lower.endswith(('.jpg', '.png', '.jpeg')):
                filepath = os.path.join(root, filename)
                try:
                    with Image.open(filepath) as im:
                        width, height = im.size

                    # 检查分辨率是否小于 800x800
                    if width < 800 and height < 800:
                        # 构建保留原始路径信息的目标路径
                        relative_path = os.path.relpath(root, folder_path)  # 获取当前文件夹相对于根文件夹的相对路径
                        target_folder = os.path.join(lowres_folder, relative_path)  # 目标文件夹路径
                        os.makedirs(target_folder, exist_ok=True)  # 创建目标文件夹（如有必要）

                        # 移动文件到目标文件夹
                        target_filepath = os.path.join(target_folder, filename)  # 目标文件路径
                        try:
                            print(f"移动低分辨率文件: {filepath} 到 {target_filepath}")
                            shutil.move(filepath, target_filepath)  # 移动文件
                            found_images = True
                        except PermissionError:
                            print(f"无法移动文件 (权限错误): {filepath}")  # 打印权限错误信息
                        except Exception as e:
                            print(f"移动文件 {filepath} 时出错：{e}")  # 打印其他错误信息
                except Exception as e:
                    print(f"处理文件 {filepath} 时出错：{e}")
                    continue

    return found_images


def delete_empty_folders(folder_path):
    for root, dirs, files in os.walk(folder_path, topdown=False):
        for dir_name in dirs:
            dir_path = os.path.join(root, dir_name)
            if not os.listdir(dir_path):  # 检查目录是否为空
                print(f"文件夹 {dir_path} 是空的，将被删除。")
                try:
                    os.rmdir(dir_path)
                except PermissionError:
                    print(f"无法删除文件夹 (权限错误): {dir_path}")  # 打印权限错误信息
                except Exception as e:
                    print(f"删除文件夹 {dir_path} 时出错：{e}")  # 打印其他错误信息


if __name__ == "__main__":
    # 创建一个对话框，选择一个文件夹
    root = Tk()
    root.withdraw()
    selected_folder = askdirectory()

    # 删除特定扩展名的文件
    found_html_files = check_and_delete_files(
        selected_folder,
        ('.htm', '.html', '.txt', '.url', '.info', '.db', '.mht', '.svg', 'editor.gif', 'signbg1.png', '上村花论坛看小姐姐.jpg')
    )
    if not found_html_files:
        print("没有找到杂项文件。")

    # 移动小分辨率图像
    found_images = move_low_resolution_images(selected_folder)
    if not found_images:
        print("没有找到小分辨率图片。")

    # 删除空文件夹
    delete_empty_folders(selected_folder)

    input("按任意键退出...")
