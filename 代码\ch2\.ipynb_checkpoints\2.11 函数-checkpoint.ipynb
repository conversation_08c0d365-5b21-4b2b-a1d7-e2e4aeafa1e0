{"cells": [{"cell_type": "code", "execution_count": 1, "id": "a32cb230", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["320x480的长方形的面积:153600.00\n"]}], "source": ["def rectangle_area(width, height): \n", "    area = width * height\n", "    return area\n", "\n", "\n", "r_area = rectangle_area(320.0, 480.0) \n", "\n", "print(\"320x480的长方形的面积:{0:.2f}\".format(r_area))"]}, {"cell_type": "code", "execution_count": null, "id": "652bc362", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "c114a908", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}