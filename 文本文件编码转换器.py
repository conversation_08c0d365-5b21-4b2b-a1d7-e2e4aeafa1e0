import tkinter as tk
from tkinter import filedialog, messagebox
import os
import chardet  # 检测文件编码的库


def detect_file_encoding(file_path):
    """
    自动检测文件编码类型
    """
    try:
        with open(file_path, 'rb') as f:  # 以二进制形式打开文件
            raw_data = f.read()  # 读取文件内容
            result = chardet.detect(raw_data)  # 检测编码
            encoding = result['encoding']  # 获取检测到的编码
            confidence = result.get('confidence', 0)  # 检测可信度
            if encoding and confidence > 0.5:  # 如果有编码且可信度较高
                return encoding
            else:
                return None
    except Exception as e:
        messagebox.showerror("错误", f"无法检测文件编码：{str(e)}")
        return None


def convert_encoding(input_file, input_encoding, output_encoding):
    try:
        # 生成输出文件的路径
        output_file = f"{os.path.splitext(input_file)[0]}-1{os.path.splitext(input_file)[1]}"

        # 读取文件
        with open(input_file, 'r', encoding=input_encoding) as infile:
            content = infile.read()

        # 写入新的编码文件
        with open(output_file, 'w', encoding=output_encoding) as outfile:
            outfile.write(content)

        messagebox.showinfo("成功", f"成功将 '{input_file}' 转换为 '{output_file}'")
    except UnicodeDecodeError:
        messagebox.showerror("错误", "输入文件的编码与指定编码不匹配，请检查编码设置。")
    except Exception as e:
        messagebox.showerror("错误", f"转换失败：{str(e)}")


def select_input_file():
    file_path = filedialog.askopenfilename(title="选择输入文件")
    if file_path:
        input_file_entry.delete(0, tk.END)  # 清空输入框
        input_file_entry.insert(0, file_path)  # 显示选择的文件路径

        # 自动检测文件编码
        detected_encoding = detect_file_encoding(file_path)
        if detected_encoding:
            input_encoding_var.set(detected_encoding)  # 自动设置初始编码
            messagebox.showinfo("编码检测", f"检测到文件编码为：{detected_encoding}")
        else:
            messagebox.showwarning("警告", "无法自动检测文件编码，请手动选择编码。")


def convert_files():
    input_file = input_file_entry.get()
    input_encoding = input_encoding_var.get()
    output_encoding = output_encoding_var.get()
    if not os.path.isfile(input_file):
        messagebox.showerror("错误", "请选择有效的输入文件。")
        return
    convert_encoding(input_file, input_encoding, output_encoding)


# 创建主窗口
root = tk.Tk()
root.title("编码转换器")

# 输入文件选择
tk.Label(root, text="输入文件:").grid(row=0, column=0, padx=10, pady=10)
input_file_entry = tk.Entry(root, width=50)
input_file_entry.grid(row=0, column=1, padx=10, pady=10)
tk.Button(root, text="选择文件", command=select_input_file).grid(row=0, column=2, padx=10, pady=10)

# 编码选择
tk.Label(root, text="初始编码:").grid(row=1, column=0, padx=10, pady=10)
input_encoding_var = tk.StringVar(value="utf-8")  # 默认值
input_encoding_menu = tk.OptionMenu(root, input_encoding_var, "utf-8", "ISO-8859-1", "Windows-1252", "GBK", "ASCII")
input_encoding_menu.grid(row=1, column=1, padx=10, pady=10)

tk.Label(root, text="目标编码:").grid(row=2, column=0, padx=10, pady=10)
output_encoding_var = tk.StringVar(value="ISO-8859-1")  # 默认值
output_encoding_menu = tk.OptionMenu(root, output_encoding_var, "utf-8", "ISO-8859-1", "Windows-1252", "GBK", "ASCII")
output_encoding_menu.grid(row=2, column=1, padx=10, pady=10)

# 转换按钮
tk.Button(root, text="开始转换", command=convert_files).grid(row=3, column=1, pady=10)

# 启动主循环
root.mainloop()
