#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HTML解码链接提取器 - 图形界面版本
用于从HTML文件中提取特定格式的解码链接，并汇总包含链接的行及其上下文
"""

import re
import os
import sys
import tkinter as tk
from tkinter import filedialog, messagebox, ttk
import threading
from typing import List, Dict
from html.parser import HTMLParser
import html

class HTMLTextExtractor(HTMLParser):
    """HTML文本提取器，用于从HTML中提取纯文本内容"""
    def __init__(self):
        super().__init__()
        self.text_lines = []
        self.current_line = ""

    def handle_data(self, data):
        """处理文本数据"""
        # 清理文本，去除多余空白
        cleaned_data = ' '.join(data.split())
        if cleaned_data:
            self.current_line += cleaned_data

    def handle_starttag(self, tag, attrs):
        """处理开始标签"""
        # 对于块级元素，在前面添加换行
        block_tags = ['div', 'p', 'br', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
                     'li', 'tr', 'td', 'th', 'pre', 'blockquote']
        if tag.lower() in block_tags:
            if self.current_line.strip():
                self.text_lines.append(self.current_line.strip())
                self.current_line = ""

    def handle_endtag(self, tag):
        """处理结束标签"""
        # 对于块级元素，在后面添加换行
        block_tags = ['div', 'p', 'br', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
                     'li', 'tr', 'td', 'th', 'pre', 'blockquote']
        if tag.lower() in block_tags:
            if self.current_line.strip():
                self.text_lines.append(self.current_line.strip())
                self.current_line = ""

    def get_text_lines(self):
        """获取提取的文本行"""
        # 添加最后一行
        if self.current_line.strip():
            self.text_lines.append(self.current_line.strip())

        # 过滤空行
        return [line for line in self.text_lines if line.strip()]

class HTMLLinkExtractor:
    def __init__(self):
        # 定义各种链接格式的正则表达式模式
        # 注意：长模式要放在前面，避免短模式匹配到长链接的一部分
        self.patterns = [
            # showfilesbot_数字字母_数字字母_数字字母_字母数字组合 格式（4段格式，最长，优先匹配）
            r'showfilesbot_\d+[A-Za-z]_\d+[A-Za-z]_\d+[A-Za-z]_[A-Za-z0-9]+',
            # showfilesbot_数字字母_数字字母_字母数字组合 格式（3段格式）
            r'showfilesbot_\d+[A-Za-z]_\d+[A-Za-z]_[A-Za-z0-9]+',
            # showfilesbot_数字字母_字母数字组合 格式（2段格式，短格式）
            r'showfilesbot_\d+[A-Za-z]_[A-Za-z0-9]+',
            # doc+...=_mda 格式
            r'doc\+[A-Za-z0-9+/]+=_mda',
            # 字母数字下划线=_grp 格式
            r'[A-Za-z0-9_]+=_grp',
            # @filepan_bot:_数字字母_数字字母_字母数字 格式（支持所有字母）
            r'@filepan_bot:_\d+[A-Za-z]_\d+[A-Za-z]_[A-Za-z0-9]+',
        ]

        # 编译正则表达式以提高性能
        self.compiled_patterns = [re.compile(pattern, re.IGNORECASE) for pattern in self.patterns]
    
    def extract_links_from_text(self, text: str) -> List[str]:
        """从文本中提取所有匹配的链接，按在文本中出现的位置排序，避免重叠匹配"""
        found_links = []

        # 收集所有匹配项及其位置和长度
        matches_with_positions = []
        for pattern in self.compiled_patterns:
            for match in pattern.finditer(text):
                matches_with_positions.append((match.start(), match.end(), match.group()))

        # 按位置排序
        matches_with_positions.sort(key=lambda x: x[0])

        # 去除重叠的匹配，保留最长的
        filtered_matches = []
        for start, end, link in matches_with_positions:
            # 检查是否与已有匹配重叠
            is_overlapping = False
            for existing_start, existing_end, existing_link in filtered_matches:
                # 如果有重叠
                if not (end <= existing_start or start >= existing_end):
                    is_overlapping = True
                    # 如果当前匹配更长，替换已有的
                    if (end - start) > (existing_end - existing_start):
                        filtered_matches.remove((existing_start, existing_end, existing_link))
                        filtered_matches.append((start, end, link))
                    break

            if not is_overlapping:
                filtered_matches.append((start, end, link))

        # 重新按位置排序并提取链接文本
        filtered_matches.sort(key=lambda x: x[0])
        found_links = [match[2] for match in filtered_matches]

        return found_links

    def merge_overlapping_results(self, results: List[Dict]) -> List[Dict]:
        """合并有重合内容的结果"""
        if not results:
            return results

        # 按行号排序
        sorted_results = sorted(results, key=lambda x: x['line_number'])
        merged_results = []

        i = 0
        while i < len(sorted_results):
            current_result = sorted_results[i]
            current_lines = current_result['context_lines'].copy()
            current_links = current_result['links'].copy()
            start_line = current_result['line_number'] - 1  # 上一行的行号
            end_line = current_result['line_number'] + 1    # 下一行的行号

            # 检查后续结果是否有重合
            j = i + 1
            while j < len(sorted_results):
                next_result = sorted_results[j]
                next_start_line = next_result['line_number'] - 1
                next_end_line = next_result['line_number'] + 1

                # 如果有重合或相邻，则合并
                if next_start_line <= end_line + 1:  # +1 允许相邻的结果也合并
                    # 合并链接
                    current_links.extend(next_result['links'])

                    # 扩展行范围
                    end_line = max(end_line, next_end_line)

                    j += 1
                else:
                    break

            # 去重但保持顺序
            unique_links = []
            seen = set()
            for link in current_links:
                if link not in seen:
                    unique_links.append(link)
                    seen.add(link)

            # 重新构建合并后的上下文
            merged_result = {
                'file_path': current_result['file_path'],
                'line_number': current_result['line_number'],
                'encoding': current_result['encoding'],
                'links': unique_links,  # 保持顺序的去重
                'merged_range': (start_line + 1, end_line + 1),  # 转回1基索引
                'merged_count': j - i
            }

            merged_results.append(merged_result)
            i = j

        return merged_results

    def build_merged_context(self, text_lines: List[str], merged_result: Dict) -> List[str]:
        """为合并后的结果构建上下文"""
        start_line, end_line = merged_result['merged_range']
        context_lines = []

        for line_num in range(start_line, end_line + 1):
            if 1 <= line_num <= len(text_lines):
                line_text = text_lines[line_num - 1]  # 转为0基索引
                context_lines.append(line_text)

        return context_lines
    
    def process_html_file(self, file_path: str) -> List[Dict]:
        """处理单个HTML文件，返回包含链接的行及其上下文"""
        results = []

        try:
            # 尝试不同的编码格式读取文件
            encodings = ['utf-8', 'gbk', 'gb2312', 'ansi', 'latin-1']
            content = None
            used_encoding = None

            for encoding in encodings:
                try:
                    with open(file_path, 'r', encoding=encoding) as f:
                        content = f.read()
                        used_encoding = encoding
                        break
                except UnicodeDecodeError:
                    continue

            if content is None:
                return results

            # 使用HTML文本提取器提取纯文本
            extractor = HTMLTextExtractor()
            extractor.feed(content)
            text_lines = extractor.get_text_lines()

            # 第一步：找到所有包含链接的行
            initial_results = []
            for i, line in enumerate(text_lines):
                found_links = self.extract_links_from_text(line)
                if found_links:
                    context = {
                        'file_path': file_path,
                        'line_number': i + 1,
                        'encoding': used_encoding,
                        'links': found_links,
                        'context_lines': []  # 暂时为空，后面会填充
                    }
                    initial_results.append(context)

            # 第二步：合并重合的结果
            merged_results = self.merge_overlapping_results(initial_results)

            # 第三步：为合并后的结果构建上下文
            for merged_result in merged_results:
                context_lines = self.build_merged_context(text_lines, merged_result)
                merged_result['context_lines'] = context_lines
                results.append(merged_result)

        except Exception as e:
            print(f"处理文件 {file_path} 时出错: {str(e)}")

        return results
    
    def process_directory(self, directory_path: str) -> List[Dict]:
        """处理目录中的所有HTML文件"""
        all_results = []
        html_files = []
        
        # 查找所有HTML文件
        for root, dirs, files in os.walk(directory_path):
            for file in files:
                if file.lower().endswith(('.html', '.htm')):
                    html_files.append(os.path.join(root, file))
        
        for file_path in html_files:
            results = self.process_html_file(file_path)
            all_results.extend(results)
        
        return all_results
    
    def save_results(self, results: List[Dict], output_file: str = 'extracted_links.txt'):
        """将结果保存到文件"""
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("HTML解码链接提取结果（已合并重合内容）\n")
            f.write("=" * 60 + "\n\n")

            for i, result in enumerate(results, 1):
                f.write(f"匹配项 {i}")

                # 如果是合并的结果，显示合并信息
                if 'merged_count' in result and result['merged_count'] > 1:
                    f.write(f"（合并了 {result['merged_count']} 个重合项）")

                f.write(":\n")

                # 显示所有上下文行
                context_lines = result['context_lines']
                for line in context_lines:
                    if line.strip():  # 只保存非空行
                        f.write(f"{line}\n")

                f.write("\n" + "-" * 60 + "\n\n")

            f.write(f"总计找到 {len(results)} 个匹配项")

class HTMLLinkExtractorGUI:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("HTML解码链接提取器")
        self.root.geometry("700x600")
        self.root.resizable(True, True)

        # 设置窗口居中
        self.center_window()

        self.extractor = HTMLLinkExtractor()
        self.results = []
        self.backup_results = []  # 用于撤销操作的备份
        self.results_saved = True  # 跟踪结果是否已保存

        # 绑定窗口关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

        self.setup_ui()
    
    def center_window(self):
        """窗口居中显示"""
        self.root.update_idletasks()
        width = 700
        height = 600
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')
    
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="15")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(3, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="HTML解码链接提取器", 
                               font=('Microsoft YaHei', 16, 'bold'))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 25))
        
        # 文件选择区域
        ttk.Label(main_frame, text="选择HTML文件或文件夹:", 
                 font=('Microsoft YaHei', 10)).grid(row=1, column=0, sticky=tk.W, pady=8)
        
        self.path_var = tk.StringVar()
        path_entry = ttk.Entry(main_frame, textvariable=self.path_var, width=60, font=('Consolas', 9))
        path_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), padx=(15, 10), pady=8)
        
        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=1, column=2, padx=(5, 0), pady=8)
        
        ttk.Button(button_frame, text="选择文件", 
                  command=self.select_file, width=10).pack(side=tk.TOP, pady=(0, 5))
        ttk.Button(button_frame, text="选择文件夹", 
                  command=self.select_folder, width=10).pack(side=tk.TOP)
        
        # 操作按钮
        action_frame = ttk.Frame(main_frame)
        action_frame.grid(row=2, column=0, columnspan=3, pady=25)

        ttk.Button(action_frame, text="开始提取",
                  command=self.start_extraction, width=12).pack(side=tk.LEFT, padx=(0, 8))
        ttk.Button(action_frame, text="调入结果",
                  command=self.load_results, width=12).pack(side=tk.LEFT, padx=(0, 8))
        ttk.Button(action_frame, text="剪贴链接",
                  command=self.copy_and_remove_links, width=12).pack(side=tk.LEFT, padx=(0, 8))
        ttk.Button(action_frame, text="保存结果",
                  command=self.save_results, width=12).pack(side=tk.LEFT, padx=(0, 8))
        ttk.Button(action_frame, text="撤销",
                  command=self.undo_copy, width=12).pack(side=tk.LEFT)
        
        # 结果显示区域
        result_frame = ttk.LabelFrame(main_frame, text="提取结果", padding="10")
        result_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(15, 0))
        result_frame.columnconfigure(0, weight=1)
        result_frame.rowconfigure(0, weight=1)
        
        # 创建文本框和滚动条
        self.result_text = tk.Text(result_frame, wrap=tk.WORD, font=('Consolas', 9),
                                  bg='#f8f9fa', fg='#333333')
        scrollbar = ttk.Scrollbar(result_frame, orient=tk.VERTICAL, command=self.result_text.yview)
        self.result_text.configure(yscrollcommand=scrollbar.set)

        # 创建右键菜单
        self.context_menu = tk.Menu(self.root, tearoff=0)
        self.context_menu.add_command(label="全选", command=lambda: self.result_text.tag_add(tk.SEL, "1.0", tk.END))
        self.context_menu.add_command(label="复制", command=lambda: self.root.clipboard_append(self.result_text.selection_get()))

        # 绑定右键菜单
        self.result_text.bind("<Button-3>", self.show_context_menu)

        self.result_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # 状态栏
        self.status_var = tk.StringVar()
        self.status_var.set("就绪 - 请选择HTML文件或文件夹")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, 
                              relief=tk.SUNKEN, anchor=tk.W, font=('Microsoft YaHei', 9))
        status_bar.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(15, 0))
        
        # 初始提示信息
        self.show_initial_info()
    
    def show_initial_info(self):
        """显示初始使用说明"""
        info_text = """欢迎使用HTML解码链接提取器！

支持的链接格式：
• showfilesbot_5D_1P_92V_j1B723J0W041O2f8o7Z3 (4段格式)
• showfilesbot_1V_D1t7H5U2y0i7z6p8W1h6 (3段格式)
• showfilesbot_1P_q1o7s5u2w0q4n9h2D5R3 (2段格式)
• VT6AjWM77I5v_Xl62uqZV0tU=_grp
• CUbkYSmly4nY_wky2uqZV0tU=_grp
• @filepan_bot:_78P_115V_eaeT1VzuCB3x

使用方法：
1. 点击"选择文件"选择单个HTML文件，或点击"选择文件夹"选择包含HTML文件的文件夹
2. 点击"开始提取"开始提取解码链接
3. 点击"调入结果"可以加载之前保存的结果文件
4. 查看提取结果，可以点击"剪贴链接"复制前3个链接到剪贴板并移除
5. 点击"撤销"可以恢复上次剪贴操作
6. 点击"保存结果"将当前结果保存到文件

程序会从HTML中提取纯文本内容，显示包含链接的上下文，并自动合并重合内容。
"""
        self.result_text.insert(tk.END, info_text)
    
    def select_file(self):
        """选择HTML文件"""
        file_path = filedialog.askopenfilename(
            title="选择HTML文件",
            filetypes=[
                ("HTML文件", "*.html *.htm"),
                ("所有文件", "*.*")
            ]
        )
        if file_path:
            self.path_var.set(file_path)
            self.status_var.set(f"已选择文件: {os.path.basename(file_path)}")
    
    def select_folder(self):
        """选择文件夹"""
        folder_path = filedialog.askdirectory(title="选择包含HTML文件的文件夹")
        if folder_path:
            self.path_var.set(folder_path)
            self.status_var.set(f"已选择文件夹: {os.path.basename(folder_path)}")
    
    def start_extraction(self):
        """开始提取链接"""
        path = self.path_var.get().strip()
        if not path:
            messagebox.showwarning("警告", "请先选择HTML文件或文件夹")
            return
        
        if not os.path.exists(path):
            messagebox.showerror("错误", f"路径不存在: {path}")
            return
        
        # 在新线程中执行提取操作，避免界面卡顿
        self.status_var.set("正在提取链接，请稍候...")
        self.result_text.delete(1.0, tk.END)
        self.result_text.insert(tk.END, "正在处理，请稍候...\n")
        
        def extract_worker():
            try:
                if os.path.isfile(path):
                    self.results = self.extractor.process_html_file(path)
                else:
                    self.results = self.extractor.process_directory(path)
                
                # 在主线程中更新UI
                self.root.after(0, self.display_results)
                
            except Exception as e:
                self.root.after(0, lambda: self.show_error(f"提取过程中出错: {str(e)}"))
        
        thread = threading.Thread(target=extract_worker)
        thread.daemon = True
        thread.start()
    
    def display_results(self):
        """显示提取结果"""
        if not self.results:
            self.result_text.delete(1.0, tk.END)
            self.result_text.insert(tk.END, "未找到任何匹配的解码链接\n\n")
            self.result_text.insert(tk.END, "请检查：\n")
            self.result_text.insert(tk.END, "1. HTML文件是否包含目标格式的链接\n")
            self.result_text.insert(tk.END, "2. 文件编码是否正确\n")
            self.result_text.insert(tk.END, "3. 链接格式是否符合支持的模式")
            self.status_var.set("提取完成 - 未找到链接")
            return

        # 使用refresh_display方法显示结果
        self.refresh_display()

        # 更新状态
        link_count = sum(len(result['links']) for result in self.results)
        self.status_var.set(f"✅ 提取完成 - 找到 {len(self.results)} 个匹配项，共 {link_count} 个链接")

        # 标记结果未保存
        self.results_saved = False
        print(f"提取结果完成 - 标记为未保存: {self.results_saved}")  # 调试信息
    
    def save_results(self):
        """保存结果到文件"""
        if not self.results:
            messagebox.showwarning("警告", "没有结果可保存")
            return

        # 设置默认目录为 D:\teldown\
        default_dir = r"D:\teldown"

        # 如果默认目录不存在，则使用当前目录
        if not os.path.exists(default_dir):
            default_dir = os.getcwd()

        file_path = filedialog.asksaveasfilename(
            title="保存结果",
            initialdir=default_dir,
            defaultextension=".txt",
            filetypes=[
                ("文本文件", "*.txt"),
                ("所有文件", "*.*")
            ],
            initialfile="HTML解码链接提取结果.txt"
        )
        
        if file_path:
            try:
                self.extractor.save_results(self.results, file_path)
                messagebox.showinfo("保存成功", f"结果已保存到:\n{file_path}")
                self.status_var.set(f"结果已保存到: {os.path.basename(file_path)}")
                # 标记结果已保存
                self.results_saved = True
            except Exception as e:
                messagebox.showerror("保存失败", f"保存失败: {str(e)}")
    
    def copy_and_remove_links(self):
        """剪贴前三个链接并从结果中移除包含这些链接的组"""
        if not self.results:
            messagebox.showwarning("警告", "没有结果可以剪贴")
            return

        # 保存当前结果作为备份（用于撤销）
        self.backup_results = self.results.copy()

        # 按顺序收集前三个链接
        copied_links = []
        new_results = []
        links_copied = 0

        for i, result in enumerate(self.results):
            result_links = result['links']
            links_to_copy = []

            # 从当前结果中取链接，直到达到3个总数
            for link in result_links:
                if links_copied < 3:
                    copied_links.append(link)
                    links_to_copy.append(link)
                    links_copied += 1
                else:
                    break

            # 计算剩余链接
            remaining_links = [link for link in result_links if link not in links_to_copy]

            if remaining_links:
                # 如果还有剩余链接，创建新的结果保留它们
                new_result = result.copy()
                new_result['links'] = remaining_links

                # 更新上下文文本，移除已剪贴的链接
                updated_context = []
                for line in result['context_lines']:
                    if line.strip():
                        updated_line = line
                        # 从文本中移除已剪贴的链接
                        for copied_link in links_to_copy:
                            updated_line = updated_line.replace(copied_link, '')

                        # 清理多余的空格和标点
                        updated_line = ' '.join(updated_line.split())  # 移除多余空格
                        updated_line = updated_line.replace('  ', ' ')  # 移除双空格
                        updated_line = updated_line.replace(' ,', ',')  # 修复逗号前的空格
                        updated_line = updated_line.replace(' .', '.')  # 修复句号前的空格
                        updated_line = updated_line.replace(',,', ',')  # 移除双逗号
                        updated_line = updated_line.strip(' ,.')  # 移除首尾的空格、逗号、句号

                        if updated_line:  # 只添加非空行
                            updated_context.append(updated_line)
                    else:
                        updated_context.append(line)

                new_result['context_lines'] = updated_context
                new_results.append(new_result)
            # 如果没有剩余链接，这个结果就被完全移除了（不添加到new_results）

            # 如果已经复制了3个链接，将后续的所有结果直接保留
            if links_copied >= 3:
                # 将剩余的所有结果添加到new_results
                remaining_results = self.results[i + 1:]
                new_results.extend(remaining_results)
                break

        # 构建剪贴板内容 - 只包含三个链接
        clipboard_text = "\n".join(copied_links)
        try:
            # 先清空剪贴板
            self.root.clipboard_clear()
            self.root.update()  # 确保清空操作完成

            # 再添加新内容
            self.root.clipboard_append(clipboard_text)
            self.root.update()  # 确保剪贴板更新

            # 更新结果列表
            self.results = new_results

            # 刷新显示
            self.refresh_display()

            # 显示成功消息
            messagebox.showinfo("成功", f"已复制 {links_copied} 个链接到剪贴板：\n\n{chr(10).join(copied_links)}\n\n相关组已从结果中移除，点击'撤销'按钮可以恢复")
            self.status_var.set(f"已剪贴 {links_copied} 个链接，剩余 {len(self.results)} 组 - 可撤销")

            # 标记结果未保存
            self.results_saved = False

        except Exception as e:
            messagebox.showerror("错误", f"复制到剪贴板失败: {str(e)}")

    def refresh_display(self):
        """刷新结果显示"""
        self.result_text.delete(1.0, tk.END)

        if not self.results:
            self.result_text.insert(tk.END, "所有结果已被剪贴完毕\n\n")
            self.result_text.insert(tk.END, "可以重新开始提取或选择其他文件。")
            self.status_var.set("所有结果已剪贴完毕")
            return

        # 显示统计信息
        link_count = sum(len(result['links']) for result in self.results)
        file_count = len(set(result['file_path'] for result in self.results))

        summary = f"🎉 剩余结果：\n\n"
        summary += f"📊 统计信息：\n"
        summary += f"   • 剩余匹配项: {len(self.results)} 个\n"
        summary += f"   • 总链接数: {link_count} 个\n"
        summary += f"   • 涉及文件数: {file_count} 个\n"
        summary += "=" * 80 + "\n\n"

        self.result_text.insert(tk.END, summary)

        # 显示精简结果 - 只显示文本内容
        for i, result in enumerate(self.results, 1):
            output = f"匹配项 {i}"

            # 如果是合并的结果，显示合并信息
            if 'merged_count' in result and result['merged_count'] > 1:
                output += f"（合并了 {result['merged_count']} 个重合项）"

            output += f":\n"

            # 显示所有上下文行
            context_lines = result['context_lines']
            for line in context_lines:
                if line.strip():  # 只显示非空行
                    output += f"{line}\n"

            output += "\n" + "-" * 60 + "\n\n"

            self.result_text.insert(tk.END, output)

        # 滚动到顶部
        self.result_text.see(1.0)

    def undo_copy(self):
        """撤销剪贴操作"""
        if not self.backup_results:
            messagebox.showwarning("警告", "没有可撤销的操作")
            return

        # 恢复备份的结果
        self.results = self.backup_results.copy()
        self.backup_results = []  # 清空备份

        # 刷新显示
        self.refresh_display()

        # 更新状态
        link_count = sum(len(result['links']) for result in self.results)
        self.status_var.set(f"✅ 已撤销剪贴操作 - 恢复到 {len(self.results)} 个匹配项，共 {link_count} 个链接")

        messagebox.showinfo("成功", "已撤销上次剪贴操作，结果已恢复")

        # 标记结果未保存
        self.results_saved = False

    def load_results(self):
        """调入之前保存的结果文件"""
        print("调入结果被调用")  # 调试信息

        try:
            # 设置默认目录为 D:\teldown\
            default_dir = r"D:\teldown"

            # 如果默认目录不存在，则使用当前目录
            if not os.path.exists(default_dir):
                default_dir = os.getcwd()

            file_path = filedialog.askopenfilename(
                title="调入结果",
                initialdir=default_dir,
                filetypes=[
                    ("文本文件", "*.txt"),
                    ("所有文件", "*.*")
                ],
                initialfile="HTML解码链接提取结果.txt"
            )

            print(f"用户选择的文件路径: {repr(file_path)}")  # 调试信息

            if file_path:
                try:
                    # 解析保存的结果文件
                    parsed_results = self.parse_saved_results(file_path)

                    if parsed_results:
                        # 清空当前结果和备份
                        self.backup_results = []

                        # 加载解析的结果
                        self.results = parsed_results

                        # 刷新显示
                        self.refresh_display()

                        # 更新状态
                        link_count = sum(len(result['links']) for result in self.results)
                        self.status_var.set(f"✅ 已调入 {len(self.results)} 个匹配项，共 {link_count} 个链接")

                        messagebox.showinfo("调入成功", f"已成功调入结果文件:\n{file_path}\n\n共 {len(self.results)} 个匹配项")
                        print(f"文件调入成功: {file_path}")  # 调试信息

                        # 标记结果已保存（因为是从文件调入的）
                        self.results_saved = True
                    else:
                        messagebox.showwarning("调入失败", "文件格式不正确或没有找到有效的结果数据")

                except Exception as e:
                    print(f"解析文件时出错: {e}")  # 调试信息
                    messagebox.showerror("调入失败", f"解析文件失败: {str(e)}")
            else:
                print("用户取消了调入操作")  # 调试信息

        except Exception as e:
            print(f"调入对话框出现错误: {e}")  # 调试信息
            messagebox.showerror("错误", f"调入对话框出现错误: {str(e)}")

    def parse_saved_results(self, file_path: str) -> List[Dict]:
        """解析保存的结果文件，重新构建结果数据"""
        results = []

        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # 按匹配项分割内容
            sections = content.split('匹配项 ')[1:]  # 跳过标题部分

            for i, section in enumerate(sections):
                try:
                    lines = section.strip().split('\n')
                    if not lines:
                        continue

                    # 解析匹配项标题
                    title_line = lines[0]
                    merged_count = 1

                    # 检查是否有合并信息
                    if '（合并了' in title_line and '个重合项）' in title_line:
                        import re
                        match = re.search(r'合并了 (\d+) 个重合项', title_line)
                        if match:
                            merged_count = int(match.group(1))

                    # 提取上下文行（跳过分隔线）
                    context_lines = []
                    for line in lines[1:]:
                        line = line.strip()
                        if line and not line.startswith('-') and not line.startswith('='):
                            context_lines.append(line)

                    # 从上下文中提取链接
                    all_text = ' '.join(context_lines)
                    extracted_links = self.extractor.extract_links_from_text(all_text)

                    if extracted_links and context_lines:
                        result = {
                            'file_path': file_path,
                            'line_number': i + 1,
                            'encoding': 'utf-8',
                            'links': extracted_links,
                            'context_lines': context_lines,
                            'merged_count': merged_count
                        }
                        results.append(result)

                except Exception as e:
                    print(f"解析匹配项 {i+1} 时出错: {e}")
                    continue

            print(f"成功解析 {len(results)} 个匹配项")  # 调试信息
            return results

        except Exception as e:
            print(f"读取文件时出错: {e}")
            raise e

    def show_context_menu(self, event):
        """显示右键菜单"""
        try:
            self.context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            self.context_menu.grab_release()
    
    def show_context_menu(self, event):
        """显示右键菜单"""
        try:
            self.context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            self.context_menu.grab_release()

    def show_error(self, message):
        """显示错误信息"""
        messagebox.showerror("错误", message)
        self.status_var.set("❌ 出现错误")

    def on_closing(self):
        """处理窗口关闭事件"""
        print(f"关闭窗口被调用 - 结果数量: {len(self.results)}, 已保存: {self.results_saved}")  # 调试信息

        # 检查是否有未保存的结果
        if self.results and not self.results_saved:
            print("弹出保存确认对话框")  # 调试信息
            # 弹出确认对话框
            message = (
                "您有未保存的提取结果，是否要保存？\n\n"
                "• 点击“是”保存结果后退出\n"
                "• 点击“否”直接退出（不保存）\n"
                "• 点击“取消”返回程序"
            )
            result = messagebox.askyesnocancel("保存结果", message)

            if result is True:  # 用户选择保存
                self.save_results()
                # 如果保存成功，则关闭窗口
                if self.results_saved:
                    self.root.destroy()
                # 如果保存失败或用户取消保存，则不关闭窗口
            elif result is False:  # 用户选择不保存
                self.root.destroy()
            # 如果用户选择取消（result is None），则不做任何操作，窗口保持打开
        else:
            print("直接关闭窗口（没有未保存的结果）")  # 调试信息
            # 没有未保存的结果，直接关闭
            self.root.destroy()

    def run(self):
        """运行GUI"""
        self.root.mainloop()

def main():
    """主函数"""
    try:
        app = HTMLLinkExtractorGUI()
        app.run()
    except Exception as e:
        print(f"程序启动失败: {str(e)}")
        input("按回车键退出...")

if __name__ == "__main__":
    main()
