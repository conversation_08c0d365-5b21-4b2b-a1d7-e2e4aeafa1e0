{"cells": [{"cell_type": "code", "execution_count": 1, "id": "8facffaa", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<!doctype html>\r\n", "<html lang=\"en\">\r\n", "<head>\r\n", "    <meta charset=\"UTF-8\">\r\n", "    <meta name=\"Generator\" content=\"EditPlus®\">\r\n", "    <meta name=\"Author\" content=\"\">\r\n", "    <meta name=\"Keywords\" content=\"\">\r\n", "    <meta name=\"Description\" content=\"\">\r\n", "    <title>Document</title>\r\n", "</head>\r\n", "<body>\r\n", "<div id=\"quotes_content_left_pnlAJAX\">\r\n", "    <table class=\"historical-data__table\">\r\n", "        <thead class=\"historical-data__table-headings\">\r\n", "        <tr class=\"historical-data__row historical-data__row--headings\">\r\n", "            <th class=\"historical-data__table-heading\" scope=\"col\">Date</th>\r\n", "            <th class=\"historical-data__table-heading\" scope=\"col\">Open</th>\r\n", "            <th class=\"historical-data__table-heading\" scope=\"col\">High</th>\r\n", "            <th class=\"historical-data__table-heading\" scope=\"col\">Low</th>\r\n", "            <th class=\"historical-data__table-heading\" scope=\"col\">Close</th>\r\n", "            <th class=\"historical-data__table-heading\" scope=\"col\">Volume</th>\r\n", "        </tr>\r\n", "        </thead>\r\n", "        <tbody class=\"historical-data__table-body\">\r\n", "        <tr class=\"historical-data__row\">\r\n", "            <th>10/04/2022</th>\r\n", "            <td>225.64</td>\r\n", "            <td>227.49</td>\r\n", "            <td>223.89</td>\r\n", "            <td>227.01</td>\r\n", "            <td>34,755,550</td>\r\n", "        </tr>\r\n", "        <tr class=\"historical-data__row\">\r\n", "            <th>10/03/2022</th>\r\n", "            <td>218.43</td>\r\n", "            <td>220.96</td>\r\n", "            <td>215.132</td>\r\n", "            <td>220.82</td>\r\n", "            <td>30,352,690</td>\r\n", "        </tr>\r\n", "        <tr class=\"historical-data__row\">\r\n", "            <th>10/02/2022</th>\r\n", "            <td>223.06</td>\r\n", "            <td>223.58</td>\r\n", "            <td>217.93</td>\r\n", "            <td>218.96</td>\r\n", "            <td>35,767,260</td>\r\n", "        </tr>\r\n", "        <tr class=\"historical-data__row\">\r\n", "            <th>10/01/2022</th>\r\n", "            <td>225.07</td>\r\n", "            <td>228.22</td>\r\n", "            <td>224.2</td>\r\n", "            <td>224.59</td>\r\n", "            <td>36,187,160</td>\r\n", "        </tr>\r\n", "        <tr class=\"historical-data__row\">\r\n", "            <th>09/30/2022</th>\r\n", "            <td>220.9</td>\r\n", "            <td>224.58</td>\r\n", "            <td>220.79</td>\r\n", "            <td>223.97</td>\r\n", "            <td>26,318,580</td>\r\n", "        </tr>\r\n", "        <tr class=\"historical-data__row\">\r\n", "            <th>09/27/2022</th>\r\n", "            <td>220.54</td>\r\n", "            <td>220.96</td>\r\n", "            <td>217.2814</td>\r\n", "            <td>218.82</td>\r\n", "            <td>25,361,290</td>\r\n", "        </tr>\r\n", "        <tr class=\"historical-data__row\">\r\n", "            <th>09/26/2022</th>\r\n", "            <td>220</td>\r\n", "            <td>220.94</td>\r\n", "            <td>218.83</td>\r\n", "            <td>219.89</td>\r\n", "            <td>19,088,310</td>\r\n", "        </tr>\r\n", "        <tr class=\"historical-data__row\">\r\n", "            <th>09/25/2022</th>\r\n", "            <td>218.55</td>\r\n", "            <td>221.5</td>\r\n", "            <td>217.1402</td>\r\n", "            <td>221.03</td>\r\n", "            <td>22,481,010</td>\r\n", "        </tr>\r\n", "        <tr class=\"historical-data__row\">\r\n", "            <th>09/24/2022</th>\r\n", "            <td>221.03</td>\r\n", "            <td>222.49</td>\r\n", "            <td>217.19</td>\r\n", "            <td>217.68</td>\r\n", "            <td>31,434,370</td>\r\n", "        </tr>\r\n", "        <tr class=\"historical-data__row\">\r\n", "            <th>09/23/2022</th>\r\n", "            <td>218.95</td>\r\n", "            <td>219.84</td>\r\n", "            <td>217.65</td>\r\n", "            <td>218.72</td>\r\n", "            <td>19,419,650</td>\r\n", "        </tr>\r\n", "        <tr class=\"historical-data__row\">\r\n", "            <th>09/20/2022</th>\r\n", "            <td>221.38</td>\r\n", "            <td>222.56</td>\r\n", "            <td>217.473</td>\r\n", "            <td>217.73</td>\r\n", "            <td>57,977,090</td>\r\n", "        </tr>\r\n", "        <tr class=\"historical-data__row\">\r\n", "            <th>09/19/2022</th>\r\n", "            <td>222.01</td>\r\n", "            <td>223.76</td>\r\n", "            <td>220.37</td>\r\n", "            <td>220.96</td>\r\n", "            <td>22,187,880</td>\r\n", "        </tr>\r\n", "        <tr class=\"historical-data__row\">\r\n", "            <th>09/18/2022</th>\r\n", "            <td>221.06</td>\r\n", "            <td>222.85</td>\r\n", "            <td>219.44</td>\r\n", "            <td>222.77</td>\r\n", "            <td>25,643,090</td>\r\n", "        </tr>\r\n", "        <tr class=\"historical-data__row\">\r\n", "            <th>09/17/2022</th>\r\n", "            <td>219.96</td>\r\n", "            <td>220.82</td>\r\n", "            <td>219.12</td>\r\n", "            <td>220.7</td>\r\n", "            <td>18,386,470</td>\r\n", "        </tr>\r\n", "        <tr class=\"historical-data__row\">\r\n", "            <th>09/16/2022</th>\r\n", "            <td>217.73</td>\r\n", "            <td>220.13</td>\r\n", "            <td>217.56</td>\r\n", "            <td>219.9</td>\r\n", "            <td>21,158,140</td>\r\n", "        </tr>\r\n", "        <tr class=\"historical-data__row\">\r\n", "            <th>09/13/2022</th>\r\n", "            <td>220</td>\r\n", "            <td>220.79</td>\r\n", "            <td>217.02</td>\r\n", "            <td>218.75</td>\r\n", "            <td>39,763,300</td>\r\n", "        </tr>\r\n", "        <tr class=\"historical-data__row\">\r\n", "            <th>09/12/2022</th>\r\n", "            <td>224.8</td>\r\n", "            <td>226.42</td>\r\n", "            <td>222.86</td>\r\n", "            <td>223.085</td>\r\n", "            <td>32,226,670</td>\r\n", "        </tr>\r\n", "        <tr class=\"historical-data__row\">\r\n", "            <th>09/11/2022</th>\r\n", "            <td>218.07</td>\r\n", "            <td>223.71</td>\r\n", "            <td>217.73</td>\r\n", "            <td>223.59</td>\r\n", "            <td>44,289,650</td>\r\n", "        </tr>\r\n", "        </tbody>\r\n", "    </table>\r\n", "</div>\r\n", "</body>\r\n", "</html>\r\n", "\n"]}], "source": ["import urllib.request\n", "\n", "# url = 'https://www.nasdaq.com/symbol/aapl/historical#.UWdnJBDMhHk'\n", "# 换成自己到路径\n", "url = \"file:///C:/Users/<USER>/OneDrive/书/北大/AI时代Python量化交易实战：ChatGPT让量化交易插上翅膀/代码/ch5/data/nasdaq-Apple1.html\"\n", "\n", "req = urllib.request.Request(url)\n", "\n", "with urllib.request.urlopen(req) as response:\n", "    data = response.read()\n", "    html_data = data.decode()\n", "    print(html_data)\n"]}, {"cell_type": "code", "execution_count": null, "id": "719e8108", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}