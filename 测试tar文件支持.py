# -*- coding: utf-8 -*-
"""
测试tar文件支持功能
"""
import os
import tempfile
import sys

# 添加当前目录到路径，以便导入解压缩模块
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def create_test_tar_files():
    """创建测试用的tar文件"""
    # 创建临时目录
    test_dir = tempfile.mkdtemp()
    print(f"测试目录：{test_dir}")
    
    # 创建各种tar文件测试
    test_files = [
        # 单个tar文件
        "测试文档.tar",
        "backup.tar",
        
        # 分卷tar文件
        "大文件.tar.001",
        "大文件.tar.002", 
        "大文件.tar.003",
        
        # part格式的tar文件
        "压缩包.part01.tar",
        "压缩包.part02.tar",
        "压缩包.part03.tar",
        
        # 其他格式（用于对比）
        "正常文件.zip",
        "测试.rar",
        "压缩.7z",
    ]
    
    created_files = []
    
    for filename in test_files:
        file_path = os.path.join(test_dir, filename)
        try:
            # 创建空文件用于测试
            with open(file_path, 'wb') as f:
                f.write(b'test content for ' + filename.encode('utf-8'))
                
            created_files.append(file_path)
            print(f"✅ 创建文件：{filename}")
            
        except Exception as e:
            print(f"❌ 创建文件失败：{filename} - {e}")
    
    return test_dir, created_files

def test_is_split_archive_main_file():
    """测试is_split_archive_main_file函数"""
    print("\n=== 测试 is_split_archive_main_file 函数 ===")
    
    try:
        # 导入解压缩模块
        from 解压缩 import is_split_archive_main_file
        
        test_dir, created_files = create_test_tar_files()
        
        for file_path in created_files:
            filename = os.path.basename(file_path)
            result = is_split_archive_main_file(file_path)
            
            print(f"文件：{filename}")
            print(f"  是否为主文件：{'✅ 是' if result else '❌ 否'}")
            
            # 预期结果分析
            expected = False
            if filename in ["测试文档.tar", "backup.tar", "正常文件.zip", "测试.rar", "压缩.7z"]:
                expected = True  # 单个文件
            elif filename in ["大文件.tar.001", "压缩包.part01.tar"]:
                expected = True  # 分卷文件的第一个
            elif filename in ["大文件.tar.002", "大文件.tar.003", "压缩包.part02.tar", "压缩包.part03.tar"]:
                expected = False  # 分卷文件的后续部分
                
            if result == expected:
                print(f"  结果：✅ 正确")
            else:
                print(f"  结果：❌ 错误（预期：{expected}）")
            print()
            
        return test_dir
        
    except ImportError as e:
        print(f"❌ 导入模块失败：{e}")
        return None
    except Exception as e:
        print(f"❌ 测试失败：{e}")
        return None

def test_file_filtering():
    """测试文件过滤逻辑"""
    print("\n=== 测试文件过滤逻辑 ===")
    
    # 测试文件扩展名检查
    test_files = [
        "test.tar",      # 应该被处理
        "test.TAR",      # 应该被处理（大小写）
        "test.zip",      # 应该被处理
        "test.rar",      # 应该被处理
        "test.7z",       # 应该被处理
        "test.txt",      # 不应该被处理
        "test.mp4",      # 不应该被处理
        "test",          # 不应该被处理（无扩展名）
    ]
    
    supported_extensions = ('.zip', '.ZIP', '.rar', '.RAR', '.tar', '.TAR', '.7z', '.7Z', '.7z.001', '.zip.001', '.z01', '.z02')
    
    for filename in test_files:
        should_process = filename.endswith(supported_extensions)
        print(f"文件：{filename}")
        print(f"  是否应该处理：{'✅ 是' if should_process else '❌ 否'}")
        
        if filename.lower().endswith('.tar'):
            print(f"  TAR文件检测：✅ 正确识别为TAR文件")
        print()

def cleanup_test_files(test_dir):
    """清理测试文件"""
    if test_dir and os.path.exists(test_dir):
        try:
            import shutil
            shutil.rmtree(test_dir)
            print(f"\n✅ 已清理测试目录：{test_dir}")
        except Exception as e:
            print(f"\n❌ 清理测试目录失败：{e}")

if __name__ == "__main__":
    print("=== TAR文件支持测试 ===")
    
    # 测试文件过滤逻辑
    test_file_filtering()
    
    # 测试is_split_archive_main_file函数
    test_dir = test_is_split_archive_main_file()
    
    print("\n=== 测试总结 ===")
    print("1. TAR文件现在应该被正确识别和处理")
    print("2. 分卷TAR文件（.tar.001, .part01.tar等）应该被正确处理")
    print("3. 单个TAR文件应该被识别为主文件")
    print("4. 分卷TAR文件的第一个文件应该被识别为主文件")
    
    # 询问是否清理测试文件
    if test_dir:
        try:
            response = input("\n是否清理测试文件？(y/N): ").strip().lower()
            if response == 'y':
                cleanup_test_files(test_dir)
            else:
                print(f"测试文件保留在：{test_dir}")
        except KeyboardInterrupt:
            print(f"\n测试文件保留在：{test_dir}")
    
    print("\n现在可以在GUI程序中测试TAR文件的解压功能！")
