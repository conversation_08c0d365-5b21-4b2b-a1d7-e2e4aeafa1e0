import os
import shutil
import tkinter as tk
from tkinter import filedialog


def move_videos_to_current_directory(start_directory):
    for root, dirs, files in os.walk(start_directory):
        for subdirectory in dirs:
            subdirectory_path = os.path.join(root, subdirectory)
            move_videos_to_current_directory(subdirectory_path)

            if has_video_files(subdirectory_path):
                for filename in os.listdir(subdirectory_path):
                    file_path = os.path.join(subdirectory_path, filename)
                    if is_video_file(filename):
                        if filename == subdirectory:
                            new_filename = add_suffix_to_filename(filename, "-no")
                            new_file_path = os.path.join(subdirectory_path, new_filename)
                            os.rename(file_path, new_file_path)
                            print(f"Added suffix to video file {filename}: {new_filename}")
                            file_path = new_file_path

                        target_path = os.path.join(start_directory, os.path.basename(file_path))
                        count = 1
                        base, extension = os.path.splitext(file_path)
                        while os.path.exists(target_path):
                            new_base = f"{base}-{count}"
                            target_path = os.path.join(start_directory, os.path.basename(new_base + extension))
                            count += 1

                        shutil.move(file_path, target_path)
                        print(f"Moved file: {file_path} to {target_path}")


def has_video_files(directory):
    for filename in os.listdir(directory):
        if is_video_file(filename):
            return True
    return False


def is_video_file(filename):
    video_extensions = [".mp4", ".avi", ".mkv", ".mov", ".wmv", ".mpg", ".ts"]
    return any(filename.endswith(ext) for ext in video_extensions)


def add_suffix_to_filename(filename, suffix):
    base, extension = os.path.splitext(filename)
    return f"{base}{suffix}{extension}"


def select_directory_and_move_videos():
    start_directory = filedialog.askdirectory(title="选择开始的目录")
    if start_directory:
        move_videos_to_current_directory(start_directory)
        print("处理完成！")


root = tk.Tk()
button = tk.Button(root, text="选择开始的目录并移动视频文件", command=select_directory_and_move_videos)
button.pack()
root.mainloop()
