{"cells": [{"cell_type": "code", "execution_count": 1, "id": "20877cfa", "metadata": {}, "outputs": [], "source": ["fobj = open('test1.txt', 'a+', encoding='utf-8')\n", "fobj.write('大家好！')\n", "fobj.close()\n", "\n", "# 使用with as自动资源管理\n", "with open('test1.txt', 'a+', encoding='utf-8') as fobj:\n", "    fobj.write('大家好！')"]}, {"cell_type": "code", "execution_count": null, "id": "88eb3665", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}