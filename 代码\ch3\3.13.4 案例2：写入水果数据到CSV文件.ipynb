{"cells": [{"cell_type": "code", "execution_count": 6, "id": "d7223fa0", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>apples</th>\n", "      <th>oranges</th>\n", "      <th>bananas</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>June</th>\n", "      <td>3</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th><PERSON></th>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Lily</th>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>David</th>\n", "      <td>1</td>\n", "      <td>3</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["        apples  oranges  bananas\n", "June         3        0        1\n", "Robert       2        1        2\n", "Lily         0        2        1\n", "David        1        3        0"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "data = { 'apples': [3, 2, 0, 1],\n", "         'oranges': [0, 1, 2, 3],\n", "         'bananas': [1, 2, 1, 0]}\n", "df = pd.DataFrame(data, index=['June','<PERSON>','<PERSON>','<PERSON>'])\n", "df"]}, {"cell_type": "code", "execution_count": 7, "id": "e02b45c1", "metadata": {}, "outputs": [], "source": ["df.to_csv('data/水果.csv', header=['苹果','桔子','香蕉'],encoding='gbk')"]}, {"cell_type": "code", "execution_count": null, "id": "64d2e256", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}