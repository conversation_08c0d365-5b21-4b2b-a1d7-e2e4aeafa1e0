{"cells": [{"cell_type": "code", "execution_count": 1, "id": "d8ff434c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[1 2 3 4 5 6 7 8 9]\n", "d的形状: (9,)\n", "[[1 2 3]\n", " [4 5 6]\n", " [7 8 9]]\n", "dd的形状: (3, 3)\n"]}], "source": ["import numpy as np\n", "d = np.arange(1, 10)\n", "print(d)\n", "print(\"d的形状:\", d.shape)\n", "dd = d.reshape((3, 3)) # 从一维到二维\n", "\n", "print(dd)\n", "print(\"dd的形状:\", dd.shape)"]}, {"cell_type": "code", "execution_count": null, "id": "5d41521a", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}