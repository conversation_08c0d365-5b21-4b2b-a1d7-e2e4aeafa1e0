{"cells": [{"cell_type": "code", "execution_count": 1, "id": "1ad7d580", "metadata": {}, "outputs": [{"ename": "FileNotFoundError", "evalue": "[Errno 2] No such file or directory: 'C:\\\\\\\\Users\\\\<USER>\\\\\\\\OneDrive\\\\\\\\书\\\\\\\\电子\\\\\\\\Python自动化办公\\\\\\\\code\\\\\\\\chapter1\\\\\\\\test1.txt'", "output_type": "error", "traceback": ["\u001b[1;31m----------------------------------------------------------------------\u001b[0m", "\u001b[1;31mFileNotFoundError\u001b[0m                    <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[1], line 5\u001b[0m\n\u001b[0;32m      2\u001b[0m fobj\u001b[38;5;241m.\u001b[39mwrite(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m大家好\u001b[39m\u001b[38;5;124m'\u001b[39m)\n\u001b[0;32m      4\u001b[0m fname1 \u001b[38;5;241m=\u001b[39m\u001b[38;5;124mr\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mC:\u001b[39m\u001b[38;5;130;01m\\\\\u001b[39;00m\u001b[38;5;124mUsers\u001b[39m\u001b[38;5;124m\\\u001b[39m\u001b[38;5;124mtony\u001b[39m\u001b[38;5;130;01m\\\\\u001b[39;00m\u001b[38;5;124mOneDrive\u001b[39m\u001b[38;5;130;01m\\\\\u001b[39;00m\u001b[38;5;124m书\u001b[39m\u001b[38;5;130;01m\\\\\u001b[39;00m\u001b[38;5;124m电子\u001b[39m\u001b[38;5;130;01m\\\\\u001b[39;00m\u001b[38;5;124mPython自动化办公\u001b[39m\u001b[38;5;130;01m\\\\\u001b[39;00m\u001b[38;5;124mcode\u001b[39m\u001b[38;5;130;01m\\\\\u001b[39;00m\u001b[38;5;124mchapter1\u001b[39m\u001b[38;5;130;01m\\\\\u001b[39;00m\u001b[38;5;124mtest1.txt\u001b[39m\u001b[38;5;124m'\u001b[39m\n\u001b[1;32m----> 5\u001b[0m fobj \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mopen\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43mfname1\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43ma+\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mencoding\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mutf-8\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m)\u001b[49m \n\u001b[0;32m      7\u001b[0m fobj\u001b[38;5;241m.\u001b[39mwrite(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m！\u001b[39m\u001b[38;5;124m'\u001b[39m)\n\u001b[0;32m     10\u001b[0m fname2 \u001b[38;5;241m=\u001b[39m \u001b[38;5;124mr\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mC:\u001b[39m\u001b[38;5;124m\\\u001b[39m\u001b[38;5;124mUsers\u001b[39m\u001b[38;5;124m\\\u001b[39m\u001b[38;5;124mtony\u001b[39m\u001b[38;5;124m\\\u001b[39m\u001b[38;5;124mOneDrive\u001b[39m\u001b[38;5;124m\\\u001b[39m\u001b[38;5;124m书\u001b[39m\u001b[38;5;124m\\\u001b[39m\u001b[38;5;124m电子\u001b[39m\u001b[38;5;124m\\\u001b[39m\u001b[38;5;124mPython自动化办公\u001b[39m\u001b[38;5;124m\\\u001b[39m\u001b[38;5;124mcode\u001b[39m\u001b[38;5;124m\\\u001b[39m\u001b[38;5;124mchapter1\u001b[39m\u001b[38;5;124m\\\u001b[39m\u001b[38;5;124mtest1.txt\u001b[39m\u001b[38;5;124m'\u001b[39m\n", "File \u001b[1;32m~\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\IPython\\core\\interactiveshell.py:284\u001b[0m, in \u001b[0;36m_modified_open\u001b[1;34m(file, *args, **kwargs)\u001b[0m\n\u001b[0;32m    277\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m file \u001b[38;5;129;01min\u001b[39;00m {\u001b[38;5;241m0\u001b[39m, \u001b[38;5;241m1\u001b[39m, \u001b[38;5;241m2\u001b[39m}:\n\u001b[0;32m    278\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mValueError\u001b[39;00m(\n\u001b[0;32m    279\u001b[0m         \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mIPython won\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mt let you open fd=\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mfile\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m by default \u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m    280\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mas it is likely to crash IPython. If you know what you are doing, \u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m    281\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124myou can use builtins\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m open.\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m    282\u001b[0m     )\n\u001b[1;32m--> 284\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mio_open\u001b[49m\u001b[43m(\u001b[49m\u001b[43mfile\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[1;31mFileNotFoundError\u001b[0m: [Errno 2] No such file or directory: 'C:\\\\\\\\Users\\\\<USER>\\\\\\\\OneDrive\\\\\\\\书\\\\\\\\电子\\\\\\\\Python自动化办公\\\\\\\\code\\\\\\\\chapter1\\\\\\\\test1.txt'"]}], "source": ["fobj = open('test1.txt', 'w+', encoding='utf-8') \n", "fobj.write('大家好')\n", "\n", "fname1 =r'C:\\Users\\<USER>\\OneDrive\\书\\北大\\AI时代Python量化交易实战：ChatGPT让量化交易插上翅膀\\代码\\ch2\\\\test1.txt'\n", "fobj = open(fname1, 'a+', encoding='utf-8') \n", "\n", "fobj.write('！')\n", "\n", "\n", "fname2 = r'C:\\Users\\<USER>\\OneDrive\\书\\北大\\AI时代Python量化交易实战：ChatGPT让量化交易插上翅膀\\代码\\ch2\\test1.txt'\n", "fobj = open(fname2, 'r+', encoding='utf-8')  \n", "fobj.write('谢谢！')"]}, {"cell_type": "code", "execution_count": null, "id": "a612b906", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}