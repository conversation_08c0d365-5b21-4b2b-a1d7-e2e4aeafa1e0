import os
import csv
import tkinter as tk
from tkinter import filedialog
from datetime import datetime


def log_operation(log_file, message):
    """Log a message to the operations log file."""
    with open(log_file, 'a', encoding='utf-8') as log:
        log.write(f"{datetime.now()}: {message}\n")
    print(message)  # 打印日志信息到控制台


def record_replacement(replacement_csv, old_str, new_str):
    """Record a string replacement in the replacements CSV file."""
    with open(replacement_csv, 'a', newline='', encoding='utf-8') as csvfile:
        writer = csv.writer(csvfile)
        writer.writerow([old_str, new_str])


def record_deletion(deletion_csv, string_to_remove):
    """Record a string deletion in the deletions CSV file."""
    with open(deletion_csv, 'a', newline='', encoding='utf-8') as csvfile:
        writer = csv.writer(csvfile)
        writer.writerow([string_to_remove])


def auto_rename(target_path):
    """Automatically rename the target path if it already exists."""
    base, extension = os.path.splitext(target_path)
    counter = 1

    # 循环检查目标路径是否存在
    while os.path.exists(target_path):
        target_path = f"{base}_{counter}{extension}"
        counter += 1

    return target_path


def process_folders(folder_path, replacements_csv, deletions_csv, operations_log):
    """Process folders for replacements and deletions."""

    # Read the replacements from the CSV file
    with open(replacements_csv, newline='', encoding='utf-8') as csvfile:
        replacements = list(csv.reader(csvfile))

    # Read the deletions from the CSV file
    with open(deletions_csv, newline='', encoding='utf-8') as csvfile:
        deletions = list(csv.reader(csvfile))

    for root, dirs, _ in os.walk(folder_path, topdown=False):
        # Process directories for replacements and deletions
        for dir_name in dirs:
            new_dir_name = dir_name

            # Check for replacements
            for old_str, new_str in replacements:
                if old_str in new_dir_name:
                    new_dir_name = new_dir_name.replace(old_str, new_str)
                    print(f"Replacing '{old_str}' with '{new_str}' in folder name: '{dir_name}' -> '{new_dir_name}'")

            # Check for deletions
            for string_to_remove in deletions:
                if string_to_remove[0] in new_dir_name:
                    log_operation(operations_log, f"Deleted folder name part: {new_dir_name}")
                    new_dir_name = new_dir_name.replace(string_to_remove[0], "")
                    print(f"Removing '{string_to_remove[0]}' from folder name: '{new_dir_name}'")

            # Rename directory if changed
            if new_dir_name != dir_name:
                old_dir_path = os.path.join(root, dir_name)
                new_dir_path = os.path.join(root, new_dir_name)

                # 如果目标目录已存在，自动重命名
                new_dir_path = auto_rename(new_dir_path)

                os.rename(old_dir_path, new_dir_path)
                log_operation(operations_log, f"Renamed folder: {old_dir_path} -> {new_dir_path}")
                print(f"Renamed folder: '{old_dir_path}' to '{new_dir_path}'")


# 示例用法
if __name__ == "__main__":
    # 定义文件路径
    replacements_csv = r'z:\work\replacements.csv'  # 替换记录文件
    deletions_csv = r'z:\work\deletions.csv'  # 删除记录文件
    operations_log = r'z:\work\operations.log'  # 操作日志文件
    folder_path = filedialog.askdirectory()  # 选择需要处理的文件夹

    # 处理文件夹
    process_folders(folder_path, replacements_csv, deletions_csv, operations_log)
