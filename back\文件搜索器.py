import tkinter as tk
from tkinter import filedialog, messagebox
import os
import subprocess
from datetime import datetime, timedelta

class FileIndexer(tk.Tk):
    def __init__(self):
        super().__init__()
        self.title("文件和文件夹索引搜索工具")
        self.geometry("600x400")

        self.index_button = tk.Button(self, text="建立或更新索引", command=self.create_or_update_index)
        self.index_button.pack(pady=20)

        self.search_label = tk.Label(self, text="输入搜索关键词:")
        self.search_label.pack(pady=10)

        self.search_entry = tk.Entry(self)
        self.search_entry.pack(pady=5)
        self.search_entry.bind("<Return>", self.search)  # 绑定回车键
        self.search_entry.bind("<Button-1>", self.paste_clipboard)  # 绑定点击事件

        self.search_button = tk.Button(self, text="搜索", command=self.search)
        self.search_button.pack(pady=5)

        self.result_list = tk.Listbox(self, width=80, height=15)
        self.result_list.pack(pady=10)
        self.result_list.bind("<Double-1>", self.open_location)  # 绑定双击事件

        self.file_index_path = ""

    def create_or_update_index(self):
        folder_path = filedialog.askdirectory()
        if folder_path:
            # 以文件夹名称命名索引文件
            folder_name = os.path.basename(folder_path)
            self.file_index_path = f"{folder_name}_index.txt"

            if os.path.exists(self.file_index_path):
                file_time = os.path.getmtime(self.file_index_path)
                file_date = datetime.fromtimestamp(file_time)
                if datetime.now() - file_date > timedelta(days=7):
                    self.update_index(folder_path)
                else:
                    self.result_list.insert(tk.END, "现有索引文件仍在有效期内。")
            else:
                self.update_index(folder_path)
        else:
            self.result_list.insert(tk.END, "没有选择文件夹。")

    def update_index(self, folder_path):
        try:
            with open(self.file_index_path, 'w', encoding='utf-8') as index_file:
                for root, dirs, files in os.walk(folder_path):
                    for name in dirs + files:
                        index_file.write(os.path.join(root, name) + '\n')
            self.result_list.insert(tk.END, "索引创建或更新完毕。")
        except Exception as e:
            messagebox.showerror("错误", str(e))

    def search(self, event=None):
        keyword = self.search_entry.get()
        self.result_list.delete(0, tk.END)
        if not keyword:
            self.result_list.insert(tk.END, "请输入搜索关键词。")
            return
        if self.file_index_path and os.path.exists(self.file_index_path):
            try:
                with open(self.file_index_path, 'r', encoding='utf-8') as index_file:
                    found = False
                    for line in index_file:
                        if keyword.lower() in line.lower():
                            self.result_list.insert(tk.END, line.strip())
                            found = True
                    if not found:
                        self.result_list.insert(tk.END, "没有找到匹配项。")
            except FileNotFoundError:
                self.result_list.insert(tk.END, "索引文件未找到，请先创建或更新索引。")
        else:
            self.result_list.insert(tk.END, "索引文件未找到，请先创建或更新索引。")

    def open_location(self, event):
        # 获取选中的项目路径
        selection = self.result_list.get(self.result_list.curselection())
        file_path = os.path.abspath(selection)
        folder_path = os.path.dirname(file_path)
        # 打开文件位置
        if os.name == 'nt':  # Windows
            subprocess.Popen(f'explorer /select,{file_path}')
        elif os.name == 'posix':  # macOS, Linux
            subprocess.Popen(['open', '--', folder_path])

    def paste_clipboard(self, event):
        self.search_entry.delete(0, tk.END)  # 清空输入框
        clipboard_content = self.clipboard_get()  # 获取剪贴板内容
        self.search_entry.insert(0, clipboard_content)  # 插入剪贴板内容

if __name__ == "__main__":
    app = FileIndexer()
    app.mainloop()
