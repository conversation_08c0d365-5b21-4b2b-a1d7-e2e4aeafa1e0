import pyautogui
import pygetwindow as gw
import pyperclip
import time


def check_cmd_window():
    """
    检查是否存在命令行窗口
    """
    try:
        cmd_windows = gw.getWindowsWithTitle('C:\\Windows\\system32\\cmd.exe')
        return cmd_windows
    except Exception as e:
        print("Error while checking command prompt window:", e)
        return []


def close_cmd_window():
    """
    关闭命令行窗口
    """
    cmd_windows = check_cmd_window()
    if cmd_windows:
        cmd_windows[0].close()


def copy_cmd_content():
    """
    复制命令行窗口内容到剪贴板
    """
    try:
        pyautogui.hotkey('ctrl', 'a')
        pyautogui.hotkey('ctrl', 'c')
        time.sleep(0.5)
        return pyperclip.paste()
    except Exception as e:
        print("Error while copying content from command prompt window:", e)
        return ""


def clear_clipboard():
    """
    清除剪贴板内容
    """
    pyperclip.copy('')


def process_cmd_window():
    """
    处理命令行窗口
    """
    cmd_windows = check_cmd_window()

    if cmd_windows:
        cmd_window = cmd_windows[0]
        cmd_window.activate()

        cmd_content = copy_cmd_content()

        if 'CPU:' in cmd_content:
            print("窗口内容包含 'CPU:'，tdl前端正在执行")
            time.sleep(40)
        else:
            print("等待15秒再次检测")
            time.sleep(15)

            cmd_windows = check_cmd_window()
            if cmd_windows:
                cmd_window = cmd_windows[0]
                cmd_window.activate()

                cmd_content = copy_cmd_content()

                if 'CPU:' not in cmd_content:
                    print("tdl前端未运行，关闭窗口")
                    close_cmd_window()
    else:
        print("未找到命令提示符窗口")
        time.sleep(25)


def main():
    while True:
        try:
            process_cmd_window()
        except Exception as e:
            print("Error occurred, clearing clipboard and restarting process_cmd_window:", e)
            clear_clipboard()
            time.sleep(5)  # 等待5秒钟后重新启动
            continue


if __name__ == "__main__":
    main()
