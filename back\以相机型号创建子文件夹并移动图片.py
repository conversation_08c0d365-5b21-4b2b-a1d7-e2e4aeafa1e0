import os
import shutil
from tkinter import Tk, filedialog
import exifread
import string

def sanitize_filename(filename):
    # 移除无效字符
    valid_chars = "-_.() %s%s" % (string.ascii_letters, string.digits)
    return ''.join(c for c in filename if c in valid_chars)

def get_camera_model(image_path):
    try:
        with open(image_path, 'rb') as image_file:
            tags = exifread.process_file(image_file)
            camera_model = tags.get('Image Model', 'Unknown')
            return sanitize_filename(str(camera_model).strip())
    except Exception as e:
        print(f"Error processing file {image_path}: {e}")
        return None

def select_folder_and_process_images():
    # 创建一个Tkinter根窗口并隐藏它
    root = Tk()
    root.withdraw()

    # 打开文件夹选择对话框
    folder_path = filedialog.askdirectory()

    if folder_path:
        # 遍历文件夹中的所有文件
        for root_dir, dirs, files in os.walk(folder_path):
            for file in files:
                # 只处理常见的图像文件类型
                if file.lower().endswith(('.jpg', '.jpeg', '.png', '.tiff', '.bmp')):
                    image_path = os.path.join(root_dir, file)
                    camera_model = get_camera_model(image_path)

                    if camera_model:
                        # 创建以相机型号命名的子文件夹
                        model_folder_path = os.path.join(folder_path, camera_model)
                        if not os.path.exists(model_folder_path):
                            os.makedirs(model_folder_path)

                        # 移动文件到相应的子文件夹
                        dest_path = os.path.join(model_folder_path, file)
                        shutil.move(image_path, dest_path)
                        print(f"Moved {image_path} to {dest_path}")

if __name__ == "__main__":
    select_folder_and_process_images()
