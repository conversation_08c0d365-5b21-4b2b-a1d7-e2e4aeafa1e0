# -*- coding: utf-8 -*-
"""
测试GUI程序的基本功能
"""
import tkinter as tk
from tkinter import messagebox
import sys
import os

def test_gui():
    """测试GUI程序是否能正常启动"""
    try:
        # 导入GUI模块
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        from 解压缩GUI import UnzipGUI
        
        # 创建测试窗口
        root = tk.Tk()
        app = UnzipGUI(root)
        
        # 显示测试信息
        messagebox.showinfo("测试", "GUI程序启动成功！\n\n主要功能：\n• 文件夹选择\n• 开始/停止解压\n• 查看日志\n• 编辑密码字典\n• 实时进度显示")
        
        # 启动主循环
        root.mainloop()
        
    except ImportError as e:
        print(f"导入错误：{e}")
        messagebox.showerror("错误", f"无法导入GUI模块：{e}")
    except Exception as e:
        print(f"程序错误：{e}")
        messagebox.showerror("错误", f"程序启动失败：{e}")

if __name__ == "__main__":
    test_gui()
