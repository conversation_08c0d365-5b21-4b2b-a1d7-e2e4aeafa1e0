{"cells": [{"cell_type": "code", "execution_count": 1, "id": "69c50a39", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Count is : 0\n", "Count is : 1\n", "Count is : 2\n", "Count is : 4\n", "Count is : 5\n", "Count is : 6\n", "Count is : 7\n", "Count is : 8\n", "Count is : 9\n"]}], "source": ["for item in range(10):\n", "    if item == 3:\n", "        continue\n", "    print(\"Count is : {0}\".format(item))"]}, {"cell_type": "code", "execution_count": null, "id": "b9c8369a", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}