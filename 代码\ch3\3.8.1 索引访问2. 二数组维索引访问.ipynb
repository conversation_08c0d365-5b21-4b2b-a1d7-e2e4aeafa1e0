{"cells": [{"cell_type": "code", "execution_count": 1, "id": "6ec7199b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["表达式1：\n", "第一行第二列元素： 2\n", "第三行第三列元素： 9\n", "表达式2：\n", "第一行第二列元素： 2\n", "第三行第三列元素： 9\n"]}], "source": ["import numpy as np\n", "\n", "arr = np.array([[1, 2, 3],\n", "                [4, 5, 6],\n", "                [7, 8, 9]])\n", "\n", "# 使用表达式1进行索引访问\n", "print(\"表达式1：\")\n", "print(\"第一行第二列元素：\", arr[0][1])  # 输出：2\n", "print(\"第三行第三列元素：\", arr[2][2])  # 输出：9\n", "\n", "# 使用表达式2进行索引访问\n", "print(\"表达式2：\")\n", "print(\"第一行第二列元素：\", arr[0, 1])  # 输出：2\n", "print(\"第三行第三列元素：\", arr[2, 2])  # 输出：9\n"]}, {"cell_type": "code", "execution_count": null, "id": "4cadc28c", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "ba3ea1ff", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}