<!DOCTYPE html>
<html>
<head>
<title>贵州茅台（600519）历史交易数据_股票行情_网易财经</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta name="keywords" content="贵州茅台,600519,股票行情,贵州茅台历史交易数据,贵州茅台每日价格统计" />
<meta name="description" content="提供贵州茅台(600519)股票的股票行情、历史交易数据、每日价格统计等信息。" />
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta http-equiv="Content-Language" content="zh-CN" />
<meta name="robots" content="index, follow" />
<meta name="googlebot" content="index, follow" />
<link href="http://img1.cache.netease.com/f2e/finance/gegu/s.1064000.css" rel="stylesheet" type="text/css" charset="utf-8">
<link href="http://img1.cache.netease.com/f2e/finance/gegu/danmaku.959699.css" rel="stylesheet" type="text/css" charset="utf-8">
</head>
<body class="test">
<script charset="gb2312" src="http://img1.cache.netease.com/cnews/js/ntes_jslib_1.x.js" language="javascript" type="text/javascript"></script>
<div class="NTES_bg_">
  <div class="NTES-nav"> <span class="nav-link"><a href="http://www.163.com/">网易首页</a>-<a href="http://news.163.com/">新闻</a>-<a href="http://sports.163.com/">体育</a>-<a href="http://sports.163.com/nba/">NBA</a>-<a href="http://ent.163.com/">娱乐</a>-<a href="http://money.163.com/">财经</a>-<a href="http://money.163.com/stock/">股票</a>-<a href="http://auto.163.com/" id="_link_auto">汽车</a>-<a href="http://tech.163.com/">科技</a>-<a href="http://mobile.163.com/">手机</a>-<a href="http://lady.163.com/">女人</a>-<a href="http://bbs.163.com/">论坛</a>-<a href="http://v.163.com/">视频</a>-<a href="http://blog.163.com/">博客</a>-<a href="http://house.163.com/" id="houseUrl">房产</a>-<a id="homeUrl" href="http://home.163.com/">家居</a>-<a href="http://edu.163.com/">教育</a>-<a href="http://book.163.com/">读书</a>-<a href="http://game.163.com/" id="_link_game">游戏</a>-<a href="http://t.163.com/">微博</a> |</span>
    <div class="rightCon">
      <div class="NTES-link"> <a href="http://email.163.com/" class="cBlue">免费邮箱</a> - <a href="http://reg.163.com/" class="cBlue">通行证登录</a> </div>
      <a href="http://www.163.com/rss"><img width="26" height="14" border="0" src="http://img3.cache.netease.com/cnews/img07/rss.gif" class="rss" alt="Rss"></a> </div>
  </div>
</div>
<script>
//<![CDATA[
NTES.ready(function($) {
    var P_INFO = NTES.cookie.get("P_INFO");
    var S_INFO = NTES.cookie.get("S_INFO");
    if (P_INFO) {
        var mailconfig = {
            "163.com": "http://entry.mail.163.com/coremail/fcg/ntesdoor2?verifycookie=1&lightweight=1",
            "126.com": "http://entry.mail.126.com/cgi/ntesdoor?verifycookie=1&lightweight=1&style=-1",
            "vip.126.com": "http://reg.vip.126.com/enterMail.m",
            "yeah.net": "http://entry.yeah.net/cgi/ntesdoor?verifycookie=1&lightweight=1&style=-1",
            "188": "http://reg.mail.188.com/servlet/enter",
            "vip.163.com": "http://reg.vip.163.com/enterMail.m?enterVip=true-----------"
        };
        var passport = P_INFO.substr(0, P_INFO.indexOf("|"));
        var username = passport.substr(0, P_INFO.indexOf("@"));
        var logstate = P_INFO.split("|")[2];
        var logiframe = "";
        var pspt = passport.length >= 6 ? passport.substr(0, 5) + "...": passport;
        /@([^*]+)/.test(passport);
        var logdomain = RegExp.$1;
        if (P_INFO && S_INFO || logstate == "1") {
            var entrylink_html = '<a href=\"http://reg.163.com/Main.jsp?username=' + passport + '\">进入通行证</a>';
            if (mailconfig[logdomain] != undefined) {
                entrylink_html += '<a href=\"' + mailconfig[logdomain] + '\">进入我的邮箱</a>'
            }
            if (logdomain == "popo.163.com" || mailconfig[logdomain] != undefined) {
                entrylink_html += '<a href="http://blog.163.com/passportIn.do?entry=163">进入我的博客</a><a href="http://photo.163.com/?username=' + passport + '\">进入我的相册</a>'
            }
            entrylink_html += '<a href="http://yuehui.163.com/">进入我的约会</a><a href="http://t.163.com">进入我的微博</a>';
            if (logdomain == "163.com" || logdomain == "126.com" || logdomain == "yeah.net") {
                logiframe = '<iframe allowTransparency=\"true\" style=\"width: 56px; height:26px; float:left; vertical-align: middle;\" id=\"ifrmNtesMailInfo\" border=\"0\" src=\"http://p.mail.' + logdomain + '/mailinfo/shownewmsg_www_1222.htm\" frameBorder=\"0\" scrolling=\"no\"></iframe>'
            }
            var login_html = '<div class=\"ntes-usercenter\"><div class=\"ntes-usercenter-logined\"><strong id=\"ntes_usercenter_name\" class=\"ntes-usercenter-name\" title=\"欢迎你，' + passport + '\">' + pspt + '</strong></div><div id=\"ntes_usercenter_entry\" class=\"ntes-usercenter-entry\"><span class=\"user-entry\">' + entrylink_html + '</span></div></div>' + logiframe + '|<a class=\"ntes-usercenter-loginout\" href=\"http://reg.163.com/Logout.jsp?username=' + passport + '\" target=\"_self\">退出</a></div>';
            $(".NTES-link")[0].innerHTML = login_html;
            $("#ntes_usercenter_name").addEvent("click",
            function(e) {
                $("#ntes_usercenter_entry").style.display = $("#ntes_usercenter_entry").style.display == "block" ? "none": "block";
                e.preventDefault();
                e.cancelBubble = true;
                document.onclick = function() {
                    $("#ntes_usercenter_entry").style.display = "none"
                }
            })
        }
    }
})
//]]>
</script>

<div class="area">
    <div class="header">
    <div class="logo_area">
        <a href="http://money.163.com" class="logo" title="网易财经"></a><span
        class="title">个股行情</span>
        <span class="crumbs"><a href="http://www.163.com">网易首页</a><span>&gt;</span><a
            href="http://money.163.com">网易财经</a><span>&gt;</span><a
            href="http://quotes.money.163.com">行情</a><span>&gt;</span><a
            href="http://quotes.money.163.com">沪深</a><span>&gt;</span><a
            href="/0600519.html">贵州茅台</a><span>&gt;</span><a
                    href="#">历史交易数据</a>
                        </span>
        <iframe src="http://money.163.com/special/ad224_30/" frameborder="0" scrolling="no" style="width:245px;height:40px;border:none;margin-top:-20px;vertical-align: middle;"></iframe>
    </div>
    <table class="main_nav">
        <tr>
            <td><a href="http://quotes.money.163.com/" title="行情中心" target="_blank">行情中心</a></td>
            <!--<td><a href="" title="数据中心">数据中心</a></td>
            <td><a href="" title="资金流向">资金流向</a></td>-->
            <!--<td><a href="http://i.money.163.com/" title="我的投资" class="separate" target="_blank">我的投资</a></td>-->
            <td><a href="http://money.163.com/stock" title="股票" target="_blank">股票</a></td>
            <td><a href="http://money.163.com/ipo" title="新股" target="_blank">新股</a></td>
            <td><a href="http://money.163.com/kechuangban/" title="科创板" target="_blank">科创板</a></td>
            <!--<td><a href="http://money.163.com/chinext" title="创业板" target="_blank">创业板</a></td>-->
            <td><a href="http://money.163.com/fund" title="基金" target="_blank">基金</a></td>
            <td><a href="http://money.163.com/hkstock" title="港股" target="_blank">港股</a></td>
            <td><a href="http://money.163.com/usstock" title="美股" target="_blank">美股</a></td>
            <td><a href="http://money.163.com/special/qhzx/" title="期货" target="_blank">期货</a></td>
            <td><a href="http://money.163.com/forex/" title="外汇" target="_blank">外汇</a></td>
        </tr>
    </table>
    <div class="top_info clearfix">
        <div class="col_1">
            <span><a class="fB" href="http://quotes.money.163.com/0000001.html" target="_blank">上证指数</a></span>
            <span _ntesquote_="code:0000001;attr:price;fixed:2;color:updown;bgchg:price"></span>
            <span _ntesquote_="code:0000001;attr:updown;fixed:2;color:updown;"></span>
            <span _ntesquote_="code:0000001;attr:percent;percent:2;color:updown;"></span>
            <span><em _ntesquote_="code:0000001;attr:turnover;fixed:0;/:100000000"></em>亿</span>
        </div>
        <div class="col_2 separate">
            <div class="mystock">
                <a href="http://i.money.163.com" target="_blank" id="mystock" class="top_mystock"></a>
            </div>
        </div>
        <div class="col_3">
            <input type="button" class="button" value="搜索" id="stockSearchBtn"><input id="stockSearch" name="stock_search" autocomplete="off" type="text" class="field"  placeholder="代码/拼音/名称">
        </div>
    </div>
    <div class="stock_info">
	<table>
		<tr>
			<td class="col_1">
			    <h1 class="name">
					<a href='/0600519.html'>贵州茅台</a>
					<span>(<a href='/0600519.html'>600519</a>)</span>
			    </h1>
			</td>
			<td class="col_2">
				<div  class="stock_detail">
					<table>
						<tr>
							<td class="price">							<span _ntesquote_="code:0600519;fmt:pic_updown;" class="price_arrow"><strong _ntesquote_="code:0600519;attr:price;fixed:2;color:updown"></strong></span><em _ntesquote_="code:0600519;attr:updown;fixed:2;color:updown"></em><br /><em _ntesquote_="code:0600519;attr:percent;percent:2;color:updown"></em>
							</td>
							<td>今开：<strong _ntesquote_="code:0600519;attr:open;fixed:2;color:updown"></strong><br />昨收：<strong _ntesquote_="code:0600519;attr:yestclose;fixed:2;color:updown"></strong></td>
							<td>最高：<strong _ntesquote_="code:0600519;attr:high;fixed:2;color:updown"></strong><br />最低：<strong _ntesquote_="code:0600519;attr:low;fixed:2;color:updown"></strong></td>
							<td>成交量：<strong _ntesquote_="code:0600519;attr:volume;fmt:volume-cut"></strong><br />成交额：<strong _ntesquote_="code:0600519;attr:turnover;fmt:turnover-cut"></strong></td>
							<td>量比：<strong>0.93</strong><br />换手：<strong _ntesquote_="code:0600519;fmt:huanshou;SCSTC27:125619.78"></strong></td>
							<td class="add_btn_cont"><a href="" class="add_btn" data-code="0600519"><span>加入自选股</span></a></td>
						</tr>
						<tr class="stock_bref">
							<td><span  _ntesquote_="code:0600519;attr:time" class="refresh_time"></span></td>
							<td>流通市值：<span _ntesquote_="code:0600519;fmt:stockmcap;SCSTC27:125619.78"></span></td>
							<td title='市盈率=最新股价/最近四个季度每股收益之和'>市盈率：<span _ntesquote_="code:0600519;fmt:pe;MFSUM:35.49"></span></td>
							<td>52周最高：<span class="cRed">2627.88</span></td>
							<td>52周最低：<span class="cGreen">991.52</span></td>
							<td></td>
						</tr>
					</table>
				</div>
			</td>
		</tr>
	</table>
</div>
<script>
	var MONEYSHOWLISTSAVE = 1;
</script>
    <script type="text/javascript">
        var STOCKCODE = '0600519';
        var STOCKSYMBOL = '600519';
        var STOCKNAME = '贵州茅台';
    </script>
</div>
    <div id="menuCont" class="main_menu_cont">
    <ul class="main_menu clearfix">
                    <li class=""><a href="/0600519.html#01a01"><strong>贵州茅台(600519)</strong></a></li>
                        <li class="current active"><a href="/trade/zjlx_600519.html#01b01">资金流向</a></li>
                        <li class=""><a href="/f10/zycwzb_600519.html#01c01">财务分析</a></li>
                        <li class=""><a href="/f10/gdfx_600519.html#01d01">股东股本</a></li>
                        <li class=""><a href="/f10/gsgg_600519.html#01e01">新闻公告</a></li>
                        <li class=""><a href="/f10/hydb_600519.html#01g01">行业对比</a></li>
                        <li class=""><a href="/f10/gszl_600519.html#01f01">公司资料</a></li>
                </ul>
    <div class="submenu_cont clearfix">
                <div class="sub_menu tab_panel clearfix">
            <ul>
                                    <li class=" last"><a href="/0600519.html#01a02">首页概览</a></li>
                                </ul>
        </div>
                <div class="sub_menu tab_panel clearfix">
            <ul>
                                    <li class=""><a href="/trade/zjlx_600519.html#01b02">资金流向</a></li>
                                        <li class=""><a href="/trade/ddtj_600519.html#01b03">大单统计</a></li>
                                        <li class=""><a href="/trade/fjb_600519.html#01b04">分价表</a></li>
                                        <li class=""><a href="/trade/cjmx_600519.html#01b05">成交明细</a></li>
                                        <li class=""><a href="/trade/lhb_600519.html#01b06">龙虎榜</a></li>
                                        <li class="current"><a href="/trade/lsjysj_600519.html#01b07">历史交易数据</a></li>
                                        <li class=" last"><a href="/trade/lszjlx_600519.html#01b08">历史资金流向</a></li>
                                </ul>
        </div>
                <div class="sub_menu tab_panel clearfix">
            <ul>
                                    <li class=""><a href="/f10/zycwzb_600519.html#01c02">主要财务指标</a></li>
                                        <li class=""><a href="/f10/yjyg_600519.html#01c03">业绩预告</a></li>
                                        <li class=""><a href="/f10/cwbbzy_600519.html#01c04">财务报表摘要</a></li>
                                        <li class=""><a href="/f10/zcfzb_600519.html#01c05">资产负债表</a></li>
                                        <li class=""><a href="/f10/lrb_600519.html#01c06">利润表</a></li>
                                        <li class=""><a href="/f10/xjllb_600519.html#01c07">现金流量表</a></li>
                                        <li class=" last"><a href="/f10/dbfx_600519.html#01c08">杜邦分析</a></li>
                                </ul>
        </div>
                <div class="sub_menu tab_panel clearfix">
            <ul>
                                    <li class=""><a href="/f10/gdfx_600519.html#01d02">股东分析</a></li>
                                        <li class=""><a href="/f10/jjcg_600519.html#01d03">基金持股</a></li>
                                        <li class=""><a href="/f10/nbcg_600519.html#01d04">内部持股</a></li>
                                        <li class=" last"><a href="/f10/fhpg_600519.html#01d05">分红配股</a></li>
                                </ul>
        </div>
                <div class="sub_menu tab_panel clearfix">
            <ul>
                                    <li class=""><a href="/f10/gsgg_600519.html#01e02">公司公告</a></li>
                                        <li class=" last"><a href="/f10/gsxw_600519.html#01e03">公司新闻</a></li>
                                </ul>
        </div>
                <div class="sub_menu tab_panel clearfix">
            <ul>
                                    <li class=""><a href="/f10/hydb_600519.html#01g02">行业对比</a></li>
                                        <li class=""><a href="/f10/hybk_600519.html#01g03">行业板块</a></li>
                                        <li class=""><a href="/f10/qybk_600519.html#01g05">区域板块</a></li>
                                        <li class=" last"><a href="/f10/zsbk_600519.html#01g06">指数板块</a></li>
                                </ul>
        </div>
                <div class="sub_menu tab_panel clearfix">
            <ul>
                                    <li class=" last"><a href="/f10/gszl_600519.html#01f02">公司资料</a></li>
                                </ul>
        </div>
            </div>
</div>
    <div class="blank9"></div>
    
<h1 class="title_01"><span class="name">贵州茅台(600519) 历史交易数据</span></h1>
<div class="inner_box">
    <div class="search_area align_r">
        <form id="date" action="/trade/lsjysj_600519.html">
            <select name="year">
                <option value="2021" selected>2021</option><option value="2020" >2020</option><option value="2019" >2019</option><option value="2018" >2018</option><option value="2017" >2017</option><option value="2016" >2016</option><option value="2015" >2015</option><option value="2014" >2014</option><option value="2013" >2013</option><option value="2012" >2012</option><option value="2011" >2011</option><option value="2010" >2010</option><option value="2009" >2009</option><option value="2008" >2008</option><option value="2007" >2007</option><option value="2006" >2006</option><option value="2005" >2005</option><option value="2004" >2004</option><option value="2003" >2003</option><option value="2002" >2002</option><option value="2001" >2001</option>            </select>
			&nbsp;&nbsp;
            <select name="season">
				<option value="4" >四季度</option><option value="3" >三季度</option><option value="2" >二季度</option><option value="1" selected>一季度</option>            </select>
			&nbsp;&nbsp;
            <input type="submit" value="查询" class="search_btn"/>
            <a href="" class="download_link"  id="downloadData">下载数据</a>
        </form>
    </div>
    <table class="table_bg001 border_box limit_sale">
        <thead>
        <tr class="dbrow">
            <th>日期</th>
            <th>开盘价</th>
            <th>最高价</th>
            <th>最低价</th>
            <th>收盘价</th>
            <th>涨跌额</th>
            <th>涨跌幅(%)</th>
            <th>成交量(手)</th>
            <th>成交金额(万元)</th>
            <th>振幅(%)</th>
            <th>换手率(%)</th>
        </tr>
        </thead>
        <tr class=''><td>2021-03-22</td><td class='cGreen'>2,000.10</td><td class='cRed'>2,026.09</td><td class='cGreen'>1,955.55</td><td class='cGreen'>1,989.99</td><td class='cGreen'>-20.01</td><td class='cGreen'>-1.00</td><td>36,914</td><td>732,339</td><td>3.51</td><td>0.29</td></tr><tr class='dbrow'><td>2021-03-19</td><td class='cGreen'>2,035.00</td><td class='cGreen'>2,043.00</td><td class='cGreen'>1,992.23</td><td class='cGreen'>2,010.00</td><td class='cGreen'>-59.70</td><td class='cGreen'>-2.88</td><td>35,781</td><td>721,152</td><td>2.45</td><td>0.28</td></tr><tr class=''><td>2021-03-18</td><td class='cRed'>2,035.00</td><td class='cRed'>2,086.36</td><td class='cRed'>2,035.00</td><td class='cRed'>2,069.70</td><td class='cRed'>39.34</td><td class='cRed'>1.94</td><td>33,865</td><td>699,703</td><td>2.53</td><td>0.27</td></tr><tr class='dbrow'><td>2021-03-17</td><td class='cGreen'>2,000.00</td><td class='cRed'>2,050.00</td><td class='cGreen'>1,976.01</td><td class='cRed'>2,030.36</td><td class='cRed'>19.86</td><td class='cRed'>0.99</td><td>31,375</td><td>635,266</td><td>3.68</td><td>0.25</td></tr><tr class=''><td>2021-03-16</td><td class='cGreen'>1,974.93</td><td class='cRed'>2,019.99</td><td class='cGreen'>1,965.00</td><td class='cRed'>2,010.50</td><td class='cRed'>35.05</td><td class='cRed'>1.77</td><td>34,649</td><td>691,860</td><td>2.78</td><td>0.28</td></tr><tr class='dbrow'><td>2021-03-15</td><td class='cRed'>2,050.00</td><td class='cRed'>2,069.80</td><td class='cGreen'>1,951.15</td><td class='cGreen'>1,975.45</td><td class='cGreen'>-50.55</td><td class='cGreen'>-2.50</td><td>62,485</td><td>1,245,996</td><td>5.86</td><td>0.50</td></tr><tr class=''><td>2021-03-12</td><td class='cRed'>2,070.00</td><td class='cRed'>2,077.00</td><td class='cGreen'>2,002.01</td><td class='cGreen'>2,026.00</td><td class='cGreen'>-22.00</td><td class='cGreen'>-1.07</td><td>40,323</td><td>818,159</td><td>3.66</td><td>0.32</td></tr><tr class='dbrow'><td>2021-03-11</td><td class='cRed'>1,975.00</td><td class='cRed'>2,079.99</td><td class='cGreen'>1,961.48</td><td class='cRed'>2,048.00</td><td class='cRed'>77.99</td><td class='cRed'>3.96</td><td>56,769</td><td>1,152,174</td><td>6.02</td><td>0.45</td></tr><tr class=''><td>2021-03-10</td><td class='cRed'>1,977.00</td><td class='cRed'>1,999.87</td><td class='cRed'>1,967.00</td><td class='cRed'>1,970.01</td><td class='cRed'>33.02</td><td class='cRed'>1.70</td><td>51,172</td><td>1,013,691</td><td>1.70</td><td>0.41</td></tr><tr class='dbrow'><td>2021-03-09</td><td class='cGreen'>1,955.00</td><td class='cRed'>2,000.00</td><td class='cGreen'>1,900.18</td><td class='cGreen'>1,936.99</td><td class='cGreen'>-23.01</td><td class='cGreen'>-1.17</td><td>82,266</td><td>1,610,077</td><td>5.09</td><td>0.65</td></tr><tr class=''><td>2021-03-08</td><td class='cRed'>2,074.96</td><td class='cRed'>2,085.00</td><td class='cGreen'>1,960.00</td><td class='cGreen'>1,960.00</td><td class='cGreen'>-100.11</td><td class='cGreen'>-4.86</td><td>63,100</td><td>1,272,425</td><td>6.07</td><td>0.50</td></tr><tr class='dbrow'><td>2021-03-05</td><td class='cGreen'>2,000.00</td><td class='cRed'>2,095.00</td><td class='cGreen'>1,988.00</td><td class='cRed'>2,060.11</td><td class='cRed'>27.11</td><td class='cRed'>1.33</td><td>63,779</td><td>1,310,032</td><td>5.26</td><td>0.51</td></tr><tr class=''><td>2021-03-04</td><td class='cGreen'>2,095.00</td><td class='cGreen'>2,096.00</td><td class='cGreen'>2,010.10</td><td class='cGreen'>2,033.00</td><td class='cGreen'>-107.00</td><td class='cGreen'>-5.00</td><td>65,088</td><td>1,329,730</td><td>4.01</td><td>0.52</td></tr><tr class='dbrow'><td>2021-03-03</td><td class='cGreen'>2,040.00</td><td class='cRed'>2,149.77</td><td class='cGreen'>2,033.00</td><td class='cRed'>2,140.00</td><td class='cRed'>82.00</td><td class='cRed'>3.98</td><td>54,215</td><td>1,136,669</td><td>5.67</td><td>0.43</td></tr><tr class=''><td>2021-03-02</td><td class='cRed'>2,180.00</td><td class='cRed'>2,180.00</td><td class='cGreen'>2,033.00</td><td class='cGreen'>2,058.00</td><td class='cGreen'>-100.00</td><td class='cGreen'>-4.63</td><td>70,765</td><td>1,479,670</td><td>6.81</td><td>0.56</td></tr><tr class='dbrow'><td>2021-03-01</td><td class='cRed'>2,179.00</td><td class='cRed'>2,179.00</td><td class='cGreen'>2,120.00</td><td class='cRed'>2,158.00</td><td class='cRed'>35.22</td><td class='cRed'>1.66</td><td>44,916</td><td>968,695</td><td>2.78</td><td>0.36</td></tr><tr class=''><td>2021-02-26</td><td class='cGreen'>2,100.00</td><td class='cRed'>2,179.95</td><td class='cGreen'>2,067.30</td><td class='cGreen'>2,122.78</td><td class='cGreen'>-27.22</td><td class='cGreen'>-1.27</td><td>66,525</td><td>1,409,702</td><td>5.24</td><td>0.53</td></tr><tr class='dbrow'><td>2021-02-25</td><td class='cRed'>2,209.00</td><td class='cRed'>2,224.50</td><td class='cGreen'>2,121.21</td><td class='cGreen'>2,150.00</td><td class='cGreen'>-39.00</td><td class='cGreen'>-1.78</td><td>59,726</td><td>1,298,215</td><td>4.72</td><td>0.48</td></tr><tr class=''><td>2021-02-24</td><td class='cRed'>2,307.99</td><td class='cRed'>2,318.00</td><td class='cGreen'>2,160.50</td><td class='cGreen'>2,189.00</td><td class='cGreen'>-118.00</td><td class='cGreen'>-5.11</td><td>82,116</td><td>1,819,503</td><td>6.83</td><td>0.65</td></tr><tr class='dbrow'><td>2021-02-23</td><td class='cGreen'>2,265.14</td><td class='cRed'>2,344.89</td><td class='cGreen'>2,265.00</td><td class='cRed'>2,307.00</td><td class='cRed'>18.98</td><td class='cRed'>0.83</td><td>57,455</td><td>1,331,611</td><td>3.49</td><td>0.46</td></tr><tr class=''><td>2021-02-22</td><td class='cGreen'>2,455.00</td><td class='cGreen'>2,455.00</td><td class='cGreen'>2,278.22</td><td class='cGreen'>2,288.02</td><td class='cGreen'>-171.98</td><td class='cGreen'>-6.99</td><td>78,935</td><td>1,849,878</td><td>7.19</td><td>0.63</td></tr><tr class='dbrow'><td>2021-02-19</td><td class='cGreen'>2,451.16</td><td class='cRed'>2,496.66</td><td class='cGreen'>2,381.60</td><td class='cGreen'>2,460.00</td><td class='cGreen'>-11.00</td><td class='cGreen'>-0.45</td><td>59,385</td><td>1,452,516</td><td>4.66</td><td>0.47</td></tr><tr class=''><td>2021-02-18</td><td class='cGreen'>2,587.98</td><td class='cRed'>2,627.88</td><td class='cGreen'>2,465.00</td><td class='cGreen'>2,471.00</td><td class='cGreen'>-130.00</td><td class='cGreen'>-5.00</td><td>65,912</td><td>1,670,414</td><td>6.26</td><td>0.52</td></tr><tr class='dbrow'><td>2021-02-10</td><td class='cRed'>2,485.00</td><td class='cRed'>2,601.20</td><td class='cRed'>2,485.00</td><td class='cRed'>2,601.00</td><td class='cRed'>144.57</td><td class='cRed'>5.89</td><td>61,371</td><td>1,567,593</td><td>4.73</td><td>0.49</td></tr><tr class=''><td>2021-02-09</td><td class='cGreen'>2,368.80</td><td class='cRed'>2,456.43</td><td class='cGreen'>2,350.00</td><td class='cRed'>2,456.43</td><td class='cRed'>87.63</td><td class='cRed'>3.70</td><td>33,297</td><td>797,293</td><td>4.49</td><td>0.27</td></tr><tr class='dbrow'><td>2021-02-08</td><td class='cRed'>2,337.00</td><td class='cRed'>2,378.88</td><td class='cGreen'>2,313.00</td><td class='cRed'>2,368.80</td><td class='cRed'>55.80</td><td class='cRed'>2.41</td><td>35,786</td><td>842,076</td><td>2.85</td><td>0.28</td></tr><tr class=''><td>2021-02-05</td><td class='cRed'>2,325.00</td><td class='cRed'>2,364.60</td><td class='cGreen'>2,291.00</td><td class='cGreen'>2,313.00</td><td class='cGreen'>-7.85</td><td class='cGreen'>-0.34</td><td>39,729</td><td>925,357</td><td>3.17</td><td>0.32</td></tr><tr class='dbrow'><td>2021-02-04</td><td class='cRed'>2,191.00</td><td class='cRed'>2,330.00</td><td class='cRed'>2,191.00</td><td class='cRed'>2,320.85</td><td class='cRed'>130.94</td><td class='cRed'>5.98</td><td>63,851</td><td>1,459,693</td><td>6.35</td><td>0.51</td></tr><tr class=''><td>2021-02-03</td><td class='cRed'>2,150.00</td><td class='cRed'>2,198.27</td><td class='cGreen'>2,140.00</td><td class='cRed'>2,189.91</td><td class='cRed'>44.91</td><td class='cRed'>2.09</td><td>38,714</td><td>838,405</td><td>2.72</td><td>0.31</td></tr><tr class='dbrow'><td>2021-02-02</td><td class='cRed'>2,112.22</td><td class='cRed'>2,149.99</td><td class='cGreen'>2,102.10</td><td class='cRed'>2,145.00</td><td class='cRed'>35.68</td><td class='cRed'>1.69</td><td>34,582</td><td>737,223</td><td>2.27</td><td>0.28</td></tr><tr class=''><td>2021-02-01</td><td class='cRed'>2,130.00</td><td class='cRed'>2,160.00</td><td class='cGreen'>2,095.00</td><td class='cGreen'>2,109.32</td><td class='cGreen'>-6.86</td><td class='cGreen'>-0.32</td><td>29,341</td><td>621,964</td><td>3.07</td><td>0.23</td></tr><tr class='dbrow'><td>2021-01-29</td><td class='cRed'>2,101.19</td><td class='cRed'>2,149.48</td><td class='cRed'>2,090.22</td><td class='cRed'>2,116.18</td><td class='cRed'>28.18</td><td class='cRed'>1.35</td><td>33,701</td><td>716,618</td><td>2.84</td><td>0.27</td></tr><tr class=''><td>2021-01-28</td><td class='cGreen'>2,080.00</td><td class='cRed'>2,109.66</td><td class='cGreen'>2,060.02</td><td class='cGreen'>2,088.00</td><td class='cGreen'>-1.00</td><td class='cGreen'>-0.05</td><td>35,129</td><td>733,708</td><td>2.38</td><td>0.28</td></tr><tr class='dbrow'><td>2021-01-27</td><td class='cGreen'>2,141.89</td><td class='cGreen'>2,141.89</td><td class='cGreen'>2,085.00</td><td class='cGreen'>2,089.00</td><td class='cGreen'>-52.89</td><td class='cGreen'>-2.47</td><td>37,120</td><td>780,945</td><td>2.66</td><td>0.30</td></tr><tr class=''><td>2021-01-26</td><td class='cRed'>2,185.00</td><td class='cRed'>2,187.00</td><td class='cGreen'>2,125.11</td><td class='cGreen'>2,141.89</td><td class='cGreen'>-33.11</td><td class='cGreen'>-1.52</td><td>31,747</td><td>682,017</td><td>2.85</td><td>0.25</td></tr><tr class='dbrow'><td>2021-01-25</td><td class='cRed'>2,083.00</td><td class='cRed'>2,179.50</td><td class='cRed'>2,083.00</td><td class='cRed'>2,175.00</td><td class='cRed'>95.05</td><td class='cRed'>4.57</td><td>55,957</td><td>1,202,947</td><td>4.64</td><td>0.45</td></tr><tr class=''><td>2021-01-22</td><td class='cGreen'>2,069.00</td><td class='cRed'>2,100.00</td><td class='cGreen'>2,060.10</td><td class='cRed'>2,079.95</td><td class='cRed'>9.95</td><td class='cRed'>0.48</td><td>29,248</td><td>609,026</td><td>1.93</td><td>0.23</td></tr><tr class='dbrow'><td>2021-01-21</td><td class='cRed'>2,048.00</td><td class='cRed'>2,090.02</td><td class='cRed'>2,041.00</td><td class='cRed'>2,070.00</td><td class='cRed'>29.37</td><td class='cRed'>1.44</td><td>38,310</td><td>795,346</td><td>2.40</td><td>0.31</td></tr><tr class=''><td>2021-01-20</td><td class='cGreen'>2,008.00</td><td class='cRed'>2,055.49</td><td class='cGreen'>1,982.50</td><td class='cRed'>2,040.63</td><td class='cRed'>31.22</td><td class='cRed'>1.55</td><td>45,082</td><td>912,645</td><td>3.63</td><td>0.36</td></tr><tr class='dbrow'><td>2021-01-19</td><td class='cRed'>2,073.11</td><td class='cRed'>2,096.30</td><td class='cGreen'>2,006.85</td><td class='cGreen'>2,009.41</td><td class='cGreen'>-53.59</td><td class='cGreen'>-2.60</td><td>46,345</td><td>947,928</td><td>4.34</td><td>0.37</td></tr><tr class=''><td>2021-01-18</td><td class='cGreen'>2,061.06</td><td class='cRed'>2,091.48</td><td class='cGreen'>2,040.27</td><td class='cGreen'>2,063.00</td><td class='cGreen'>-19.00</td><td class='cGreen'>-0.91</td><td>39,144</td><td>807,672</td><td>2.46</td><td>0.31</td></tr><tr class='dbrow'><td>2021-01-15</td><td class='cGreen'>2,115.00</td><td class='cRed'>2,134.35</td><td class='cGreen'>2,029.00</td><td class='cGreen'>2,082.00</td><td class='cGreen'>-52.00</td><td class='cGreen'>-2.44</td><td>59,509</td><td>1,237,210</td><td>4.94</td><td>0.47</td></tr><tr class=''><td>2021-01-14</td><td class='cGreen'>2,156.00</td><td class='cGreen'>2,163.00</td><td class='cGreen'>2,116.11</td><td class='cGreen'>2,134.00</td><td class='cGreen'>-30.00</td><td class='cGreen'>-1.39</td><td>36,152</td><td>772,209</td><td>2.17</td><td>0.29</td></tr><tr class='dbrow'><td>2021-01-13</td><td class='cRed'>2,164.00</td><td class='cRed'>2,173.33</td><td class='cGreen'>2,135.00</td><td class='cRed'>2,164.00</td><td class='cRed'>3.10</td><td class='cRed'>0.14</td><td>34,390</td><td>741,833</td><td>1.77</td><td>0.27</td></tr><tr class=''><td>2021-01-12</td><td class='cGreen'>2,088.00</td><td class='cRed'>2,160.90</td><td class='cGreen'>2,085.00</td><td class='cRed'>2,160.90</td><td class='cRed'>61.17</td><td class='cRed'>2.91</td><td>41,059</td><td>880,056</td><td>3.61</td><td>0.33</td></tr><tr class='dbrow'><td>2021-01-11</td><td class='cGreen'>2,090.00</td><td class='cRed'>2,150.50</td><td class='cGreen'>2,058.60</td><td class='cRed'>2,099.73</td><td class='cRed'>9.73</td><td class='cRed'>0.47</td><td>51,080</td><td>1,076,857</td><td>4.40</td><td>0.41</td></tr><tr class=''><td>2021-01-08</td><td class='cRed'>2,142.00</td><td class='cRed'>2,150.88</td><td class='cGreen'>2,063.02</td><td class='cGreen'>2,090.00</td><td class='cGreen'>-50.00</td><td class='cGreen'>-2.34</td><td>53,862</td><td>1,134,520</td><td>4.11</td><td>0.43</td></tr><tr class='dbrow'><td>2021-01-07</td><td class='cGreen'>2,097.00</td><td class='cRed'>2,140.00</td><td class='cGreen'>2,075.00</td><td class='cRed'>2,140.00</td><td class='cRed'>40.00</td><td class='cRed'>1.90</td><td>37,931</td><td>799,118</td><td>3.10</td><td>0.30</td></tr><tr class=''><td>2021-01-06</td><td class='cRed'>2,064.80</td><td class='cRed'>2,125.00</td><td class='cGreen'>2,036.03</td><td class='cRed'>2,100.00</td><td class='cRed'>40.55</td><td class='cRed'>1.97</td><td>47,410</td><td>988,259</td><td>4.32</td><td>0.38</td></tr><tr class='dbrow'><td>2021-01-05</td><td class='cGreen'>1,990.00</td><td class='cRed'>2,059.45</td><td class='cGreen'>1,982.46</td><td class='cRed'>2,059.45</td><td class='cRed'>62.45</td><td class='cRed'>3.13</td><td>52,116</td><td>1,059,256</td><td>3.86</td><td>0.41</td></tr><tr class=''><td>2021-01-04</td><td class='cRed'>1,999.98</td><td class='cRed'>2,004.99</td><td class='cGreen'>1,983.81</td><td class='cGreen'>1,997.00</td><td class='cGreen'>-1.00</td><td class='cGreen'>-0.05</td><td>43,514</td><td>868,692</td><td>1.06</td><td>0.35</td></tr></tr>    </table>
</div>
<div id="dropBox1" class="drop_box">
	<div  class="hd">
		下载数据
	</div>
	<div class="bd">
		<form action="" name="tradeData">
			<table id="tcdatafields" width="300" border="0" cellspacing="0" cellpadding="0">
			  <tbody>
				  <tr>
					<td height="25"><input name="" type="checkbox" value="TCLOSE" checked="checked"> 收盘价</td>
					<td><input name="" type="checkbox" value="HIGH" checked="checked"> 最高价</td>
					<td><input name="" type="checkbox" value="LOW" checked="checked"> 最低价</td>
					<td><input name="" type="checkbox" value="TOPEN" checked="checked"> 开盘价</td>
				  </tr>
				  <tr>
					<td height="25"><input name="" type="checkbox" value="LCLOSE" checked="checked"> 前收盘</td>
					<td><input name="" type="checkbox" value="CHG" checked="checked"> 涨跌额</td>
					<td><input name="" type="checkbox" value="PCHG" checked="checked"> 涨跌幅</td>
					<td><input name="" type="checkbox" value="TURNOVER" checked="checked"> 换手率</td>
				  </tr>
				  <tr>
					<td height="25"><input name="" type="checkbox" value="VOTURNOVER" checked="checked"> 成交量</td>
					<td><input name="" type="checkbox" value="VATURNOVER" checked="checked"> 成交金额</td>
					<td height="25"><input name="" type="checkbox" value="TCAP" checked="checked"> 总市值</td>
					<td><input name="" type="checkbox" value="MCAP" checked="checked"> 流通市值</td>
				  </tr>
				</tbody>
			</table>
			<div class="blank9"></div>
			<table width="300" border="0" cellspacing="0" cellpadding="0">
				  <tbody><tr>
					<td height="28"><span class="cDBlue">起始日期</span></td>
					<td>
					<input type="radio" name="date_start_type" value="2001-08-27" checked="checked">
					<input type="text" name="date_start_value" style="width:120px" value="2001-08-27">
					<input type="radio" name="date_start_type" value="2001-08-27" >上市日
					<!--select name="">
				<option value="" selected="selected">2010-03-26</option>
				<option value="">2010-03-27</option>
				</select-->
					</td>
					<td></td>
				  </tr>
				  <tr>
					<td height="28"><span class="cDBlue">截止日期</span></td>
					<td>
					<input type="radio" name="date_end_type" value="2021-03-22" checked="checked">
					<input type="text" name="date_end_value" style="width:120px" value="2021-03-22">
					<input type="radio" name="date_end_type" value="2021-03-22">今日
					<!--select name="">
				<option value="" selected="selected">2010-03-26</option>
				<option value="">2010-03-27</option>
				</select-->
					</td>
					<td></td>
				  </tr>
				</tbody>
			</table>
			<div class="blank9"></div>
			<div class="align_c"><a class="blue_btn submit">下载</a>&nbsp;&nbsp;&nbsp;&nbsp;<a class="grey_btn cancel">取消</a></div>
		</form>
	</div>
</div>
<em id="pageIdentityCode" data-code="b5"></em>

    <script type="text/javascript">
var f2eConfig = {
	stockts: 'http://file.ws.126.net/f2e/finance/gegu/js/stock_ts_js.667271.swf',
	stockkl: 'http://file.ws.126.net/f2e/finance/gegu/js/stock_kl_js.667271.swf'
}

/*
function doTracker(){
	setTimeout(function(){if(typeof neteaseTracker == "function")neteaseTracker();doTracker();},5*60*1000);
} 
doTracker();
*/

// 二维码
!(function(){
	var version = window.navigator.appVersion;
	var isIE6 = (version.indexOf("MSIE 6.0") != -1 || version.indexOf("MSIE 5.5") != -1)? true:false;
	if(location.hostname.indexOf("news") == -1){
           showQR(35, "http://img4.cache.netease.com/stock/2017/5/24/20170524105815a8be1.png");
           // http://img2.cache.netease.com/stock/2014/8/14/20140814165055a7606.jpg
           //
           // http://img6.cache.netease.com/stock/2014/2/18/20140218110538d9103.jpg 微信易信二维码
           //
           // http://img2.cache.netease.com/stock/2014/10/21/201410211523115048d.png
		   // http://img1.cache.netease.com/stock/2014/11/26/201411261107570399d.png
           //
	}
	function showQR(bottom, imgUrl){
		var QRbox= document.createElement("div");
		var QRboxCSS ={
			'width': "110px",
			'height': "147px",
			'position': "fixed",
			'_position': "absolute",
			'bottom': bottom + "px",
			'right': "3px",
			'display':"block",
			'zIndex':100
		};
		for(var key in QRboxCSS){
			QRbox.style[key] = QRboxCSS[key];
		}
	 	if(isIE6){
	 		QRbox.style.position = "absolute";
	 	}

		//QRbox.innerHTML="<a href='javascript:;' target='_blank'></a>";
		QRbox.innerHTML="<a href='http://money.163.com/special/app_spread230/?tc01' target='_blank'></a>";
		var link =QRbox.getElementsByTagName("a")[0];
		var backgroundImage = "url(" + imgUrl + ")";
		var linkCSS = {
			'display' : "block",
			'width' : "100%",
			'height' : "100%",
			'backgroundImage' : backgroundImage,
			'backgroundPosition' : "0px 0px"
		};
		for(var key in linkCSS){
			link.style[key] = linkCSS[key]
		}
		document.body.appendChild(QRbox);
		var linkEventMap = {
			"mouseover": function(){
				link.style.backgroundPosition = "-110px 0px";
			},
			"mouseout": function(){
				link.style.backgroundPosition = "0px 0px";
			}
		}
		for(var evt in linkEventMap){
			var func = linkEventMap[evt];
			addEvent(evt, link, func);
		}
		// addEvent("scroll", window, function(){
		// 	if( getScrollTop() > 200 ){
		// 		QRbox.style.display = 'block';
		// 	}else{
		// 		QRbox.style.display = 'none';
		// 	}
		// 	if(isIE6){
		// 		QRbox.style.top = getScrollTop() + top + "px";
		// 	}
		// });
		var body = document.body;
		addEvent("resize", window, function(){
			var bodyWidth = body.clientWidth;
			if( bodyWidth < 1180 ){
				QRbox.style.right = (bodyWidth - 1180)/2 + 'px';
			}else{
				QRbox.style.right = '3px';
			}
		});
	}
	function addEvent(evt, elem, callback){
		if(elem.addEventListener){
			elem.addEventListener(evt, callback, false)
		} else {
			elem.attachEvent && elem.attachEvent("on"+evt, callback);
		}
	}
	function getScrollTop(){
	    if(typeof pageYOffset!= 'undefined'){
	        //most browsers
	        return pageYOffset;
	    }
	    else{
	        var B= document.body; //IE 'quirks'
	        var D= document.documentElement; //IE with doctype
	        D= (D.clientHeight)? D: B;
	        return D.scrollTop;
	    }
	}
})();

</script>
<script src="http://img1.cache.netease.com/f2e/lib/js/ne.js"></script>
<script src="http://img2.cache.netease.com/f2e/libs/jquery.js"></script>
<script src="http://img1.cache.netease.com/f2e/finance/gegu/js/ne_gegu_a.VxEO4twoqLvl.1.js"></script>
<script src="http://img1.cache.netease.com/f2e/finance/gegu/js/ne_gegu_b.jR2HPJ8AZpUI.1.js"></script>
    <script src="http://img1.cache.netease.com/f2e/component/datepicker/datepicker.734886.min.js"></script>
<script src="http://img1.cache.netease.com/f2e/finance/gegu/js/b.667271.min.js"></script>
    <div class="blank20"></div>
    <div class="foot">
<div class="text"><a href="http://money.163.com/special/zhubianmail/">主编信箱</a>　热线:010-82558752　<a href="http://money.163.com/10/1201/14/6MQU982O00251OB6.html">加入我们</a> <a href="http://fankui.163.com/ft/cq.fb?pid=10005" target="_blank">意见反馈</a>　</div>
<a href="http://corp.163.com/">About NetEase</a> - <a href="http://gb.corp.163.com/gb/about/overview.html">公司简介</a> - <a href="http://gb.corp.163.com/gb/contactus.html">联系方法</a> - <a href="http://corp.163.com/gb/job/job.html">招聘信息</a> - <a href="http://help.163.com/">客户服务</a> - <a href="http://gb.corp.163.com/gb/legal.html">隐私政策</a> - <a href="http://emarketing.biz.163.com/">网络营销</a> - <a href="http://sitemap.163.com/">网站地图</a> <br>
网易公司版权所有<br>
<span class="cRed">©1997-2021</span>
<div class="blank20"></div>
</div>
<!-- START WRating v1.0 -->
<script type="text/javascript" src="http://img6.cache.netease.com/common/script/wrating.js">
</script>
<script type="text/javascript">
var vjAcc="860010-0507010000";
var wrUrl="http://163.wrating.com/";
vjTrack("");
</script>
<noscript><img src="http://163.wrating.com/a.gif?a=&c=860010-0507010000" width="1" height="1"/></noscript>
<!-- END WRating v1.0 -->
<!-- START NetEase Devilfish 2006 -->
<script src="http://analytics.163.com/ntes.js" type="text/javascript"></script>
<script type="text/javascript">
_ntes_nacc = "stock";     //站点ID。
neteaseTracker();
neteaseClickStat();
</script>
<!-- END NetEase Devilfish 2006 -->
<!-- START monitor -->
<!-- END monitor -->
</div>
</body>
</html>
