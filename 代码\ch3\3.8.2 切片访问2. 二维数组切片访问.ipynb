{"cells": [{"cell_type": "code", "execution_count": 1, "id": "ae3d006a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["多维数组切片访问：\n", "[[4 5]\n", " [7 8]]\n", "[[2 3]\n", " [5 6]]\n", "[[1 3]\n", " [7 9]]\n"]}], "source": ["import numpy as np\n", "\n", "arr = np.array([[1, 2, 3],\n", "                [4, 5, 6],\n", "                [7, 8, 9]])\n", "\n", "# 多维数组切片访问\n", "print(\"多维数组切片访问：\")\n", "print(arr[1:3, 0:2])            # 输出：[[4, 5], [7, 8]]\n", "print(arr[:2, 1:])              # 输出：[[2, 3], [5, 6]]\n", "print(arr[::2, ::2])            # 输出：[[1, 3], [7, 9]]\n"]}, {"cell_type": "code", "execution_count": null, "id": "ec673967", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "1ec588e3", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}