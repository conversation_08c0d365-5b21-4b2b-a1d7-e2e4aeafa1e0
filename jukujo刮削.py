import shutil
import time
from bs4 import BeautifulSoup
import os
import re
import tkinter as tk
from tkinter import filedialog
from PIL import Image
import subprocess
def extract_number(file_name):
    pattern = r"(?i)(jukujo-?\d{4})" # 匹配形式为 jukujo-1234或jukujo1234，不区分大小写
    match = re.search(pattern, file_name, re.IGNORECASE)
    if match:
        return match.group()  # 返回完整的匹配项
    return None

def select_folder():
    root = tk.Tk()
    root.withdraw()  # 隐藏根窗口
    folder_selected = filedialog.askdirectory()  # 弹出对话框选择文件夹
    return folder_selected

from bs4 import BeautifulSoup

def extract_info_from_html(file_name, number):
    with open(file_name, 'r', encoding='utf-8') as file:
        soup = BeautifulSoup(file, 'html.parser')

        # 初始化影片名称
        title = ""


        # 找到<title>标签
        title_tag = soup.find('title')
        if title_tag:
            # 提取<title>和</title>标签之间的文本内容作为影片名称
            original_title = title_tag.text.strip()
            # 去掉逗号、空格和单引号
            #original_title = original_title.replace(',', '').replace(' ', '')
            # 去掉  - AVSOX
            title = original_title.replace(' - AVSOX', '')
            title = title.replace('/', '-')
            print(title)

        keywords = []

        # 在网页中搜索包含 keywords 的 meta 标签
        meta_tag = soup.find('meta', {'name': 'keywords'})

        if meta_tag and meta_tag.get('content'):
            content = meta_tag['content']
            # 将 content 以逗号分割为关键词列表
            all_keywords = [keyword.strip() for keyword in content.split(',')]
            # 去掉 "熟女倶楽部" 和以 "jukujo" 开头的字符串（不区分大小写）
            keywords = [
                keyword for keyword in all_keywords
                if keyword != "熟女倶楽部" and not keyword.lower().startswith('jukujo')
            ]

        # 输出关键词列表
        print(keywords)

        # 初始化演员名
        actor_name = ""

        # 在网页中搜索 <h4>演员</h4>
        h4_tags = soup.find_all('h4')
        for h4 in h4_tags:
            if h4.text == "演员":
                # 找到 <h4>演员</h4> 后，获取下一个 <span> 标签中的文本
                span_tag = h4.find_next('span')
                if span_tag:
                    actor_name = span_tag.text.strip()
                break

        # 输出演员名
        print(actor_name)

        title = f"{title} {actor_name}"

        #print("影片名称: {}, 关键词: {}, 演员名字: {}".format(title, keywords, actor_name))

        # 初始化发行日期
        release_date = ""

        # 在网页中搜索 <span class="header">发行时间:</span>
        span_tags = soup.find_all('span', class_='header')
        for span in span_tags:
            if span.text == "发行时间:":
                # 找到 <span class="header">发行时间:</span> 后，获取父标签中的文本
                parent_tag = span.find_parent('p')
                if parent_tag:
                    # 提取包含日期的文本，并去除 "发行时间:" 字样
                    release_date = parent_tag.text.replace("发行时间:", "").strip()
                break

        # 输出发行日期
        print("发行日期:", release_date)

        # 初始化影片时长
        duration = ""

        # 在网页中搜索 <span class="header">长度:</span>
        span_tags = soup.find_all('span', class_='header')
        for span in span_tags:
            if span.text == "长度:":
                # 找到 <span class="header">长度:</span> 后，获取父标签中的文本
                parent_tag = span.find_parent('p')
                if parent_tag:
                    # 提取包含时长的文本，并去除 "长度:" 字样
                    duration = parent_tag.text.replace("长度:", "").strip()
                break

        # 输出影片时长
        print("时长:", duration)

        # 提取片商信息
        producer = "熟女倶楽部"
        print("片商:", producer)


        return title, keywords, actor_name, release_date, duration, producer, original_title


def create_nfo_content(title, original_title, actor_name, release_date, duration, producer, keywords):
    # 排除的关键字列表
    excluded_keywords = ["無碼", "獨佔動畫", "企劃物", "1080p", "60fps", "HEYZO"]

    # 生成标签
    genre_tags = "\n".join([
        f"  <genre>{keyword.strip()}</genre>" for keyword in keywords
        if keyword.strip() and
           keyword.strip() not in excluded_keywords and
           not any(char.isdigit() for char in keyword.strip())
    ])

    # 构造 NFO 文件内容
    nfo_content = f"""
<movie>
    <title>{title}</title>
    <originaltitle>{original_title}</originaltitle>
    <actor>
        <name>{actor_name}</name>
        <type>Actor</type>
    </actor>
    <releasedate>{release_date}</releasedate>
    <runtime>{duration}</runtime>
    <studio>{producer}</studio>
    {genre_tags}
</movie>
"""
    return nfo_content



def create_movie_folder(folder_path, movie_name):
    movie_folder_path = os.path.join(folder_path, movie_name)
    if not os.path.exists(movie_folder_path):
        os.makedirs(movie_folder_path)
    return movie_folder_path

def main():
    folder_path = select_folder()  # 选择文件夹
    if folder_path:
        print("选择的文件夹:", folder_path)
        video_files = [f for f in os.listdir(folder_path) if f.endswith((".mp4", ".mkv", ".avi", ".wmv"))]
        if video_files:
            print("找到的视频文件:")
            for file_name in video_files:
                try:
                    number = extract_number(file_name)
                    if number:
                        file_path = 'd:\\tt\\page_source.html'
                        pic_path = 'd:\\tt\\image.jpg'
                        if os.path.exists(file_path):
                            os.remove(file_path)
                        if os.path.exists(pic_path):
                            os.remove(pic_path)
                        url = f"https://avsox.click/cn/search/{number}"
                        browser_path = "C:/Program Files/Waterfox/waterfox.exe"
                        subprocess.run([browser_path, url])
                        print("文件名: {}, 文件番号: {}".format(file_name, number))

                        time.sleep(20)  # 暂停10秒等待页面加载
                        title, keywords, actor, release_date, duration, producer, original_title = extract_info_from_html('d:\\tt\\page_source.html', number)

                        # 从 original_title 中提取番号
                        extracted_number = extract_number(title)

                        # 将提取的番号和文件名中的番号转换为小写
                        extracted_number_lower = extracted_number.lower()
                        number_lower = number.lower()
                        number_lower = number_lower.replace("-", "").replace("_", "").replace(" ", "")
                        extracted_number_lower = extracted_number_lower.replace("-", "").replace("_", "").replace(" ",
                                                                                                                  "")

                        # 检查转换后的番号是否一致
                        if extracted_number_lower != number_lower:
                            print(f"文件 {file_name} 的番号与提取的番号不一致，跳过该文件。")
                            continue

                        # 创建Emby格式的nfo内容
                        nfo_content = create_nfo_content(title, original_title, actor, release_date, duration, producer, keywords)

                        # 创建以影片名命名的文件夹
                        movie_folder_path = create_movie_folder('V:\\wuma\\亚洲\\=m=', title)
                        # 检测是否包含特定的cd标记，并添加到文件名
                        cd_tag = ""
                        for cd in ['cd1', 'cd2', 'cd3', 'cd4']:
                            if cd in file_name.lower():
                                cd_tag = f"-{cd}"
                                break

                        # 更新文件名以包含CD标记
                        title_with_cd = f"{title}{cd_tag}"
                        new_video_name = f"{title_with_cd}{os.path.splitext(file_name)[1]}"

                        original_video_path = os.path.join(folder_path, file_name)
                        new_video_path = os.path.join(movie_folder_path, new_video_name)
                        os.rename(original_video_path, new_video_path)

                        # 移动图片和写入nfo文件时也应使用更新后的影片名
                        new_jpg_name = f"{title_with_cd}-poster.jpg"
                        new_jpg_path = os.path.join(movie_folder_path, new_jpg_name)

                        jpg_file_path = os.path.join('d:\\tt\\', f"image.jpg")
                        if os.path.exists(jpg_file_path):
                            # 打开原始图片
                            original_image = Image.open(jpg_file_path)

                            # 计算裁剪区域，以右上角为基点裁剪
                            width, height = original_image.size
                            new_width = 360
                            new_height = height
                            right = width
                            top = 0
                            left = right - new_width
                            bottom = top + new_height

                            # 裁剪图片
                            cropped_image = original_image.crop((left, top, right, bottom))

                            # 保存裁剪后的图片
                            cropped_image.save(new_jpg_path)

                            # 移动原始 jpg 文件到影片文件夹
                            shutil.move(jpg_file_path, os.path.join(movie_folder_path, f"{title_with_cd}-fanart.jpg"))

                        # 写入信息到.nfo文件
                        nfo_file_path = os.path.join(movie_folder_path, f"{title_with_cd}.nfo")
                        with open(nfo_file_path, 'w', encoding='utf-8') as nfo_file:
                            nfo_file.write(nfo_content)

                    else:
                        print("文件名: {}, 未找到番号".format(file_name))
                except Exception as e:
                    print(f"处理文件 {file_name} 时出现异常: {e}")
                    continue
        else:
            print("在所选文件夹中未找到视频文件。")

if __name__ == "__main__":
    main()
