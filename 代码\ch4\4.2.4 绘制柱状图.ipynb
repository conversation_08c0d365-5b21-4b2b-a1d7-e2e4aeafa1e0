{"cells": [{"cell_type": "code", "execution_count": 2, "id": "df9a7a2c", "metadata": {}, "outputs": [{"data": {"image/png": "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*************************************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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "\n", "plt.rcParams['font.family'] = ['SimHei'] # 设置中文字体\n", "plt.rcParams['axes.unicode_minus'] = False # 设置负号显示\n", "\n", "x1 = [1, 3, 5, 7, 9]  # x1轴坐标数据\n", "y1 = [5, 2, 7, 8, 2]  # y1轴坐标数据\n", "\n", "x2 = [2, 4, 6, 8, 10]  # x2轴坐标数据\n", "y2 = [8, 6, 2, 5, 6]  # y2轴坐标数据\n", "\n", "# 绘制柱状图\n", "plt.bar(x1, y1, label='柱状图1') \n", "plt.bar(x2, y2, label='柱状图2')\n", "\n", "plt.title('绘制柱状图')  # 添加图表标题\n", "\n", "plt.ylabel('y轴')  # 添加y轴标题\n", "plt.xlabel('x轴')  # 添加x轴标题\n", "\n", "plt.legend()  # 设置图例\n", "plt.show()  "]}, {"cell_type": "code", "execution_count": null, "id": "df6074bd", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}