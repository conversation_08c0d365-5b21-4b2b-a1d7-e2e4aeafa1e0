{"cells": [{"cell_type": "code", "execution_count": 1, "id": "5492b32c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Count is : 0\n", "Count is : 1\n", "Count is : 2\n"]}], "source": ["for item in range(10):\n", "    if item == 3:\n", "        # 跳出循环\n", "        break\n", "    print(\"Count is : {0}\".format(item))"]}, {"cell_type": "code", "execution_count": null, "id": "65a2dc81", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}