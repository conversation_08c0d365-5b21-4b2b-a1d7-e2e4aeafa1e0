{"cells": [{"cell_type": "code", "execution_count": 1, "id": "a1bf1556", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["a[0] =  H\n", "a[1] =  e\n", "a[4] =  o\n", "a[-1] =  o\n", "a[-2] =  l\n"]}, {"ename": "IndexError", "evalue": "string index out of range", "output_type": "error", "traceback": ["\u001b[1;31m----------------------------------------------------------------------\u001b[0m", "\u001b[1;31mIndexError\u001b[0m                           <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[1], line 7\u001b[0m\n\u001b[0;32m      5\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124ma[-1] = \u001b[39m\u001b[38;5;124m'\u001b[39m, a[\u001b[38;5;241m-\u001b[39m\u001b[38;5;241m1\u001b[39m]) \n\u001b[0;32m      6\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124ma[-2] = \u001b[39m\u001b[38;5;124m'\u001b[39m, a[\u001b[38;5;241m-\u001b[39m\u001b[38;5;241m2\u001b[39m])\n\u001b[1;32m----> 7\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124ma[5] = \u001b[39m\u001b[38;5;124m'\u001b[39m, \u001b[43ma\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;241;43m5\u001b[39;49m\u001b[43m]\u001b[49m) \n", "\u001b[1;31mIndexError\u001b[0m: string index out of range"]}], "source": ["a = 'Hello'\n", "print('a[0] = ', a[0])\n", "print('a[1] = ', a[1])\n", "print('a[4] = ', a[4])\n", "print('a[-1] = ', a[-1]) \n", "print('a[-2] = ', a[-2])\n", "print('a[5] = ', a[5]) "]}, {"cell_type": "code", "execution_count": null, "id": "346ea68a", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}