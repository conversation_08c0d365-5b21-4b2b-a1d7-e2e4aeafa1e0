from selenium import webdriver
from selenium.webdriver.common.by import By
import time
import os
import requests

# 初始化 WebDriver
driver = webdriver.Chrome()  # 确保已安装 ChromeDriver，并配置好环境变量
driver.get("https://example.com")  # 替换为目标网页地址

try:
    # 查找 <h2> 标签
    h2_tag = driver.find_element(By.CSS_SELECTOR, "h2.title.is-4")

    # 查找 <strong> 标签
    strong_tags = h2_tag.find_elements(By.TAG_NAME, "strong")

    # 提取编号和原始名称
    strong_text = strong_tags[0].text.strip() if len(strong_tags) > 0 else ''
    code = strong_text.split(' ')[0]  # 使用空格分割，取第一部分
    original_title = strong_tags[1].text.strip() if len(strong_tags) > 1 else ''

    # 构建标题，并过滤特殊字符
    title = f"{code} {original_title}".replace(/[\/\\:*?"<>|]/g, " ").replace("：", " ").replace("\s+", " ").strip()

    print("提取的标题:", title)

    # 查找 img 标签
    img_tag = driver.find_element(By.CSS_SELECTOR, "img.video-cover")

    if img_tag:
        img_url = img_tag.get_attribute("src")  # 获取图片 URL
        print("图片 URL:", img_url)

        # 下载图片
        try:
            response = requests.get(img_url, stream=True)
            response.raise_for_status()  # 检查请求是否成功
            with open(f"{title}.jpg", "wb") as file:
                for chunk in response.iter_content(chunk_size=8192):
                    file.write(chunk)
            print("图片下载成功:", f"{title}.jpg")
        except Exception as e:
            print("图片下载失败:", e)
    else:
        print("未找到视频封面图片。")

    # 保存网页内容到 HTML 文件
    html_content = driver.page_source
    with open("page_source.html", "w", encoding="utf-8") as file:
        file.write(html_content)
    print("网页内容已保存为 page_source.html")

except Exception as e:
    print("发生错误:", e)

finally:
    # 关闭浏览器
    driver.quit()
