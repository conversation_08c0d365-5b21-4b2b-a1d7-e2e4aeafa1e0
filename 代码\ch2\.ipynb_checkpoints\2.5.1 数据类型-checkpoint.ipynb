{"cells": [{"cell_type": "code", "execution_count": 2, "id": "809e7e4e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["int1 =  28\n", "int2 =  28\n", "int3 =  28\n", "int4 =  28\n", "int5 =  28\n", "int6 =  28\n", "f1 =  1.0\n", "f2 =  336.0\n", "f3 =  0.0156\n", "complex1 =  (1+2j)\n", "complex2 =  (2+4j)\n", "(bool(0) =  False\n", "(bool(1) =  True\n", "(bool('') =  False\n", "(bool(' ') =  True\n", "(bool([]) =  False\n"]}], "source": ["# coding=utf-8\n", "\n", "# 整数表示\n", "int1 = 28\n", "int2 = 0b11100\n", "int3 = 0O34\n", "int4 = 0o34\n", "int5 = 0x1C\n", "int6 = 0X1C\n", "\n", "print('int1 = ', int1)\n", "print('int2 = ', int2)\n", "print('int3 = ', int3)\n", "print('int4 = ', int4)\n", "print('int5 = ', int5)\n", "print('int6 = ', int6)\n", "\n", "# 浮点数表示\n", "f1 = 1.0\n", "f2 = 3.36e2\n", "f3 = 1.56e-2\n", "print('f1 = ', f1)\n", "print('f2 = ', f2)\n", "print('f3 = ', f3)\n", "\n", "# 复数表示\n", "complex1 = 1 + 2j\n", "complex2 = complex1 + (1 + 2j)\n", "\n", "print('complex1 = ', complex1)\n", "print('complex2 = ', complex2)\n", "\n", "# 测试bool函数\n", "print('(bool(0) = ', (bool(0)))  # 0转换为False\n", "print('(bool(1) = ', (bool(1)))  # 1转换为True\n", "print(\"(bool('') = \", (bool('')))  # 空字符串转''换为False\n", "print(\"(bool(' ') = \", (bool(' ')))  # 空格字符串' '转换为True\n", "print('(bool([]) = ', (bool([])))  # 空列表([]转换为False"]}, {"cell_type": "code", "execution_count": null, "id": "7e41c009", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}