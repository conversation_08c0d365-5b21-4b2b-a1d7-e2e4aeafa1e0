import pygetwindow as gw
import pyautogui
import time
import pyperclip
import keyboard
import sys
import win32gui
import win32con
import win32api
import win32process
import psutil
import win32clipboard
import ctypes
from ctypes import wintypes
import os
from datetime import datetime

# 定义Windows API常量
WM_GETTEXT = 0x000D
WM_GETTEXTLENGTH = 0x000E
SW_RESTORE = 9
EM_GETSELTEXT = 0x043E

def clear_clipboard():
    """清空剪贴板"""
    try:
        win32clipboard.OpenClipboard()
        win32clipboard.EmptyClipboard()
        win32clipboard.CloseClipboard()
        return True
    except Exception as e:
        print(f"清空剪贴板时出错: {str(e)}")
        return False

def get_window_text(hwnd):
    """使用Windows API获取窗口文本"""
    try:
        # 获取文本长度
        length = win32gui.SendMessage(hwnd, WM_GETTEXTLENGTH, 0, 0)
        if length == 0:
            return None
            
        # 创建缓冲区
        buffer = ctypes.create_unicode_buffer(length + 1)
        
        # 获取文本
        win32gui.SendMessage(hwnd, WM_GETTEXT, length + 1, buffer)
        
        return buffer.value
    except Exception as e:
        print(f"获取窗口文本时出错: {str(e)}")
        return None

def get_edit_text(hwnd):
    """获取编辑框中的文本"""
    try:
        # 获取文本长度
        length = win32gui.SendMessage(hwnd, EM_GETSELTEXT, 0, 0)
        if length == 0:
            return None
            
        # 创建缓冲区
        buffer = ctypes.create_unicode_buffer(length + 1)
        
        # 获取文本
        win32gui.SendMessage(hwnd, EM_GETSELTEXT, 0, buffer)
        
        return buffer.value
    except Exception as e:
        print(f"获取编辑框文本时出错: {str(e)}")
        return None

def get_window_info(hwnd):
    """获取窗口的详细信息"""
    try:
        # 获取窗口标题
        title = win32gui.GetWindowText(hwnd)
        # 获取窗口类名
        class_name = win32gui.GetClassName(hwnd)
        # 获取窗口位置和大小
        left, top, right, bottom = win32gui.GetWindowRect(hwnd)
        # 获取进程ID
        _, pid = win32process.GetWindowThreadProcessId(hwnd)
        # 获取进程名称
        try:
            process = psutil.Process(pid)
            process_name = process.name()
        except:
            process_name = "未知"
            
        return {
            "标题": title,
            "类名": class_name,
            "位置": f"左={left}, 上={top}, 右={right}, 下={bottom}",
            "进程ID": pid,
            "进程名": process_name
        }
    except Exception as e:
        return {"错误": str(e)}

def get_window_at_cursor():
    """获取鼠标所在位置的窗口信息"""
    try:
        # 获取鼠标位置
        x, y = pyautogui.position()
        # 获取该位置的窗口句柄
        hwnd = win32gui.WindowFromPoint((x, y))
        if hwnd:
            return get_window_info(hwnd)
        return None
    except Exception as e:
        return {"错误": str(e)}

def is_valid_window(hwnd):
    """检查窗口是否有效"""
    try:
        if not win32gui.IsWindow(hwnd):
            return False
            
        # 获取窗口位置
        left, top, right, bottom = win32gui.GetWindowRect(hwnd)
        
        # 检查窗口大小是否合理（至少100像素宽）
        if right - left < 100 or bottom - top < 100:
            return False
            
        # 检查窗口是否可见
        if not win32gui.IsWindowVisible(hwnd):
            return False
            
        return True
    except:
        return False

def find_telegram_window():
    """查找Telegram窗口"""
    try:
        # 获取所有窗口
        def callback(hwnd, windows):
            if win32gui.IsWindowVisible(hwnd):
                _, pid = win32process.GetWindowThreadProcessId(hwnd)
                try:
                    process = psutil.Process(pid)
                    if process.name().lower() == 'telegram.exe':
                        # 检查窗口是否有效
                        if is_valid_window(hwnd):
                            windows.append(hwnd)
                except:
                    pass
            return True

        windows = []
        win32gui.EnumWindows(callback, windows)
        
        if windows:
            # 获取第一个找到的有效Telegram窗口
            hwnd = windows[0]
            window_info = get_window_info(hwnd)
            print(f"找到Telegram窗口: {window_info['标题']}")
            return hwnd
        
        return None
    except Exception as e:
        print(f"查找窗口时发生错误: {str(e)}")
        return None

def activate_window(hwnd):
    """激活窗口的可靠方法"""
    try:
        # 获取当前窗口状态
        placement = win32gui.GetWindowPlacement(hwnd)
        
        # 如果窗口最小化，先恢复
        if placement[1] == win32con.SW_SHOWMINIMIZED:
            win32gui.ShowWindow(hwnd, SW_RESTORE)
            time.sleep(0.5)
        
        # 获取当前线程ID和窗口线程ID
        current_thread = win32api.GetCurrentThreadId()
        window_thread = win32process.GetWindowThreadProcessId(hwnd)[0]
        
        # 附加线程
        win32process.AttachThreadInput(current_thread, window_thread, True)
        
        try:
            # 将窗口置于前台
            win32gui.SetForegroundWindow(hwnd)
            win32gui.SetFocus(hwnd)
            win32gui.BringWindowToTop(hwnd)
        finally:
            # 分离线程
            win32process.AttachThreadInput(current_thread, window_thread, False)
        
        return True
    except Exception as e:
        print(f"激活窗口时出错: {str(e)}")
        return False

def select_text_with_mouse(hwnd):
    """使用鼠标选择文本"""
    try:
        # 获取窗口位置
        left, top, right, bottom = win32gui.GetWindowRect(hwnd)
        
        # 计算靠近右侧的位置（距离右边缘100像素）
        right_x = right - 100
        
        # 确保鼠标位置在窗口内部
        # 设置安全边距（距离窗口边缘至少20像素）
        safe_margin = 20
        safe_left = left + safe_margin
        safe_right = right - safe_margin
        safe_top = top + safe_margin
        safe_bottom = bottom - safe_margin
        
        # 确保右侧位置在安全区域内
        right_x = max(safe_left, min(right_x, safe_right))
        
        # 保存当前鼠标位置
        current_x, current_y = pyautogui.position()
        
        # 移动到窗口顶部（在安全区域内）
        start_y = safe_top + 150
        
        # 使用win32api直接控制鼠标位置，避免pyautogui的自动对齐
        win32api.SetCursorPos((right_x, start_y))
        time.sleep(0.1)
        
        # 点击窗口
        win32api.mouse_event(win32con.MOUSEEVENTF_LEFTDOWN, 0, 0, 0, 0)
        time.sleep(0.3)
        
        # 向下拖动到窗口底部（在安全区域内）
        end_y = safe_bottom - 50
        
        # 计算移动步长（每次移动50像素）
        step = 50
        for y in range(start_y, end_y, step):
            # 确保y坐标不超过安全区域
            y = min(y, end_y)
            # 使用win32api直接设置鼠标位置
            win32api.SetCursorPos((right_x, y))
            time.sleep(0.001)
        
        # 释放鼠标左键
        win32api.mouse_event(win32con.MOUSEEVENTF_LEFTUP, 0, 0, 0, 0)
        time.sleep(0.1)
        
        # 复制选中的文本
        pyautogui.hotkey('ctrl', 'c')
        
        # 获取剪贴板内容
        win32clipboard.OpenClipboard()
        try:
            if win32clipboard.IsClipboardFormatAvailable(win32clipboard.CF_UNICODETEXT):
                text = win32clipboard.GetClipboardData(win32clipboard.CF_UNICODETEXT)
                return text
        finally:
            win32clipboard.CloseClipboard()
        
        # 恢复鼠标位置
        win32api.SetCursorPos((current_x, current_y))
        
        return None
    except Exception as e:
        print(f"使用鼠标选择文本时出错: {str(e)}")
        return None

def get_telegram_unread(is_running):
    try:
        if not is_running:
            return None
            
        print("正在查找Telegram窗口...")
        # 查找Telegram窗口
        hwnd = find_telegram_window()
        if not hwnd:
            print("未找到Telegram窗口，请确保：")
            print("1. Telegram已经打开")
            print("2. 程序以管理员权限运行")
            return None
            
        # 检查窗口是否仍然有效
        if not is_valid_window(hwnd):
            print("窗口状态无效，重新查找...")
            return None
        
        # 激活窗口
        print("正在激活窗口...")
        if not activate_window(hwnd):
            print("激活窗口失败，尝试继续...")
        
        # 清空剪贴板
        print("正在清空剪贴板...")
        if not clear_clipboard():
            print("清空剪贴板失败，尝试继续...")
            
        # 使用鼠标选择文本
        print("正在选择文本...")
        text = select_text_with_mouse(hwnd)
        
        if text and is_running:  # 再次检查是否应该继续
            # 模拟按键
            print("正在模拟按键...")
            pyautogui.press('esc')
            time.sleep(0.1)
            pyautogui.press('pagedown')
            
            # 恢复鼠标位置
            print("正在继续操作...")
            time.sleep(0.5)  # 等待页面滚动完成
            
            return text
        
        print("未能获取到文本内容，请确保：")
        print("1. Telegram窗口中有可选择的文本")
        print("2. 窗口没有被其他窗口遮挡")
        print("3. 尝试手动点击窗口后再试")
        return None
        
    except Exception as e:
        print(f"发生错误: {str(e)}")
        print("请确保：")
        print("1. Telegram窗口已打开且未被最小化")
        print("2. 窗口中有可选择的文本内容")
        print("3. 程序以管理员权限运行")
        print("4. 没有其他程序占用快捷键")
        return None

def filter_text(text):
    """过滤文本，只保留包含特定关键词的行"""
    if not text:
        return None
        
    # 定义关键词列表（不区分大小写）
    keywords = [
        'vi_',
        'showfilesbot_',
        'magnet:?',
        'd_',
        '@filepan_bot',
        'pk_',
        '=_',
        'P_',
        'V_'
    ]
    
    # 将文本按行分割
    lines = text.split('\n')
    # 过滤出包含关键词的行
    filtered_lines = []
    for line in lines:
        line_lower = line.lower()
        if any(keyword.lower() in line_lower for keyword in keywords):
            filtered_lines.append(line)
    
    # 如果没有匹配的行，返回None
    if not filtered_lines:
        return None
        
    # 返回过滤后的文本
    return '\n'.join(filtered_lines)

def main():
    print("=" * 50)
    print("Telegram消息获取工具")
    print("=" * 50)
    print("使用说明：")
    print("1. 按 F2 键开始自动循环获取Telegram消息")
    print("2. 按 F3 键获取鼠标所在位置的窗口信息")
    print("3. 按 Ctrl+C 退出程序")
    print("4. 确保Telegram窗口已打开且未被最小化")
    print("5. 建议以管理员权限运行此程序")
    print("6. 如果获取失败，请先手动点击Telegram窗口")
    print("=" * 50)
    
    try:
        is_running = False
        should_stop = False
        
        def on_f2_press(e):
            nonlocal is_running, should_stop
            if not is_running:
                print("\n开始自动循环获取消息...")
                is_running = True
                should_stop = False
            else:
                print("\n停止自动循环")
                should_stop = True
                is_running = False
        
        def on_f3_press(e):
            print("\n正在获取窗口信息...")
            window_info = get_window_at_cursor()
            if window_info:
                print("\n窗口信息：")
                print("-" * 50)
                for key, value in window_info.items():
                    print(f"{key}: {value}")
                print("-" * 50)
        
        # 注册按键事件
        keyboard.on_press_key('F2', on_f2_press)
        keyboard.on_press_key('F3', on_f3_press)
        
        # 创建保存目录
        save_dir = r'D:\teldown\t'
        if not os.path.exists(save_dir):
            os.makedirs(save_dir)
        
        while True:
            if is_running and not should_stop:
                # 创建新的文件名
                filename = os.path.join(save_dir, f'messages_{datetime.now().strftime("%Y%m%d_%H%M%S")}.txt')
                print(f"消息将保存到: {filename}")
                
                while is_running and not should_stop:
                    text = get_telegram_unread(is_running)
                    if text:
                        # 过滤文本
                        filtered_text = filter_text(text)
                        if filtered_text:
                            # 追加保存到当前会话文件
                            with open(filename, 'a', encoding='utf-8') as f:
                                f.write(filtered_text + '\n\n')
                            print(".", end="", flush=True)  # 显示进度点
                    else:
                        if is_running and not should_stop:  # 只有在仍然运行状态下才显示未获取到消息
                            print("\n未获取到新消息，按F2停止循环")
                        break
                    time.sleep(0.1)  # 减少等待时间，使响应更快
            
            time.sleep(0.1)  # 减少CPU使用率
            
    except KeyboardInterrupt:
        print("\n程序已退出")
        sys.exit(0)
    except Exception as e:
        print(f"\n程序发生错误: {str(e)}")
        sys.exit(1)
    finally:
        # 清理按键事件
        keyboard.unhook_all()

if __name__ == "__main__":
    main()