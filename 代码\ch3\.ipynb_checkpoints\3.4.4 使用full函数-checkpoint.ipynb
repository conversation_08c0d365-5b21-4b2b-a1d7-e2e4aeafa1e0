{"cells": [{"cell_type": "code", "execution_count": 1, "id": "d06ce815", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[[10 10 10 10]\n", " [10 10 10 10]]\n", "int32\n", "[[10. 10. 10. 10.]\n", " [10. 10. 10. 10.]]\n", "float64\n", "[10 10 10 10 10]\n", "int32\n"]}], "source": ["import numpy as np\n", "a = np.full((2, 4), 10)\n", "print(a)\n", "print(a.dtype)\n", "\n", "b = np.full((2, 4), 10, dtype=float)\n", "print(b)\n", "print(b.dtype)\n", "\n", "c = np.full(5, 10)\n", "print(c)\n", "print(c.dtype)"]}, {"cell_type": "code", "execution_count": null, "id": "bbde9442", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}