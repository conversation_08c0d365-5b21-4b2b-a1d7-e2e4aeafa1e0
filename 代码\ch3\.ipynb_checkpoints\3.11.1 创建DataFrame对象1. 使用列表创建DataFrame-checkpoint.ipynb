{"cells": [{"cell_type": "code", "execution_count": 1, "id": "7a1a27d3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["   0  1  2\n", "0  3  0  1\n", "1  2  1  2\n", "2  0  2  1\n", "3  1  3  0\n"]}], "source": ["import pandas as pd\n", "L =[[3,0,1], [2,1,2],  [0,2,1], [1,3,0]]\n", "df = pd.DataFrame(L)\n", "print(df)"]}, {"cell_type": "code", "execution_count": null, "id": "17e9f875", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}