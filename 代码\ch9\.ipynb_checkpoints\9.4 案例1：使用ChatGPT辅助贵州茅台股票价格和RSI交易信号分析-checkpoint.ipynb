{"cells": [{"cell_type": "code", "execution_count": 9, "id": "042aca43", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["缺失值数量：\n", "Date      0\n", "Open      0\n", "Close     0\n", "High      0\n", "Low       0\n", "Volume    0\n", "dtype: int64\n"]}], "source": ["import pandas as pd\n", "\n", "# 读取贵州茅台股票历史交易数据\n", "df = pd.read_csv('data/贵州茅台股票历史交易数据.csv')\n", "missing_values = df.isnull().sum()\n", "\n", "print(\"缺失值数量：\")\n", "print(missing_values)"]}, {"cell_type": "markdown", "id": "c8ec1cb5", "metadata": {}, "source": ["### 计算RSI指标"]}, {"cell_type": "code", "execution_count": 10, "id": "94008f8b", "metadata": {}, "outputs": [{"data": {"text/plain": ["0           NaN\n", "1           NaN\n", "2           NaN\n", "3           NaN\n", "4           NaN\n", "        ...    \n", "75    33.953190\n", "76    44.664032\n", "77    45.525292\n", "78    46.502385\n", "79    43.119571\n", "Name: RSI, Length: 80, dtype: float64"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["# 计算RSI指标\n", "def calculate_rsi(data, window=14):\n", "    delta = data['Close'].diff()\n", "    gain = delta.copy()\n", "    loss = delta.copy()\n", "    gain[gain < 0] = 0\n", "    loss[loss > 0] = 0\n", "    avg_gain = gain.rolling(window).mean()\n", "    avg_loss = abs(loss.rolling(window).mean())\n", "    rs = avg_gain / avg_loss\n", "    rsi = 100 - (100 / (1 + rs))\n", "    return rsi\n", "\n", "# 调用calculate_rsi函数计算RSI指标\n", "df['RSI'] = calculate_rsi(df)\n", "# 打印\n", "df['RSI'] "]}, {"cell_type": "code", "execution_count": 11, "id": "eae07144", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Date</th>\n", "      <th>Open</th>\n", "      <th>Close</th>\n", "      <th>High</th>\n", "      <th>Low</th>\n", "      <th>Volume</th>\n", "      <th>RSI</th>\n", "      <th>Signal</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2021-04-02</td>\n", "      <td>2056.00</td>\n", "      <td>2162.00</td>\n", "      <td>2056.00</td>\n", "      <td>2165.00</td>\n", "      <td>52028</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2021-04-01</td>\n", "      <td>2021.00</td>\n", "      <td>2044.50</td>\n", "      <td>2001.22</td>\n", "      <td>2046.80</td>\n", "      <td>26588</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2021-03-31</td>\n", "      <td>2045.10</td>\n", "      <td>2009.00</td>\n", "      <td>2000.00</td>\n", "      <td>2046.02</td>\n", "      <td>37154</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2021-03-30</td>\n", "      <td>2040.00</td>\n", "      <td>2056.05</td>\n", "      <td>2035.08</td>\n", "      <td>2086.00</td>\n", "      <td>32627</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2021-03-29</td>\n", "      <td>2043.20</td>\n", "      <td>2034.10</td>\n", "      <td>2026.15</td>\n", "      <td>2096.35</td>\n", "      <td>56992</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75</th>\n", "      <td>2020-12-10</td>\n", "      <td>1840.00</td>\n", "      <td>1832.90</td>\n", "      <td>1828.00</td>\n", "      <td>1849.77</td>\n", "      <td>32654</td>\n", "      <td>33.953190</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>76</th>\n", "      <td>2020-12-09</td>\n", "      <td>1865.95</td>\n", "      <td>1840.00</td>\n", "      <td>1839.00</td>\n", "      <td>1866.00</td>\n", "      <td>31152</td>\n", "      <td>44.664032</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>77</th>\n", "      <td>2020-12-08</td>\n", "      <td>1815.00</td>\n", "      <td>1850.00</td>\n", "      <td>1813.00</td>\n", "      <td>1875.00</td>\n", "      <td>61454</td>\n", "      <td>45.525292</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>78</th>\n", "      <td>2020-12-07</td>\n", "      <td>1802.70</td>\n", "      <td>1812.40</td>\n", "      <td>1800.55</td>\n", "      <td>1840.39</td>\n", "      <td>58331</td>\n", "      <td>46.502385</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>79</th>\n", "      <td>2020-12-04</td>\n", "      <td>1752.00</td>\n", "      <td>1793.11</td>\n", "      <td>1752.00</td>\n", "      <td>1800.10</td>\n", "      <td>62491</td>\n", "      <td>43.119571</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>80 rows × 8 columns</p>\n", "</div>"], "text/plain": ["          Date     Open    Close     High      Low  Volume        RSI  Signal\n", "0   2021-04-02  2056.00  2162.00  2056.00  2165.00   52028        NaN       0\n", "1   2021-04-01  2021.00  2044.50  2001.22  2046.80   26588        NaN       0\n", "2   2021-03-31  2045.10  2009.00  2000.00  2046.02   37154        NaN       0\n", "3   2021-03-30  2040.00  2056.05  2035.08  2086.00   32627        NaN       0\n", "4   2021-03-29  2043.20  2034.10  2026.15  2096.35   56992        NaN       0\n", "..         ...      ...      ...      ...      ...     ...        ...     ...\n", "75  2020-12-10  1840.00  1832.90  1828.00  1849.77   32654  33.953190       0\n", "76  2020-12-09  1865.95  1840.00  1839.00  1866.00   31152  44.664032       0\n", "77  2020-12-08  1815.00  1850.00  1813.00  1875.00   61454  45.525292       0\n", "78  2020-12-07  1802.70  1812.40  1800.55  1840.39   58331  46.502385       0\n", "79  2020-12-04  1752.00  1793.11  1752.00  1800.10   62491  43.119571       0\n", "\n", "[80 rows x 8 columns]"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["#  交易信号生成\n", "df['Signal'] = 0\n", "df.loc[df['RSI'] > 70, 'Signal'] = -1\n", "df.loc[df['RSI'] < 30, 'Signal'] = 1\n", "# 打印df对象\n", "df"]}, {"cell_type": "markdown", "id": "0fe4cc39", "metadata": {}, "source": ["### 2.RSI指标曲线"]}, {"cell_type": "code", "execution_count": 12, "id": "6623af58", "metadata": {"scrolled": true}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1200x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "plt.rcParams['font.family'] = ['SimHei']  # 设置中文字体\n", "plt.rcParams['axes.unicode_minus'] = False  # 设置负号显示\n", "\n", "rsi = calculate_rsi(df)  # 计算RSI指标\n", "plt.figure(figsize=(12, 6))\n", "plt.plot(df.index, rsi, label='RSI')\n", "plt.title('RSI指标')\n", "plt.xlabel('日期')\n", "plt.ylabel('RSI')\n", "plt.legend()\n", "plt.grid(True)\n", "plt.show()"]}, {"cell_type": "markdown", "id": "9f9b9dc3", "metadata": {}, "source": ["### 1. 绘制K线图"]}, {"cell_type": "code", "execution_count": 13, "id": "ec41484f", "metadata": {"scrolled": true}, "outputs": [{"data": {"image/png": "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********************************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", "text/plain": ["<Figure size 1000x600 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "import mplfinance as mpf\n", "\n", "\n", "plt.rcParams['font.family'] = ['SimHei']  # 设置中文字体\n", "plt.rcParams['axes.unicode_minus'] = False  # 设置负号显示\n", "# 重新加载数据\n", "df = pd.read_csv('data/贵州茅台股票历史交易数据.csv')\n", "\n", "\n", "# 创建日期索引\n", "df['Date'] = pd.to_datetime(df['Date'])\n", "df.set_index('Date', inplace=True)\n", "\n", "\n", "market_colors = mpf.make_marketcolors(up='red', down='green')\n", "my_style = mpf.make_mpf_style(marketcolors=market_colors)\n", "# 绘制K线图\n", "mpf.plot(df, type='candle',  \n", "         figsize=(10, 6),\n", "         mav=(10, 20),\n", "         volume=True,\n", "         style=my_style)"]}, {"cell_type": "markdown", "id": "93afb689", "metadata": {}, "source": ["### 2.绘制价格和交易信号图表"]}, {"cell_type": "code", "execution_count": 14, "id": "49f3e4e0", "metadata": {}, "outputs": [{"ename": "KeyError", "evalue": "'Signal'", "output_type": "error", "traceback": ["\u001b[1;31m----------------------------------------------------------------------\u001b[0m", "\u001b[1;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                             <PERSON><PERSON> (most recent call last)", "File \u001b[1;32m~\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pandas\\core\\indexes\\base.py:3652\u001b[0m, in \u001b[0;36mIndex.get_loc\u001b[1;34m(self, key)\u001b[0m\n\u001b[0;32m   3651\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m-> 3652\u001b[0m     \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_engine\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget_loc\u001b[49m\u001b[43m(\u001b[49m\u001b[43mcasted_key\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m   3653\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m err:\n", "File \u001b[1;32m~\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pandas\\_libs\\index.pyx:147\u001b[0m, in \u001b[0;36mpandas._libs.index.IndexEngine.get_loc\u001b[1;34m()\u001b[0m\n", "File \u001b[1;32m~\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pandas\\_libs\\index.pyx:176\u001b[0m, in \u001b[0;36mpandas._libs.index.IndexEngine.get_loc\u001b[1;34m()\u001b[0m\n", "File \u001b[1;32mpandas\\_libs\\hashtable_class_helper.pxi:7080\u001b[0m, in \u001b[0;36mpandas._libs.hashtable.PyObjectHashTable.get_item\u001b[1;34m()\u001b[0m\n", "File \u001b[1;32mpandas\\_libs\\hashtable_class_helper.pxi:7088\u001b[0m, in \u001b[0;36mpandas._libs.hashtable.PyObjectHashTable.get_item\u001b[1;34m()\u001b[0m\n", "\u001b[1;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m: 'Signal'", "\nThe above exception was the direct cause of the following exception:\n", "\u001b[1;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                             <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[14], line 13\u001b[0m\n\u001b[0;32m     11\u001b[0m plt\u001b[38;5;241m.\u001b[39mfigure(figsize\u001b[38;5;241m=\u001b[39m(\u001b[38;5;241m12\u001b[39m, \u001b[38;5;241m6\u001b[39m))\n\u001b[0;32m     12\u001b[0m plt\u001b[38;5;241m.\u001b[39mplot(df\u001b[38;5;241m.\u001b[39mindex, df[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mClose\u001b[39m\u001b[38;5;124m'\u001b[39m], label\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mClose Price\u001b[39m\u001b[38;5;124m'\u001b[39m)\n\u001b[1;32m---> 13\u001b[0m plt\u001b[38;5;241m.\u001b[39mscatter(df[\u001b[43mdf\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mSignal\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m]\u001b[49m \u001b[38;5;241m==\u001b[39m \u001b[38;5;241m1\u001b[39m]\u001b[38;5;241m.\u001b[39mindex, df[df[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mSignal\u001b[39m\u001b[38;5;124m'\u001b[39m] \u001b[38;5;241m==\u001b[39m \u001b[38;5;241m1\u001b[39m][\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mClose\u001b[39m\u001b[38;5;124m'\u001b[39m], color\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mgreen\u001b[39m\u001b[38;5;124m'\u001b[39m, marker\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m^\u001b[39m\u001b[38;5;124m'\u001b[39m, label\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mBuy Signal\u001b[39m\u001b[38;5;124m'\u001b[39m)\n\u001b[0;32m     14\u001b[0m plt\u001b[38;5;241m.\u001b[39mscatter(df[df[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mSignal\u001b[39m\u001b[38;5;124m'\u001b[39m] \u001b[38;5;241m==\u001b[39m \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m1\u001b[39m]\u001b[38;5;241m.\u001b[39mindex, df[df[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mSignal\u001b[39m\u001b[38;5;124m'\u001b[39m] \u001b[38;5;241m==\u001b[39m \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m1\u001b[39m][\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mClose\u001b[39m\u001b[38;5;124m'\u001b[39m], color\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mred\u001b[39m\u001b[38;5;124m'\u001b[39m, marker\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mv\u001b[39m\u001b[38;5;124m'\u001b[39m, label\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mSell Signal\u001b[39m\u001b[38;5;124m'\u001b[39m)\n\u001b[0;32m     15\u001b[0m plt\u001b[38;5;241m.\u001b[39mtitle(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m贵州茅台股票价格和交易信号\u001b[39m\u001b[38;5;124m'\u001b[39m)\n", "File \u001b[1;32m~\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pandas\\core\\frame.py:3761\u001b[0m, in \u001b[0;36mDataFrame.__getitem__\u001b[1;34m(self, key)\u001b[0m\n\u001b[0;32m   3759\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mcolumns\u001b[38;5;241m.\u001b[39mnlevels \u001b[38;5;241m>\u001b[39m \u001b[38;5;241m1\u001b[39m:\n\u001b[0;32m   3760\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_getitem_multilevel(key)\n\u001b[1;32m-> 3761\u001b[0m indexer \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcolumns\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget_loc\u001b[49m\u001b[43m(\u001b[49m\u001b[43mkey\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m   3762\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m is_integer(indexer):\n\u001b[0;32m   3763\u001b[0m     indexer \u001b[38;5;241m=\u001b[39m [indexer]\n", "File \u001b[1;32m~\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\pandas\\core\\indexes\\base.py:3654\u001b[0m, in \u001b[0;36mIndex.get_loc\u001b[1;34m(self, key)\u001b[0m\n\u001b[0;32m   3652\u001b[0m     \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_engine\u001b[38;5;241m.\u001b[39mget_loc(casted_key)\n\u001b[0;32m   3653\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mKeyError\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m err:\n\u001b[1;32m-> 3654\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mKeyError\u001b[39;00m(key) \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01merr\u001b[39;00m\n\u001b[0;32m   3655\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mTypeError\u001b[39;00m:\n\u001b[0;32m   3656\u001b[0m     \u001b[38;5;66;03m# If we have a listlike key, _check_indexing_error will raise\u001b[39;00m\n\u001b[0;32m   3657\u001b[0m     \u001b[38;5;66;03m#  InvalidIndexError. Otherwise we fall through and re-raise\u001b[39;00m\n\u001b[0;32m   3658\u001b[0m     \u001b[38;5;66;03m#  the TypeError.\u001b[39;00m\n\u001b[0;32m   3659\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_check_indexing_error(key)\n", "\u001b[1;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m: 'Signal'"]}, {"data": {"image/png": "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**************************+Xwn0VuFnN5q3Lm7YdZqr20Q0YHqcbLe3t2PBggVYvHgxlixZgpaWFrS0tFx1VXbatGnQaDR44403AACrV6/GzJkzIZfLsWjRIhw4cAC7du2CXq/HunXrMGfOnP6/IhfhoZAjMdTc9INN0ogGXl7FxZXta7lYSt5wxePHbcl2oH2CI3JSz80biZFD/FHfqsNT7x/D+cY2nK5uRs65Rnx3pg47Tlbho6Pn8dbBMvxzXzHW7yzE//3vBABzs6voQK8BizU53A8+KjlatAYUXWOagKv6MPs8DCYR42IDMSLSdSr+ZDIBt44egk9+PgVvP3gdbkgKgcEkYsdJ8wcHc0ZFItzPviMUH7wxAX6eCpyubsEXJy7Y9dpERN1R9PTEnTt3Ij8/H/n5+di0aZPt8ZKSEsTHx1/54goFNm/ejMzMTKxYsQIymQx79+4FAISGhmLDhg2YN28efH19ERgYaJvBPVikRPrhTE0LTlc1Y8bw/u9LIqKe0RtNtn4J11rZBi6Zt11i3rd9eRmstTkaV7bJ3Xkq5fhb5ngs/Nt3OFBUjxtf3NPj595jx5XKnpDLBKTFBCLrbD2OlTfaGpO6C1EUbXPLM51o3FdvCIKAG5NDcWNyKI6VN+HVPUUorG7G4zcPs/u9AryUeGhqItZ/fRp/3XUa88YMsVuZOhFRd3qcbC9evPiae4uvdHzRokUoLi5GdnY2MjIyEBISYju2fPlyzJkzB6dOncLUqVPh6+vbi9Bd34gIP3yBC2ySRjTAimpaoDOa4OehQFyw9zXPHxMdAG+VHE1tehRWN2PkkIsJekOrDucbzXsNR0cz2Sb3NyzcF2tuG4NffXgcggD4eCjg4yGHj0oBHw8FvFUX/93HQw4fDwWGBnvjVssIqIE0Ls6cbOeca8Ld6c5fZt0bWWfrUVrfBl8PBRakRUkdTr+Niw3E6z+e5NB7LJ0Sjy3flaC4thWf5VbiR+MHz/ZFIpJGj5Pt/oiMjMT8+fOveCwhIaHb0WHujrO2iaRhbY42Msq/R82azPu2g/Ht6VocPFvfKdk+blnVTgz1QYDX1ZtGErmLH42PxuJxUVdtLugMxscGAnDPJmnvHjZPZVk0Lgo+HgPyds7l+Xkq8fC0RPx5RyFe/uYMFqQNgULep8E8REQ9wp8wErLuryqqbYHB2Pu5pUTUN3kVPWuOdqmMxGAAXedtH2dzNBqknD3RBi52JC+sbkaL1iBtMHZ0proZO/LMXbUz3WzF3tF+ckM8gn1UKKlrxf+OVUodDhG5OSbbEooJ8oK3Sg6dwYTS+lapwyEaNPItK9uje9Aczep6S5O0QyUNnTrZWpPtMWyORuR0wv08ER3oBVEEXt9XjHadUeqQ+i3nXCPufC0LOqMJ6fFBGMMP+nrF10OBR6YlAgBe+eYM9FzsICIHYrItIZlMQEoES8mJBpLJJCL/gqUTeXTPV7ZHRwfAx7Jv+9K/r9Yy8rF8w0vklOZa9oq/srsIN764G6/uLUJzh17iqPpm/5la3Lf5EJra9BgXG4hNDt7j7K4euH4oQn1VONfQho+Onpc6HCJyY0y2JTbCsm+bTdKIBsa5hja0aA1QKWRICut5U0brvm3gYil5lboDNc1ayISejRAjooH367kjsOa2MYgN9kJ9qw7rvirElLW7sf7r02hs1UkdXo99cfwCfrb1B7TpjJiaHIpty65DoLdK6rBckrdKgeXTkwAAr3xTBJ2Bq9tE5BhMtiVmXdlmsk00MPIqzWXfIyL9oOxlYxzrvO0sS7JtXdVOifCDl0puvyCJyG6UchkyJ8dhzzM3YcPdY5EU5gNNhwGvfHMGN764G2u+LEBNc4fUYV7VtkNlePzdo9AbRcxPG4LNP5nEpmj9dH/GUIT5eaCiqR0fZJdLHQ4RuSkm2xKzrWxXM9kmGgjWTuR9WYm2zts+bNm3zeZoRK5DIZdhyfgYfP3UdLx63wSkDvFHq86I1749i6kv7sHvPj2JyqZ2qcPsRBRFbNxThP/7OA+iCNx7XRxeuWc8PBT8cK+/PJVy/Pwm8+r233cXQWtw/f38ROR8mGxLzDr+61xDG9p07tMp1d3VNmvxj73FULe75r6/wexist3z/dpWo6P84aOSQ92uR0GVBrmWle00NkcjchkymYB5Y4bgiydvxL9+OgnjYgOhNZiw9ftSTP/zHvzx8/xOTRClYjKJ+H9fFODPOwoBAI/PGIY//Wg05D0YV0g9c8/kOET6e+KCugP/+YGr20Rkf0y2JRbi64FQXw+IInC6ukXqcKiHVn9ZgBe/OoXn/5cndSjUC6Io4mQfxn5ZKeQypCeY921nFdfjRAVXtolclSAIuHlEBD5+7AZsW3Ydrk8Mgd4oYst3Jfh3VqmksRmMJqz48Di2fFcCAHh+/kg8O2e4S4xbcyWeSjl+fvMwAMDGPUXo0HN1m4jsi8m2E7jYJE0jcSTUE+06I3acNM83/Sy30jZGipxftUaL+lYdZMLFOfe9Zd23/WH2eTS16aGSy/p8LSKSniAImDIsFO8+nIGVC1IBAGu2n8IZibZ3deiNWP72Ufz36HnIZQJeunMslk1NlCSWweCuSTGIDvRCtUaLdw6dkzocInIzTLadgLWUnOO/XMPuUzVou2RW6/qvCyWMhnrjpKU52rBw3z43NLPO27b+fR05xA8qBX+UErmDn02Jx/SUMGgNJvzivWMD3qVa06HHj/91GLsKqqFSyPDP+yfi9okxAxrDYOOhkONxy+r2q3vdYxY7ETkPvkN0AsM5/sulfJpbAcA8u1UuE7CroAbZZY0SR0U90Z/maFajovzhe0kX4DEsISdyG4Ig4M93pCHIW4n8Cxps2HV6QO+//K1sHC5pgJ+HAv/+2WTMSo0Y0PsPVndMjEFssBfqWrR4+2CZ1OEQkRthsu0EhlvGf51mR3Knp+nQY09hLQDgiZuTcccE84rDn3ecgihK31BnsBBFEf/6rqTXH3JYV7b7sl/bSiGXIT0+yPY9m6MRuZdwf0+suW0MAOCf+4pxyDLqz9HyKzX4vrgeSrmAdx/OsG1ZIcdTymV44uZkAOb/5q1aNqwlIvtgsu0EUiL8IAhAXYsOdS1aqcOhq/j6ZDV0BhOSwnwwcogfnpyZDJVchoNnG3CgaGDekBHwXVEd/vB5Ph7YcghFNT3/kCqvov8r2wA6vQkey2SbyO3cOnoI7pwYA1EEnn4/F5oOx0+eeP+IuRv2rNQIjI5mxcxAu218NIaGeKO+VYf32JmciOyEybYT8FLJMTTYG4BjSsm/zq+2rehR/3x2vBIAsGhsNARBQHSgF+7LiAPA1e2BdLSsCQDQpjPi0beP9mhsXlObDhWWGbqp/VjZBoApw0IBAH4eCgwL9+3XtYjIOb2waBTigr1R0dSO33160qH30hqM+N8x8xalOyfFOvRedGUKuQw/m5IAwNz8lIjIHphsOwlHNUk7eLYeD/37CG7/x/c4XNJg12sPNg2tOnx3pg4AsGDsENvjj900DN4qOXLPq7Ezv1qq8AaV45b51gBwpqYFz3+cd80POqxd42ODvRDgpezX/UdHB2DtbWPw9/smcOYtkZvy9VBgw91jIROAj45W4IvjFxx2r6/zq9HUpkekvyemJYc57D50dXNHR0IQgGPlTbYPZ4mI+oPJtpMYbhkdZO/xX9YZnR16E3629QecOM8V7r7anncBBpOIUVH+SAq7uJoZ5udh+zR8/c7TMJq4uu1IoijiuGW+9a9vHQG5TMBHORW2Eszu2JqjDbFPeeY9k+MwPYVvionc2cShwXjsJnOn6t9+fAJV6g6H3Of9I+cBmBt18QM86YT7eyJ9aDAAYPsJx324QkSDB5NtJzHCAR3Jy+pbsavAvNI6OtofLVoDfvLG4V7tcaWLrGVlC8dGdTn20LRE+HsqUFjdzPIzB6vSdKC2WQu5TMBPb4jHs7OHAwBWfXLyqjPP8yxbKUZHcyY2EfXcL2YmIy0mAOp2PZ79IBcmO3+gWtnUjv1nzI0375zEMV9SmzcmEgCwPa9K4kiIyB0w2XYS1jLy09UtdvtFvvX7UogicNPwMLz7UAbSYgLQ0KrD/ZsPo7yhzS73GCyqNR04ZCnDX5A2pMvxAC8lHpmeBABY//Vp6I0DO5vVXi6o21GjcczKjb3klpuT5pQIP3ip5HhkWiJuHhEOrcGEx7Zlo7mbRkb2GPtFRIOPUi7DhrvHwVMpw3dFddj6faldr/9h9nmIIpCRGIyhIT52vTb13q2jzb/js8sacUHNUnIi6h8m205iaLA3VAoZ2vVGlDf2PxFu7tDjA0tZ2s+mJMDPU4mtSycjOdwXVZoO3L/lkNMnVc7k8+MXIIrAxKFBiAnyvuI5S6fEI9RXhXMNbdcsaXZGh87WY8Zf9mLeK99BazBKHU63TlQ0AQDSLN16ZTIBL905FtGBXiitb8Nv/nuiy/7tdp0RZ2tbAPRv7BcRDU5JYb74v/mpAIC1X52yWxWaySTig2zz74u72BjNKUQGeGLiUPN4x6+4uk1E/cRk20ko5DIkW7oa26NJ2vtHzqNFa8CwcF9MTTZ3Tg72UeGtB69DbLAXyurb8MCWw2hq0/X7XoOBrYT8CqvaVt4qBR6fYd7b98o3Z9Chd96E9XInzqvx4JtH0KE3oa5Fi5xzTVKH1K3jlr4DabEXV6iDfFT4+73joZQL+OLEBbx52cpTQZUGJhEI9fVAuL/nQIZLRG7i/uviMGN4GHQGE375n2N2+VDyYEk9yhva4eehwNzR3f9+oYE1b4z5v8X2E0y2iah/mGw7kZFDzCtuuwtq+nUdo0nE1u/NjdF+NiUBgnCx2UpkgCe2PZiBcD8PFFY346dv/IBW7bXHJg1m5Q1tOFbeBJkAzLtKsg0AmdfFITrQC9UaLd7KKhugCPunqKYZP3njMFq0Blj78hwoqpM2qG6Iongx2Y4O7HRsfFwQfjtvJADgT18W4Fh5k+3YyQru1yai/hEEAS/ekYZgHxUKLmiwfufpfl/zfcs854XjouClkvf7emQfc0eb923/UNbAKkAi6hcm207knnRzCdn72eXIq+h71/BdBdUob2hHoLcSS8ZHdzkeF+KNtx68DoHeShwrb8LDbx1xqVXYgfapZVX7+qQQhPtdfVXUQyHHL2YmAwBe3VvU7f5hZ1He0Ib7Nx9GQ6sOaTEBtjJJZ022zzW0Qd2uh0ous/U5uNRPb4jH3NGR0BtF/HzbUVvlxsX92ky2iajvwv08sfa2MQCA1/efxcGz9X2+lrpdb2vCxRJy5xIV6IXxcYEQReCrk1zdJqK+Y7LtRCbFB2PR2CiIIvCHz/KvOTe4O/+yjPu6d3Jct5+UD4/0w9alk+GjkuNAUT2eeDfHZZt6OdrFEvKuXciv5Lbx0UgM80Fjm942es0Z1TR34IEth1Cl6UByuC+2Lp2MOaMiAAC559XQOOEHBbmWVe2RUf5QKbr++LKuPMWHeKOiqR3PvG/uHMzmaERkL7NHReKe9FiIIvDM+7l9rg77LLcSWoMJKRG+GBvDn03OZp6lrP9LjgAjon5gsu1kfjN3BDyVMhwubcDnx3v/Az6vQo1DJQ1QyAQ8cP3Qq547LjYQm3+SDpVChq/zq/GrD4/bfaSJqztT3YxTVc1QygXcaikruxaFXIZnZpnHUW3eX4KGVufbF69u0+PHWw6jtL4NMUFeeOvB6xDso0JMkDfiQ7xhNIk4dLZB6jC7OG4pDbc2R7sSf08lNt43ASqFDN+cqsGre4tszYy4sk1E9rByQSpigrxQ0dSOF7861adrWBtp3jUpttN2L3IO1t/5h0saUNuslTgaInJVTLadTFSgFx6dbm6ytebLArTrelfe/caBUgDm5h5DAryuef71SSH4x30ToJAJ+DinAr/77GSfV9Td0WeWDzymJYch0FvV4+fNHR2JUVHm2eb/3FfsqPD6pFVrwNKth3Gqqhlhfh7Ytuw6RAZcLI+fMszcUM8ZS8mPW7ZXpF1jFWhUVAB+v2gUAOAvO09DZzTBz1OBuOArd5InIuoNHw8F1t6WBgD4d1ZZr8vJCy5ocPy8GgqZcMXtXiS92GBvjI0JgEkEdrCUnIj6iMm2E3pkeiKiA71Qqe7oVaJW09xhK3n+2Y0JPX7eLSMj8NJdYyEI5jcNL35VyIQb5mZcthLysT0rIbeSyQQ8O8e8uv3m96WoUjtHgxWtwYjlb2fj6LkmBHgp8daDk7vMdXXWZNtoEm29DMbGBl7z/HvSY3HbJW9iU4f4c/WIiOzmxuRQZE4277X+9X+P9+rDceuq9syREQjx9XBIfNR/c8ewlJyI+ofJthPyVMptXZX/ua8YFU3tPXretoPnoDOaMCEuEON6kIxcavG4aPxx8WjbPf+660yvnu+OTlZqUFLXCg+FDDNTI3r9/JtSwpAeHwStwYS/7Zb+z9NgNOEX7x7D/jN18FbJ8cbSdIyI7FpWfX1iCAQBOFPT4lRdWItrW9CmM8JbJUdSmO81zxcEAf9vyWjbSL1rrYYTEfXWc/NGYkiAJ8rq2/CXnYU9eo7WYMT/cioAAHenszGaM7Pu2z54th71LSwlJ6LeY7LtpOaNicR1CcHQGkxY/WXBNc/v0Bux7ZB51NSDNyb26Z73ZwzFygXmbtQvf3MGf3eCBFFK1lXtW0aGw9dD0evnC4KAFXNGAAD+80O5pCvFJpOI33x0Al+drIJKLsOmH0/ChLigK54b5KOy7W0+UOw8q9vWkV+jowIgl/VshdpbpcDWn03GEzcPw0PT+vb3goioO/6eSqyxdCf/14ESZJddu9fFNwU1aGzTI8LfA1OTQx0dIvVDXIg3Rkf7wyQCO/OrpQ6HiFwQk20nJQgCVi1MhUwAvjh+AYeusR/ss9xK1LXoEBXgaeso3RcP3piA38w1J4h/2Xkar3/rXPuNB4rJJNoa1C3qZQn5pSYnBGP+mCEwmEQs3foD9hT2b4Z6X4iiiD9+kY8Ps89DLhPwSuZ4W6l4dy6Wkvd9rI29HT/fBKD3K9TRgV54Zvbwa45tIyLqi5uGh+OOiTEQRWDFh8evOUrzP5bZ2ndMjIFCzrdhzm4uu5ITUT/wp7wTGxUVgHsmxwEAfvdZPozddAoXRRH/sjRG+8kN8f3+5b18ehKemZUCAFj95Sm8ccB5x1c5ytFzjahoaoevhwI3DQ/v17XW3z0Ws1IjoDOY8PC/j+CrvIFttLJp/1lb47x1t6f1qKv6jZfs23aW/fvWsV9jWA5ORE5m5fxUhPt54GxtKzbsOt3teZVN7fj2TC0A4M6JLCF3BfMs+7a/L65HoxNOFyEi58Zk28k9MysF/p4KFFzQ2D4Nv9zBsw0ouKCBl1KOe9Lj7HLfJ25JxpM3m7ui//6zfLx1sMwu13UV1hLy2akR8FReeVZ5T3ko5Hj1vgmYnzYEeqOIn79zFJ9aru9o7Toj/ra7CADw/PyRuH1iTI+eN2loMFRyGS6oO1BS1+rIEHtEZzCh4IJ5VvbYmEBpgyEiukyAtxJ/WmIuJ9/07Vkcs4wpvNxHR89DFM1VT/GhPlc8h5xLQqgPRg7xh9Ek4muWkhNRLzHZdnIhvh745UzzKvNfdhZC3a7vcs6/LCvPd0yMQYC30m73fmpWCpZPTwIArPxfHv7zwzm7XduZGYwmfGEpF+ttF/LuKOUyvHz3ONw2PhpGk4hfvpeDD45c+cMTe/ostxLNHQbEBnvhZ1N63qHeSyXHxKHmPd3O0JX8dHUzdAYT/D0VGBrC8V1E5HxmpUZg8bgomETgVx/mQmvoXE5uMol4/8h5AMDdk7iq7UrmjzFXhH3BUnIi6iUm2y7ggeuHYli4LxpadXj5si7hZfWt2FVg/qT1p1Pi7XpfQRDw61uH40HLGLHffHQC/80+b9d7OKODZxtQ16JDoLcSN9qxeY1CLsNf7hyLzMlxMFn29r3t4IoBa9O8eycPhayHTcWspgwLAQB85wTJdq5tv3Ygx3cRkdP63cJRCPVV4XR1C/5uqSqyOlTSgHMNbfD1UGDumGtv5yHnYR0BdqCoDuq2roseRETdYbLtApRyma1L+L+zSlFU02w7tvX7UogiMGN4WI/GIfWWIAh4fv5I/Pj6oZbmL7kDVgItFWsJ+dzRQ6C0c/MamUzA6iWj8dMb4gEAz/8vD1u+c8ye+BPn1cg9r4ZSLuDOST0rH7+UtUlaVnF9t/0CBsoJy35tju8iImcW5KOyjdF8dW8x8irUtmPWaqaFY4fAW9X7CRcknaQwX4yI9IPBJGJn/sD2XSEi18Zk20VMTwnDzJHhMJhE/OHzAoiiiOYOPT6wlKT97Maelwj3liAI+N3CUcicHAuTCDz1n2PY7qalVDqDCdvzrCXkQxxyD0EQ8MLCVFuJ/h8/z8fGPUXXeFbvvXPYvKo9d/QQhPp69Pr5Y6ID4OehgKbD0OkNoxRybcl2oKRxEBFdy9wxQzBvTCSMJhErPjwOncEETYceX1p+t9zFEnKXZO1Kvn2Am5wSkWtjsu1Cnp+fCqVcwLena7H7VA3eP3IeLVoDksN9bd2jHUUmE/CnH43BHRNjYDSJeOLdHLdsFLL/TC00HQaE+3nguoQQh93HWqL/y5nJAIA/7yjE+p2Fduv8renQ43855hX6+67rW9M8hVyGjCTzn4GU87Y79EacrjZXc3Blm4hcwR8Wj0aQtxIFFzT4x95ifJZbiQ69CcnhvhgXGyh1eNQH8yyl/+b3CSwlJ6KeYbLtQuJDfWwr2H/8PB9bvzeXH//sxoQB2ccqkwl48fY0LB4XBYNJxGPbspFfqXH4fQeStUR+ftoQyHu5x7m3BEHAL2em4Ne3mueav7K7CGu2n7JLwv2/nAq0640YFu6LyQnBfb7OpSPApHKyUgOjSUSorweGBHBWNhE5v1BfD/xu0SgAwN/3nMFr+84CMK9qs++Ea0qO8ENyuC/0RhG73HCxgYgcg8m2i3ni5mSE+XmgtL4N5Q3tCPJWYsn46AG7v1wm4KU7x2LG8DDojSJe/qb7eaKupl1ntK3W26sLeU88elMSXlho3pP/+rdnsfrLgn5dTxRFbDto7hx/33Vx/XpjZ22S9kNpIzr0xmuc7RjHLc3RxsYE8E0qEbmMRWOjMCs1AnqjiHMNbVDIBCyZMHC/r8n+rI3SvjzBUnIi6hkm2y7G10OBX80Zbvv+3uvi+j0HurcUchl+O28kBAHYcbIap6pcf3VbazDiyfdy0KYzIibIC+MHuMxv6ZQErLbOaN1fguyyhj5fK7usEYXVzfBUynDbhN43RrtUUpgvIvw9oDOYkF3W2K9r9ZW1OdoYlpATkQsRBAF/+tFoBHiZR3LeMjK8T/0zyHnMtyTb356pRTNLyYmoB5hsu6DbJ8RganIoQn098OPr4yWJITnCD/MszUL+ttv+zb0GUofeiEfeysbX+dVQKWRYvWSMJCuo914XhzsnmpPj332a3+cO4NZxYovGRtne5PWVIAiYkiRtKXmubWU7UJL7ExH1Vbi/JzbcPRZjYwPx5C3JUodD/ZQS4YvEMB/oDCbsPlUjdThE5AJ6nWx/8sknSExMhEKhwLhx41BQYC55zcvLQ3p6OoKCgrBixYpO+0737duHkSNHIjQ0FOvXr+90vQ8//BBDhw5FVFQU3n333X6+nMFBJhPw5tLJOPzbWxDhL90e1sdvHgYA+PLEhU7jyFxJu86IZW8ewd7CWngqZfjXT9IxLSVMsnh+desI+HkocKJCbRsT0xsNrTpbedt91w21S0xTJNy33dyhx9m6VgBc2SYi13TziAh88vMpGBXFn2GuThAE20LDl246lYWI7KtXyXZxcTGWLl2KtWvXoqKiAikpKVi2bBm0Wi0WLlyIiRMn4siRI8jPz8fWrVsBALW1tVi0aBEyMzORlZWFbdu2Yc+ePQDMCfp9992HlStXYseOHVi1ahUKCwvt/iLdkUwmQObgBl7XMnKIP2anRkAUgb+74Op2q9aAn75xGN8V1cFbJcfWpZNxY7Jju7pfS5ifB35h6VC+bkch1G29K1P7MLscOqMJY6IDMNZOpfDWZPt4hbrX8fRXXoUGoghEB3qx/JKIiCQ3z1JKvrewFq1ag8TREJGz61WyXVBQgLVr1+Kuu+5CREQEHn30UeTk5GD79u1Qq9VYv349kpKSsHr1amzZsgUAsG3bNkRFRWHlypVITk7GqlWrbMc2b96MGTNmYNmyZRgzZgwef/xxvPXWW/Z/leQwT9xsTgw/za1EiWUF0hVoOvT48b8O41BJA/w8FHjrwcnISHTcqK/e+MkN8RgW7ouGVh027Op5AzqTScQ7hy42RrOXyABPJIX5QBSBrLP1drtuT1ibo3HkFxEROYORQ/wQH+INLUvJiagHepVsL1iwAA8//LDt+8LCQiQnJyM3NxcZGRnw9vYGAKSlpSE/Px8AkJubixkzZtj2wE6ePBnZ2dm2YzfffLPtepceu5xWq4VGo+n0RdIbExOAm0eEwyQCr+5xjdVtdZseD2w+hOyyRvh7KvD2suswcWjfx2PZm1Ius3Unf+tgGQqrelaif6C4DqX1bfDzUNi9m7pUI8COW5qjpXG/NhEROQFBEGyr29vzupaS6wwmNLbqcL6xDYVVzTh6rhEHiurQ0Kob6FCJyAko+vpEnU6Hl156CU8//TSKioqQkJBgOyYIAuRyORobG6HRaJCammo75u/vj8pK8yxjjUbT6XmXHrvcmjVr8Pvf/76v4ZIDPXHzMOw+VYOPcirw5C3JiA32dvg9W7UGnGtow7BwXyjlPf/MqKFVh/s3H0L+BQ2CvJV4e9l1TrmPbmpyGOaMisCOk9X43acn8c5D112zaZt13NeSCdHw8ejzX+0rumFYKN7MKsOB4gFOtiuaAHBlm4iInMe8MUPw6t5ifJ1fjdkb9qFVa0SrzoBWrQF645Wbm6ZE+GLHL6dxhCXRINPnbuQvvPACfHx8sGzZMigUCnh4dN5P6enpiba2ti7HrI8DuOqxyz333HNQq9W2r/Ly3jePIscYHxeEqcmhMJpEvLq32GH3adcZ8cXxC3j07WxM+OPXmPvyfkz449f4+baj+DD7POpatFd9fm2zFpmvH0T+BQ1CfVV47+HrnTLRtnp+fio8FDJkna3H9ryrz/Ss1nTg6wLzjHB7NUa7VEZiCGQCcLa2FRfU7Xa//pU0tOpQ3mC+1+ho5/3vREREg8uoKH8kh/tCbxRxuroFFU3taGrTd0q0PRQyBPuoEBvsBYVMwOnqFuRfYFUm0WDTp+Wv3bt3Y+PGjTh48CCUSiWCg4ORl5fX6Zzm5maoVCoEBwejtra2y+MArnrsch4eHl0SenIeT96SjP1n6vBhdjkev3kYogO97HLdDr0R+07X4vPjF/BNQTXadEbbMQ+FDM0dBnxx4gK+OHEBggCkRQdgxohw3DwiHKOjAmxN5Ko1Hbh300EU17Yi3M8D7zyUgWHhvnaJ0VFig73xyPQkvPLNGfzpiwLMGB4OL9WVZ6r/54dyGE0i0uODMDzSz+6xBHgpMSYmELnlTThQVI87JvZvfndPWPdrJ4T69HuEGRERkb0IgoBtD12H4+VqeKvk8PFQwMfD/E9vlQLeKnmnqrtH3jqCHSersSOvyqk/5Cci++t1sl1SUoLMzExs3LjRVh6enp6OTZs2dTpHq9UiODgY6enpeOedd2zHcnJyEB0dbXteVlYWHnzwwS7HyLWkxwfj+sQQZJ2tx2v7ivGHxaP7fC2dwYQDRXX47Hglvj5ZjeZLun3GBHlhftoQLEyLwsgh/sg934Q9p2qw+1QNTlZqkHtejdzzavx11xmE+nrgpuFhmDIsBC/vOoPS+jZEBXjinYcyEB/qY4+X7XCPTk/Cf7PPo6KpHf/YV4ynZ6V0OcdgNOHdw9bGaPZf1ba6cViIJdmuG5Bk+4RtvzbfmBARkXMJ9/PEzNSejV+dMyrSnGyfrMbTs4c7ODIicia9KiNvb2/HggULsHjxYixZsgQtLS1oaWnB1KlTodFo8MYbbwAAVq9ejZkzZ0Iul2PRokU4cOAAdu3aBb1ej3Xr1mHOnDkAgNtvvx3vvfceTpw4gZaWFrzyyiu2Y+R6nrjFPHf7vR/KUa3p6PXzG1p1eO6jE0j/0y4s3foDPjpagWatAZH+nnjwxgR8/NgN2P+rGXhu7kiMjg6AXCZgQlwQnpk9HF88ORWHfnsLXrx9DOaMioCPSo66Fi0+zD6Pp/6Ti9L6NsQGe+E/j1zvMok2AHip5Pi/+SMBAP/cV4zyhq7bLPYU1uKCugPBPirMHRPpsFgunbctilfek2ZPuWyORkREbuCWERFQyAQUVjej1IUmtxBR//VqZXvnzp3Iz89Hfn5+l5XszZs3IzMzEytWrIBMJsPevXsBAKGhodiwYQPmzZsHX19fBAYG2mZwjx07Fr/4xS8wadIkeHp6Ijk5GY899pjdXhwNrOsTQzBpaBCOlDXitX1nsWph6rWfZFFa14qfvnEYpfXmZDLMzwPzRkdi4dgoTIgL6tFM8Qh/T9ydHoe70+OgNRhxpLQRu0/VYG9hDbxVCrz2wERE2am8fSDNHR1pqxr4f1/k47UHJnU6vu1QGQDgzokx8FBcuczcHibEBcFDIUNNsxZFNS1IjrB/ufqlOPaLiIjcQYC3EhmJIfiuqA47TlbhkelJUodERANEEO24RFVVVYXs7GxkZGQgJKTzzOKSkhKcOnUKU6dOha9v572y+fn5qKiowPTp07vds305jUaDgIAAqNVq+Pv72+slUD99e7oWP/7XYXgoZPju1zcjzO/a++yzyxqx7M0f0NimR0yQF168PQ0ZiSGQ9yDBHiwKq5ox75X9MJpEvPXgZExNDgMAlDe0Ydqf90AUgb3P3uTwVfsHthzC/jN1eGFhKpZOSbj2E/qoWtOB61Z/A5kA5P1+DrxV9u2uTkRENJDeyirFyk9OYkJcID56bIrU4RBRP/QmD+1zN/IriYyMxPz587sk2gCQkJCAuXPndkm0ASA1NRWzZs3qcaJNzmtqcijGxgZCazBh8/6z1zx/+4kLuHfTQTS26ZEWE4CPH5uCKcNCmWhfZnikHx7IMO/H/v1n+dAbTQCAdw6fgyia/9wHojz+hiRrKXm9Q++TW94EAEiJ8GOiTURELm9Wqnmb19FzTajpw1Y7InJNdk22iQRBwC8se7ffOliGhlZdt+du+a4Ej71zFFqDCTNHhuO9hzN6tBI+WD01KwXBPioU1bTgze9LoTOY8P4P5hF4jmyMdqkbLfu2D52th8GS8DvCcct+7TEc+UVERG4gMsAT42IDAQA786ulDYaIBgyTbbK7GcPDMTraH206I7Z813V122gS8btPT+KPn+dDFIEHMobitQcmcQXzGgK8lPjVHHMX05d3ncG2Q2Wob9Uhwt8Dt4wMH5AYUqP8EeitRLPWgOMVaofdx3rtNMsbEyIiIlc3Z5R5dXvHySqJIyGigcJkm+xOEAQ8PiMZAPDm92VQt+ltx9p1Rjy2LRtbvy8FADw3dwT+sHgUy8Z76K5JsUiLCUCz1oA/fJ4PALg7Pa7TPE9HkssEXJ9o3iZy4EydQ+4hiqKtOdpYNkcjIiI3MWdUBAAgq7ge6nb9Nc4mInfAZJscYnZqBEZE+qFFa8C/DpQAAOpbtMjcdBA7TlZDJZfhb5nj8cj0JAgCE+2ekskEvLBwFABAFAGZAGROjh3QGGwjwIodk2yXN7SjqU0PpVzA8EjHdjwnIiIaKIlhvkgO94XBJGLPqRqpwyGiAcBkmxxCJhPw+M3mvdtvHCjB8fNNuO0f3+NYeRMCvJR4e9l1WDg2SuIoXdPEoUG4bUI0AOCWkREYEjCw48ysyfbRsia06Qx2v/7xiiYAwMgh/g4dZUZERDTQWEpONLgw2SaHmTt6CIaF+0LTYcDijQdQVt+G2GAvfPTYDZicECx1eC7tj4tHY9WCVPxpyegBv3d8iDeiA72gM5rwQ2mj3a9vbY7G+dpERORurMn23sJadOiNEkdDRI7GZJscRi4T8PgM8+q2KJr333706BQkhXUd/0a94+OhwM9uTEC4n+eA31sQBNyQZNm3XWT/UnLr2K+06EC7X5uIiEhKo6P9ER3ohXa9Efsd1PuEiJwHk21yqAVpQ5A5OQ4PZAzFuxzt5TamDw8DALx3+Bxqm7V2u67JJCLP1omcK9tEROReBEHArFRzozSWkhO5Pybb5FAKuQxrbhuDP/5oNEd7uZFbR0VidLQ/NB0G/L8v8u123bN1LWjVGeGllGMYKyCIiMgNWUvJdxVUw2A0SRwNETkSk20i6jWFXIY1S9IgE4BPjlXi29O1drlubrl5VXtUlD8UAzTOjIiIaCClxwch2EeFpjY9Dpc0SB0OETkQ380SUZ+MiQnAj6+PBwCs/CTPLo1eTlhLyGMC+30tIiIiZ6SQyzBzZDgAlpITuTsm20TUZ8/MTkGkvyfK6tvw991F/b5e7vkmAMBY7tcmIiI3Zi0l35lfDVEUJY6GiByFyTYR9ZmfpxK/W5QKAHjt22KcqW7u87W+yqvCMUsn8rFc2SYiIjc2ZVgofFRyXFB32EZeEpH7YbJNRP0yZ1QkZo4Mh94o4rcfn4DJ1PtP6H8obcCT7+VAFIF7r4tDfKiPAyIlIiJyDp5KOW4azlJyInfHZJuI+kUQBPx+8Wh4q+T4obQRH2SX9+r5Z6qb8eDWH6AzmDBzZAT+sGiUgyIlIiJyHrNHcQQYkbtjsk1E/RYd6IWnZqYAAFZ/eQp1LT2bvX1B3Y6f/OswNB0GTIgLxN8yx7MLORERDQozRoRDKRdQXNuKopoWqcMhIgfgu1oisoulU+Ixcog/1O16rP6i4Jrnq9v1+Om/fkClugOJYT7Y8pN0eKnkAxApERGR9Pw9lbghKRQAV7eJ3BWTbSKyC4VchjW3jYEgAB/lVOBAUV2353bojXj430dQWN2McD8PvLl0MoJ8VAMYLRERkfRsXcmZbBO5JSbbRGQ342ID8UDGUADA8/+78uxtk0nEM+/n4lBJA/w8FNi6dDJig70HOlQiIiLJzUqNgCAAuefVuKBulzocIrIzJttEZFfPzhmOcD8PlNS14tW9xZ2OiaKIP3yejy9OXIBSLuC1ByYiNcpfokiJiIikFebngYlxQQCAnSerJY6GiOyNyTYR2ZW/pxIvLDR3FP/H3qJOTV9e+/Ystn5fCgB46a5xuGFYqBQhEhEROQ1rKTn3bRO5HybbRGR388ZEYsbwMOiNIv7v4xMQRREfHT2PtdtPAQCenz8Si8ZGSRwlERGR9KzJ9qGSBjS26iSOhojsick2EdmdIAj4w+LR8FTKcKikAb/9OA+/+vA4AOChqQlYNjVR4giJiIicQ1yIN0ZE+sFoErGrgKXkRO6EyTYROURssDd+aZm9/e7hczCYRCweF4Xn5o6UODIiIiLncrGUnMk2kTthsk1EDvPgjQkYEekHAJgyLAR/vmMsZDJB4qiIiIicizXZ3n+mFm06g8TREJG9KKQOgIjcl1Iuw78fnIzdBTVYODYKKgU/3yMiIrrcyCF+iAv2xrmGNuwrrMXcMUOkDomI7IDvfInIocL9PHHP5Dj4ePCzPSIioisRBAFzRkUAYFdyInfCZJuIiIiISGKzLaXku0/VQG80SRwNEdkDk20iIiIiIolNiAtCiI8Kmg4DDpc0SB0OEdkBk20iIiIiIonJZQJmjjSXku9kKTmRW2CyTURERETkBGZb9m1/nV8NURQljoaI+ovJNhERERGRE5gyLBTeKjkq1R04WamROhwi6icm20RERERETsBTKcf0lDAALCUncgdMtomIiIiInMSsVMu+7fxqiSMhov5isk1ERERE5CRuHhEOuUzAqapmlNW3Sh0OEfUDk20iIiIiIicR6K3CdQnBAMyN0ojIdTHZJiIiIiJyIrOtpeQnmWwTuTIm20RERERETmTWqEgAwJGyBtS3aCWOhoj6isk2EREREZETiQ70wuhof5hE4JtTNVKHQ0R9xGSbiIiIiMjJzE41r26zlJzIdfU62a6rq0NCQgJKS0ttj73xxhsYPXo0AgMDkZmZibq6OtuxvLw8pKenIygoCCtWrIAoirZj+/btw8iRIxEaGor169f375UQEREREbkJ6wiw/Wdq0aYzSBwNEfVFr5Lturo6LFiwoFOivWvXLjz55JPYsGEDjh8/Do1GgyVLlgAAtFotFi5ciIkTJ+LIkSPIz8/H1q1bAQC1tbVYtGgRMjMzkZWVhW3btmHPnj12e2FERERERK5qRKQfYoO9oDWY8O3pums/gYicTq+S7XvuuQf33ntvp8f+/e9/46c//SlmzZqFuLg4/PnPf8Z3332HhoYGbN++HWq1GuvXr0dSUhJWr16NLVu2AAC2bduGqKgorFy5EsnJyVi1apXtGBERERHRYCYIwsVS8vwqiaMhor7oVbK9adMmPPnkk50eq6urQ1xcnO17uVxu+2dubi4yMjLg7e0NAEhLS0N+fj4AIDc3FzNmzIAgCACAyZMnIzs7u9t7a7VaaDSaTl9ERERERO7KOgLsm4IaGIwmiaMhot7qVbKdkJDQ5bEJEybg888/h8lk/gGwdetWpKenIyAgABqNptNzBEGAXC5HY2Njl2P+/v6orKzs9t5r1qxBQECA7Ss2NrY3oRMRERERuZSJQ4MQ7KOCul2Pw6UNUodDRL3U727kzz77LEwmEyZMmIDrr78ea9euxRNPPAEAUCgU8PDw6HS+p6cn2trauhyzPt6d5557Dmq12vZVXl7e39CJiIiIiJyWQi7DLSPCAQBf57MrOZGr6XeyHRgYiP379+PDDz/E2LFjMWLECNu+7uDgYNTW1nY6v7m5GSqVqssx6+Pd8fDwgL+/f6cvIiIiIiJ3NnvUxRFgl071ISLnZ7c521FRUfjoo4+wZs0a277t9PR0ZGVl2c4pKSmBVqtFcHBwl2M5OTmIjo62VzhERERERC7vxmGh8FTKUNHUjvwL7FlE5Erslmz/7W9/w4gRI/CjH/3I9ti0adOg0WjwxhtvAABWr16NmTNnQi6XY9GiRThw4AB27doFvV6PdevWYc6cOfYKh4iIiIjI5Xmp5JiWHAbAvLpNRK5DYY+LNDY2Yt26dfjqq686X1yhwObNm5GZmYkVK1ZAJpNh7969AIDQ0FBs2LAB8+bNg6+vLwIDA20zuImIiIiIyGz2qEjszK/GzvxqPDUrRepwiKiHBHEANn9UVVUhOzsbGRkZCAkJ6XSspKQEp06dwtSpU+Hr69vja2o0GgQEBECtVnP/NhERERG5rcZWHSb+v69hEoH9v5qB2GBvqUMiGrR6k4farYz8aiIjIzF//vwuiTZgHic2d+7cXiXaRERERESDRZCPCpMTggEAO9mVnMhlDEiyTUREREREfTc71dyV/Ov8KokjcQxRFPFVXhVK6lqlDoXIbphsExERERE5uVmpEQCAwyUNaGzVSRyN/X1yrBLL387GfZsOokNvlDocIrtgsk1ERERE5ORig70xcog/TCLwzakaqcOxK53BhPVfnwYAVKo78PbBMokjIrIPJttERERERC5gtmV1e+dJ9yolf/9IOc41tEEuEwAAf99TBE2HXuKoiPqPyTYRERERkQuYPcqcbH97phbtumuXWptMIk5WqnFB3e7o0PqsQ2/E33afAQD8dt5IJIX5oKlNj9f3nZU4MqL+s8ucbSIiIiIicqzUIf6IDvRCRVM79p+pxexRkV3O0XTosf90HXafqsG+0zWoa9Eh2EeFPc/ehAAvpQRRX91bWWWo1mgRHeiF+zPiEB3oheVvZ2PLdyX48fVDEe7vKXWIRH3GlW0iIiIiIhcgCIJtdftrywgwURRxuroZr+0rxt2vZWHCH77Gz985iv8ePY+6FnMjtYZWHd7KKpUq7G41d+jx6t4iAMAvZibDQyHHnFERGB8XiHa9Ea9YVryJXBWTbSIiIiIiF2EbAVZQjZX/y8ONL+7B7A3fYs32UzhU0gCDSURSmA8empqAd5Zdh7/cORYAsOW7ErTpDFKG3sWW70rQ2KZHYpgPbhsfDcD8gcKvbx0BAHjvcDlKOQqMXBjLyImIiIiIXER6fBACvZVoatPjLUvXbpVChusTQ3DziHDMGB6OuBBv2/kGowl/230GZfVtePdwOR68MUGq0DtpaNVh8/4SAMAzs4ZDIb+4BpiRGIKbhodhb2EtXvr6NP6WOX5AYsoua0CorweGhvgMyP3I/XFlm4iIiIjIRSjkMvzylmSMiPTDfdfFYctPJiF31Wy8+bPJ+MkN8Z0Sbev5j05PAgC8/m0xtAbnmGH9z33FaNEaMCrKH3NHd917vmLOcADAZ7mVyKtQD0g8t/8jC/Ne3o8z1c0Ovx8NDky2iYiIiIhcyE+nJOCrX07Dn5aMwS0jI+Clkl/1/CUTohHp74lqjRb/za4YoCi7V6XuwJvflwIAnp0zHDLLyK9LjYoKwOJxUQCAF7865dB4/rG3GGu3m+/RqjPi4beyoW7n6DHqPybbRERERERuzEMhx8PTEgGYV3ANRpOk8fxt9xloDSakxwfhppSwbs97ZtZwKOUC9p+pw/dFdQ6J5R97i23J/CPTExEd6IWSulb84r0cGE2iQ+5JgweTbSIiIiIiN3fP5FgE+6hwrqENnx+/IFkcZfWt+M8P5QCAFXNGQBC6rmpbxYV4497JcQDMq9uiaN/k99W9RbZE+5lZKXhu7ki89sBEeCpl2FtYi7/sLLTr/WjwYbJNREREROTmvFUKW3O0V/cWwSTRqu1fd52BwSRiekoYJicEX/P8x29OhrdKjtzzamzPq7JbHBv3FGHdV+Zk+tnZKXjilmQAwOjoALx4exoA86r3Z7mVdrsnDT5MtomIiIiIBoH7M4bCz0OB09Ut+LqgesDvX1jVjP8dM+8Zf3b28B49J8zPA8ummkvg/7Kj0C4l8Bv3FOHPO8yJ9oo5w/H4zcmdji8eF41HppvvueLDXORXavp9TxqcmGwTEREREQ0CAV5K/PiGoQCAV/cU2b0s+1rWf10IUQTmjo7EmJiAHj/voakJCPZR4WxdKz7IPt+vGP6++0ynRPvnM4Zd8bxfzRmBaSlh6NCb8NC/j6ChVdev+9LgxGSbiIiIiGiQ+NmUBHgqZcg9r8Z3Dmo6diW55U3YcbIaMgF4elZKr57r56nE45ak+K+7TqNd17fxZX/75gz+svM0AOBXt3afaAOAXCbgb/eMx9AQb1Q0tePn245K3ljOWZhMIrLLGlHXopU6FKfHZJuIiIiIaJAI8fVApqXp2N93Fw3Yfa3NxpaMj0FyhF+vn39fRhyiA71QrdFiq2VsWG+88s0ZvPS1OdH+9a0j8NhN3SfaVgHeSmz68ST4qOTIOluPP31Z0Ov7uhOjScSnuZWY+/J+3P6P73H3a1nQ8wOIq2KyTUREREQ0iDw8LRFKuYBDJQ04Utrg8Pt9X1yH/WfqoJQL+OXM5Gs/4Qo8FHLbivg/9hZB3dbzOdgv7zqD9ZZE+zdzR+DRm5J6/NyUCD+8dNc4AMAbB0rxYT/L2F2R3mjCh9nnMWv9Pjz5bg4Kq5sBAMW1rXj38DmJo3NuTLaJiIiIiAaRIQFeuH1CDABzszBHEkURf7Hskc6cHIfYYO8+X+tH46MxPMIPmg4DXt3XNW51ux75lRp8nV+NrQdK8Kcv8rH0jcPYsMucaD83dwSWT+95om116+hIPGnpVv7bj0/gWHlTn1+DK9EajNh2qAwz/rIXz36Qi7N1rQjwUuKpmSn41a3mBncv7zqD5o6ef/Ax2CikDoCIiIiIiAbW8ulJeP9IOfYU1uJkpRqjonresKw3dp+qwdFzTfBUymz7rvtKLhPwq1uH48E3j2DrgVK064yoaGxHRVM7Khrb0aw1dPvc5+aOwCN9SLStfnlLMvIrNdhVUI3lb2Xj0yemINzPs8/Xc2btOiPe++EcXtt3FlWaDgBAqK8Ky6Ym4v6MofD1UJhXu4+cx9m6Vry27yyendOz7vKDjSAOdBtCO9FoNAgICIBarYa/v7/U4RARERERuZQn383Bp7mVmD9mCDbeN8Gu1xZFEXtP1+L5j/NQ0dSO5dOT8Ju5I+xy3btey8IPpY1XPB7so0J0oJf5K8j8z7GxAZg49Nozva+luUOPJa9+j6KaFkwcGoR3H8qASuE+hcItWgO2HSzDpv1nUddi7r4e6e+JR6Yn4p70OHip5J3O/yqvCsvfzoanUoa9z85AZIB7fvhwud7koUy2iYiIiIgGoVNVGtz61/0QBGDX09ORFOZrl+vmVaixZnsBDhTVAzAnbF/9cioCvVV2uX5pXSte338WAV5KW1IdY/mnt8qxhbtna1uweOMBNHcYcNekGLx4exoEQXDoPR1JazBiX2EtPjt+Abvyq9GuN3d6jwnywqM3JeGOiTHwUMiv+FxRFHHnP7NwpKwRd0+KxYt3pA1k6JJhsk1ERERERNe07M0j2FVQjTsmxuAvd47t17XON7bhpZ2n8XFOBQBAJZfhJzcMxeMzkhHgrbRHuE5hb2ENfrb1B5hE8wixnnQ2dyYGowkHiuvxWW4ldpysQnPHxfL7xDAfPDo9CT8aHw2l/Nqr9tlljbj9H99DJgDbfzENwyN732ne1TDZJiIiIiKia8o514glr34PhUzA3hU3ISao9w3M1O36/9/evUdVWed7HP9s2FxEBLlkqGCgoUGKGqGUUnrKy0HFLJsRnVXWWJlzDmudaThNTuXRymackrSlmZfUKfGcPIOh02ReMEtFHVTwgloieCfFgA2igOzn/GHuiTqZ5Mb9bHm/1tpr+fweYH+33/V71v6svZ/fT3M3HtbirSWqu3R5K6iRvTrod4O7XdeCaGa2dGuJpqzaL0maM/YuDYtr7+KKrs5uN/SPkm+0es8p/X1vqb45X+c4Fxbgq+Fx7TWiZwfFhQc2+ZP6Zz/YqU/2lepf7min98YnOLt002lKDmWBNAAAAKCF6t0pSP1uD9GWw+c0//Mjmjay+zX/bt0lu97fdlRv53ylim+34krsHKzJyTGKC2/bTBWbw+P3Rqrk3Hkt3lKi//gwX2GBvoq/LcjVZf3AifIaLdlSor/tOe1Y7Ey6fG97co8wjYjroITIYHl4/PyvwqcP6aZ1hV8r5+AZbS0q071dQp1R+k2BT7YBAACAFmxrUZnGLtgub6uHNj8/0LHKtt1u6JLdUIPdUL3droaGfx7/o+Qb/fnTQzr2TY0kKbqdv15IvkMDu7Vz63uYm6LBbuiZ9/O0/sAZhbT21spJ/dQpxByf5F9qsOu9LcXKWPeV4z7sNj5WDekephE9O6hflxBZr+Fr4tfq5ex9+kvuUfXoGKjs3/S7rvBudnyNHAAAAMA1MQxDD7+zVbuPVcjb00OGLofqa0kJ7dr46LeDump0fLhTw5u7OF97Sb+cn6t9J23qcktrZT3b72ffn761qEz1DYaSbg+9rrCaf7xCL2Tt1YHTNklSn6hg/bp/lO7veot8vf7/xc6uV1l1re6fsVHn6xo0a0wvjezVsVmexwwI2wAAAACu2ZbDZfrVou3XFLC9PC0K8PXSY/dE6qn7opp9BXCz+9p2UQ/N2aLTlRd1b5cQLXmiT5O2BCutvKj/WrVfa/aXSpKiQltr/L2RGh0frtY+1/5/W3WxXm98ekh/2XZUhiG19fPS5OQYPRoffkO+bfD2hq/05rovFR7UShueu/9HVzF3d4RtAAAAAE1yrrpWNXUNsnpa5OlhkdXDQ1ZPi6we/zz2sKjFfE28KQpP2fTovK06X9egR+PDNWP0T28JZrcbWrb9qP605pCqay/J08MiPy9PVdVeXh28ja9VYxIi9Ng9kVddaM4wDH26v1RTVu3X17ZaSdLDvTvqD8NiFOLv47wX+RNq6i5pwJ8/05mqWr04LEYTkjrfsOe+kQjbAAAAAHADbTx0Rr/+dkuw9CHd9JuBP74l2MFSm17I2qvdxyokSb0i2ur1h3uoU7Cf/rrrhBZvKVFx2XlJkodFGnJnmJ7sH6W7bwtqFOJPVlzQlOx9Wn/gjCQpMsRPr43qoX63u2aRsv/5xzE9/9e9Cmzlpc/TB95UW75dQdgGAAAAgBvs/dwSvZR9eUuwt1N7a0TPDo3OX6xv0Ns5X+ndTUd0yW7I38eq9CHd9KvE2+T5nfu07XZDn315Ru9tLtHmw2WO8R4dA/Vk/0j9a/f2+mDbUc1c96Vq6hrk5WnRs/d30aSBtzfbfdnX4lKDXcmzv9CXX1frmfs664XkGJfV0lwI2wAAAADgAq/8rVCLNhfL2+qh5U/1VfxtwZIu3xf/h5V7VXLu8gruQ+68Vf+VcqfaB7a66t87VFqlJVuLlbXrpGq/3cfc29NDdQ2X/50QGaTpo3oo+tY2zfiqrl3Owa/15JI8eVs9lPPc/T9r73YzI2wDAAAAgAs02A1N/GCn1hV+reDW3lr0+N16f9tRZe06KUkKC/DV1JF3asidYU36u9+cr9PyHce0dGuJzlTVKsDXqsnJMfrF3RGm2mrLMAylLtimbUe+0cO9O2rmL3u5uiSnImwDAAAAgIvU1F3SL9/dpr0nKx1jFov0WOJt+t2Qbmrj+/PvZa67ZNf24nOKbR9wQxdAa4qC4xUaOWeLLBZp9b/1V/eOga4uyWmakkNb3mZ4AAAAANCM/LytWvT43eoQ6CtJuiOsjbKevVdTR3a/rqAtSd5WDyVF32LaoC1JPSPaakTPDjIM6U9rDrq6HJdp2ZviAQAAAEAzaBfgq49+00+7jlXogZh28vJsWZ9zpg/upjX7TuuLr8q0Zl+phnZv2tfmbwYtq+MAAAAAcIO0C/DV0O5hLS5oS1KnED89fk+kJClt+W6t2Vfq2oJcoOV1HQAAAADQ7P5z6B1K7hGmuga7Ji3bqRV5x11d0g1F2AYAAAAAOJ231UOzx/TWL+4Ol92Q0v93j97bXOzqsm6YJoftsrIyRUVFqaSkxDG2cOFCRUREyM/PTwMGDNCRI0cc5/bt26eEhAQFBQUpPT1d3138fNOmTYqJiVFoaKhmzpx5fa8EAAAAAGAqVk8P/emROP26f5QkadrfCjVr/Vdy002xmqRJYbusrEzDhw9vFLSLioo0bdo0ZWdn6+DBg+rSpYvGjx8vSaqtrdWIESMUHx+vvLw8FRYWasmSJZKks2fPKiUlRampqcrNzdWyZcu0ceNGZ70uAAAAAIAJWCwWvTgsRr8d1FWSlLH+S7368YGbPnA3KWyPGTNGY8eObTS2e/duJSYm6q677lKnTp305JNP6vDhw5KkTz75RJWVlZo5c6a6dOmi6dOna9GiRZKkZcuWqUOHDnrppZcUHR2tl19+2XEOAAAAAHDzsFgsSnsgWlNGxEqSFm0u1vN/3aMG+80buJsUthcsWKC0tLRGY7GxscrJyVF+fr4qKys1d+5cDRo0SJJUUFCgxMRE+fn5SZLi4uJUWFjoODdw4EBZLBZJUp8+fbRz584ffe7a2lrZbLZGDwAAAACA+3iiX5TeeLSnPCzSh3kn9O/Ld6n2UoOry2oWTdpnOyoq6gdjsbGxGj16tHr37u34me3bt0uSbDZbo9+xWCzy9PRUeXm5bDabYmNjHecCAgJ06tSpH33u119/XVOnTm1KuQAAAAAAkxkdHy5/H0+lLc/X3/eWqrp2p+b96i75eTcpnpreda9GvmPHDq1evVrbtm1TRUWFUlNTlZycLMMwZLVa5ePj0+jnfX19VVNT84NzV8Z/zAsvvKDKykrH4/jxlrVsPAAAAADcLIZ2b69F4+9WKy9Pff7lWT22aIcqL9S7uiynuu6wvXz5co0ZM0Z9+/ZVYGCgXn31VRUVFamgoEDBwcE6e/Zso5+vqqqSt7f3D85dGf8xPj4+CggIaPQAAAAAALinpOhb9MGEPgrwtSrvaLlS529TWXWtq8tymusO23a7XWfOnHEcV1VVqaamRg0NDUpISFBubq7jXHFxsWpraxUcHPyDc7t371bHjh2vtxwAAAAAgJuIvy1Y//30PQr191bhaZs2HTr707/kJq47bCclJSkrK0sZGRnKzMzUQw89pLCwMMXFxem+++6TzWbT4sWLJUnTp0/Xgw8+KE9PT6WkpGjLli1av3696uvrNWPGDA0ZMuS6XxAAAAAAwH3EdgjQion3amrKnXokPtzV5TjNdd+B/sgjj+jAgQN66623dPr0aXXv3l0rV66Ul5eXJGnhwoVKTU1Venq6PDw89Nlnn0mSQkNDlZGRoeTkZPn7+6tt27aOPbgBAAAAAC1HVGhrRYW2dnUZTmUxbsBO4qWlpdq5c6cSExMVEhLS6FxxcbEOHjyopKQk+fv7X/PftNlsCgwMVGVlJfdvAwAAAACaXVNy6A0J282BsA0AAAAAuJGakkOv+55tAAAAAADQGGEbAAAAAAAnI2wDAAAAAOBkhG0AAAAAAJyMsA0AAAAAgJMRtgEAAAAAcDLCNgAAAAAATkbYBgAAAADAyQjbAAAAAAA4GWEbAAAAAAAnI2wDAAAAAOBkhG0AAAAAAJyMsA0AAAAAgJMRtgEAAAAAcDLCNgAAAAAATmZ1dQE/l2EYkiSbzebiSgAAAAAALcGV/Hklj16N24btqqoqSVJERISLKwEAAAAAtCRVVVUKDAy86s9YjGuJ5CZkt9t16tQptWnTRhaLxdXlXJXNZlNERISOHz+ugIAAV5eDq6BX7oNeuQ965T7olfugV+6DXrkPeuU+XNkrwzBUVVWlDh06yMPj6ndlu+0n2x4eHgoPD3d1GU0SEBDAxHUT9Mp90Cv3Qa/cB71yH/TKfdAr90Gv3IerevVTn2hfwQJpAAAAAAA4GWEbAAAAAAAnI2zfAD4+PpoyZYp8fHxcXQp+Ar1yH/TKfdAr90Gv3Ae9ch/0yn3QK/fhLr1y2wXSAAAAAAAwKz7ZBgAAAADAyQjbAAAAAAA4GWEbAAAAAAAnI2wDAJyqoqJC27dvV3l5uatLAQAAcBnCdjPbt2+fEhISFBQUpPT0dLEenbmUlZUpKipKJSUljjF6Zj7Z2dnq3LmzrFarevXqpQMHDkiiV2a0YsUKRUZGasKECQoPD9eKFSsk0SuzGzp0qJYsWSJJ2rRpk2JiYhQaGqqZM2e6tjBIktLS0mSxWByP22+/XRLzysyef/55jRgxwnFMr8xnyZIljebVlceSJUu4DprMwoULFRERIT8/Pw0YMEBHjhyR5B7zirDdjGprazVixAjFx8crLy9PhYWFjjczcL2ysjINHz68UdCmZ+ZTVFSkJ554Qn/84x918uRJde3aVRMmTKBXJlRZWalJkybp888/1969ezVnzhylp6fTK5NbtmyZPv30U0nS2bNnlZKSotTUVOXm5mrZsmXauHGjiytEXl6ePv74Y5WXl6u8vFy7d+9mXpnYnj17NHfuXM2aNUsS7y3MauzYsY45VV5eruPHjys0NFQxMTFcB02kqKhI06ZNU3Z2tg4ePKguXbpo/Pjx7jOvDDSblStXGkFBQcb58+cNwzCM/Px8o1+/fi6uClc88MADxqxZswxJRnFxsWEY9MyMVq9ebbz77ruO45ycHKNVq1b0yoSOHTtmfPDBB47jgoICw9/fn16Z2Llz54xbb73V6Natm7F48WIjIyPDuOOOOwy73W4YhmF89NFHxrhx41xcZctWX19vBAQEGFVVVY3GmVfm1NDQYPTt29d46aWXHGP0yj289tprxlNPPcV10GRWrFhhPProo47jzZs3G+3bt3ebecUn282ooKBAiYmJ8vPzkyTFxcWpsLDQxVXhigULFigtLa3RGD0zn+HDh+vpp592HB86dEjR0dH0yoQiIiI0btw4SVJ9fb0yMjI0atQoemVizz33nEaNGqXExERJl6+BAwcOlMVikST16dNHO3fudGWJLd7evXtlt9vVq1cvtWrVSkOHDtWxY8eYVyY1b9487d27V5GRkVq1apXq6urolRu4ePGiZs2apcmTJ3MdNJnY2Fjl5OQoPz9flZWVmjt3rgYNGuQ284qw3YxsNpuioqIcxxaLRZ6eniwaZBLf7c0V9Mzc6urq9Oabb2rixIn0ysQKCgoUFhamNWvWaPbs2fTKpDZu3KgNGzZoxowZjrHv9yogIECnTp1yRXn4VmFhobp166b3339fe/bskdVq1dNPP828MqHq6mpNmTJFnTt31tGjR5WRkaH+/fvTKzeQmZmpvn37KjIykuugycTGxmr06NHq3bu32rZtq9zcXL3xxhtuM68I283IarXKx8en0Zivr69qampcVBF+Cj0ztylTpqh169aaMGECvTKxuLg4rV27VtHR0fTKpC5evKhnnnlG77zzjtq0aeMY/36v6JPrjRs3Tnl5ebrnnnsUHR2tuXPnat26dbLb7cwrk8nKytL58+e1ceNGTZ06VevWrVNVVZXee+89emVy8+bN08SJEyVxHTSbHTt2aPXq1dq2bZsqKiqUmpqq5ORkt3lvQdhuRsHBwTp79myjsaqqKnl7e7uoIvwUemZeOTk5mjNnjjIzM+Xl5UWvTMxisSg+Pl5Lly5VVlYWvTKhV155RQkJCRo2bFij8e/3ij6ZT7t27WS32xUWFsa8MpkTJ04oMTFRoaGhki6Htri4OFVUVNArEzt8+LAOHz6sQYMGSeI6aDbLly/XmDFj1LdvXwUGBurVV19VUVGR27y3IGw3o4SEBOXm5jqOi4uLVVtbq+DgYBdWhauhZ+ZUXFys1NRUzZkzR7GxsZLolRlt2rRJ6enpjmNvb29ZLBbFxMTQK5PJzMxUdna22rZtq7Zt2yozM1OTJk3S0qVLG/Vq9+7d6tixowsrRXp6ujIzMx3Hubm58vDwUI8ePZhXJhMeHq4LFy40Gjt69KjeeustemViH374oYYPHy4vLy9JP3x/wXXQtex2u86cOeM4rqqqUk1NjaxWq1vMK8J2M7rvvvtks9m0ePFiSdL06dP14IMPytPT08WV4cfQM/O5cOGChg8frpEjR2rUqFGqrq5WdXW1kpKS6JXJdO3aVfPnz9f8+fN1/PhxTZ48WYMHD1ZycjK9MpkvvvhC+/btU35+vvLz85WSkqJp06bp2LFj2rJli9avX6/6+nrNmDFDQ4YMcXW5LVrPnj314osvasOGDVq7dq0mTpyoxx57TIMHD2ZemcywYcNUWFioefPm6cSJE5o9e7YKCgr08MMP0ysTW7NmjQYMGOA4TklJ4TpoIklJScrKylJGRoYyMzP10EMPKSwsTGlpae4xr1y9HPrNLjs72/Dz8zNCQkKMW265xdi/f7+rS8L36DtbfxkGPTObjz76yJD0g0dxcTG9MqG1a9casbGxRps2bYzRo0cbZ86cMQyDeWV2jz/+uLF48WLDMAzjnXfeMby8vIygoCAjKirKKC0tdW1xMH7/+98bgYGBRnBwsJGWlmZUV1cbhsG8MqPNmzcbiYmJRqtWrYzOnTsbq1atMgyDXplVTU2N4e3tbRw4cKDRONdB87Db7ca0adOMTp06GV5eXkbv3r2NXbt2GYbhHvPKYhiG4cqw3xKUlpZq586dSkxMVEhIiKvLwTWgZ+6DXrkPeuU+iouLdfDgQSUlJcnf39/V5eAqmFfug165F66D7sHs84qwDQAAAACAk3HPNgAAAAAATkbYBgAAAADAyQjbAAAAAAA4GWEbAAAAAAAnI2wDAAAAAOBkhG0AAAAAAJyMsA0AAAAAgJMRtgEAAAAAcDLCNgAAAAAATvZ/Hwnv3Ct0/kQAAAAASUVORK5CYII=", "text/plain": ["<Figure size 1200x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import pandas as pd\n", "import matplotlib.pyplot as plt\n", "\n", "plt.rcParams['font.family'] = ['SimHei']  # 设置中文字体\n", "plt.rcParams['axes.unicode_minus'] = False  # 设置负号显示\n", "\n", "# 读取贵州茅台股票历史交易数据\n", "df = pd.read_csv('data/贵州茅台股票历史交易数据.csv')\n", "\n", "# 创建日期索引\n", "df['Date'] = pd.to_datetime(df['Date'])\n", "df.set_index('Date', inplace=True)\n", "\n", "# 计算RSI指标\n", "def calculate_rsi(data, window=14):\n", "    delta = data['Close'].diff()\n", "    gain = delta.copy()\n", "    loss = delta.copy()\n", "    gain[gain < 0] = 0\n", "    loss[loss > 0] = 0\n", "    avg_gain = gain.rolling(window).mean()\n", "    avg_loss = abs(loss.rolling(window).mean())\n", "    rs = avg_gain / avg_loss\n", "    rsi = 100 - (100 / (1 + rs))\n", "    return rsi\n", "\n", "# 计算RSI指标\n", "df['RSI'] = calculate_rsi(df)\n", "\n", "#  交易信号生成\n", "df['Signal'] = 0\n", "df.loc[df['RSI'] > 70, 'Signal'] = -1\n", "df.loc[df['RSI'] < 30, 'Signal'] = 1\n", "#\n", "# 绘制价格和交易信号图表\n", "plt.figure(figsize=(12, 6))\n", "plt.plot(df.index, df['Close'], label='Close Price')\n", "plt.scatter(df[df['Signal'] == 1].index, df[df['Signal'] == 1]['Close'], color='green', marker='^', label='Buy Signal')\n", "plt.scatter(df[df['Signal'] == -1].index, df[df['Signal'] == -1]['Close'], color='red', marker='v', label='Sell Signal')\n", "plt.title('贵州茅台股票价格和交易信号')\n", "plt.xlabel('日期')\n", "plt.ylabel('股价')\n", "plt.legend()\n", "plt.grid(True)\n", "plt.show()\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "2584b984", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}