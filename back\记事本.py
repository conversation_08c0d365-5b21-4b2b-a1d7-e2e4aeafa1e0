import tkinter as tk
import csv
import os
import pyperclip


class NotepadApp:
    def __init__(self, root):
        self.root = root
        self.root.title("记事本")
        self.root.lift()  # 将窗口置于最顶层

        # 创建文本框
        self.textbox = tk.Text(root)
        self.textbox.pack(expand=True, fill='both')
        self.textbox.bind('<Button-1>', self.copy_selected_line)

        # 添加开始按钮和状态标签
        self.start_button = tk.Button(root, text="开始", command=self.toggle_monitoring)
        self.start_button.pack()
        self.status_label = tk.Label(root, text="未开始监视", fg="red")
        self.status_label.pack()

        # 创建菜单
        menubar = tk.Menu(root)
        root.config(menu=menubar)

        file_menu = tk.Menu(menubar, tearoff=0)
        file_menu.add_command(label="保存", command=self.save_to_csv)
        file_menu.add_command(label="退出", command=self.exit)
        menubar.add_cascade(label="文件", menu=file_menu)

        # 初始化剪贴板内容
        self.last_clipboard_content = None
        self.monitoring = False

        # 读取已保存的内容
        self.load_from_csv()

        # 重写窗口关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.exit)

    def toggle_monitoring(self):
        if self.monitoring:
            self.stop_monitoring()
        else:
            self.start_monitoring()

    def start_monitoring(self):
        self.monitoring = True
        self.start_button.config(text="停止")
        self.status_label.config(text="正在监视", fg="green")
        self.root.after(1000, self.check_clipboard)

    def stop_monitoring(self):
        self.monitoring = False
        self.start_button.config(text="开始")
        self.status_label.config(text="未开始监视", fg="red")

    def check_clipboard(self):
        if self.monitoring:
            clipboard_content = pyperclip.paste()
            if clipboard_content != self.last_clipboard_content:
                self.last_clipboard_content = clipboard_content
                self.textbox.insert(tk.END, clipboard_content + "\n")
            self.root.after(1000, self.check_clipboard)

    def load_from_csv(self):
        if os.path.exists("notepad.csv"):
            with open("notepad.csv", "r", newline="", encoding="utf-8") as file:
                reader = csv.reader(file)
                for row in reader:
                    self.textbox.insert(tk.END, row[0] + "\n")

    def save_to_csv(self):
        with open("notepad.csv", "w", newline="", encoding="utf-8") as file:
            writer = csv.writer(file)
            text_content = self.textbox.get("1.0", tk.END).strip()
            lines = text_content.split("\n")
            for line in lines:
                writer.writerow([line])

    def copy_selected_line(self, event):
        # 获取点击位置的行号
        index = self.textbox.index(f"@{event.x},{event.y}")
        line_number = index.split('.')[0]
        line_start = f"{line_number}.0"
        line_end = f"{line_number}.end"
        selected_line = self.textbox.get(line_start, line_end)

        # 将选中的行复制到剪贴板
        self.root.clipboard_clear()
        self.root.clipboard_append(selected_line.strip())

        # 闪烁效果
        self.flash_line(line_start, line_end)

    def flash_line(self, line_start, line_end):
        self.textbox.tag_add("flash", line_start, line_end)
        self.textbox.tag_config("flash", background="blue", foreground="white")
        self.root.after(2000, lambda: self.textbox.tag_remove("flash", line_start, line_end))

    def exit(self):
        self.save_to_csv()
        self.root.quit()


if __name__ == "__main__":
    root = tk.Tk()
    app = NotepadApp(root)
    root.mainloop()
