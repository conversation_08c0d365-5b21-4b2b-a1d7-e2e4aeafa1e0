# -*- coding: utf-8 -*-
"""
创建持久的测试文件用于GUI测试
"""
import os

def create_test_files():
    """创建测试文件"""
    # 在当前目录创建测试文件夹
    test_dir = os.path.join(os.getcwd(), "测试文件后缀规范化")
    
    if not os.path.exists(test_dir):
        os.makedirs(test_dir)
        print(f"创建测试目录：{test_dir}")
    else:
        print(f"测试目录已存在：{test_dir}")
    
    # 创建各种测试文件
    test_files = [
        # 需要处理的文件
        ("测试文件.txt汉字", 1024),           # 移除汉字
        ("另一个文件.doc中文", 2048),         # 移除汉字
        ("压缩文件.7", 1024),               # 标准化扩展名
        ("另一个压缩.7zz", 2048),           # 标准化扩展名
        ("旧格式.z", 512),                  # 标准化扩展名
        
        # 不需要处理的文件
        ("正常视频.mp4", 200 * 1024 * 1024),  # 正常文件
        ("正常图片.jpg", 5 * 1024 * 1024),    # 正常文件
        ("正常文档.pdf", 10 * 1024 * 1024),   # 小PDF文件
        ("压缩包.7z.001", 100 * 1024 * 1024), # 分卷文件
        ("压缩包.zip.002", 100 * 1024 * 1024), # 分卷文件
    ]
    
    created_files = []
    
    for filename, size in test_files:
        file_path = os.path.join(test_dir, filename)
        
        # 如果文件已存在，跳过
        if os.path.exists(file_path):
            print(f"⏭️ 文件已存在，跳过：{filename}")
            continue
            
        try:
            # 创建指定大小的文件
            with open(file_path, 'wb') as f:
                # 写入一些内容来模拟文件大小
                chunk_size = min(1024 * 1024, size)  # 1MB chunks
                remaining = size
                while remaining > 0:
                    write_size = min(chunk_size, remaining)
                    f.write(b'0' * write_size)
                    remaining -= write_size
                    
            created_files.append(file_path)
            print(f"✅ 创建文件：{filename} ({size / (1024*1024):.1f}MB)")
            
        except Exception as e:
            print(f"❌ 创建文件失败：{filename} - {e}")
    
    return test_dir, created_files

if __name__ == "__main__":
    print("=== 创建测试文件 ===")
    
    test_dir, created_files = create_test_files()
    
    print(f"\n=== 测试说明 ===")
    print(f"测试目录：{test_dir}")
    print(f"创建文件数：{len(created_files)}")
    print(f"\n现在可以在GUI程序中选择此目录进行测试")
    print(f"预期会处理以下文件：")
    print(f"- 测试文件.txt汉字 -> 测试文件.txt")
    print(f"- 另一个文件.doc中文 -> 另一个文件.doc")
    print(f"- 压缩文件.7 -> 压缩文件.7z")
    print(f"- 另一个压缩.7zz -> 另一个压缩.7z")
    print(f"- 旧格式.z -> 旧格式.7z")
    print(f"\n其他文件应该不会被处理")
