import os
import csv
import tkinter as tk
from tkinter import filedialog, messagebox, simpledialog

def remove_string_in_folder_names(folder_path, string_to_remove):
    folders_renamed = 0
    files_renamed = 0
    skipped_items = 0

    for root, dirs, files in os.walk(folder_path, topdown=False):
        for dir_name in dirs:
            if string_to_remove in dir_name:
                old_dir_path = os.path.join(root, dir_name)
                new_dir_name = dir_name.replace(string_to_remove, "")
                new_dir_path = os.path.join(root, new_dir_name)
                if os.path.exists(new_dir_path):
                    skipped_items += 1
                    continue
                os.rename(old_dir_path, new_dir_path)
                folders_renamed += 1

        for filename in files:
            if string_to_remove in filename:
                old_file_path = os.path.join(root, filename)
                new_filename = filename.replace(string_to_remove, "")
                new_file_path = os.path.join(root, new_filename)
                if os.path.exists(new_file_path):
                    skipped_items += 1
                    continue
                os.rename(old_file_path, new_file_path)
                files_renamed += 1

    return folders_renamed, files_renamed, skipped_items

def load_strings_from_csv(csv_path):
    try:
        with open(csv_path, mode='r', newline='') as file:
            reader = csv.reader(file)
            return [row[0] for row in reader if row]
    except FileNotFoundError:
        return []

def save_string_to_csv(string, csv_path):
    with open(csv_path, mode='a', newline='') as file:
        writer = csv.writer(file)
        writer.writerow([string])

if __name__ == "__main__":
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口
    csv_path = 'strings.csv'  # CSV文件路径

    folder_path = filedialog.askdirectory(title="选择文件夹")
    if folder_path:
        while True:
            strings = load_strings_from_csv(csv_path)
            for string_to_remove in strings:
                folders_renamed, files_renamed, skipped_items = remove_string_in_folder_names(folder_path, string_to_remove)
                print(f"处理字符串 '{string_to_remove}':\n  文件夹重命名：{folders_renamed}\n  文件重命名：{files_renamed}\n  跳过数量：{skipped_items}")

            new_string = simpledialog.askstring("输入新字符串", "请输入要移除的新字符串（可选）:")
            if new_string:
                save_string_to_csv(new_string, csv_path)
                folders_renamed, files_renamed, skipped_items = remove_string_in_folder_names(folder_path, new_string)
                print(f"新字符串 '{new_string}' 处理完成:\n  文件夹重命名：{folders_renamed}\n  文件重命名：{files_renamed}\n  跳过数量：{skipped_items}")

    #root.mainloop()  # 保持程序运行，直到窗口关闭
