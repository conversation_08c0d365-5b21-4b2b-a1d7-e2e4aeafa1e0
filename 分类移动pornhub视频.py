import os
import shutil
import tkinter as tk
from tkinter import filedialog

class VideoMoverGUI:
    def __init__(self, master):
        self.master = master
        self.master.title("Video Mover")

        self.folder_path_var = tk.StringVar()
        self.key_file_path_var = tk.StringVar()

        self.create_widgets()

    def create_widgets(self):
        # 文件夹路径输入
        folder_label = tk.Label(self.master, text="Enter folder path:")
        folder_label.pack(pady=5)
        folder_entry = tk.Entry(self.master, textvariable=self.folder_path_var)
        folder_entry.pack(pady=5)
        folder_button = tk.Button(self.master, text="Browse", command=self.browse_folder)
        folder_button.pack(pady=5)

        # key.txt文件路径输入
        key_label = tk.Label(self.master, text="Enter key.txt path:")
        key_label.pack(pady=5)
        key_entry = tk.Entry(self.master, textvariable=self.key_file_path_var)
        key_entry.pack(pady=5)
        key_button = tk.Button(self.master, text="Browse", command=self.browse_key_file)
        key_button.pack(pady=5)

        # 执行移动操作的按钮
        move_button = tk.Button(self.master, text="Move Videos", command=self.move_videos)
        move_button.pack(pady=10)

    def browse_folder(self):
        folder_path = filedialog.askdirectory()
        if folder_path:
            self.folder_path_var.set(folder_path)

    def browse_key_file(self):
        key_file_path = filedialog.askopenfilename(filetypes=[("Text Files", "*.txt")])
        if key_file_path:
            self.key_file_path_var.set(key_file_path)

    def move_videos(self):
        folder_path = self.folder_path_var.get()
        key_file_path = self.key_file_path_var.get()

        if not folder_path or not key_file_path:
            tk.messagebox.showerror("Error", "Please enter folder path and key.txt path.")
            return

        rules = load_rules(key_file_path)
        total_files_moved = 0  # 统计总共移动的文件数量
        for rule in rules:
            folder_name, keywords = rule
            files_moved = search_and_move_videos(folder_path, folder_name, keywords)
            total_files_moved += files_moved

        tk.messagebox.showinfo("Success", f"Video move operation completed. Moved a total of {total_files_moved} files.")

def load_rules(file_path):
    rules = []
    with open(file_path, 'r', encoding='utf-8') as file:
        for line in file:
            line = line.strip()
            if line:
                parts = line.split(':')
                folder_name = parts[0].strip()
                keywords = [keyword.strip().lower() for keyword in parts[1].split(',')]
                rules.append((folder_name, keywords))
    return rules

def search_and_move_videos(folder_path, folder_name, keywords):
    files = [file for file in os.listdir(folder_path) if os.path.isfile(os.path.join(folder_path, file)) and is_video_file(file)]

    files_moved = 0  # 统计当前规则下移动的文件数量
    for file in files:
        file_path = os.path.join(folder_path, file)
        if is_video_file(file_path):
            if move_video(file_path, folder_path, folder_name, keywords):
                files_moved += 1

    return files_moved

def is_video_file(file_path):
    video_extensions = ['.mp4', '.avi', '.mkv', '.mov', '.ts']  # Add more if needed
    return any(file_path.lower().endswith(ext) for ext in video_extensions)

def move_video(file_path, folder_path, folder_name, keywords):
    file_name = os.path.basename(file_path)
    for keyword in keywords:
        if keyword.lower() in file_name.lower():
            destination_folder = os.path.join(folder_path, folder_name)
            if not os.path.exists(destination_folder):
                os.makedirs(destination_folder)
            # 实际移动文件的代码这里先注释掉，你确认后再取消注释
            shutil.move(file_path, os.path.join(destination_folder, file_name))
            print(f"Moved {file_name} to {destination_folder}")
            return True  # 移动成功，返回 True
    return False  # 移动失败，返回 False

if __name__ == "__main__":
    root = tk.Tk()
    app = VideoMoverGUI(root)
    root.mainloop()