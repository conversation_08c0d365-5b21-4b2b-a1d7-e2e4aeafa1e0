{"cells": [{"cell_type": "code", "execution_count": 1, "id": "e73312f4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'two': 2, 'four': 4}\n", "['two', 'four']\n"]}], "source": ["input_dict = {'one': 1, 'two': 2, 'three': 3, 'four': 4}\n", "\n", "output_dict = {k: v for k, v in input_dict.items() if v % 2 == 0}\n", "print(output_dict)\n", "\n", "keys = [k for k, v in input_dict.items() if v % 2 == 0] \n", "print(keys)"]}, {"cell_type": "code", "execution_count": null, "id": "839e2489", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}