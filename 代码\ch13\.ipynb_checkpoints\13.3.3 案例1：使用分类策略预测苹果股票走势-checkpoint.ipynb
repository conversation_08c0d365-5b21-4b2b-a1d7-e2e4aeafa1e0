{"cells": [{"cell_type": "code", "execution_count": 33, "id": "6a44f447", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["准确率: 0.783410138248848\n"]}], "source": ["import pandas as pd\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.preprocessing import StandardScaler\n", "from sklearn.linear_model import LogisticRegression\n", "from sklearn.pipeline import Pipeline\n", "from sklearn.impute import SimpleImputer\n", "from sklearn.metrics import accuracy_score\n", "import joblib\n", "\n", "# 数据准备和处理\n", "data = pd.read_csv('data/AAPL.csv')\n", "data['Close'] = data['Close'].str.replace('$', '').astype(float)\n", "data['Open'] = data['Open'].str.replace('$', '').astype(float)\n", "data['High'] = data['High'].str.replace('$', '').astype(float)\n", "data['Low'] = data['Low'].str.replace('$', '').astype(float)\n", "\n", "# 创建标签列\n", "data['Label'] = data['Close'].diff().gt(0).astype(int)\n", "\n", "# 提取特征和目标变量\n", "X = data[['Volume', 'Open', 'High', 'Low']]\n", "y = data['Label']\n", "\n", "# 划分训练集测试集\n", "X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3, random_state=1)\n", "\n", "# 构建Pipeline\n", "pipe = Pipeline([\n", "    ('imputer', SimpleImputer(strategy='mean')),\n", "    ('scaler', StandardScaler()),\n", "    ('model', LogisticRegression())\n", "])\n", "\n", "# 模型训练\n", "pipe.fit(X_train, y_train)\n", "\n", "# 保存模型\n", "joblib.dump(pipe, 'model.pkl')\n", "\n", "# 测试集预测\n", "y_pred = pipe.predict(X_test)\n", "\n", "# 准确率\n", "accuracy = accuracy_score(y_test, y_pred)\n", "print(f\"准确率: {accuracy}\")\n"]}, {"cell_type": "markdown", "id": "3dca1c5e", "metadata": {}, "source": ["### 预测股票走势"]}, {"cell_type": "code", "execution_count": null, "id": "25c2a690", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import joblib\n", "\n", "# 加载模型\n", "loaded_model = joblib.load('model.pkl')\n", "\n", "# 新数据准备\n", "new_data = pd.read_csv('data/HistoricalData_1687681340565.csv')\n", "new_data['Close'] = new_data['Close'].str.replace('$', '').astype(float)\n", "new_data['Open'] = new_data['Open'].str.replace('$', '').astype(float)\n", "new_data['High'] = new_data['High'].str.replace('$', '').astype(float)\n", "new_data['Low'] = new_data['Low'].str.replace('$', '').astype(float)\n", "\n", "# 删除Close和Date特征列\n", "new_data.drop('Close', axis=1, inplace=True)\n", "new_data.drop('Date', axis=1, inplace=True)\n", "\n", "# 预测结果\n", "predicted_labels = loaded_model.predict(new_data)\n", "\n", "# 输出预测结果\n", "for i, label in enumerate(predicted_labels):\n", "    print(f\"样本{i+1}的预测结果：{label}\")\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}