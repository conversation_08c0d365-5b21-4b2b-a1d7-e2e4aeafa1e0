import os
import tkinter as tk
from tkinter import filedialog
import xml.etree.ElementTree as ET


def select_folder():
    print("Opening folder selection dialog...")
    root = tk.Tk()
    root.withdraw()
    folder_path = filedialog.askdirectory()
    print(f"Selected folder: {folder_path}")
    return folder_path


def parse_nfo(nfo_path):
    print(f"Parsing NFO file: {nfo_path}")
    try:
        tree = ET.parse(nfo_path)
        root = tree.getroot()

        genres = [genre.text for genre in root.findall('.//genre')]
        actors = [actor.findtext('name') for actor in root.findall('.//actor')]

        return genres, actors
    except ET.ParseError as e:
        print(f"Error parsing NFO file {nfo_path}: {e}")
        return [], []


def create_symlinks(base_path, subfolder, names, original_folder):
    print(f"Creating symlinks for {names} in {subfolder}")
    for name in names:
        if name:
            target_path = os.path.join(base_path, subfolder, name)
            os.makedirs(target_path, exist_ok=True)

            symlink_path = os.path.join(target_path, os.path.basename(original_folder))
            if not os.path.exists(symlink_path):
                os.symlink(original_folder, symlink_path)
                print(f"Created symlink: {symlink_path}")
            else:
                print(f"Symlink already exists: {symlink_path}")


def process_folder(folder_path):
    base_path = "d:/影片库/"

    for root, dirs, files in os.walk(folder_path):
        for file in files:
            if file.endswith(".nfo"):
                nfo_path = os.path.join(root, file)
                genres, actors = parse_nfo(nfo_path)

                # Create symlinks for each genre under "类型" folder
                create_symlinks(base_path, "类型", genres, root)

                # Create symlinks for each actor under "演员" folder
                create_symlinks(base_path, "演员", actors, root)


if __name__ == "__main__":
    print("Script started.")
    selected_folder = select_folder()
    if selected_folder:
        process_folder(selected_folder)
    else:
        print("No folder selected")
    print("Script finished.")
