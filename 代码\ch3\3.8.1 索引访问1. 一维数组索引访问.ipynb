{"cells": [{"cell_type": "code", "execution_count": 1, "id": "10e1d5a5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["6\n", "6\n"]}], "source": ["import numpy as np\n", "a = np.array([1, 2, 3, 4, 5, 6])\n", "print(a[5])\n", "print(a[-1])"]}, {"cell_type": "code", "execution_count": null, "id": "e591a776", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}