import shutil
import time
from bs4 import BeautifulSoup
import os
import re
import tkinter as tk
from tkinter import filedialog
from PIL import Image
import subprocess
def extract_number(file_name):
    pattern = r"(\d{6})"  # 匹配形式为123456
    match = re.search(pattern, file_name)
    if match:
        return match.group()
    return None

def select_folder():
    root = tk.Tk()
    root.withdraw()  # 隐藏根窗口
    folder_selected = filedialog.askdirectory()  # 弹出对话框选择文件夹
    return folder_selected

from bs4 import BeautifulSoup

def extract_info_from_html(file_name, number):
    with open(file_name, 'r', encoding='utf-8') as file:
        soup = BeautifulSoup(file, 'html.parser')

        # 初始化影片名称
        title = ""

        # 在网页全文中搜索包含指定番号的字符串
        text = soup.get_text()
        lines = text.split('\n')
        for line in lines:
            if number in line:
                # 如果找到匹配的字符串，则将整行文本作为影片名称
                title = line.strip()
                break

        # 在影片名称中找到包含 "avs" 的部分，删除其之前的字符
        index_avs = title.lower().find("avs")
        if index_avs != -1:
            title = title[index_avs:].strip()

        # 找到 "– High quality"，只保留其之前的部分
        index_high_quality = title.lower().find("– high quality")
        if index_high_quality != -1:
            title = title[:index_high_quality].strip()
            # 将斜杠替换为短横线
        title = title.replace('/', '-')
        title = title.replace('、', ' ')
        # 删除文件名中的最后一个空格
        title = title.rstrip(' ')
        # 初始化影片名称
        original_title = ""

        # 找到<title>标签
        title_tag = soup.find('title')
        if title_tag:
            # 提取<title>和</title>标签之间的文本内容作为影片名称
            original_title = title_tag.text.strip()
            # 去掉逗号、空格和单引号
            #original_title = original_title.replace(',', '').replace(' ', '')
            # 去掉 -HighqualityJAV
            original_title = original_title.replace(' – High quality JAV', '')


        # 初始化关键词列表
        keywords = []

        # 在网页全文中搜索包含 "ジャンル： " 的行
        text = soup.get_text()
        lines = text.split('\n')
        for line in lines:
            if "ジャンル：" in line:
                # 找到 "ジャンル： " 所在行
                # 将该行以空格分割为关键词列表，并将 "ジャンル： " 去除
                keywords = line.split("ジャンル： ")[1].split()
                break

        # 初始化演员名
        actor_name = ""

        # 在网页全文中搜索包含 "女優名" 的行
        text = soup.get_text()
        lines = text.split('\n')
        for line in lines:
            if "女優名" in line:
                # 找到包含 "女優名" 的行
                # 将该行的字符串作为演员名
                actor_name = line.split("女優名")[1].strip()
                break

        title = f"{title} {actor_name}"

        print("影片名称: {}, 关键词: {}, 演员名字: {}".format(title, keywords, actor_name))

        # 初始化发行日期
        release_date = ""

        # 在网页全文中搜索包含 "発売日： " 的行
        text = soup.get_text()
        lines = text.split('\n')
        for line in lines:
            if "発売日：" in line:
                # 找到包含 "発売日： " 的行
                # 将该行的字符串作为发行日期
                release_date = line.split("発売日：")[1].strip()
                break
        print("发行日期:", release_date)

        # 初始化影片时间
        duration = ""
        # 在网页全文中搜索包含 "収録時間： " 的行
        text = soup.get_text()
        lines = text.split('\n')
        for line in lines:
            if "収録時間：" in line:
                # 找到包含 "収録時間： " 的行
                # 将该行的字符串作为影片时间
                duration = line.split("収録時間：")[1].strip()
                break

        print("时长:", duration)

        # 提取片商信息
        producer = "AVS"
        print("片商:", producer)


        return title, keywords, actor_name, release_date, duration, producer, original_title


def create_nfo_content(title, original_title, actor_name, release_date, duration, producer, keywords):
    # 排除的关键字列表
    excluded_keywords = ["無碼", "獨佔動畫", "企劃物", "1080p", "60fps", "HEYZO"]

    # 生成标签
    genre_tags = "\n".join([
        f"  <genre>{keyword.strip()}</genre>" for keyword in keywords
        if keyword.strip() and
           keyword.strip() not in excluded_keywords and
           not any(char.isdigit() for char in keyword.strip())
    ])

    # 构造 NFO 文件内容
    nfo_content = f"""
<movie>
    <title>{title}</title>
    <originaltitle>{original_title}</originaltitle>
    <actor>
        <name>{actor_name}</name>
        <type>Actor</type>
    </actor>
    <releasedate>{release_date}</releasedate>
    <runtime>{duration}</runtime>
    <studio>{producer}</studio>
    {genre_tags}
</movie>
"""
    return nfo_content



def create_movie_folder(folder_path, movie_name):
    movie_folder_path = os.path.join(folder_path, movie_name)
    if not os.path.exists(movie_folder_path):
        os.makedirs(movie_folder_path)
    return movie_folder_path

def main():
    folder_path = select_folder()  # 选择文件夹
    if folder_path:
        print("选择的文件夹:", folder_path)
        video_files = [f for f in os.listdir(folder_path) if f.endswith((".mp4", ".mkv", ".avi", ".wmv"))]
        if video_files:
            print("找到的视频文件:")
            for file_name in video_files:
                try:
                    number = extract_number(file_name)
                    if number:
                        file_path = 'd:\\tt\\page_source.html'
                        pic_path = 'd:\\tt\\image.jpg'
                        if os.path.exists(file_path):
                            os.remove(file_path)
                        if os.path.exists(pic_path):
                            os.remove(pic_path)
                        url = f"https://javfree.me/?s={number}"
                        browser_path = "C:/Program Files/Waterfox/waterfox.exe"
                        subprocess.run([browser_path, url])
                        print("文件名: {}, 文件番号: {}".format(file_name, number))

                        time.sleep(20)  # 暂停10秒等待页面加载
                        title, keywords, actor, release_date, duration, producer, original_title = extract_info_from_html('d:\\tt\\page_source.html', number)

                        # 从 original_title 中提取番号
                        extracted_number = extract_number(title)

                        # 将提取的番号和文件名中的番号转换为小写
                        extracted_number_lower = extracted_number.lower()
                        number_lower = number.lower()
                        number_lower = number_lower.replace("-", "").replace("_", "").replace(" ", "")
                        extracted_number_lower = extracted_number_lower.replace("-", "").replace("_", "").replace(" ",
                                                                                                                  "")

                        # 检查转换后的番号是否一致
                        if extracted_number_lower != number_lower:
                            print(f"文件 {file_name} 的番号与提取的番号不一致，跳过该文件。")
                            continue

                        # 创建Emby格式的nfo内容
                        nfo_content = create_nfo_content(title, original_title, actor, release_date, duration, producer, keywords)

                        # 创建以影片名命名的文件夹
                        movie_folder_path = create_movie_folder('V:\\wuma\\亚洲\\=m=', title)
                        # 检测是否包含特定的cd标记，并添加到文件名
                        cd_tag = ""
                        for cd in ['cd1', 'cd2', 'cd3', 'cd4']:
                            if cd in file_name.lower():
                                cd_tag = f"-{cd}"
                                break

                        # 更新文件名以包含CD标记
                        title_with_cd = f"{title}{cd_tag}"
                        new_video_name = f"{title_with_cd}{os.path.splitext(file_name)[1]}"

                        original_video_path = os.path.join(folder_path, file_name)
                        new_video_path = os.path.join(movie_folder_path, new_video_name)
                        os.rename(original_video_path, new_video_path)

                        # 移动图片和写入nfo文件时也应使用更新后的影片名
                        new_jpg_name = f"{title_with_cd}-poster.jpg"
                        new_jpg_path = os.path.join(movie_folder_path, new_jpg_name)

                        jpg_file_path = os.path.join('d:\\tt\\', f"image.jpg")
                        if os.path.exists(jpg_file_path):
                            # 打开原始图片
                            original_image = Image.open(jpg_file_path)

                            # 计算裁剪区域，以右上角为基点裁剪
                            width, height = original_image.size
                            new_width = 360
                            new_height = height
                            right = width
                            top = 0
                            left = right - new_width
                            bottom = top + new_height

                            # 裁剪图片
                            cropped_image = original_image.crop((left, top, right, bottom))

                            # 保存裁剪后的图片
                            cropped_image.save(new_jpg_path)

                            # 移动原始 jpg 文件到影片文件夹
                            shutil.move(jpg_file_path, os.path.join(movie_folder_path, f"{title_with_cd}-fanart.jpg"))

                        # 写入信息到.nfo文件
                        nfo_file_path = os.path.join(movie_folder_path, f"{title_with_cd}.nfo")
                        with open(nfo_file_path, 'w', encoding='utf-8') as nfo_file:
                            nfo_file.write(nfo_content)

                    else:
                        print("文件名: {}, 未找到番号".format(file_name))
                except Exception as e:
                    print(f"处理文件 {file_name} 时出现异常: {e}")
                    continue
        else:
            print("在所选文件夹中未找到视频文件。")

if __name__ == "__main__":
    main()
