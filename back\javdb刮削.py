import os
import re
import shutil
import requests
from PIL import Image
from selenium import webdriver
from selenium.webdriver.common.by import By
from bs4 import BeautifulSoup
import tkinter as tk
from tkinter import filedialog, messagebox
import logging
from selenium.webdriver.firefox.service import Service as FirefoxService
from selenium.webdriver.firefox.options import Options as FirefoxOptions
import sys
sys.path.append(r'Z:\work')  # 添加配置文件路径
from config import YOUDAO_APP_KEY, YOUDAO_APP_SECRET  # 导入有道API配置
import uuid
import hashlib
import time
from urllib.parse import quote
import json
from time import sleep
from random import uniform
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, WebDriverException
from webdriver_manager.firefox import GeckoDriverManager


def clean_title(title):
    """
    清理抓取到的标题，替换不必要的字符，提取 `!` 前的文字（若存在 `!`），
    并限制标题长度不超过 42 个字符。
    """
    # 替换不必要的字符
    cleaned_title = (
        title.replace("：", " ")
        .replace(":", " ")
        .replace("\n", " ")
        .replace("\t", " ")
        .replace("、", " ")
        .replace("?", " ")
        .replace("~", " ")
        .strip()
        .replace("  ", " ")
    )

    # 检查是否存在 `!`，如果存在提取 `!` 前的文字
    if "！" in cleaned_title:
        cleaned_title = cleaned_title.split("!")[0].strip()

    # 限制标题长度不超过 42 个字符
    if len(cleaned_title) > 50:
        cleaned_title = cleaned_title[:50].strip()

    return cleaned_title


def extract_cd_info(file_name):
    """
    提取文件名中的多盘 CD 信息，例如 CD1、CD2 或 Disc1、Disc2 等
    """
    pattern = re.compile(r"(CD\d+|Disc\d+)", re.IGNORECASE)
    match = pattern.search(file_name)
    return match.group() if match else None


def extract_number(file_name):
    """
    从文件名中提取番号
    """
    pattern = re.compile(
        r"(\d{6}[-_]\d{3})|"  # 匹配形式为123456-789或123456_789
        # r"([a-zA-Z]{3}-\d{3})|"  # 匹配形式为abc-123，不区分大小写
        # r"([a-zA-Z]{4}-\d{3})|"  # 匹配形式为abcd-123，不区分大小写
        # r"([a-zA-Z]{5}-\d{4})|"  # 匹配形式为abcde-123，不区分大小写
        r"(heyzo[_ -]?\d{4})|"  # 匹配形式为heyzo-0123，不区分大小写
        r"([nN]\d{4})"  # 匹配形式为n0123或N0123
        r"([a-zA-Z]{2}-\d{3})|"  # 匹配形式为ab-123，不区分大小写
        r"([a-zA-Z]{2}-\d{4})|"  # 匹配形式为ab-1234，不区分大小写
        r"([a-zA-Z]{3}-\d{3})|"  # 匹配形式为abc-123，不区分大小写
        r"([a-zA-Z]{4}-\d{4})|"  # 匹配形式为abcd-1234，不区分大小写
        r"([a-zA-Z]{4}-\d{3})|"  # 匹配形式为abcd-123，不区分大小写
        r"([a-zA-Z]{5}-\d{3})|",  # 匹配形式为abcde-123，不区分大小写
        re.IGNORECASE
    )
    match = pattern.search(file_name)
    return match.group() if match else None


def encrypt(signStr):
    """计算有道API签名"""
    hash_algorithm = hashlib.sha256()
    hash_algorithm.update(signStr.encode('utf-8'))
    return hash_algorithm.hexdigest()


def translate_to_chinese(text):
    """使用有道API将文本翻译成中文，保留原文的空格格式"""
    try:
        # 找到最后一个空格的位置
        last_space_index = text.rfind(' ')
        
        if last_space_index != -1:
            # 分割文本为两部分
            part1 = text[:last_space_index]
            part2 = text[last_space_index + 1:]
            
            # 去除多余空格后分别翻译
            part1_translated = translate_text(part1)
            part2_translated = translate_text(part2)
            
            # 组合翻译后的文本
            translated_text = f"{part1_translated} {part2_translated}"
        else:
            # 如果没有空格，直接翻译整个文本
            translated_text = translate_text(text)
        
        return translated_text
    except Exception as e:
        logging.error(f"翻译出错: {str(e)}")
        return text


def translate_text(text):
    """调用有道API翻译文本"""
    try:
        youdao_url = 'https://openapi.youdao.com/api'
        curtime = str(int(time.time()))
        salt = str(uuid.uuid1())
        
        sign_str = YOUDAO_APP_KEY + truncate(text) + salt + curtime + YOUDAO_APP_SECRET
        sign = encrypt(sign_str)
        
        data = {
            'q': text,
            'from': 'ja',
            'to': 'zh-CHS',
            'appKey': YOUDAO_APP_KEY,
            'salt': salt,
            'sign': sign,
            'signType': 'v3',
            'curtime': curtime,
        }
        
        headers = {'Content-Type': 'application/x-www-form-urlencoded'}
        response = requests.post(youdao_url, data=data, headers=headers)
        result = response.json()
        
        if result.get('translation'):
            return result['translation'][0]
        else:
            logging.error(f"翻译失败: {result.get('errorCode')}")
            return text
            
    except Exception as e:
        logging.error(f"翻译出错: {str(e)}")
        return text


def truncate(q):
    """截取字符串"""
    if q is None:
        return None
    size = len(q)
    return q if size <= 20 else q[0:10] + str(size) + q[size - 10:size]


def scrape_javdb(search_url, video_folder, max_retries=3, retry_delay=5):
    """
    从 JavDB 抓取视频信息，失败时会重试
    """
    driver = None
    attempt = 0
    
    while attempt < max_retries:
        try:
            # 配置 Waterfox
            firefox_options = FirefoxOptions()
            firefox_options.binary_location = r"C:\Program Files\Waterfox\waterfox.exe"
            firefox_options.add_argument('--disable-gpu')
            firefox_options.add_argument('--no-sandbox')
            firefox_options.set_preference("dom.webdriver.enabled", False)
            firefox_options.set_preference('useAutomationExtension', False)
            
            # 使用 webdriver_manager 自动管理 GeckoDriver
            driver = webdriver.Firefox(
                service=FirefoxService(GeckoDriverManager().install()),
                options=firefox_options
            )
            
            # 不再需要执行 CDP 命令，因为 Firefox 使用不同的方式
            driver.set_page_load_timeout(30)
            
            # 初始化结果字典
            result = {
                "title": None,
                "original_title": None,
                "number": None,
                "img_url": None,
                "keywords": [],
                "actor_name": None,
                "release_date": None,
                "duration": None,
                "producer": None,
                "success": False,
                "error": None,
            }

            # 修改搜索URL格式
            base_url = "https://javdb.com"  # 使用最新的域名
            # 从 search_url 中提取番号
            number_match = re.search(r'q=([^&]+)', search_url)
            if number_match:
                search_number = number_match.group(1)
                search_path = f"/search?q={search_number}&f=all"
                full_url = base_url + search_path
            else:
                full_url = search_url

            try:
                driver.get(full_url)
                # 等待页面加载完成
                WebDriverWait(driver, 10).until(
                    lambda driver: driver.execute_script('return document.readyState') == 'complete'
                )
                # 添加随机延迟，模拟人类行为
                sleep(uniform(1, 3))
            except TimeoutException:
                raise Exception("页面加载超时")

            # 查找第一个匹配的链接并跳转
            if "search?q=" in driver.current_url:
                try:
                    # 等待链接元素出现
                    link = WebDriverWait(driver, 10).until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, 'a[href^="/v/"]'))
                    )
                    href = link.get_attribute('href')
                    
                    # 添加随机延迟，模拟人类行为
                    sleep(uniform(0.5, 1.5))
                    
                    # 使用JavaScript点击链接而不是直接get
                    driver.execute_script("arguments[0].click();", link)
                    
                    # 等待新页面加载完成
                    WebDriverWait(driver, 10).until(
                        lambda driver: driver.execute_script('return document.readyState') == 'complete'
                    )
                    # 添加随机延迟
                    sleep(uniform(1, 2))
                except TimeoutException:
                    raise Exception("详情页加载超时")
                except Exception as e:
                    raise Exception(f"未找到匹配内容: {str(e)}")

            # 使用 BeautifulSoup 解析详情页
            soup = BeautifulSoup(driver.page_source, "html.parser")

            # 提取番号和影片标题
            h2_tag = soup.find('h2', class_='title is-4')
            if h2_tag:
                strong_tags = h2_tag.find_all('strong')
                if len(strong_tags) >= 1:
                    # 提取番号
                    result['number'] = strong_tags[0].text.strip().split()[0]

                    # 提取影片的原始标题
                    if len(strong_tags) >= 2:
                        original_title = strong_tags[1].text.strip()
                        original_title = re.sub(r'\s+', ' ', clean_title(original_title))
                        result['original_title'] = f"{result['number']} {original_title}"

            # 提取封面图片 URL
            img_tag = soup.find('img', class_='video-cover')
            if img_tag:
                result['img_url'] = img_tag['src']

            # 提取关键词
            print("\n开始提取关键词...")
            result['keywords'] = []
            
            # 打印完整页面源码以进行调试
            print("\n页面完整源码:")
            print(driver.page_source)
            
            # 查找包含"類別:"的 panel-block
            category_block = soup.find('strong', string=lambda x: x and '類別:' in x)
            if category_block:
                print("找到類別区块")
                # 找到父级的 panel-block
                panel_block = category_block.find_parent('div', class_='panel-block')
                if panel_block:
                    # 找到 value span 并提取其中的所有链接
                    value_span = panel_block.find('span', class_='value')
                    if value_span:
                        print("找到 value span，内容：", value_span.text.strip())
                        category_links = value_span.find_all('a')
                        print(f"找到 {len(category_links)} 个类别链接")
                        for link in category_links:
                            keyword = link.text.strip()
                            result['keywords'].append(keyword)
                            print(f"找到关键词: {keyword}")
                    else:
                        print("未找到 value span")
                        print("panel-block的内容:", panel_block.prettify())
                else:
                    print("未找到 panel-block")
            else:
                print("未找到類別标签")
                # 尝试查找所有strong标签
                print("\n所有strong标签:")
                for strong in soup.find_all('strong'):
                    print(f"- {strong.text.strip()}")
            
            print(f"提取到的所有关键词: {result['keywords']}\n")

            # 提取演员名字
            strong_tags = soup.find_all('strong')
            for tag in strong_tags:
                if tag.text.strip() == '演員:':
                    next_a_tag = tag.find_next('a', href=True)
                    if next_a_tag:
                        result['actor_name'] = next_a_tag.text.strip()
                        break

            # 提取发行日期
            for tag in strong_tags:
                if tag.text.strip() == '日期:':
                    next_span_tag = tag.find_next('span', class_='value')
                    if next_span_tag:
                        result['release_date'] = next_span_tag.text.strip()
                        break

            # 提取时长
            for tag in strong_tags:
                if tag.text.strip() == '時長:':
                    next_span_tag = tag.find_next('span', class_='value')
                    if next_span_tag:
                        result['duration'] = next_span_tag.text.strip()
                        break

            # 提取片商信息
            for tag in strong_tags:
                if tag.text.strip() == '片商:':
                    next_span_tag = tag.find_next('span', class_='value')
                    if next_span_tag and next_span_tag.a:
                        result['producer'] = next_span_tag.a.text.strip()
                        break

            # 在设置标题之前添加翻译步骤
            if result['original_title']:
                # 提取日文标题部分（去掉番号）
                number_part = result['number']
                title_part = result['original_title'].replace(number_part, '').strip()
                
                # 翻译标题
                translated_title = translate_to_chinese(title_part)
                
                # 重新组合标题（番号 + 翻译后的标题）
                if re.match(r"(heyzo|kb\d{4})", result['original_title'].lower()):
                    result['title'] = f"{number_part} {translated_title}"
                else:
                    # 如果初始文件夹路径包含youma（不区分大小写），则不加演员名字
                    if 'youma' in video_folder.lower():
                        result['title'] = f"{number_part} {translated_title}"
                    else:
                        result['title'] = f"{number_part} {translated_title} {result['actor_name'] or ''}".strip()
            
            result['success'] = True
            return result

        except Exception as e:
            attempt += 1
            if driver:
                try:
                    driver.quit()
                except WebDriverException:
                    pass  # 忽略关闭浏览器时的错误
                driver = None
            
            if attempt < max_retries:
                # 将重试等待时间限制在10秒内
                wait_time = min(retry_delay * (attempt + 1), 10) + uniform(0, 2)
                logging.warning(f"第{attempt}次抓取失败: {str(e)}，{wait_time:.1f}秒后重试...")
                sleep(wait_time)
            else:
                logging.error(f"抓取失败，已重试{max_retries}次: {str(e)}")
                return {
                    "success": False,
                    "error": f"抓取失败，已重试{max_retries}次: {str(e)}"
                }

        finally:
            if driver:
                try:
                    driver.quit()
                except WebDriverException:
                    pass  # 忽略关闭浏览器时的错误


def download_image(img_url, save_path):
    """
    下载图片
    """
    try:
        response = requests.get(img_url, stream=True)
        response.raise_for_status()
        with open(save_path, "wb") as file:
            for chunk in response.iter_content(chunk_size=8192):
                file.write(chunk)
        #print(f"图片已下载: {save_path}")
        return save_path
    except Exception as e:
        logging.error(f"图片下载失败: {e}")
        return None


def crop_image_to_poster(fanart_path, poster_path):
    """
    从 fanart 裁剪出 poster 图片（右上角竖版部分）
    """
    try:
        with Image.open(fanart_path) as img:
            width, height = img.size
            new_width = 360  # 竖版宽度
            left = width - new_width
            top = 0
            right = width
            bottom = height

            # 裁剪
            cropped_image = img.crop((left, top, right, bottom))
            cropped_image.save(poster_path)
            print(f"Poster 图片已保存: {poster_path}")
    except Exception as e:
        logging.error(f"裁剪 poster 图片失败: {e}")


def create_nfo_content(title, actor_name, release_date, duration, producer, keywords):
    """
    根据提供的影片信息生成 NFO 文件内容，符合您提供的格式要求。

    :param title: 影片标题
    :param actor_name: 演员名字
    :param release_date: 发行日期
    :param duration: 时长
    :param producer: 片商
    :param keywords: 关键词列表
    :return: 生成的 NFO 内容字符串
    """
    # 排除的关键字列表
    excluded_keywords = ["無碼", "獨佔動畫", "企劃物", "1080p", "60fps", "HEYZO"]
    
    print("\n处理 NFO 关键词...")
    print(f"原始关键词列表: {keywords}")

    # 生成 <genre> 标签
    genre_tags = []
    for keyword in keywords:
        keyword = keyword.strip()
        if (keyword and 
            keyword not in excluded_keywords and 
            not any(char.isdigit() for char in keyword) and 
            "HEYZO" not in keyword):
            genre_tags.append(f"    <genre>{keyword}</genre>")

    genre_tags_str = "\n".join(genre_tags)

    # 构造 NFO 文件内容
    nfo_content = f"""
<movie>
    <title>{title}</title>
    <originaltitle>{title}</originaltitle>
    <actor>
        <name>{actor_name}</name>
        <type>Actor</type>
    </actor>
    <releasedate>{release_date}</releasedate>
    <runtime>{duration}</runtime>
    <studio>{producer}</studio>
{genre_tags_str}
</movie>
"""
    return nfo_content.strip()


def select_folder(prompt):
    """
    弹出对话框选择文件夹
    """
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口
    folder = filedialog.askdirectory(title=prompt)
    if not folder:
        messagebox.showerror("错误", f"您未选择{prompt}，程序即将退出。")
        raise SystemExit
    return folder


def main():
    # 使用对话框选择视频文件夹和目标文件夹
    video_folder = select_folder("请选择存放视频文件的文件夹")
    target_folder = select_folder("请选择目标文件夹")

    # 遍历视频文件
    for file_name in os.listdir(video_folder):
        try:
            # 从文件名中提取番号
            original_number = extract_number(file_name)
            print(file_name, f"提取到的番号：", original_number)
            if not original_number:
                print(f"文件 {file_name} 未发现番号，跳过")
                continue

            # 从网页抓取番号
            search_url = f"https://javdb.com/search?q={original_number}&f=all"
            scrape_result = scrape_javdb(search_url, video_folder)

            if not scrape_result["success"]:
                logging.error(f"抓取失败：跳过文件 {file_name}")
                continue

            # 从抓取结果中提取番号
            scraped_number = scrape_result["number"]
            if not scraped_number:
                logging.error(f"抓取结果中未找到番号：跳过文件 {file_name}")
                continue

            # 对比番号
            if original_number.lower() != scraped_number.lower():
                logging.warning(
                    f"番号不匹配！文件名提取：{original_number}，网页抓取：{scraped_number}，跳过处理 {file_name}")
                continue

            print(f"番号匹配成功：{original_number}")

            # 如果番号匹配，继续处理（文件移动、生成 NFO 等）
            scraped_title = scrape_result["title"]
            print(f"提取的标题：", scraped_title)
            #scraped_number = original_number

            # 检查是否为多 CD 文件
            cd_info = extract_cd_info(file_name)

            # 构造文件夹路径（忽略多盘信息）
            movie_folder_path = os.path.join(target_folder, scraped_title)
            os.makedirs(movie_folder_path, exist_ok=True)

            # 移动视频文件（文件名包含番号和标题信息，并保留多盘信息）
            new_file_name = f"{scraped_title}"
            if cd_info:
                new_file_name = f"{new_file_name} - {cd_info}"
            new_file_name += os.path.splitext(file_name)[-1]
            video_target_path = os.path.join(movie_folder_path, new_file_name)
            shutil.move(os.path.join(video_folder, file_name), video_target_path)
            print(f"视频文件已移动: {video_target_path}")


            # 下载封面图片并生成 poster
            fanart_name = f"{scraped_title}"
            if cd_info:
                fanart_name += f" - {cd_info}"
            fanart_path = os.path.join(movie_folder_path, f"{fanart_name}-fanart.jpg")

            if scrape_result["img_url"]:
                downloaded_fanart_path = download_image(scrape_result["img_url"], fanart_path)
                poster_name = f"{scraped_title}"
                if cd_info:
                    poster_name += f" - {cd_info}"
                poster_path = os.path.join(movie_folder_path, f"{poster_name}-poster.jpg")
                crop_image_to_poster(downloaded_fanart_path, poster_path)

            # 写入信息到 .nfo 文件
            try:
                if cd_info:
                    nfo_file_path = os.path.join(movie_folder_path,
                                                 f"{scraped_title} - {cd_info}.nfo")
                else:
                    nfo_file_path = os.path.join(movie_folder_path, f"{scraped_title}.nfo")

                nfo_content = create_nfo_content(
                    title=scrape_result["title"],
                    actor_name=scrape_result["actor_name"],
                    release_date=scrape_result["release_date"],
                    duration=scrape_result["duration"],
                    producer=scrape_result["producer"],
                    keywords=scrape_result["keywords"],
                )

                with open(nfo_file_path, "w", encoding="utf-8") as file:
                    file.write(nfo_content)
                    print(f"NFO 文件已成功写入：{nfo_file_path}")

            except Exception as e:
                logging.error(f"写入 NFO 文件失败：{e}")


        except Exception as e:
            logging.error(f"处理文件 {file_name} 时发生错误: {e}")


if __name__ == "__main__":
    main()
