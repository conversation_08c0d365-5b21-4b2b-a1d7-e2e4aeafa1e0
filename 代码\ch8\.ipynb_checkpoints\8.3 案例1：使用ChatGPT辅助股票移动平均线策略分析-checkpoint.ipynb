{"cells": [{"cell_type": "markdown", "id": "c9b66cbd", "metadata": {}, "source": ["### 计算移动平均线"]}, {"cell_type": "code", "execution_count": 1, "id": "e6dcba30", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Date</th>\n", "      <th>Close</th>\n", "      <th>Volume</th>\n", "      <th>Open</th>\n", "      <th>High</th>\n", "      <th>Low</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2023-06-23</td>\n", "      <td>186.68</td>\n", "      <td>53117000</td>\n", "      <td>$185.55</td>\n", "      <td>$187.56</td>\n", "      <td>$185.01</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2023-06-22</td>\n", "      <td>187.00</td>\n", "      <td>51245330</td>\n", "      <td>$183.74</td>\n", "      <td>$187.045</td>\n", "      <td>$183.67</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2023-06-21</td>\n", "      <td>183.96</td>\n", "      <td>49515700</td>\n", "      <td>$184.90</td>\n", "      <td>$185.41</td>\n", "      <td>$182.5901</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2023-06-20</td>\n", "      <td>185.01</td>\n", "      <td>49799090</td>\n", "      <td>$184.41</td>\n", "      <td>$186.10</td>\n", "      <td>$184.41</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2023-06-16</td>\n", "      <td>184.92</td>\n", "      <td>101256200</td>\n", "      <td>$186.73</td>\n", "      <td>$186.99</td>\n", "      <td>$184.27</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>2023-06-15</td>\n", "      <td>186.01</td>\n", "      <td>65433170</td>\n", "      <td>$183.96</td>\n", "      <td>$186.52</td>\n", "      <td>$183.78</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>2023-06-14</td>\n", "      <td>183.95</td>\n", "      <td>57462880</td>\n", "      <td>$183.37</td>\n", "      <td>$184.39</td>\n", "      <td>$182.02</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>2023-06-13</td>\n", "      <td>183.31</td>\n", "      <td>54929130</td>\n", "      <td>$182.80</td>\n", "      <td>$184.15</td>\n", "      <td>$182.44</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>2023-06-12</td>\n", "      <td>183.79</td>\n", "      <td>54755000</td>\n", "      <td>$181.27</td>\n", "      <td>$183.89</td>\n", "      <td>$180.97</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>2023-06-09</td>\n", "      <td>180.96</td>\n", "      <td>48899970</td>\n", "      <td>$181.50</td>\n", "      <td>$182.23</td>\n", "      <td>$180.63</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>2023-06-08</td>\n", "      <td>180.57</td>\n", "      <td>50214880</td>\n", "      <td>$177.895</td>\n", "      <td>$180.84</td>\n", "      <td>$177.46</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>2023-06-07</td>\n", "      <td>177.82</td>\n", "      <td>61944620</td>\n", "      <td>$178.44</td>\n", "      <td>$181.21</td>\n", "      <td>$177.32</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>2023-06-06</td>\n", "      <td>179.21</td>\n", "      <td>64848370</td>\n", "      <td>$179.965</td>\n", "      <td>$180.12</td>\n", "      <td>$177.43</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>2023-06-05</td>\n", "      <td>179.58</td>\n", "      <td>121946500</td>\n", "      <td>$182.63</td>\n", "      <td>$184.951</td>\n", "      <td>$178.035</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>2023-06-02</td>\n", "      <td>180.95</td>\n", "      <td>61996910</td>\n", "      <td>$181.03</td>\n", "      <td>$181.78</td>\n", "      <td>$179.26</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>2023-06-01</td>\n", "      <td>180.09</td>\n", "      <td>68901810</td>\n", "      <td>$177.70</td>\n", "      <td>$180.12</td>\n", "      <td>$176.9306</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>2023-05-31</td>\n", "      <td>177.25</td>\n", "      <td>99625290</td>\n", "      <td>$177.325</td>\n", "      <td>$179.35</td>\n", "      <td>$176.76</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>2023-05-30</td>\n", "      <td>177.30</td>\n", "      <td>55964400</td>\n", "      <td>$176.96</td>\n", "      <td>$178.99</td>\n", "      <td>$176.57</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>2023-05-26</td>\n", "      <td>175.43</td>\n", "      <td>54834980</td>\n", "      <td>$173.32</td>\n", "      <td>$175.77</td>\n", "      <td>$173.11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>2023-05-25</td>\n", "      <td>172.99</td>\n", "      <td>56058260</td>\n", "      <td>$172.41</td>\n", "      <td>$173.895</td>\n", "      <td>$171.69</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         Date   Close     Volume      Open      High        Low\n", "0  2023-06-23  186.68   53117000   $185.55   $187.56    $185.01\n", "1  2023-06-22  187.00   51245330   $183.74  $187.045    $183.67\n", "2  2023-06-21  183.96   49515700   $184.90   $185.41  $182.5901\n", "3  2023-06-20  185.01   49799090   $184.41   $186.10    $184.41\n", "4  2023-06-16  184.92  101256200   $186.73   $186.99    $184.27\n", "5  2023-06-15  186.01   65433170   $183.96   $186.52    $183.78\n", "6  2023-06-14  183.95   57462880   $183.37   $184.39    $182.02\n", "7  2023-06-13  183.31   54929130   $182.80   $184.15    $182.44\n", "8  2023-06-12  183.79   54755000   $181.27   $183.89    $180.97\n", "9  2023-06-09  180.96   48899970   $181.50   $182.23    $180.63\n", "10 2023-06-08  180.57   50214880  $177.895   $180.84    $177.46\n", "11 2023-06-07  177.82   61944620   $178.44   $181.21    $177.32\n", "12 2023-06-06  179.21   64848370  $179.965   $180.12    $177.43\n", "13 2023-06-05  179.58  121946500   $182.63  $184.951   $178.035\n", "14 2023-06-02  180.95   61996910   $181.03   $181.78    $179.26\n", "15 2023-06-01  180.09   68901810   $177.70   $180.12  $176.9306\n", "16 2023-05-31  177.25   99625290  $177.325   $179.35    $176.76\n", "17 2023-05-30  177.30   55964400   $176.96   $178.99    $176.57\n", "18 2023-05-26  175.43   54834980   $173.32   $175.77    $173.11\n", "19 2023-05-25  172.99   56058260   $172.41  $173.895    $171.69"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "import matplotlib.pyplot as plt\n", "\n", "plt.rcParams['font.family'] = ['SimHei']\n", "plt.rcParams['axes.unicode_minus'] = False\n", "\n", "# 从文件中读取数据，并进行数据类型转换\n", "df = pd.read_csv('data/AAPL.csv', parse_dates=['Date'])\n", "df['Close'] = df['Close'].str.replace('$', '').astype(float)\n", "df"]}, {"cell_type": "markdown", "id": "4d5340e2", "metadata": {}, "source": ["### 计算5日移动平均线"]}, {"cell_type": "code", "execution_count": 2, "id": "8bd8fa1c", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Date</th>\n", "      <th>Close</th>\n", "      <th>Volume</th>\n", "      <th>Open</th>\n", "      <th>High</th>\n", "      <th>Low</th>\n", "      <th>MA5</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2023-06-23</td>\n", "      <td>186.68</td>\n", "      <td>53117000</td>\n", "      <td>$185.55</td>\n", "      <td>$187.56</td>\n", "      <td>$185.01</td>\n", "      <td>186.6800</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2023-06-22</td>\n", "      <td>187.00</td>\n", "      <td>51245330</td>\n", "      <td>$183.74</td>\n", "      <td>$187.045</td>\n", "      <td>$183.67</td>\n", "      <td>186.8400</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2023-06-21</td>\n", "      <td>183.96</td>\n", "      <td>49515700</td>\n", "      <td>$184.90</td>\n", "      <td>$185.41</td>\n", "      <td>$182.5901</td>\n", "      <td>185.8800</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2023-06-20</td>\n", "      <td>185.01</td>\n", "      <td>49799090</td>\n", "      <td>$184.41</td>\n", "      <td>$186.10</td>\n", "      <td>$184.41</td>\n", "      <td>185.6625</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2023-06-16</td>\n", "      <td>184.92</td>\n", "      <td>101256200</td>\n", "      <td>$186.73</td>\n", "      <td>$186.99</td>\n", "      <td>$184.27</td>\n", "      <td>185.5140</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>2023-06-15</td>\n", "      <td>186.01</td>\n", "      <td>65433170</td>\n", "      <td>$183.96</td>\n", "      <td>$186.52</td>\n", "      <td>$183.78</td>\n", "      <td>185.3800</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>2023-06-14</td>\n", "      <td>183.95</td>\n", "      <td>57462880</td>\n", "      <td>$183.37</td>\n", "      <td>$184.39</td>\n", "      <td>$182.02</td>\n", "      <td>184.7700</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>2023-06-13</td>\n", "      <td>183.31</td>\n", "      <td>54929130</td>\n", "      <td>$182.80</td>\n", "      <td>$184.15</td>\n", "      <td>$182.44</td>\n", "      <td>184.6400</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>2023-06-12</td>\n", "      <td>183.79</td>\n", "      <td>54755000</td>\n", "      <td>$181.27</td>\n", "      <td>$183.89</td>\n", "      <td>$180.97</td>\n", "      <td>184.3960</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>2023-06-09</td>\n", "      <td>180.96</td>\n", "      <td>48899970</td>\n", "      <td>$181.50</td>\n", "      <td>$182.23</td>\n", "      <td>$180.63</td>\n", "      <td>183.6040</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>2023-06-08</td>\n", "      <td>180.57</td>\n", "      <td>50214880</td>\n", "      <td>$177.895</td>\n", "      <td>$180.84</td>\n", "      <td>$177.46</td>\n", "      <td>182.5160</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>2023-06-07</td>\n", "      <td>177.82</td>\n", "      <td>61944620</td>\n", "      <td>$178.44</td>\n", "      <td>$181.21</td>\n", "      <td>$177.32</td>\n", "      <td>181.2900</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>2023-06-06</td>\n", "      <td>179.21</td>\n", "      <td>64848370</td>\n", "      <td>$179.965</td>\n", "      <td>$180.12</td>\n", "      <td>$177.43</td>\n", "      <td>180.4700</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>2023-06-05</td>\n", "      <td>179.58</td>\n", "      <td>121946500</td>\n", "      <td>$182.63</td>\n", "      <td>$184.951</td>\n", "      <td>$178.035</td>\n", "      <td>179.6280</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>2023-06-02</td>\n", "      <td>180.95</td>\n", "      <td>61996910</td>\n", "      <td>$181.03</td>\n", "      <td>$181.78</td>\n", "      <td>$179.26</td>\n", "      <td>179.6260</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>2023-06-01</td>\n", "      <td>180.09</td>\n", "      <td>68901810</td>\n", "      <td>$177.70</td>\n", "      <td>$180.12</td>\n", "      <td>$176.9306</td>\n", "      <td>179.5300</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>2023-05-31</td>\n", "      <td>177.25</td>\n", "      <td>99625290</td>\n", "      <td>$177.325</td>\n", "      <td>$179.35</td>\n", "      <td>$176.76</td>\n", "      <td>179.4160</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>2023-05-30</td>\n", "      <td>177.30</td>\n", "      <td>55964400</td>\n", "      <td>$176.96</td>\n", "      <td>$178.99</td>\n", "      <td>$176.57</td>\n", "      <td>179.0340</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>2023-05-26</td>\n", "      <td>175.43</td>\n", "      <td>54834980</td>\n", "      <td>$173.32</td>\n", "      <td>$175.77</td>\n", "      <td>$173.11</td>\n", "      <td>178.2040</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>2023-05-25</td>\n", "      <td>172.99</td>\n", "      <td>56058260</td>\n", "      <td>$172.41</td>\n", "      <td>$173.895</td>\n", "      <td>$171.69</td>\n", "      <td>176.6120</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         Date   Close     Volume      Open      High        Low       MA5\n", "0  2023-06-23  186.68   53117000   $185.55   $187.56    $185.01  186.6800\n", "1  2023-06-22  187.00   51245330   $183.74  $187.045    $183.67  186.8400\n", "2  2023-06-21  183.96   49515700   $184.90   $185.41  $182.5901  185.8800\n", "3  2023-06-20  185.01   49799090   $184.41   $186.10    $184.41  185.6625\n", "4  2023-06-16  184.92  101256200   $186.73   $186.99    $184.27  185.5140\n", "5  2023-06-15  186.01   65433170   $183.96   $186.52    $183.78  185.3800\n", "6  2023-06-14  183.95   57462880   $183.37   $184.39    $182.02  184.7700\n", "7  2023-06-13  183.31   54929130   $182.80   $184.15    $182.44  184.6400\n", "8  2023-06-12  183.79   54755000   $181.27   $183.89    $180.97  184.3960\n", "9  2023-06-09  180.96   48899970   $181.50   $182.23    $180.63  183.6040\n", "10 2023-06-08  180.57   50214880  $177.895   $180.84    $177.46  182.5160\n", "11 2023-06-07  177.82   61944620   $178.44   $181.21    $177.32  181.2900\n", "12 2023-06-06  179.21   64848370  $179.965   $180.12    $177.43  180.4700\n", "13 2023-06-05  179.58  121946500   $182.63  $184.951   $178.035  179.6280\n", "14 2023-06-02  180.95   61996910   $181.03   $181.78    $179.26  179.6260\n", "15 2023-06-01  180.09   68901810   $177.70   $180.12  $176.9306  179.5300\n", "16 2023-05-31  177.25   99625290  $177.325   $179.35    $176.76  179.4160\n", "17 2023-05-30  177.30   55964400   $176.96   $178.99    $176.57  179.0340\n", "18 2023-05-26  175.43   54834980   $173.32   $175.77    $173.11  178.2040\n", "19 2023-05-25  172.99   56058260   $172.41  $173.895    $171.69  176.6120"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["# 计算移动平均线\n", "window = 5  # 移动平均线窗口大小\n", "df['MA5'] = df['Close'].rolling(window,min_periods=1).mean()\n", "\n", "df"]}, {"cell_type": "markdown", "id": "cfcda983", "metadata": {}, "source": ["### 计算5日与20日移动平均线"]}, {"cell_type": "code", "execution_count": 3, "id": "03700c6e", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Date</th>\n", "      <th>Close</th>\n", "      <th>Volume</th>\n", "      <th>Open</th>\n", "      <th>High</th>\n", "      <th>Low</th>\n", "      <th>MA5</th>\n", "      <th>MA20</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2023-06-23</td>\n", "      <td>186.68</td>\n", "      <td>53117000</td>\n", "      <td>$185.55</td>\n", "      <td>$187.56</td>\n", "      <td>$185.01</td>\n", "      <td>186.6800</td>\n", "      <td>186.680000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2023-06-22</td>\n", "      <td>187.00</td>\n", "      <td>51245330</td>\n", "      <td>$183.74</td>\n", "      <td>$187.045</td>\n", "      <td>$183.67</td>\n", "      <td>186.8400</td>\n", "      <td>186.840000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2023-06-21</td>\n", "      <td>183.96</td>\n", "      <td>49515700</td>\n", "      <td>$184.90</td>\n", "      <td>$185.41</td>\n", "      <td>$182.5901</td>\n", "      <td>185.8800</td>\n", "      <td>185.880000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2023-06-20</td>\n", "      <td>185.01</td>\n", "      <td>49799090</td>\n", "      <td>$184.41</td>\n", "      <td>$186.10</td>\n", "      <td>$184.41</td>\n", "      <td>185.6625</td>\n", "      <td>185.662500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2023-06-16</td>\n", "      <td>184.92</td>\n", "      <td>101256200</td>\n", "      <td>$186.73</td>\n", "      <td>$186.99</td>\n", "      <td>$184.27</td>\n", "      <td>185.5140</td>\n", "      <td>185.514000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>2023-06-15</td>\n", "      <td>186.01</td>\n", "      <td>65433170</td>\n", "      <td>$183.96</td>\n", "      <td>$186.52</td>\n", "      <td>$183.78</td>\n", "      <td>185.3800</td>\n", "      <td>185.596667</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>2023-06-14</td>\n", "      <td>183.95</td>\n", "      <td>57462880</td>\n", "      <td>$183.37</td>\n", "      <td>$184.39</td>\n", "      <td>$182.02</td>\n", "      <td>184.7700</td>\n", "      <td>185.361429</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>2023-06-13</td>\n", "      <td>183.31</td>\n", "      <td>54929130</td>\n", "      <td>$182.80</td>\n", "      <td>$184.15</td>\n", "      <td>$182.44</td>\n", "      <td>184.6400</td>\n", "      <td>185.105000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>2023-06-12</td>\n", "      <td>183.79</td>\n", "      <td>54755000</td>\n", "      <td>$181.27</td>\n", "      <td>$183.89</td>\n", "      <td>$180.97</td>\n", "      <td>184.3960</td>\n", "      <td>184.958889</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>2023-06-09</td>\n", "      <td>180.96</td>\n", "      <td>48899970</td>\n", "      <td>$181.50</td>\n", "      <td>$182.23</td>\n", "      <td>$180.63</td>\n", "      <td>183.6040</td>\n", "      <td>184.559000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>2023-06-08</td>\n", "      <td>180.57</td>\n", "      <td>50214880</td>\n", "      <td>$177.895</td>\n", "      <td>$180.84</td>\n", "      <td>$177.46</td>\n", "      <td>182.5160</td>\n", "      <td>184.196364</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>2023-06-07</td>\n", "      <td>177.82</td>\n", "      <td>61944620</td>\n", "      <td>$178.44</td>\n", "      <td>$181.21</td>\n", "      <td>$177.32</td>\n", "      <td>181.2900</td>\n", "      <td>183.665000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>2023-06-06</td>\n", "      <td>179.21</td>\n", "      <td>64848370</td>\n", "      <td>$179.965</td>\n", "      <td>$180.12</td>\n", "      <td>$177.43</td>\n", "      <td>180.4700</td>\n", "      <td>183.322308</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>2023-06-05</td>\n", "      <td>179.58</td>\n", "      <td>121946500</td>\n", "      <td>$182.63</td>\n", "      <td>$184.951</td>\n", "      <td>$178.035</td>\n", "      <td>179.6280</td>\n", "      <td>183.055000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>2023-06-02</td>\n", "      <td>180.95</td>\n", "      <td>61996910</td>\n", "      <td>$181.03</td>\n", "      <td>$181.78</td>\n", "      <td>$179.26</td>\n", "      <td>179.6260</td>\n", "      <td>182.914667</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>2023-06-01</td>\n", "      <td>180.09</td>\n", "      <td>68901810</td>\n", "      <td>$177.70</td>\n", "      <td>$180.12</td>\n", "      <td>$176.9306</td>\n", "      <td>179.5300</td>\n", "      <td>182.738125</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>2023-05-31</td>\n", "      <td>177.25</td>\n", "      <td>99625290</td>\n", "      <td>$177.325</td>\n", "      <td>$179.35</td>\n", "      <td>$176.76</td>\n", "      <td>179.4160</td>\n", "      <td>182.415294</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>2023-05-30</td>\n", "      <td>177.30</td>\n", "      <td>55964400</td>\n", "      <td>$176.96</td>\n", "      <td>$178.99</td>\n", "      <td>$176.57</td>\n", "      <td>179.0340</td>\n", "      <td>182.131111</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>2023-05-26</td>\n", "      <td>175.43</td>\n", "      <td>54834980</td>\n", "      <td>$173.32</td>\n", "      <td>$175.77</td>\n", "      <td>$173.11</td>\n", "      <td>178.2040</td>\n", "      <td>181.778421</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>2023-05-25</td>\n", "      <td>172.99</td>\n", "      <td>56058260</td>\n", "      <td>$172.41</td>\n", "      <td>$173.895</td>\n", "      <td>$171.69</td>\n", "      <td>176.6120</td>\n", "      <td>181.339000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         Date   Close     Volume      Open      High        Low       MA5  \\\n", "0  2023-06-23  186.68   53117000   $185.55   $187.56    $185.01  186.6800   \n", "1  2023-06-22  187.00   51245330   $183.74  $187.045    $183.67  186.8400   \n", "2  2023-06-21  183.96   49515700   $184.90   $185.41  $182.5901  185.8800   \n", "3  2023-06-20  185.01   49799090   $184.41   $186.10    $184.41  185.6625   \n", "4  2023-06-16  184.92  101256200   $186.73   $186.99    $184.27  185.5140   \n", "5  2023-06-15  186.01   65433170   $183.96   $186.52    $183.78  185.3800   \n", "6  2023-06-14  183.95   57462880   $183.37   $184.39    $182.02  184.7700   \n", "7  2023-06-13  183.31   54929130   $182.80   $184.15    $182.44  184.6400   \n", "8  2023-06-12  183.79   54755000   $181.27   $183.89    $180.97  184.3960   \n", "9  2023-06-09  180.96   48899970   $181.50   $182.23    $180.63  183.6040   \n", "10 2023-06-08  180.57   50214880  $177.895   $180.84    $177.46  182.5160   \n", "11 2023-06-07  177.82   61944620   $178.44   $181.21    $177.32  181.2900   \n", "12 2023-06-06  179.21   64848370  $179.965   $180.12    $177.43  180.4700   \n", "13 2023-06-05  179.58  121946500   $182.63  $184.951   $178.035  179.6280   \n", "14 2023-06-02  180.95   61996910   $181.03   $181.78    $179.26  179.6260   \n", "15 2023-06-01  180.09   68901810   $177.70   $180.12  $176.9306  179.5300   \n", "16 2023-05-31  177.25   99625290  $177.325   $179.35    $176.76  179.4160   \n", "17 2023-05-30  177.30   55964400   $176.96   $178.99    $176.57  179.0340   \n", "18 2023-05-26  175.43   54834980   $173.32   $175.77    $173.11  178.2040   \n", "19 2023-05-25  172.99   56058260   $172.41  $173.895    $171.69  176.6120   \n", "\n", "          MA20  \n", "0   186.680000  \n", "1   186.840000  \n", "2   185.880000  \n", "3   185.662500  \n", "4   185.514000  \n", "5   185.596667  \n", "6   185.361429  \n", "7   185.105000  \n", "8   184.958889  \n", "9   184.559000  \n", "10  184.196364  \n", "11  183.665000  \n", "12  183.322308  \n", "13  183.055000  \n", "14  182.914667  \n", "15  182.738125  \n", "16  182.415294  \n", "17  182.131111  \n", "18  181.778421  \n", "19  181.339000  "]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["# 计算20移动平均线\n", "window = 20  # 移动平均线窗口大小\n", "df['MA20'] = df['Close'].rolling(window,min_periods=1).mean()\n", "df"]}, {"cell_type": "markdown", "id": "0f04a477", "metadata": {}, "source": ["### 绘制收盘价和移动平均线走势图"]}, {"cell_type": "code", "execution_count": 4, "id": "46339aef", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 绘制收盘价和移动平均线走势图\n", "plt.figure(figsize=(10, 6))\n", "plt.plot(df['Date'], df['Close'], label='Close')\n", "plt.plot(df['Date'], df['MA5'], label='5日移动平均线')\n", "plt.plot(df['Date'], df['MA20'], label='20日移动平均线')\n", "\n", "plt.xlabel('日期')\n", "plt.ylabel('收盘价')\n", "plt.title('APPL收盘价和移动平均线走势图')\n", "\n", "plt.xticks(rotation=45)\n", "plt.legend()\n", "plt.grid(True)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "e703213d", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}