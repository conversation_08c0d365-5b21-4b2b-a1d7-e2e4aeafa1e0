{"cells": [{"cell_type": "code", "execution_count": 2, "id": "655d505e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[[1 2 3]\n", " [4 5 6]\n", " [7 8 9]]\n", "int32\n"]}], "source": ["import numpy as np\n", "L = [[1,2,3], [4,5,6], [7,8,9]]\n", "a = np.array(L)  # 嵌套列表创建ndarray数组\n", "print(a)\n", "print(a.dtype)"]}, {"cell_type": "code", "execution_count": null, "id": "e25734eb", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}