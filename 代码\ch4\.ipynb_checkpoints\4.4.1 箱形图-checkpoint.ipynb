{"cells": [{"cell_type": "code", "execution_count": 19, "id": "2a992929", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import pandas as pd\n", "import seaborn as sns\n", "import matplotlib.pyplot as plt\n", "\n", "# 设置中文显示\n", "plt.rcParams['font.family'] = ['SimHei']\n", "plt.rcParams['axes.unicode_minus'] = False\n", "\n", "\n", "# 读取数据\n", "data = pd.read_csv('data/stock_data.csv')\n", "# 绘制箱线图\n", "sns.boxplot(x='Day', y='Price', data=data)\n", "# 设置图表标题和轴标签\n", "plt.title('股票收盘价')\n", "plt.xlabel('日期')\n", "plt.ylabel('收盘价')\n", "\n", "# 显示图形\n", "plt.show()\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}