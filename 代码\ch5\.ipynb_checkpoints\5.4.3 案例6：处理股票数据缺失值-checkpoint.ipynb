{"cells": [{"cell_type": "code", "execution_count": 5, "id": "1320ad04", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>日期</th>\n", "      <th>开盘价</th>\n", "      <th>收盘价</th>\n", "      <th>最低价</th>\n", "      <th>最高价</th>\n", "      <th>成交量</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2022-01-01</td>\n", "      <td>50.2</td>\n", "      <td>51.5</td>\n", "      <td>49.8</td>\n", "      <td>52.1</td>\n", "      <td>1000000.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2022-01-02</td>\n", "      <td>52.0</td>\n", "      <td>53.2</td>\n", "      <td>51.5</td>\n", "      <td>54.0</td>\n", "      <td>1200000.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2022-01-03</td>\n", "      <td>54.5</td>\n", "      <td>NaN</td>\n", "      <td>53.8</td>\n", "      <td>56.0</td>\n", "      <td>-500000.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2022-01-04</td>\n", "      <td>55.2</td>\n", "      <td>53.8</td>\n", "      <td>52.5</td>\n", "      <td>55.5</td>\n", "      <td>900000.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2022-01-05</td>\n", "      <td>54.0</td>\n", "      <td>53.2</td>\n", "      <td>52.1</td>\n", "      <td>54.8</td>\n", "      <td>1100000.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>2022-01-06</td>\n", "      <td>53.5</td>\n", "      <td>52.7</td>\n", "      <td>51.8</td>\n", "      <td>54.2</td>\n", "      <td>950000.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>2022-01-07</td>\n", "      <td>52.8</td>\n", "      <td>54.3</td>\n", "      <td>52.4</td>\n", "      <td>55.1</td>\n", "      <td>800000.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>2022-01-08</td>\n", "      <td>54.2</td>\n", "      <td>55.6</td>\n", "      <td>NaN</td>\n", "      <td>56.2</td>\n", "      <td>750000.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>2022-01-09</td>\n", "      <td>55.7</td>\n", "      <td>56.2</td>\n", "      <td>54.9</td>\n", "      <td>56.5</td>\n", "      <td>850000.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>2022-01-10</td>\n", "      <td>56.0</td>\n", "      <td>55.5</td>\n", "      <td>54.7</td>\n", "      <td>56.8</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["           日期   开盘价   收盘价   最低价   最高价        成交量\n", "0  2022-01-01  50.2  51.5  49.8  52.1  1000000.0\n", "1  2022-01-02  52.0  53.2  51.5  54.0  1200000.0\n", "2  2022-01-03  54.5   NaN  53.8  56.0  -500000.0\n", "3  2022-01-04  55.2  53.8  52.5  55.5   900000.0\n", "4  2022-01-05  54.0  53.2  52.1  54.8  1100000.0\n", "5  2022-01-06  53.5  52.7  51.8  54.2   950000.0\n", "6  2022-01-07  52.8  54.3  52.4  55.1   800000.0\n", "7  2022-01-08  54.2  55.6   NaN  56.2   750000.0\n", "8  2022-01-09  55.7  56.2  54.9  56.5   850000.0\n", "9  2022-01-10  56.0  55.5  54.7  56.8        NaN"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "\n", "# 读取数据文件\n", "df = pd.read_csv('股票数据Test.csv')\n", "# 处理前的数据：\n", "df"]}, {"cell_type": "code", "execution_count": 6, "id": "36f0a5f3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["缺失值数量：\n", "日期     0\n", "开盘价    0\n", "收盘价    1\n", "最低价    1\n", "最高价    0\n", "成交量    1\n", "dtype: int64\n"]}], "source": ["# 查找缺失值\n", "missing_values = df.isnull().sum()\n", "print(\"缺失值数量：\")\n", "print(missing_values)"]}, {"cell_type": "code", "execution_count": null, "id": "c8e8043b", "metadata": {}, "outputs": [], "source": ["# 填充缺失值\n", "# 使用平均值填充\"收盘价\"\n", "mean_close = df['收盘价'].mean()\n", "df['收盘价'].fillna(mean_close, inplace=True)\n", "\n", "# 使用中位数填充\"最低价\"\n", "median_low = df['最低价'].median()\n", "df['最低价'].fillna(median_low, inplace=True)\n", "\n", "# 使用邻近值填充\"最高价\"\n", "df['最高价'].fillna(method='ffill', inplace=True)\n", "\n", "# 使用0填充\"成交量\"\n", "df['成交量'].fillna(0, inplace=True)\n", "\n", "# 查看填充后的数据\n", "print(\"处理后的数据：\")\n", "df"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}