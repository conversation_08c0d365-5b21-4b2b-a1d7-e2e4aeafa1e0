import os
import shutil
import re
import tkinter as tk
from tkinter import filedialog

# 定义最小文件大小（100 MB）
MIN_FILE_SIZE_MB = 100
MIN_FILE_SIZE_BYTES = MIN_FILE_SIZE_MB * 1024 * 1024

def move_videos_to_current_directory(start_directory):
    # 打开重命名记录文件
    with open(os.path.join(start_directory, 'rename_log.txt'), 'w', encoding='utf-8') as log_file:
        log_file.write("重命名记录:\n")

        # 遍历所有子文件夹和文件
        for root, dirs, files in os.walk(start_directory):
            for filename in files:
                file_path = os.path.join(root, filename)
                if is_video_file(filename):
                    # 检查文件大小是否大于100 MB
                    if os.path.getsize(file_path) < MIN_FILE_SIZE_BYTES:
                        print(f"跳过文件: {file_path} (文件大小小于100 MB)")
                        continue

                    # 检查是否在根目录下
                    if root == start_directory:
                        print(f"跳过根目录下的文件: {file_path}")
                        log_file.write(f"跳过根目录下的文件: {file_path}\n")
                        continue

                    original_filename = filename

                    # 检查文件名是否包含符合条件的模式
                    if not contains_valid_pattern(filename):
                        # 不包含符合条件的模式时，检查文件夹名是否符合条件
                        folder_name = os.path.basename(os.path.dirname(file_path))
                        if not contains_valid_pattern(folder_name):
                            print(f"文件名和文件夹名均不符合条件，跳过: {file_path}")
                            log_file.write(f"跳过: {file_path} (文件名和文件夹名均不符合条件)\n")
                            continue

                        # 使用文件夹名重命名文件
                        new_filename = rename_to_folder_name(file_path)
                        new_file_path = os.path.join(root, new_filename)
                        os.rename(file_path, new_file_path)
                        file_path = new_file_path
                        filename = new_filename
                        print(f"文件名 {original_filename} 不符合条件，重命名为：{new_filename}")
                        log_file.write(f"{original_filename} -> {new_filename}\n")

                    # 检查目标路径是否有重名文件，处理重名问题
                    target_path = os.path.join(start_directory, os.path.basename(file_path))
                    target_path = handle_file_conflict(target_path)

                    # 记录文件移动操作
                    if contains_valid_pattern(os.path.basename(target_path)):
                        shutil.move(file_path, target_path)
                        print(f"移动文件: {file_path} 到 {target_path}")
                        log_file.write(f"{file_path} -> {target_path}\n")

def contains_valid_pattern(name):
    # 检查文件名或文件夹名是否包含指定的模式
    patterns = [
        r'\d{6}[-_]\d{3}',  # 六位数字-三位数字或六位数字_三位数字
        r'n\d{4}',  # n四位数字
        r'heyzo-\d{4}',  # heyzo-四位数字
        r'fc2-ppv-\d{7}',  # FC2-PPV-七位数字
        r'fc2-ppv-\d{6}',  # FC2-PPV-六位数字ppv
        r'heyzo_hd_\d{4}',  # heyzo_hd_四位数字
        r'\d{6}_\d{2}-\d{2}mu'  # 六位数字_两位数字-两位数字mu
    ]
    name = name.lower()
    for pattern in patterns:
        if re.search(pattern, name):
            return True
    return False

def is_video_file(filename):
    video_extensions = [".mp4", ".avi", ".mkv", ".mov", ".wmv", ".mpg", ".ts", ".nrg", ".iso", ".xltd", ".jpg", ".png", ".JPG"]
    return any(filename.lower().endswith(ext) for ext in video_extensions)

def rename_to_folder_name(file_path):
    # 使用文件所在文件夹的名称重命名文件，并保留hd1, hd2, hd3
    folder_name = os.path.basename(os.path.dirname(file_path))
    base, extension = os.path.splitext(file_path)
    hd_suffix = ""

    # 查找并保留hd1, hd2, hd3标记
    hd_match = re.search(r'(hd[1-3])', base, re.IGNORECASE)
    if hd_match:
        hd_suffix = hd_match.group(0)
        base = base.replace(hd_suffix, '', 1)

    return f"{folder_name}{hd_suffix}{extension}"

def handle_file_conflict(file_path):
    # 处理文件名冲突，添加-1, -2等后缀
    base, extension = os.path.splitext(file_path)
    counter = 1
    while os.path.exists(file_path):
        file_path = f"{base}-{counter}{extension}"
        counter += 1
    return file_path

def select_directory_and_move_videos():
    # 弹出选择目录对话框
    start_directory = filedialog.askdirectory(title="选择开始的目录")
    if start_directory:
        # 调用函数移动视频文件
        move_videos_to_current_directory(start_directory)
        print("处理完成！")

# 创建主窗口
root = tk.Tk()

# 创建按钮来选择目录并移动视频文件
button = tk.Button(root, text="选择开始的目录并移动视频文件", command=select_directory_and_move_videos)
button.pack()

# 运行程序
root.mainloop()
