{"cells": [{"cell_type": "code", "execution_count": 1, "id": "ce6dff7b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Hello, <PERSON>!\n"]}], "source": ["print(\"Hello, <PERSON>!\")"]}, {"cell_type": "code", "execution_count": null, "id": "4d593677", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}