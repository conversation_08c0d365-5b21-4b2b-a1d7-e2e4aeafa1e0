import os
import csv
import shutil
import re
from tkinter import Tk
from tkinter.filedialog import askdirectory
from datetime import datetime

def log_operation(log_file, message):
    """记录操作日志"""
    with open(log_file, 'a', encoding='utf-8') as log:
        log.write(f"{datetime.now()}: {message}\n")
    print(message)

def record_replacement(replacement_csv, old_str, new_str):
    """记录字符串替换"""
    with open(replacement_csv, 'a', newline='', encoding='utf-8') as csvfile:
        writer = csv.writer(csvfile)
        writer.writerow([old_str, new_str])

def record_deletion(deletion_csv, string_to_remove):
    """记录字符串删除"""
    with open(deletion_csv, 'a', newline='', encoding='utf-8') as csvfile:
        writer = csv.writer(csvfile)
        writer.writerow([string_to_remove])

def auto_rename(target_path):
    """自动重命名避免冲突"""
    base, extension = os.path.splitext(target_path)
    counter = 1
    while os.path.exists(target_path):
        target_path = f"{base}_{counter}{extension}"
        counter += 1
    return target_path

def get_earliest_separator_position(file_name):
    """找到最早的分隔符位置"""
    positions = [
        file_name.find('_'),
        file_name.find('-'),
        file_name.find(','),
        file_name.find('，')
    ]
    positions = [pos for pos in positions if pos != -1]
    return min(positions) if positions else -1

def organize_files_by_prefix(folder_path):
    """按前缀整理文件"""
    files = [f for f in os.listdir(folder_path) if os.path.isfile(os.path.join(folder_path, f))]
    
    for file in files:
        clean_file_name = file
        pos = get_earliest_separator_position(clean_file_name)
        
        if pos != -1:
            prefix = clean_file_name[:pos].strip()
        else:
            prefix = clean_file_name.rsplit('.', 1)[0].strip()
            
        subfolder_path = os.path.join(folder_path, prefix)
        
        if not os.path.exists(subfolder_path):
            try:
                os.makedirs(subfolder_path)
                print(f"创建文件夹: {prefix}")
            except Exception as e:
                print(f"创建文件夹失败: {str(e)}")
                continue
                
        try:
            shutil.move(os.path.join(folder_path, file), os.path.join(subfolder_path, file))
            print(f"移动文件: {file} -> {prefix}")
        except Exception as e:
            print(f"移动文件失败: {str(e)}")

def read_deletion_chars(deletion_csv):
    """读取需要删除的字符列表"""
    chars_to_delete = set()
    if os.path.exists(deletion_csv):
        with open(deletion_csv, 'r', encoding='utf-8') as f:
            reader = csv.reader(f)
            for row in reader:
                if row and row[0].strip():  # 确保行不为空且有内容
                    chars_to_delete.add(row[0])
        print(f"读取到需要删除的字符: {', '.join(chars_to_delete)}")
    return chars_to_delete

def normalize_folder_name(folder_path, deletion_csv):
    """规范化文件夹名称"""
    chars_to_delete = read_deletion_chars(deletion_csv)
    folders = [f for f in os.listdir(folder_path) if os.path.isdir(os.path.join(folder_path, f))]
    
    for folder in folders:
        old_path = os.path.join(folder_path, folder)
        new_name = folder
        
        # 删除指定字符
        for char in chars_to_delete:
            if char in new_name:
                new_name = new_name.replace(char, '')
                print(f"从'{folder}'中删除字符: '{char}'")
        
        # 替换非法字符
        new_name = re.sub(r'[\\/:*?"<>|]', '_', new_name)
        new_name = ' '.join(new_name.split())
        new_name = new_name.strip()
        
        if new_name != folder:
            new_path = os.path.join(folder_path, new_name)
            try:
                os.rename(old_path, new_path)
                print(f"重命名文件夹: {folder} -> {new_name}")
            except Exception as e:
                print(f"重命名失败 {folder}: {str(e)}")

def clean_single_file_folders(folder_path):
    """处理单文件文件夹"""
    for root, dirs, files in os.walk(folder_path, topdown=False):
        if root == folder_path:
            continue
            
        if len(files) == 1 and len(dirs) == 0:
            file_path = os.path.join(root, files[0])
            dest_path = os.path.join(folder_path, files[0])
            try:
                shutil.move(file_path, dest_path)
                print(f"移动单文件: {files[0]} -> 根目录")
                os.rmdir(root)
                print(f"删除空文件夹: {root}")
            except Exception as e:
                print(f"处理失败: {str(e)}")

def main():
    root = Tk()
    root.withdraw()
    
    folder_path = askdirectory(title="选择要整理的文件夹")
    if not folder_path:
        return
        
    log_dir = r"Z:\work"
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
        
    deletion_csv = os.path.join(log_dir, "deletions.csv")
    
    print("\n正在整理文件...")
    organize_files_by_prefix(folder_path)
    
    print("\n正在处理单文件文件夹...")
    clean_single_file_folders(folder_path)
    
    print("\n正在规范化文件夹名称...")
    normalize_folder_name(folder_path, deletion_csv)
    
    print("\n所有操作已完成!")

if __name__ == "__main__":
    main()