{"cells": [{"cell_type": "code", "execution_count": 66, "id": "e1d6ca73", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Close</th>\n", "      <th>Volume</th>\n", "      <th>Open</th>\n", "      <th>High</th>\n", "      <th>Low</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2023-06-23</th>\n", "      <td>186.68</td>\n", "      <td>53117000</td>\n", "      <td>185.550</td>\n", "      <td>187.560</td>\n", "      <td>185.0100</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-06-22</th>\n", "      <td>187.00</td>\n", "      <td>51245330</td>\n", "      <td>183.740</td>\n", "      <td>187.045</td>\n", "      <td>183.6700</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-06-21</th>\n", "      <td>183.96</td>\n", "      <td>49515700</td>\n", "      <td>184.900</td>\n", "      <td>185.410</td>\n", "      <td>182.5901</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-06-20</th>\n", "      <td>185.01</td>\n", "      <td>49799090</td>\n", "      <td>184.410</td>\n", "      <td>186.100</td>\n", "      <td>184.4100</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-06-16</th>\n", "      <td>184.92</td>\n", "      <td>101256200</td>\n", "      <td>186.730</td>\n", "      <td>186.990</td>\n", "      <td>184.2700</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-06-15</th>\n", "      <td>186.01</td>\n", "      <td>65433170</td>\n", "      <td>183.960</td>\n", "      <td>186.520</td>\n", "      <td>183.7800</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-06-14</th>\n", "      <td>183.95</td>\n", "      <td>57462880</td>\n", "      <td>183.370</td>\n", "      <td>184.390</td>\n", "      <td>182.0200</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-06-13</th>\n", "      <td>183.31</td>\n", "      <td>54929130</td>\n", "      <td>182.800</td>\n", "      <td>184.150</td>\n", "      <td>182.4400</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-06-12</th>\n", "      <td>183.79</td>\n", "      <td>54755000</td>\n", "      <td>181.270</td>\n", "      <td>183.890</td>\n", "      <td>180.9700</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-06-09</th>\n", "      <td>180.96</td>\n", "      <td>48899970</td>\n", "      <td>181.500</td>\n", "      <td>182.230</td>\n", "      <td>180.6300</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-06-08</th>\n", "      <td>180.57</td>\n", "      <td>50214880</td>\n", "      <td>177.895</td>\n", "      <td>180.840</td>\n", "      <td>177.4600</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-06-07</th>\n", "      <td>177.82</td>\n", "      <td>61944620</td>\n", "      <td>178.440</td>\n", "      <td>181.210</td>\n", "      <td>177.3200</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-06-06</th>\n", "      <td>179.21</td>\n", "      <td>64848370</td>\n", "      <td>179.965</td>\n", "      <td>180.120</td>\n", "      <td>177.4300</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-06-05</th>\n", "      <td>179.58</td>\n", "      <td>121946500</td>\n", "      <td>182.630</td>\n", "      <td>184.951</td>\n", "      <td>178.0350</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-06-02</th>\n", "      <td>180.95</td>\n", "      <td>61996910</td>\n", "      <td>181.030</td>\n", "      <td>181.780</td>\n", "      <td>179.2600</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-06-01</th>\n", "      <td>180.09</td>\n", "      <td>68901810</td>\n", "      <td>177.700</td>\n", "      <td>180.120</td>\n", "      <td>176.9306</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-05-31</th>\n", "      <td>177.25</td>\n", "      <td>99625290</td>\n", "      <td>177.325</td>\n", "      <td>179.350</td>\n", "      <td>176.7600</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-05-30</th>\n", "      <td>177.30</td>\n", "      <td>55964400</td>\n", "      <td>176.960</td>\n", "      <td>178.990</td>\n", "      <td>176.5700</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-05-26</th>\n", "      <td>175.43</td>\n", "      <td>54834980</td>\n", "      <td>173.320</td>\n", "      <td>175.770</td>\n", "      <td>173.1100</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2023-05-25</th>\n", "      <td>172.99</td>\n", "      <td>56058260</td>\n", "      <td>172.410</td>\n", "      <td>173.895</td>\n", "      <td>171.6900</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["             Close     Volume     Open     High       Low\n", "Date                                                     \n", "2023-06-23  186.68   53117000  185.550  187.560  185.0100\n", "2023-06-22  187.00   51245330  183.740  187.045  183.6700\n", "2023-06-21  183.96   49515700  184.900  185.410  182.5901\n", "2023-06-20  185.01   49799090  184.410  186.100  184.4100\n", "2023-06-16  184.92  101256200  186.730  186.990  184.2700\n", "2023-06-15  186.01   65433170  183.960  186.520  183.7800\n", "2023-06-14  183.95   57462880  183.370  184.390  182.0200\n", "2023-06-13  183.31   54929130  182.800  184.150  182.4400\n", "2023-06-12  183.79   54755000  181.270  183.890  180.9700\n", "2023-06-09  180.96   48899970  181.500  182.230  180.6300\n", "2023-06-08  180.57   50214880  177.895  180.840  177.4600\n", "2023-06-07  177.82   61944620  178.440  181.210  177.3200\n", "2023-06-06  179.21   64848370  179.965  180.120  177.4300\n", "2023-06-05  179.58  121946500  182.630  184.951  178.0350\n", "2023-06-02  180.95   61996910  181.030  181.780  179.2600\n", "2023-06-01  180.09   68901810  177.700  180.120  176.9306\n", "2023-05-31  177.25   99625290  177.325  179.350  176.7600\n", "2023-05-30  177.30   55964400  176.960  178.990  176.5700\n", "2023-05-26  175.43   54834980  173.320  175.770  173.1100\n", "2023-05-25  172.99   56058260  172.410  173.895  171.6900"]}, "execution_count": 66, "metadata": {}, "output_type": "execute_result"}], "source": ["import matplotlib.pyplot as plt\n", "import mplfinance as mpf\n", "import pandas as pd\n", "\n", "plt.rcParams['font.family'] = ['SimHei']  # 设置中文字体\n", "plt.rcParams['axes.unicode_minus'] = False  # 设置负号显示\n", "\n", "# 读取股票数据\n", "df = pd.read_csv('data/AAPL.csv', index_col='Date', parse_dates=True)\n", "# 清洗数据\n", "df['Close'] = df['Close'].str.replace('$', '').astype(float)\n", "df['Open'] = df['Open'].str.replace('$', '').astype(float)\n", "df['High'] = df['High'].str.replace('$', '').astype(float)\n", "df['Low'] = df['Low'].str.replace('$', '').astype(float)\n", "\n", "df"]}, {"cell_type": "code", "execution_count": 101, "id": "79c48812", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 800x575 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["market_colors = mpf.make_marketcolors(up='red', down='green')\n", "\n", "my_style = mpf.make_mpf_style(marketcolors=market_colors)\n", "# 绘制K线图\n", "mpf.plot(df, type='candle', \n", "         mav=(10, 20),\n", "         volume=True, \n", "         show_nontrading=True,\n", "         style=my_style)\n"]}, {"cell_type": "code", "execution_count": null, "id": "a244d97a", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}