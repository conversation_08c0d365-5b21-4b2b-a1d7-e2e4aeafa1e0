import os
import re
import subprocess
import threading
import time
import tkinter as tk
import shutil
from threading import Thread
import tkinter.messagebox as messagebox
import pyperclip
import json

class AddressGenerator:
    def __init__(self, root):
        self.root = root
        self.root.title("电报文件下载器_by <PERSON><PERSON><PERSON>")
        self.root.geometry("600x450")
        self.root.attributes('-topmost', True)

        self.first_label = tk.Label(root, text="起始地址:")
        self.first_label.pack()
        self.first_entry = tk.Entry(root, width=60)
        self.first_entry.pack()
        self.first_entry.bind("<Button-1>", self.paste_clipboard)

        self.second_label = tk.Label(root, text="结束地址:")
        self.second_label.pack()
        self.second_entry = tk.Entry(root, width=60)
        self.second_entry.pack()
        self.second_entry.bind("<Button-1>", self.paste_clipboard)

        self.mov_name_label = tk.Label(root, text="文件名前缀:")
        self.mov_name_label.pack()
        self.mov_name_entry = tk.Entry(root, width=60)
        self.mov_name_entry.pack()
        self.mov_name_entry.bind("<Button-1>", self.paste_clipboard)

        self.button_frame = tk.Frame(root)
        self.button_frame.pack()

        self.download_button = tk.Button(self.button_frame, text="批量地址", command=self.add_addresses)
        self.download_button.pack(side=tk.LEFT, padx=5)

        self.single_download_button = tk.Button(self.button_frame, text="单独地址", command=self.add_single_address)
        self.single_download_button.pack(side=tk.LEFT, padx=5)

        self.start_download_button = tk.Button(self.button_frame, text="开始下载", command=self.start_download)
        self.start_download_button.pack(side=tk.LEFT, padx=5)
        self.start_download_button["state"] = "disabled"

        self.stop_download_button = tk.Button(self.button_frame, text="停止下载", command=self.stop_download)
        self.stop_download_button.pack(side=tk.LEFT, padx=5)
        self.stop_download_button["state"] = "disabled"
        self.stop_flag = False
        self.stop_event = threading.Event()

        self.addresses_text = tk.Text(root, height=20, width=80)
        self.addresses_text.pack()
        # 绑定文本变化事件
        self.addresses_text.bind('<<Modified>>', self.on_text_modified)

        self.addresses = []
        self.timer = None
        self.initial_file_list = []
        self.final_file_list = []
        self.prefix_count = {}
        self.prefix_addresses = {}

        # 绑定关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

    def paste_clipboard(self, event):
        event.widget.delete(0, tk.END)
        clipboard_content = pyperclip.paste()
        event.widget.insert(tk.END, clipboard_content)

    def add_addresses(self):
        # 获取用户输入
        first_address = self.first_entry.get()
        second_address = self.second_entry.get()
        # 处理电影名称，去掉换行符和其他非目录可用字符
        mov_name = self.clean_input(self.mov_name_entry.get())

        # 从地址中提取数字部分
        first_numbers = [int(num) for num in re.findall(r'/(\d+)\D*$', first_address)]
        second_numbers = [int(num) for num in re.findall(r'/(\d+)\D*$', second_address)]

        new_addresses = []
        # 生成新的地址范围
        for number in range(first_numbers[0], second_numbers[0] + 1):
            # 替换原地址中的数字部分
            new_address = re.sub(r'/\d+\D*$', f'/{number}', first_address)
            # 构造新的命令行地址，确保使用清理后的mov_name
            new_address = f"d:/tdl_Windows_64bit/.\\tdl dl -u {new_address} --template \"{mov_name}_{{{{.MessageID}}}}_{{{{.FileName}}}}\""
            new_addresses.append(f'{new_address} & timeout /t 5 & exit')

        # 将生成的地址添加到全局地址列表
        self.addresses.extend(new_addresses)

        # 记录前缀需下载的数量和对应地址
        if mov_name in self.prefix_count:
            self.prefix_count[mov_name] += len(new_addresses)
            self.prefix_addresses[mov_name].extend(new_addresses)
        else:
            self.prefix_count[mov_name] = len(new_addresses)
            self.prefix_addresses[mov_name] = new_addresses

        # 更新界面显示
        self.show_addresses()
        # 清空输入框
        self.clear_input_fields()
        # 启用下载按钮
        self.start_download_button["state"] = "normal"
        # 保存地址到文件
        self.save_addresses_to_file()

    def clean_input(self, input_string):
        # 清除换行符和其他非目录可用字符（允许字母、数字、下划线、连字符和空格）
        # 首先移除换行和回车符
        cleaned_string = input_string.replace('\n', '').replace('\r', '')
        # 移除 '#' 字符
        cleaned_string = cleaned_string.replace('#', '')
        cleaned_string = cleaned_string.replace('・', ' ')
        # 使用正则表达式替换掉不允许的字符
        cleaned_string = re.sub(r'[<>:"/\\|?*]+', '_', cleaned_string)
        # 附加处理：移除多余的空白字符
        cleaned_string = re.sub(r'\s+', ' ', cleaned_string).strip()
        return cleaned_string

    def add_single_address(self):
        first_address = self.first_entry.get()
        # 处理电影名称，去掉换行符和其他非目录可用字符
        mov_name = self.clean_input(self.mov_name_entry.get())
        new_address = f"d:/tdl_Windows_64bit/.\\tdl dl -u {first_address} --template \"{mov_name}_{{{{.MessageID}}}}_{{{{.FileName}}}}\""
        new_addresses = [f'{new_address} & timeout /t 5 >nul & exit']
        self.addresses.extend(new_addresses)

        # 记录前缀需下载的数量和对应地址
        if mov_name in self.prefix_count:
            self.prefix_count[mov_name] += 1
            self.prefix_addresses[mov_name].extend(new_addresses)
        else:
            self.prefix_count[mov_name] = 1
            self.prefix_addresses[mov_name] = new_addresses

        self.show_addresses()
        self.clear_input_fields()
        self.start_download_button["state"] = "normal"
        self.save_addresses_to_file()

    def clear_input_fields(self):
        self.first_entry.delete(0, tk.END)
        self.second_entry.delete(0, tk.END)

    def show_addresses(self):
        """显示地址时不触发on_text_modified"""
        # 暂时解绑事件
        self.addresses_text.unbind('<<Modified>>')
        self.addresses_text.delete(1.0, tk.END)
        for address in self.addresses:
            self.addresses_text.insert(tk.END, address + "\n")
        # 重新绑定事件
        self.addresses_text.bind('<<Modified>>', self.on_text_modified)
        self.addresses_text.edit_modified(False)

    def on_text_modified(self, event):
        """当文本框内容改变时触发"""
        if self.addresses_text.edit_modified():
            # 获取文本框中的所有内容
            content = self.addresses_text.get(1.0, tk.END).strip()
            # 更新地址列表
            new_addresses = [line for line in content.split('\n') if line.strip()]
            
            # 清空并重建prefix_addresses
            self.prefix_addresses = {}
            self.prefix_count = {}
            
            # 遍历新地址，重建prefix_addresses和prefix_count
            for address in new_addresses:
                # 从地址中提取前缀
                match = re.search(r'--template "([^_]+)', address)
                if match:
                    prefix = match.group(1)
                    if prefix in self.prefix_addresses:
                        self.prefix_addresses[prefix].append(address)
                        self.prefix_count[prefix] += 1
                    else:
                        self.prefix_addresses[prefix] = [address]
                        self.prefix_count[prefix] = 1
            
            self.addresses = new_addresses
            # 保存到文件
            self.save_addresses_to_file()
            # 重置修改标志
            self.addresses_text.edit_modified(False)

    def start_download(self):
        self.generate_backup()
        self.start_download_button["state"] = "disabled"
        self.download_button["state"] = "disabled"
        self.single_download_button["state"] = "disabled"
        self.stop_download_button["state"] = "normal"

        self.stop_flag = False
        self.stop_event.clear()

        # 记录开始下载前的文件列表
        self.initial_file_list = self.get_current_file_list()

        thread = Thread(target=self.execute_download)
        thread.start()

    def generate_backup(self):
        backup_file_path = "z:/work/addresses_backup.txt"
        shutil.copy("z:/work/addresses.txt", backup_file_path)

    def execute_download(self):
        while self.addresses:
            #if not self.check_cmd_window():
            addresses_copy = self.addresses[:]
            for address in addresses_copy:
                if self.stop_event.is_set():
                    print("下载已停止。")
                    self.stop_download()
                    return
                if self.send_address_to_cmd(address):
                    self.addresses.remove(address)
                    self.show_addresses()
                    self.root.update()
                    time.sleep(5)  # 等待命令完成
                self.save_addresses_to_file()
            else:
                print("正在下载，等待10秒重试...")
                time.sleep(10)  # 等待 10 秒重试

        # 如果地址列表为空，也需要调用 stop_download
        self.stop_download()

    def stop_download(self):
        self.start_download_button["state"] = "normal"
        self.download_button["state"] = "normal"
        self.single_download_button["state"] = "normal"
        self.stop_download_button["state"] = "disabled"

        self.stop_flag = True
        self.stop_event.set()

        if self.timer:
            self.root.after_cancel(self.timer)

        self.save_addresses_to_file()

        # 记录停止下载时的文件列表
        self.final_file_list = self.get_current_file_list()

        # 计算每个前缀的实际下载文件和对应的消息ID
        actual_prefix_files = {}
        for file in self.final_file_list:
            try:
                parts = file.split('_')
                if len(parts) >= 2:
                    prefix = parts[0]
                    message_id = parts[1]
                    if prefix in actual_prefix_files:
                        actual_prefix_files[prefix].append((message_id, file))
                    else:
                        actual_prefix_files[prefix] = [(message_id, file)]
            except Exception as e:
                print(f"Error processing file {file}: {e}")

        # 比较预期和实际下载数量，并记录缺失文件的链接
        missing_files_info = []
        missing_links = []
        
        for prefix, addresses in self.prefix_addresses.items():
            expected_count = self.prefix_count.get(prefix, 0)
            actual_files = actual_prefix_files.get(prefix, [])
            actual_count = len(actual_files)
            
            # 如果实际数量小于预期数量，记录缺失信息
            if actual_count < expected_count:
                missing_files_info.append(f"{prefix}: 预期 {expected_count}, 实际 {actual_count}")
                
                # 获取该前缀对应的所有未完成的链接
                downloaded_msg_ids = {msg_id for msg_id, _ in actual_files}
                for address in addresses:
                    # 从地址中提取消息ID
                    match = re.search(r'/(\d+)(?:\s|$)', address)
                    if match:
                        message_id = match.group(1)
                        if message_id not in downloaded_msg_ids:
                            missing_links.append(address)

        if not missing_files_info:
            messagebox.showinfo("下载完成", "所有文件下载完成且数量相符。")
            # 清除已下载的文件名前缀和地址
            self.prefix_count = {}
            self.prefix_addresses = {}
        else:
            missing_info_str = "\n".join(missing_files_info)
            messagebox.showwarning("警告", 
                f"下载文件数量不匹配，缺失文件信息如下：\n{missing_info_str}\n"
                f"缺失的下载链接已保存到 missing_files_info.txt")
            
            # 保存缺失信息和对应链接到文件
            with open("z:/work/missing_files_info.txt", "w", encoding="utf-8") as f:
                f.write(f"下载时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n\n")
                f.write("缺失文件信息:\n")
                f.write(missing_info_str)
                f.write("\n\n缺失文件对应的下载链接:\n")
                for link in missing_links:
                    f.write(f"{link}\n")

        # 无论数量是否相符都提示，并清除数据
        if not self.addresses:
            messagebox.showinfo("提示", "下载列表为空。")
            self.prefix_count = {}
            self.prefix_addresses = {}

    def send_address_to_cmd(self, address):
        #while self.check_cmd_window():
        #    print("正在下载，等待10秒重试...")
        #    time.sleep(10)

        try:
            if not self.stop_flag:
                batch_content = f"""
                    @echo off
                    cd d:\\tdl_Windows_64bit
                    {address}
                    pause
                    """

                batch_file_path = os.path.join(os.getenv('TEMP'), 'temp_cmd.bat')
                with open(batch_file_path, 'w') as batch_file:
                    batch_file.write(batch_content)

                process = subprocess.Popen(["cmd.exe", "/c", batch_file_path], creationflags=subprocess.CREATE_NEW_CONSOLE)
                process.wait()
                return True
        except Exception as e:
            print(f"Error sending address to cmd: {e}")
        return False

    def get_current_file_list(self):
        download_folder = "D:/tdl_Windows_64bit/downloads"
        return [name for name in os.listdir(download_folder) if os.path.isfile(os.path.join(download_folder, name))]

    def on_closing(self):
        # 只保存当前状态而不清空数据
        self.save_addresses_to_file()
        self.root.destroy()

    def save_addresses_to_file(self):
        try:
            data = {
                "addresses": self.addresses,
                "prefix_count": self.prefix_count,
                "prefix_addresses": self.prefix_addresses
            }
            
            # 清理数据中的非法Unicode字符
            def clean_data(obj):
                if isinstance(obj, dict):
                    return {k: clean_data(v) for k, v in obj.items()}
                elif isinstance(obj, list):
                    return [clean_data(x) for x in obj]
                elif isinstance(obj, str):
                    # 移除或替换非法Unicode字符
                    return obj.encode('utf-16', 'surrogatepass').decode('utf-16', 'replace')
                return obj
            
            cleaned_data = clean_data(data)
            
            with open("z:/work/addresses.txt", "w", encoding="utf-8") as f:
                json.dump(cleaned_data, f, ensure_ascii=False, indent=4)
        except Exception as e:
            print(f"保存文件时出错: {str(e)}")
            # 如果出错，尝试使用更安全的方式保存
            try:
                with open("z:/work/addresses.txt", "w", encoding="utf-8") as f:
                    json.dump(data, f, ensure_ascii=True, indent=4)
            except Exception as e2:
                print(f"备用保存方式也失败: {str(e2)}")
                messagebox.showerror("错误", "保存地址文件时出错，请检查文件权限或磁盘空间。")

    def load_addresses_from_file(self):
        try:
            with open("z:/work/addresses.txt", "r", encoding="utf-8") as f:
                content = f.read().strip()
                if content:
                    try:
                        data = json.loads(content)
                        self.addresses = data.get("addresses", [])
                        self.prefix_count = data.get("prefix_count", {})
                        self.prefix_addresses = data.get("prefix_addresses", {})
                    except json.JSONDecodeError as e:
                        print(f"解析JSON时出错: {str(e)}")
                        self.addresses = []
                        self.prefix_count = {}
                        self.prefix_addresses = {}
                else:
                    self.addresses = []
                    self.prefix_count = {}
                    self.prefix_addresses = {}
        except FileNotFoundError:
            print("地址文件不存在")
            self.addresses = []
            self.prefix_count = {}
            self.prefix_addresses = {}
        except Exception as e:
            print(f"加载文件时出错: {str(e)}")
            self.addresses = []
            self.prefix_count = {}
            self.prefix_addresses = {}

        self.show_addresses()
        if self.addresses:
            self.start_download_button["state"] = "normal"
            self.stop_download_button["state"] = "normal"
        else:
            self.start_download_button["state"] = "disabled"
            self.stop_download_button["state"] = "disabled"


root = tk.Tk()
app = AddressGenerator(root)
app.load_addresses_from_file()
root.mainloop()
app.save_addresses_to_file()
