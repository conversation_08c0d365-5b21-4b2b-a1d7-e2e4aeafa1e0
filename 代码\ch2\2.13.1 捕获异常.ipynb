{"cells": [{"cell_type": "code", "execution_count": 3, "id": "3608150e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["日期 = 2023-08-18 00:00:00\n"]}], "source": ["\n", "import datetime as dt\n", "\n", "\n", "def read_date(in_date):\n", "    try:\n", "        date = dt.datetime.strptime(in_date, '%Y-%m-%d') \n", "        return date\n", "    except ValueError as e: \n", "        print('处理ValueError异常')\n", "        print(e)\n", "\n", "if __name__ == '__main__': \n", "    str_date = '2023-8-18'  # '2023-B-18'\n", "    date = read_date(str_date) \n", "    print('日期 = {0}'.format(date))"]}, {"cell_type": "code", "execution_count": null, "id": "8dbd385f", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}