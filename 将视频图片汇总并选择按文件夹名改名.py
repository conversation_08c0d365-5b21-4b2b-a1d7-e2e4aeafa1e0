import os
import shutil
import re
import tkinter as tk
from tkinter import filedialog, messagebox

# 定义图片和视频的扩展名
IMAGE_EXTENSIONS = ('.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.webp')
VIDEO_EXTENSIONS = ('.mp4', '.mkv', '.avi', '.mov', '.wmv', '.flv', '.ts')


def is_image(file_name):
    """检查文件是否为图片文件"""
    return os.path.splitext(file_name)[1].lower() in IMAGE_EXTENSIONS


def is_video(file_name):
    """检查文件是否为视频文件"""
    return os.path.splitext(file_name)[1].lower() in VIDEO_EXTENSIONS


def contains_chinese(text):
    """检查字符串中是否包含中文字符"""
    return bool(re.search('[\u4e00-\u9fff]', text))


def move_with_unique_name(src_path, dst_folder):
    """将文件移动到目标文件夹，如果文件名冲突则自动添加后缀"""
    base_name = os.path.basename(src_path)
    dst_path = os.path.join(dst_folder, base_name)

    # 如果目标文件夹中已存在同名文件，则添加后缀
    if os.path.exists(dst_path):
        suffix = 2
        file_name, file_extension = os.path.splitext(base_name)
        while True:
            new_file_name = f"{file_name}-{suffix}{file_extension}"
            dst_path = os.path.join(dst_folder, new_file_name)
            if not os.path.exists(dst_path):
                break
            suffix += 1

    # 移动文件
    shutil.move(src_path, dst_path)
    print(f"Moved: {src_path} -> {dst_path}")


def process_files(folder_path, rename_files):
    """处理文件，根据选项决定是否重命名"""
    # 获取文件夹下的一级子文件夹
    subfolders = [f.path for f in os.scandir(folder_path) if f.is_dir()]

    # 创建图片和视频文件夹
    image_folder = os.path.join(folder_path, "图片")
    video_folder = os.path.join(folder_path, "视频")
    os.makedirs(image_folder, exist_ok=True)
    os.makedirs(video_folder, exist_ok=True)

    for subfolder in subfolders:
        # 获取子文件夹的名称
        folder_name = os.path.basename(subfolder)

        # 如果子文件夹名是“视频”或“图片”，或者不包含中文字符，则跳过重命名
        if folder_name in ["视频", "图片", "合集"] or not contains_chinese(folder_name):
            print(f"Skipped renaming files in folder: {folder_name}")
            continue

        # 获取子文件夹下的所有文件
        files = [f for f in os.listdir(subfolder) if os.path.isfile(os.path.join(subfolder, f))]

        # 对每个文件进行重命名和分类
        for file_name in files:
            file_path = os.path.join(subfolder, file_name)

            # 获取文件扩展名
            file_extension = os.path.splitext(file_name)[1]

            # 如果选择重命名，则重命名文件
            if rename_files:
                new_file_name = folder_name + file_extension
                new_file_path = os.path.join(subfolder, new_file_name)

                # 如果新文件名已存在，则添加后缀 -1, -2, ...
                if os.path.exists(new_file_path):
                    suffix = 1
                    while True:
                        new_file_name_with_suffix = f"{folder_name}-{suffix}{file_extension}"
                        new_file_path_with_suffix = os.path.join(subfolder, new_file_name_with_suffix)
                        if not os.path.exists(new_file_path_with_suffix):
                            new_file_path = new_file_path_with_suffix
                            break
                        suffix += 1

                # 重命名文件
                os.rename(file_path, new_file_path)
                print(f"Renamed: {file_path} -> {new_file_path}")
                file_path = new_file_path  # 更新文件路径

            # 移动文件到对应的分类文件夹
            if is_image(file_path):
                move_with_unique_name(file_path, image_folder)
            elif is_video(file_path):
                move_with_unique_name(file_path, video_folder)
            else:
                move_with_unique_name(file_path, folder_path)  # 其他文件移动到根文件夹

        # 删除空文件夹
        if not os.listdir(subfolder):
            os.rmdir(subfolder)
            print(f"Deleted empty folder: {subfolder}")


def select_folder():
    # 创建一个Tkinter根窗口
    root = tk.Tk()
    root.withdraw()  # 隐藏根窗口

    # 打开文件夹选择对话框
    folder_path = filedialog.askdirectory()

    if folder_path:
        # 弹出对话框询问是否重命名文件
        rename_choice = messagebox.askyesno("重命名选项", "是否按文件夹重命名文件？")
        process_files(folder_path, rename_choice)
    else:
        print("No folder selected.")


if __name__ == "__main__":
    select_folder()