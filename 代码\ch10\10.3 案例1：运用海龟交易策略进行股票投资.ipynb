{"cells": [{"cell_type": "code", "execution_count": 1, "id": "ef7c1cc5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["处理完成。\n"]}], "source": ["# 原始文件\n", "inputfile =  'data/0601857股票历史交易数据.csv'\n", "# 目标文件\n", "outfile =  'data/0601857股票历史交易数据（清洗后）.csv'\n", "\n", "# 打开原始文件和目标文件\n", "with open(inputfile, 'r') as input_file, open(outfile, 'w') as output_file:\n", "    # 逐行读取原始文件\n", "    for line in input_file:\n", "        # 去除行末的换行符\n", "        line = line.rstrip('\\n')\n", "        # 判断是否为空行\n", "        if line:\n", "            # 写入非空行到目标文件\n", "            output_file.write(line + '\\n')\n", "\n", "print('处理完成。')"]}, {"cell_type": "code", "execution_count": 2, "id": "2ccea902", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>股票代码</th>\n", "      <th>名称</th>\n", "      <th>收盘价</th>\n", "      <th>最高价</th>\n", "      <th>最低价</th>\n", "      <th>开盘价</th>\n", "      <th>前收盘</th>\n", "      <th>涨跌额</th>\n", "      <th>涨跌幅</th>\n", "      <th>换手率</th>\n", "      <th>成交量</th>\n", "      <th>成交金额</th>\n", "      <th>总市值</th>\n", "      <th>流通市值</th>\n", "    </tr>\n", "    <tr>\n", "      <th>日期</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2021-03-23</th>\n", "      <td>'601857</td>\n", "      <td>中国石油</td>\n", "      <td>4.32</td>\n", "      <td>4.35</td>\n", "      <td>4.31</td>\n", "      <td>4.35</td>\n", "      <td>4.36</td>\n", "      <td>-0.04</td>\n", "      <td>-0.9174</td>\n", "      <td>0.0394</td>\n", "      <td>63729753</td>\n", "      <td>2.756663e+08</td>\n", "      <td>7.906506e+11</td>\n", "      <td>6.995034e+11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-03-22</th>\n", "      <td>'601857</td>\n", "      <td>中国石油</td>\n", "      <td>4.36</td>\n", "      <td>4.36</td>\n", "      <td>4.30</td>\n", "      <td>4.31</td>\n", "      <td>4.32</td>\n", "      <td>0.04</td>\n", "      <td>0.9259</td>\n", "      <td>0.0464</td>\n", "      <td>75187588</td>\n", "      <td>3.257384e+08</td>\n", "      <td>7.979715e+11</td>\n", "      <td>7.059803e+11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-03-19</th>\n", "      <td>'601857</td>\n", "      <td>中国石油</td>\n", "      <td>4.32</td>\n", "      <td>4.36</td>\n", "      <td>4.30</td>\n", "      <td>4.32</td>\n", "      <td>4.41</td>\n", "      <td>-0.09</td>\n", "      <td>-2.0408</td>\n", "      <td>0.0902</td>\n", "      <td>146109801</td>\n", "      <td>6.322013e+08</td>\n", "      <td>7.906506e+11</td>\n", "      <td>6.995034e+11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-03-18</th>\n", "      <td>'601857</td>\n", "      <td>中国石油</td>\n", "      <td>4.41</td>\n", "      <td>4.44</td>\n", "      <td>4.41</td>\n", "      <td>4.43</td>\n", "      <td>4.44</td>\n", "      <td>-0.03</td>\n", "      <td>-0.6757</td>\n", "      <td>0.0479</td>\n", "      <td>77613380</td>\n", "      <td>3.431803e+08</td>\n", "      <td>8.071225e+11</td>\n", "      <td>7.140764e+11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-03-17</th>\n", "      <td>'601857</td>\n", "      <td>中国石油</td>\n", "      <td>4.44</td>\n", "      <td>4.45</td>\n", "      <td>4.39</td>\n", "      <td>4.45</td>\n", "      <td>4.47</td>\n", "      <td>-0.03</td>\n", "      <td>-0.6711</td>\n", "      <td>0.0574</td>\n", "      <td>92878001</td>\n", "      <td>4.103296e+08</td>\n", "      <td>8.126131e+11</td>\n", "      <td>7.189340e+11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2007-11-09</th>\n", "      <td>'601857</td>\n", "      <td>中国石油</td>\n", "      <td>38.18</td>\n", "      <td>38.39</td>\n", "      <td>36.66</td>\n", "      <td>37.85</td>\n", "      <td>38.19</td>\n", "      <td>-0.01</td>\n", "      <td>-0.0262</td>\n", "      <td>4.7742</td>\n", "      <td>143226603</td>\n", "      <td>5.379485e+09</td>\n", "      <td>6.987741e+12</td>\n", "      <td>1.145400e+11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2007-11-08</th>\n", "      <td>'601857</td>\n", "      <td>中国石油</td>\n", "      <td>38.19</td>\n", "      <td>39.75</td>\n", "      <td>38.00</td>\n", "      <td>39.20</td>\n", "      <td>40.43</td>\n", "      <td>-2.24</td>\n", "      <td>-5.5404</td>\n", "      <td>4.6684</td>\n", "      <td>140050961</td>\n", "      <td>5.447045e+09</td>\n", "      <td>6.989571e+12</td>\n", "      <td>1.145700e+11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2007-11-07</th>\n", "      <td>'601857</td>\n", "      <td>中国石油</td>\n", "      <td>40.43</td>\n", "      <td>40.73</td>\n", "      <td>38.28</td>\n", "      <td>39.70</td>\n", "      <td>39.99</td>\n", "      <td>0.44</td>\n", "      <td>1.1003</td>\n", "      <td>7.2206</td>\n", "      <td>216618870</td>\n", "      <td>8.575267e+09</td>\n", "      <td>7.399538e+12</td>\n", "      <td>1.212900e+11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2007-11-06</th>\n", "      <td>'601857</td>\n", "      <td>中国石油</td>\n", "      <td>39.99</td>\n", "      <td>42.40</td>\n", "      <td>39.80</td>\n", "      <td>41.40</td>\n", "      <td>43.96</td>\n", "      <td>-3.97</td>\n", "      <td>-9.0309</td>\n", "      <td>11.4326</td>\n", "      <td>342977820</td>\n", "      <td>1.400025e+10</td>\n", "      <td>7.319009e+12</td>\n", "      <td>1.199700e+11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2007-11-05</th>\n", "      <td>'601857</td>\n", "      <td>N石油</td>\n", "      <td>43.96</td>\n", "      <td>48.62</td>\n", "      <td>41.70</td>\n", "      <td>48.60</td>\n", "      <td>16.70</td>\n", "      <td>27.26</td>\n", "      <td>163.2335</td>\n", "      <td>51.5833</td>\n", "      <td>1547499487</td>\n", "      <td>6.999139e+10</td>\n", "      <td>8.045602e+12</td>\n", "      <td>1.318800e+11</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>3256 rows × 14 columns</p>\n", "</div>"], "text/plain": ["               股票代码    名称    收盘价    最高价    最低价    开盘价    前收盘    涨跌额       涨跌幅  \\\n", "日期                                                                              \n", "2021-03-23  '601857  中国石油   4.32   4.35   4.31   4.35   4.36  -0.04   -0.9174   \n", "2021-03-22  '601857  中国石油   4.36   4.36   4.30   4.31   4.32   0.04    0.9259   \n", "2021-03-19  '601857  中国石油   4.32   4.36   4.30   4.32   4.41  -0.09   -2.0408   \n", "2021-03-18  '601857  中国石油   4.41   4.44   4.41   4.43   4.44  -0.03   -0.6757   \n", "2021-03-17  '601857  中国石油   4.44   4.45   4.39   4.45   4.47  -0.03   -0.6711   \n", "...             ...   ...    ...    ...    ...    ...    ...    ...       ...   \n", "2007-11-09  '601857  中国石油  38.18  38.39  36.66  37.85  38.19  -0.01   -0.0262   \n", "2007-11-08  '601857  中国石油  38.19  39.75  38.00  39.20  40.43  -2.24   -5.5404   \n", "2007-11-07  '601857  中国石油  40.43  40.73  38.28  39.70  39.99   0.44    1.1003   \n", "2007-11-06  '601857  中国石油  39.99  42.40  39.80  41.40  43.96  -3.97   -9.0309   \n", "2007-11-05  '601857   N石油  43.96  48.62  41.70  48.60  16.70  27.26  163.2335   \n", "\n", "                换手率         成交量          成交金额           总市值          流通市值  \n", "日期                                                                         \n", "2021-03-23   0.0394    63729753  2.756663e+08  7.906506e+11  6.995034e+11  \n", "2021-03-22   0.0464    75187588  3.257384e+08  7.979715e+11  7.059803e+11  \n", "2021-03-19   0.0902   146109801  6.322013e+08  7.906506e+11  6.995034e+11  \n", "2021-03-18   0.0479    77613380  3.431803e+08  8.071225e+11  7.140764e+11  \n", "2021-03-17   0.0574    92878001  4.103296e+08  8.126131e+11  7.189340e+11  \n", "...             ...         ...           ...           ...           ...  \n", "2007-11-09   4.7742   143226603  5.379485e+09  6.987741e+12  1.145400e+11  \n", "2007-11-08   4.6684   140050961  5.447045e+09  6.989571e+12  1.145700e+11  \n", "2007-11-07   7.2206   216618870  8.575267e+09  7.399538e+12  1.212900e+11  \n", "2007-11-06  11.4326   342977820  1.400025e+10  7.319009e+12  1.199700e+11  \n", "2007-11-05  51.5833  1547499487  6.999139e+10  8.045602e+12  1.318800e+11  \n", "\n", "[3256 rows x 14 columns]"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "# 数据文件\n", "f =  'data/0601857股票历史交易数据（清洗后）.csv'\n", "# 读取股票历史交易数据\n", "df = pd.read_csv(f, encoding='gbk', index_col='日期', parse_dates=True)\n", "df "]}, {"cell_type": "code", "execution_count": 3, "id": "4ac02c17", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>收盘价</th>\n", "      <th>最高价</th>\n", "      <th>最低价</th>\n", "      <th>开盘价</th>\n", "      <th>前收盘</th>\n", "      <th>涨跌额</th>\n", "      <th>涨跌幅</th>\n", "      <th>换手率</th>\n", "      <th>成交量</th>\n", "      <th>成交金额</th>\n", "      <th>总市值</th>\n", "      <th>流通市值</th>\n", "    </tr>\n", "    <tr>\n", "      <th>日期</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2021-03-23</th>\n", "      <td>4.32</td>\n", "      <td>4.35</td>\n", "      <td>4.31</td>\n", "      <td>4.35</td>\n", "      <td>4.36</td>\n", "      <td>-0.04</td>\n", "      <td>-0.9174</td>\n", "      <td>0.0394</td>\n", "      <td>63729753</td>\n", "      <td>2.756663e+08</td>\n", "      <td>7.906506e+11</td>\n", "      <td>6.995034e+11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-03-22</th>\n", "      <td>4.36</td>\n", "      <td>4.36</td>\n", "      <td>4.30</td>\n", "      <td>4.31</td>\n", "      <td>4.32</td>\n", "      <td>0.04</td>\n", "      <td>0.9259</td>\n", "      <td>0.0464</td>\n", "      <td>75187588</td>\n", "      <td>3.257384e+08</td>\n", "      <td>7.979715e+11</td>\n", "      <td>7.059803e+11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-03-19</th>\n", "      <td>4.32</td>\n", "      <td>4.36</td>\n", "      <td>4.30</td>\n", "      <td>4.32</td>\n", "      <td>4.41</td>\n", "      <td>-0.09</td>\n", "      <td>-2.0408</td>\n", "      <td>0.0902</td>\n", "      <td>146109801</td>\n", "      <td>6.322013e+08</td>\n", "      <td>7.906506e+11</td>\n", "      <td>6.995034e+11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-03-18</th>\n", "      <td>4.41</td>\n", "      <td>4.44</td>\n", "      <td>4.41</td>\n", "      <td>4.43</td>\n", "      <td>4.44</td>\n", "      <td>-0.03</td>\n", "      <td>-0.6757</td>\n", "      <td>0.0479</td>\n", "      <td>77613380</td>\n", "      <td>3.431803e+08</td>\n", "      <td>8.071225e+11</td>\n", "      <td>7.140764e+11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-03-17</th>\n", "      <td>4.44</td>\n", "      <td>4.45</td>\n", "      <td>4.39</td>\n", "      <td>4.45</td>\n", "      <td>4.47</td>\n", "      <td>-0.03</td>\n", "      <td>-0.6711</td>\n", "      <td>0.0574</td>\n", "      <td>92878001</td>\n", "      <td>4.103296e+08</td>\n", "      <td>8.126131e+11</td>\n", "      <td>7.189340e+11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2007-11-09</th>\n", "      <td>38.18</td>\n", "      <td>38.39</td>\n", "      <td>36.66</td>\n", "      <td>37.85</td>\n", "      <td>38.19</td>\n", "      <td>-0.01</td>\n", "      <td>-0.0262</td>\n", "      <td>4.7742</td>\n", "      <td>143226603</td>\n", "      <td>5.379485e+09</td>\n", "      <td>6.987741e+12</td>\n", "      <td>1.145400e+11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2007-11-08</th>\n", "      <td>38.19</td>\n", "      <td>39.75</td>\n", "      <td>38.00</td>\n", "      <td>39.20</td>\n", "      <td>40.43</td>\n", "      <td>-2.24</td>\n", "      <td>-5.5404</td>\n", "      <td>4.6684</td>\n", "      <td>140050961</td>\n", "      <td>5.447045e+09</td>\n", "      <td>6.989571e+12</td>\n", "      <td>1.145700e+11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2007-11-07</th>\n", "      <td>40.43</td>\n", "      <td>40.73</td>\n", "      <td>38.28</td>\n", "      <td>39.70</td>\n", "      <td>39.99</td>\n", "      <td>0.44</td>\n", "      <td>1.1003</td>\n", "      <td>7.2206</td>\n", "      <td>216618870</td>\n", "      <td>8.575267e+09</td>\n", "      <td>7.399538e+12</td>\n", "      <td>1.212900e+11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2007-11-06</th>\n", "      <td>39.99</td>\n", "      <td>42.40</td>\n", "      <td>39.80</td>\n", "      <td>41.40</td>\n", "      <td>43.96</td>\n", "      <td>-3.97</td>\n", "      <td>-9.0309</td>\n", "      <td>11.4326</td>\n", "      <td>342977820</td>\n", "      <td>1.400025e+10</td>\n", "      <td>7.319009e+12</td>\n", "      <td>1.199700e+11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2007-11-05</th>\n", "      <td>43.96</td>\n", "      <td>48.62</td>\n", "      <td>41.70</td>\n", "      <td>48.60</td>\n", "      <td>16.70</td>\n", "      <td>27.26</td>\n", "      <td>163.2335</td>\n", "      <td>51.5833</td>\n", "      <td>1547499487</td>\n", "      <td>6.999139e+10</td>\n", "      <td>8.045602e+12</td>\n", "      <td>1.318800e+11</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>3256 rows × 12 columns</p>\n", "</div>"], "text/plain": ["              收盘价    最高价    最低价    开盘价    前收盘    涨跌额       涨跌幅      换手率  \\\n", "日期                                                                        \n", "2021-03-23   4.32   4.35   4.31   4.35   4.36  -0.04   -0.9174   0.0394   \n", "2021-03-22   4.36   4.36   4.30   4.31   4.32   0.04    0.9259   0.0464   \n", "2021-03-19   4.32   4.36   4.30   4.32   4.41  -0.09   -2.0408   0.0902   \n", "2021-03-18   4.41   4.44   4.41   4.43   4.44  -0.03   -0.6757   0.0479   \n", "2021-03-17   4.44   4.45   4.39   4.45   4.47  -0.03   -0.6711   0.0574   \n", "...           ...    ...    ...    ...    ...    ...       ...      ...   \n", "2007-11-09  38.18  38.39  36.66  37.85  38.19  -0.01   -0.0262   4.7742   \n", "2007-11-08  38.19  39.75  38.00  39.20  40.43  -2.24   -5.5404   4.6684   \n", "2007-11-07  40.43  40.73  38.28  39.70  39.99   0.44    1.1003   7.2206   \n", "2007-11-06  39.99  42.40  39.80  41.40  43.96  -3.97   -9.0309  11.4326   \n", "2007-11-05  43.96  48.62  41.70  48.60  16.70  27.26  163.2335  51.5833   \n", "\n", "                   成交量          成交金额           总市值          流通市值  \n", "日期                                                                \n", "2021-03-23    63729753  2.756663e+08  7.906506e+11  6.995034e+11  \n", "2021-03-22    75187588  3.257384e+08  7.979715e+11  7.059803e+11  \n", "2021-03-19   146109801  6.322013e+08  7.906506e+11  6.995034e+11  \n", "2021-03-18    77613380  3.431803e+08  8.071225e+11  7.140764e+11  \n", "2021-03-17    92878001  4.103296e+08  8.126131e+11  7.189340e+11  \n", "...                ...           ...           ...           ...  \n", "2007-11-09   143226603  5.379485e+09  6.987741e+12  1.145400e+11  \n", "2007-11-08   140050961  5.447045e+09  6.989571e+12  1.145700e+11  \n", "2007-11-07   216618870  8.575267e+09  7.399538e+12  1.212900e+11  \n", "2007-11-06   342977820  1.400025e+10  7.319009e+12  1.199700e+11  \n", "2007-11-05  1547499487  6.999139e+10  8.045602e+12  1.318800e+11  \n", "\n", "[3256 rows x 12 columns]"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["# 移除“股票代码”和“名称”列\n", "df = df.drop(['股票代码','名称'], axis=1)\n", "df"]}, {"cell_type": "code", "execution_count": 4, "id": "9dc68b5d", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>收盘价</th>\n", "      <th>最高价</th>\n", "      <th>最低价</th>\n", "      <th>开盘价</th>\n", "      <th>前收盘</th>\n", "      <th>涨跌额</th>\n", "      <th>涨跌幅</th>\n", "      <th>换手率</th>\n", "      <th>成交量</th>\n", "      <th>成交金额</th>\n", "      <th>总市值</th>\n", "      <th>流通市值</th>\n", "    </tr>\n", "    <tr>\n", "      <th>日期</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2021-03-23</th>\n", "      <td>4.32</td>\n", "      <td>4.35</td>\n", "      <td>4.31</td>\n", "      <td>4.35</td>\n", "      <td>4.36</td>\n", "      <td>-0.04</td>\n", "      <td>-0.9174</td>\n", "      <td>0.0394</td>\n", "      <td>63729753</td>\n", "      <td>275666312.0</td>\n", "      <td>7.906506e+11</td>\n", "      <td>6.995034e+11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-03-22</th>\n", "      <td>4.36</td>\n", "      <td>4.36</td>\n", "      <td>4.30</td>\n", "      <td>4.31</td>\n", "      <td>4.32</td>\n", "      <td>0.04</td>\n", "      <td>0.9259</td>\n", "      <td>0.0464</td>\n", "      <td>75187588</td>\n", "      <td>325738439.0</td>\n", "      <td>7.979715e+11</td>\n", "      <td>7.059803e+11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-03-19</th>\n", "      <td>4.32</td>\n", "      <td>4.36</td>\n", "      <td>4.30</td>\n", "      <td>4.32</td>\n", "      <td>4.41</td>\n", "      <td>-0.09</td>\n", "      <td>-2.0408</td>\n", "      <td>0.0902</td>\n", "      <td>146109801</td>\n", "      <td>632201307.0</td>\n", "      <td>7.906506e+11</td>\n", "      <td>6.995034e+11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-03-18</th>\n", "      <td>4.41</td>\n", "      <td>4.44</td>\n", "      <td>4.41</td>\n", "      <td>4.43</td>\n", "      <td>4.44</td>\n", "      <td>-0.03</td>\n", "      <td>-0.6757</td>\n", "      <td>0.0479</td>\n", "      <td>77613380</td>\n", "      <td>343180296.0</td>\n", "      <td>8.071225e+11</td>\n", "      <td>7.140764e+11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-03-17</th>\n", "      <td>4.44</td>\n", "      <td>4.45</td>\n", "      <td>4.39</td>\n", "      <td>4.45</td>\n", "      <td>4.47</td>\n", "      <td>-0.03</td>\n", "      <td>-0.6711</td>\n", "      <td>0.0574</td>\n", "      <td>92878001</td>\n", "      <td>410329613.0</td>\n", "      <td>8.126131e+11</td>\n", "      <td>7.189340e+11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-03-16</th>\n", "      <td>4.47</td>\n", "      <td>4.50</td>\n", "      <td>4.44</td>\n", "      <td>4.49</td>\n", "      <td>4.51</td>\n", "      <td>-0.04</td>\n", "      <td>-0.8869</td>\n", "      <td>0.0574</td>\n", "      <td>92965905</td>\n", "      <td>414855664.0</td>\n", "      <td>8.181038e+11</td>\n", "      <td>7.237917e+11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-03-15</th>\n", "      <td>4.51</td>\n", "      <td>4.52</td>\n", "      <td>4.43</td>\n", "      <td>4.43</td>\n", "      <td>4.45</td>\n", "      <td>0.06</td>\n", "      <td>1.3483</td>\n", "      <td>0.0886</td>\n", "      <td>143431847</td>\n", "      <td>644391276.0</td>\n", "      <td>8.254246e+11</td>\n", "      <td>7.302686e+11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-03-12</th>\n", "      <td>4.45</td>\n", "      <td>4.48</td>\n", "      <td>4.41</td>\n", "      <td>4.46</td>\n", "      <td>4.44</td>\n", "      <td>0.01</td>\n", "      <td>0.2252</td>\n", "      <td>0.0668</td>\n", "      <td>108147674</td>\n", "      <td>480526340.0</td>\n", "      <td>8.144434e+11</td>\n", "      <td>7.205532e+11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-03-11</th>\n", "      <td>4.44</td>\n", "      <td>4.45</td>\n", "      <td>4.39</td>\n", "      <td>4.42</td>\n", "      <td>4.39</td>\n", "      <td>0.05</td>\n", "      <td>1.1390</td>\n", "      <td>0.0658</td>\n", "      <td>106602200</td>\n", "      <td>471419674.0</td>\n", "      <td>8.126131e+11</td>\n", "      <td>7.189340e+11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-03-10</th>\n", "      <td>4.39</td>\n", "      <td>4.47</td>\n", "      <td>4.39</td>\n", "      <td>4.46</td>\n", "      <td>4.47</td>\n", "      <td>-0.08</td>\n", "      <td>-1.7897</td>\n", "      <td>0.0722</td>\n", "      <td>116971016</td>\n", "      <td>518078260.0</td>\n", "      <td>8.034621e+11</td>\n", "      <td>7.108379e+11</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["             收盘价   最高价   最低价   开盘价   前收盘   涨跌额     涨跌幅     换手率        成交量  \\\n", "日期                                                                          \n", "2021-03-23  4.32  4.35  4.31  4.35  4.36 -0.04 -0.9174  0.0394   63729753   \n", "2021-03-22  4.36  4.36  4.30  4.31  4.32  0.04  0.9259  0.0464   75187588   \n", "2021-03-19  4.32  4.36  4.30  4.32  4.41 -0.09 -2.0408  0.0902  146109801   \n", "2021-03-18  4.41  4.44  4.41  4.43  4.44 -0.03 -0.6757  0.0479   77613380   \n", "2021-03-17  4.44  4.45  4.39  4.45  4.47 -0.03 -0.6711  0.0574   92878001   \n", "2021-03-16  4.47  4.50  4.44  4.49  4.51 -0.04 -0.8869  0.0574   92965905   \n", "2021-03-15  4.51  4.52  4.43  4.43  4.45  0.06  1.3483  0.0886  143431847   \n", "2021-03-12  4.45  4.48  4.41  4.46  4.44  0.01  0.2252  0.0668  108147674   \n", "2021-03-11  4.44  4.45  4.39  4.42  4.39  0.05  1.1390  0.0658  106602200   \n", "2021-03-10  4.39  4.47  4.39  4.46  4.47 -0.08 -1.7897  0.0722  116971016   \n", "\n", "                   成交金额           总市值          流通市值  \n", "日期                                                   \n", "2021-03-23  275666312.0  7.906506e+11  6.995034e+11  \n", "2021-03-22  325738439.0  7.979715e+11  7.059803e+11  \n", "2021-03-19  632201307.0  7.906506e+11  6.995034e+11  \n", "2021-03-18  343180296.0  8.071225e+11  7.140764e+11  \n", "2021-03-17  410329613.0  8.126131e+11  7.189340e+11  \n", "2021-03-16  414855664.0  8.181038e+11  7.237917e+11  \n", "2021-03-15  644391276.0  8.254246e+11  7.302686e+11  \n", "2021-03-12  480526340.0  8.144434e+11  7.205532e+11  \n", "2021-03-11  471419674.0  8.126131e+11  7.189340e+11  \n", "2021-03-10  518078260.0  8.034621e+11  7.108379e+11  "]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["# 筛选出2021年的数据\n", "df = df.query('日期.dt.year == 2021')\n", "# 打印前10条数据\n", "df.head(10)"]}, {"cell_type": "code", "execution_count": 5, "id": "c47d3f20", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Close</th>\n", "      <th>High</th>\n", "      <th>Low</th>\n", "      <th>Open</th>\n", "      <th>前收盘</th>\n", "      <th>涨跌额</th>\n", "      <th>涨跌幅</th>\n", "      <th>换手率</th>\n", "      <th>成交量</th>\n", "      <th>成交金额</th>\n", "      <th>总市值</th>\n", "      <th>流通市值</th>\n", "    </tr>\n", "    <tr>\n", "      <th>日期</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2021-03-23</th>\n", "      <td>4.32</td>\n", "      <td>4.35</td>\n", "      <td>4.31</td>\n", "      <td>4.35</td>\n", "      <td>4.36</td>\n", "      <td>-0.04</td>\n", "      <td>-0.9174</td>\n", "      <td>0.0394</td>\n", "      <td>63729753</td>\n", "      <td>275666312.0</td>\n", "      <td>7.906506e+11</td>\n", "      <td>6.995034e+11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-03-22</th>\n", "      <td>4.36</td>\n", "      <td>4.36</td>\n", "      <td>4.30</td>\n", "      <td>4.31</td>\n", "      <td>4.32</td>\n", "      <td>0.04</td>\n", "      <td>0.9259</td>\n", "      <td>0.0464</td>\n", "      <td>75187588</td>\n", "      <td>325738439.0</td>\n", "      <td>7.979715e+11</td>\n", "      <td>7.059803e+11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-03-19</th>\n", "      <td>4.32</td>\n", "      <td>4.36</td>\n", "      <td>4.30</td>\n", "      <td>4.32</td>\n", "      <td>4.41</td>\n", "      <td>-0.09</td>\n", "      <td>-2.0408</td>\n", "      <td>0.0902</td>\n", "      <td>146109801</td>\n", "      <td>632201307.0</td>\n", "      <td>7.906506e+11</td>\n", "      <td>6.995034e+11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-03-18</th>\n", "      <td>4.41</td>\n", "      <td>4.44</td>\n", "      <td>4.41</td>\n", "      <td>4.43</td>\n", "      <td>4.44</td>\n", "      <td>-0.03</td>\n", "      <td>-0.6757</td>\n", "      <td>0.0479</td>\n", "      <td>77613380</td>\n", "      <td>343180296.0</td>\n", "      <td>8.071225e+11</td>\n", "      <td>7.140764e+11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-03-17</th>\n", "      <td>4.44</td>\n", "      <td>4.45</td>\n", "      <td>4.39</td>\n", "      <td>4.45</td>\n", "      <td>4.47</td>\n", "      <td>-0.03</td>\n", "      <td>-0.6711</td>\n", "      <td>0.0574</td>\n", "      <td>92878001</td>\n", "      <td>410329613.0</td>\n", "      <td>8.126131e+11</td>\n", "      <td>7.189340e+11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-03-16</th>\n", "      <td>4.47</td>\n", "      <td>4.50</td>\n", "      <td>4.44</td>\n", "      <td>4.49</td>\n", "      <td>4.51</td>\n", "      <td>-0.04</td>\n", "      <td>-0.8869</td>\n", "      <td>0.0574</td>\n", "      <td>92965905</td>\n", "      <td>414855664.0</td>\n", "      <td>8.181038e+11</td>\n", "      <td>7.237917e+11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-03-15</th>\n", "      <td>4.51</td>\n", "      <td>4.52</td>\n", "      <td>4.43</td>\n", "      <td>4.43</td>\n", "      <td>4.45</td>\n", "      <td>0.06</td>\n", "      <td>1.3483</td>\n", "      <td>0.0886</td>\n", "      <td>143431847</td>\n", "      <td>644391276.0</td>\n", "      <td>8.254246e+11</td>\n", "      <td>7.302686e+11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-03-12</th>\n", "      <td>4.45</td>\n", "      <td>4.48</td>\n", "      <td>4.41</td>\n", "      <td>4.46</td>\n", "      <td>4.44</td>\n", "      <td>0.01</td>\n", "      <td>0.2252</td>\n", "      <td>0.0668</td>\n", "      <td>108147674</td>\n", "      <td>480526340.0</td>\n", "      <td>8.144434e+11</td>\n", "      <td>7.205532e+11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-03-11</th>\n", "      <td>4.44</td>\n", "      <td>4.45</td>\n", "      <td>4.39</td>\n", "      <td>4.42</td>\n", "      <td>4.39</td>\n", "      <td>0.05</td>\n", "      <td>1.1390</td>\n", "      <td>0.0658</td>\n", "      <td>106602200</td>\n", "      <td>471419674.0</td>\n", "      <td>8.126131e+11</td>\n", "      <td>7.189340e+11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2021-03-10</th>\n", "      <td>4.39</td>\n", "      <td>4.47</td>\n", "      <td>4.39</td>\n", "      <td>4.46</td>\n", "      <td>4.47</td>\n", "      <td>-0.08</td>\n", "      <td>-1.7897</td>\n", "      <td>0.0722</td>\n", "      <td>116971016</td>\n", "      <td>518078260.0</td>\n", "      <td>8.034621e+11</td>\n", "      <td>7.108379e+11</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["            Close  High   Low  Open   前收盘   涨跌额     涨跌幅     换手率        成交量  \\\n", "日期                                                                           \n", "2021-03-23   4.32  4.35  4.31  4.35  4.36 -0.04 -0.9174  0.0394   63729753   \n", "2021-03-22   4.36  4.36  4.30  4.31  4.32  0.04  0.9259  0.0464   75187588   \n", "2021-03-19   4.32  4.36  4.30  4.32  4.41 -0.09 -2.0408  0.0902  146109801   \n", "2021-03-18   4.41  4.44  4.41  4.43  4.44 -0.03 -0.6757  0.0479   77613380   \n", "2021-03-17   4.44  4.45  4.39  4.45  4.47 -0.03 -0.6711  0.0574   92878001   \n", "2021-03-16   4.47  4.50  4.44  4.49  4.51 -0.04 -0.8869  0.0574   92965905   \n", "2021-03-15   4.51  4.52  4.43  4.43  4.45  0.06  1.3483  0.0886  143431847   \n", "2021-03-12   4.45  4.48  4.41  4.46  4.44  0.01  0.2252  0.0668  108147674   \n", "2021-03-11   4.44  4.45  4.39  4.42  4.39  0.05  1.1390  0.0658  106602200   \n", "2021-03-10   4.39  4.47  4.39  4.46  4.47 -0.08 -1.7897  0.0722  116971016   \n", "\n", "                   成交金额           总市值          流通市值  \n", "日期                                                   \n", "2021-03-23  275666312.0  7.906506e+11  6.995034e+11  \n", "2021-03-22  325738439.0  7.979715e+11  7.059803e+11  \n", "2021-03-19  632201307.0  7.906506e+11  6.995034e+11  \n", "2021-03-18  343180296.0  8.071225e+11  7.140764e+11  \n", "2021-03-17  410329613.0  8.126131e+11  7.189340e+11  \n", "2021-03-16  414855664.0  8.181038e+11  7.237917e+11  \n", "2021-03-15  644391276.0  8.254246e+11  7.302686e+11  \n", "2021-03-12  480526340.0  8.144434e+11  7.205532e+11  \n", "2021-03-11  471419674.0  8.126131e+11  7.189340e+11  \n", "2021-03-10  518078260.0  8.034621e+11  7.108379e+11  "]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["# 重新命名列名\n", "column_mapping = {\n", "    '日期': 'Date',\n", "    '收盘价': 'Close',\n", "    '最高价': 'High',\n", "    '最低价': 'Low',\n", "    '开盘价': 'Open',\n", "}\n", "df = df.rename(columns=column_mapping)\n", "# 打印前10条数据\n", "df.head(10)"]}, {"cell_type": "markdown", "id": "45b94668", "metadata": {}, "source": ["### 海龟交易策略"]}, {"cell_type": "code", "execution_count": 6, "id": "be0bc0d2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["总交易次数：11\n", "总盈利：-1000000.00元\n", "平均收益：-90909.09元/交易\n"]}], "source": ["import numpy as np\n", "import pandas as pd\n", "\n", "# 设置移动平均线窗口期\n", "ma_short_window = 20\n", "ma_long_window = 50\n", "\n", "# 计算移动平均线\n", "df['MA20'] = df['Close'].rolling(window=ma_short_window, min_periods=1).mean()\n", "df['MA50'] = df['Close'].rolling(window=ma_long_window, min_periods=1).mean()\n", "\n", "# 移除NaN值\n", "df.dropna(subset=['MA50'], inplace=True)\n", "\n", "# 定义海龟策略函数\n", "def turtle_trading_strategy(df):\n", "    # 从策略参数\n", "    initial_capital = 1000000  # 初始资金\n", "    unit_size = 100  # 每次交易量\n", "\n", "    # 确定买入和卖出信号\n", "    df['Buy_Signal'] = df['Close'].gt(df['MA20']) & df['Close'].shift(1).lt(df['MA20'].shift(1))\n", "    df['Sell_Signal'] = df['Close'].lt(df['MA20']) & df['Close'].shift(1).gt(df['MA20'].shift(1))\n", "\n", "    # 计算持仓量和资金曲线\n", "    df['Position'] = 0\n", "    df.loc[df['Buy_Signal'], 'Position'] = unit_size\n", "    df.loc[df['Sell_Signal'], 'Position'] = -unit_size\n", "    df['Total_Value'] = df['Position'] * df['Close'].shift(-1)\n", "\n", "    # 计算每日盈亏和总盈亏\n", "    df['Daily_Return'] = df['Total_Value'].pct_change()\n", "    # 清除NaN和inf值\n", "    df['Daily_Return'].replace([np.inf, -np.inf], np.nan, inplace=True)\n", "    df['Daily_Return'].fillna(0, inplace=True)\n", "\n", "    df['Cumulative_Return'] = (df['Daily_Return'] + 1).cumprod()\n", "\n", "    # 计算总收益和平均收益\n", "    cumulative_returns = df['Cumulative_Return'].iloc[-1] * initial_capital - initial_capital\n", "    total_trades = df[df['Position'] != 0].shape[0]\n", "    average_return = cumulative_returns / total_trades\n", "\n", "    return cumulative_returns, average_return\n", "\n", "# 调用turtle_trading_strategy函数\n", "total_profit, average_return = turtle_trading_strategy(df)\n", "\n", "print(f\"总交易次数：{df[df['Position'] != 0]['Position'].count()}\")\n", "print(f\"总盈利：{total_profit:.2f}元\")\n", "print(f\"平均收益：{average_return:.2f}元/交易\")\n"]}, {"cell_type": "code", "execution_count": 7, "id": "bd51c3ff", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1437.5x575 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import numpy as np\n", "import pandas as pd\n", "import mplfinance as mpf\n", "import matplotlib.pyplot as plt\n", "\n", "# 绘制K线图和移动平均线图\n", "plt.rcParams['font.family'] = ['SimHei']  # 设置中文字体\n", "plt.rcParams['axes.unicode_minus'] = False  # 设置负号显示\n", "\n", "# 添加移动平均线参数\n", "ap0 = [\n", "    mpf.make_addplot(df['MA20'], color=\"b\", width=1.5),\n", "    mpf.make_addplot(df['MA50'], color=\"y\", width=1.5),\n", "]\n", "\n", "market_colors = mpf.make_marketcolors(up='red', down='green')\n", "my_style = mpf.make_mpf_style(marketcolors=market_colors)\n", "\n", "# 绘制K线图\n", "mpf.plot(df, type='candle',\n", "         figratio=(10, 4),\n", "         mav=(ma_short_window, ma_long_window),\n", "         show_nontrading=True,\n", "         addplot=ap0,\n", "         style=my_style)\n", "\n", "mpf.show()\n"]}, {"cell_type": "code", "execution_count": 8, "id": "0d78a36b", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "plt.rcParams['font.family'] = ['SimHei']  # 设置中文字体\n", "plt.rcParams['axes.unicode_minus'] = False  # 设置负号显示\n", "# 设置图表大小\n", "plt.figure(figsize=(10, 6))\n", "\n", "# 绘制交易信号图\n", "plt.plot(df['Close'], label='收盘价')\n", "plt.plot(df.loc[df['Buy_Signal'], 'Close'], 'o', markersize=8, color='green', label='买入信号')\n", "plt.plot(df.loc[df['Sell_Signal'], 'Close'], 'o', markersize=8, color='red', label='卖出信号')\n", "plt.title('交易信号')\n", "plt.xlabel('日期')\n", "plt.ylabel('价格')\n", "plt.legend()\n", "\n", "# 调整 x 轴标签倾斜\n", "plt.xticks(rotation=45)\n", "\n", "plt.grid(True)\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": null, "id": "af9e1e03", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}