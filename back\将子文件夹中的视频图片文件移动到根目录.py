import os
import shutil
import tkinter as tk
from tkinter import filedialog


def move_videos_to_current_directory(start_directory):
    # 遍历所有子文件夹和文件
    for root, dirs, files in os.walk(start_directory):
        for filename in files:
            file_path = os.path.join(root, filename)
            if is_video_file(filename):
                # 如果文件名与所在目录名相同，给文件名加后缀
                if filename == os.path.basename(root):
                    new_filename = add_suffix_to_filename(filename, "-no")
                    new_file_path = os.path.join(root, new_filename)
                    os.rename(file_path, new_file_path)
                    file_path = new_file_path
                    print(f"给视频文件 {filename} 添加后缀：{new_filename}")

                # 将视频文件移动到开始目录
                shutil.move(file_path, os.path.join(start_directory, os.path.basename(file_path)))
                print(f"移动文件: {file_path} 到 {os.path.join(start_directory, os.path.basename(file_path))}")


def has_video_files(directory):
    # 检查目录下是否有视频文件
    for filename in os.listdir(directory):
        if is_video_file(filename):
            return True
    return False


def is_video_file(filename):
    video_extensions = [".mp4", ".avi", ".mkv", ".mov", ".wmv", ".mpg", ".ts", ".nrg", ".iso", ".xltd", ".jpg", ".png", ".JPG"]
    return any(filename.endswith(ext) for ext in video_extensions)


def add_suffix_to_filename(filename, suffix):
    # 将后缀添加到文件名中（在文件扩展名之前）
    base, extension = os.path.splitext(filename)
    return f"{base}{suffix}{extension}"


def select_directory_and_move_videos():
    # 弹出选择目录对话框
    start_directory = filedialog.askdirectory(title="选择开始的目录")
    if start_directory:
        # 调用函数移动视频文件
        move_videos_to_current_directory(start_directory)
        print("处理完成！")


# 创建主窗口
root = tk.Tk()

# 创建按钮来选择目录并移动视频文件
button = tk.Button(root, text="选择开始的目录并移动视频文件", command=select_directory_and_move_videos)
button.pack()

# 运行程序
root.mainloop()
