#!/usr/bin/env python3
# -*- coding: utf-8 -*-

# 测试保存对话框功能
import tkinter as tk
from tkinter import filedialog, messagebox

def test_save_dialog():
    """测试文件保存对话框是否正常工作"""
    
    root = tk.Tk()
    root.title("测试保存对话框")
    root.geometry("400x200")
    
    def test_dialog():
        print("开始测试保存对话框...")
        
        try:
            file_path = filedialog.asksaveasfilename(
                title="保存结果",
                defaultextension=".txt",
                filetypes=[
                    ("文本文件", "*.txt"),
                    ("所有文件", "*.*")
                ],
                initialfile="HTML解码链接提取结果.txt"
            )
            
            print(f"用户选择的文件路径: {repr(file_path)}")
            
            if file_path:
                # 尝试写入测试内容
                try:
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write("测试保存功能\n")
                        f.write("这是一个测试文件\n")
                        f.write("如果您看到这个文件，说明保存功能正常工作\n")
                    
                    messagebox.showinfo("成功", f"测试文件已保存到:\n{file_path}")
                    print(f"文件保存成功: {file_path}")
                    
                except Exception as e:
                    messagebox.showerror("错误", f"保存失败: {str(e)}")
                    print(f"保存失败: {e}")
            else:
                print("用户取消了保存操作")
                messagebox.showinfo("提示", "您取消了保存操作")
                
        except Exception as e:
            print(f"对话框出现错误: {e}")
            messagebox.showerror("错误", f"对话框出现错误: {str(e)}")
    
    # 创建测试按钮
    test_button = tk.Button(root, text="测试保存对话框", command=test_dialog, 
                           font=('Microsoft YaHei', 12), width=20, height=2)
    test_button.pack(pady=50)
    
    # 添加说明
    info_label = tk.Label(root, text="点击按钮测试文件保存对话框是否正常工作", 
                         font=('Microsoft YaHei', 10))
    info_label.pack(pady=10)
    
    print("测试窗口已启动，请点击按钮测试保存对话框")
    root.mainloop()

if __name__ == "__main__":
    test_save_dialog()
