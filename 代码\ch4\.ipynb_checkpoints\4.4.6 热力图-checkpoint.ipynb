{"cells": [{"cell_type": "code", "execution_count": 8, "id": "95466663", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\seaborn\\utils.py:80: UserWarning: Glyph 32929 (\\N{CJK UNIFIED IDEOGRAPH-80A1}) missing from current font.\n", "  fig.canvas.draw()\n", "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\seaborn\\utils.py:80: UserWarning: Glyph 31080 (\\N{CJK UNIFIED IDEOGRAPH-7968}) missing from current font.\n", "  fig.canvas.draw()\n", "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\IPython\\core\\pylabtools.py:152: UserWarning: Glyph 32929 (\\N{CJK UNIFIED IDEOGRAPH-80A1}) missing from current font.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\IPython\\core\\pylabtools.py:152: UserWarning: Glyph 31080 (\\N{CJK UNIFIED IDEOGRAPH-7968}) missing from current font.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1000x800 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import pandas as pd\n", "import seaborn as sns\n", "import matplotlib.pyplot as plt\n", "\n", "# 设置中文显示\n", "plt.rcParams['font.family'] = ['SimHei']\n", "plt.rcParams['axes.unicode_minus'] = False\n", "\n", "# 创建测试数据\n", "data = pd.DataFrame({\n", "    '股票1': [10, 12, 8, 15, 9],\n", "    '股票2': [20, 18, 25, 22, 24],\n", "    '股票3': [7, 9, 6, 8, 10],\n", "    '股票4': [13, 11, 14, 10, 12]\n", "})\n", "\n", "# 提取多个股票的收盘价数据列\n", "股票_prices = data[['股票1', '股票2', '股票3', '股票4']]\n", "\n", "# 计算收盘价之间的相关性\n", "correlation_matrix = 股票_prices.corr()\n", "\n", "# 绘制热力图\n", "plt.figure(figsize=(10, 8))  # 设置图形大小\n", "sns.heatmap(correlation_matrix, annot=True, cmap='coolwarm')\n", "\n", "# 设置图形标题\n", "plt.title('股票 Prices Correlation')\n", "\n", "# 显示图形\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": null, "id": "a27e7daf", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}