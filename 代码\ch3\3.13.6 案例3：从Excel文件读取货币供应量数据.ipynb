{"cells": [{"cell_type": "code", "execution_count": 3, "id": "e90353fa", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>2023年4月</th>\n", "      <th>2023年3月</th>\n", "      <th>2023年2月</th>\n", "      <th>2023年1月</th>\n", "      <th>2022年12月</th>\n", "      <th>2022年11月</th>\n", "      <th>2022年10月</th>\n", "      <th>2022年9月</th>\n", "      <th>2022年8月</th>\n", "      <th>2022年7月</th>\n", "      <th>2022年6月</th>\n", "      <th>2022年5月</th>\n", "    </tr>\n", "    <tr>\n", "      <th>指标</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>货币和准货币(M2)供应量期末值(亿元)</th>\n", "      <td>2808500.0</td>\n", "      <td>2814566.31</td>\n", "      <td>2755249.23</td>\n", "      <td>2738072.06</td>\n", "      <td>2664320.84</td>\n", "      <td>2647008.48</td>\n", "      <td>2612914.57</td>\n", "      <td>2626600.92</td>\n", "      <td>2595068.27</td>\n", "      <td>2578078.57</td>\n", "      <td>2581451.20</td>\n", "      <td>2527026.15</td>\n", "    </tr>\n", "    <tr>\n", "      <th>货币和准货币(M2)供应量同比增长(%)</th>\n", "      <td>12.4</td>\n", "      <td>12.70</td>\n", "      <td>12.90</td>\n", "      <td>12.60</td>\n", "      <td>11.80</td>\n", "      <td>12.40</td>\n", "      <td>11.80</td>\n", "      <td>12.10</td>\n", "      <td>12.20</td>\n", "      <td>12.00</td>\n", "      <td>11.40</td>\n", "      <td>11.10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>货币(M1)供应量期末值(亿元)</th>\n", "      <td>669800.0</td>\n", "      <td>678059.63</td>\n", "      <td>657938.74</td>\n", "      <td>655214.16</td>\n", "      <td>671674.76</td>\n", "      <td>667042.61</td>\n", "      <td>662140.99</td>\n", "      <td>664535.17</td>\n", "      <td>664604.85</td>\n", "      <td>661832.33</td>\n", "      <td>674374.81</td>\n", "      <td>645107.52</td>\n", "    </tr>\n", "    <tr>\n", "      <th>货币(M1)供应量同比增长(%)</th>\n", "      <td>5.3</td>\n", "      <td>5.10</td>\n", "      <td>5.80</td>\n", "      <td>6.70</td>\n", "      <td>3.70</td>\n", "      <td>4.60</td>\n", "      <td>5.80</td>\n", "      <td>6.40</td>\n", "      <td>6.10</td>\n", "      <td>6.70</td>\n", "      <td>5.80</td>\n", "      <td>4.60</td>\n", "    </tr>\n", "    <tr>\n", "      <th>流通中现金(M0)供应量期末值(亿元)</th>\n", "      <td>105900.0</td>\n", "      <td>105591.30</td>\n", "      <td>107602.58</td>\n", "      <td>114601.30</td>\n", "      <td>104706.03</td>\n", "      <td>99740.12</td>\n", "      <td>98416.71</td>\n", "      <td>98672.06</td>\n", "      <td>97231.03</td>\n", "      <td>96509.19</td>\n", "      <td>96011.17</td>\n", "      <td>95546.86</td>\n", "    </tr>\n", "    <tr>\n", "      <th>流通中现金(M0)供应量同比增长(%)</th>\n", "      <td>10.7</td>\n", "      <td>11.00</td>\n", "      <td>10.60</td>\n", "      <td>7.90</td>\n", "      <td>15.30</td>\n", "      <td>14.10</td>\n", "      <td>14.30</td>\n", "      <td>13.60</td>\n", "      <td>14.30</td>\n", "      <td>13.90</td>\n", "      <td>13.80</td>\n", "      <td>13.50</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                        2023年4月     2023年3月     2023年2月     2023年1月  \\\n", "指标                                                                    \n", "货币和准货币(M2)供应量期末值(亿元)  2808500.0  2814566.31  2755249.23  2738072.06   \n", "货币和准货币(M2)供应量同比增长(%)       12.4       12.70       12.90       12.60   \n", "货币(M1)供应量期末值(亿元)       669800.0   678059.63   657938.74   655214.16   \n", "货币(M1)供应量同比增长(%)            5.3        5.10        5.80        6.70   \n", "流通中现金(M0)供应量期末值(亿元)    105900.0   105591.30   107602.58   114601.30   \n", "流通中现金(M0)供应量同比增长(%)        10.7       11.00       10.60        7.90   \n", "\n", "                        2022年12月    2022年11月    2022年10月     2022年9月  \\\n", "指标                                                                     \n", "货币和准货币(M2)供应量期末值(亿元)  2664320.84  2647008.48  2612914.57  2626600.92   \n", "货币和准货币(M2)供应量同比增长(%)       11.80       12.40       11.80       12.10   \n", "货币(M1)供应量期末值(亿元)       671674.76   667042.61   662140.99   664535.17   \n", "货币(M1)供应量同比增长(%)            3.70        4.60        5.80        6.40   \n", "流通中现金(M0)供应量期末值(亿元)    104706.03    99740.12    98416.71    98672.06   \n", "流通中现金(M0)供应量同比增长(%)        15.30       14.10       14.30       13.60   \n", "\n", "                         2022年8月     2022年7月     2022年6月     2022年5月  \n", "指标                                                                    \n", "货币和准货币(M2)供应量期末值(亿元)  2595068.27  2578078.57  2581451.20  2527026.15  \n", "货币和准货币(M2)供应量同比增长(%)       12.20       12.00       11.40       11.10  \n", "货币(M1)供应量期末值(亿元)       664604.85   661832.33   674374.81   645107.52  \n", "货币(M1)供应量同比增长(%)            6.10        6.70        5.80        4.60  \n", "流通中现金(M0)供应量期末值(亿元)     97231.03    96509.19    96011.17    95546.86  \n", "流通中现金(M0)供应量同比增长(%)        14.30       13.90       13.80       13.50  "]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "df = pd.read_excel('data/货币供应量月度数据.xls', sheet_name='月度数据',skiprows=2,skipfooter= 2, index_col=0)\n", "df"]}, {"cell_type": "code", "execution_count": null, "id": "c2db3a0f", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}