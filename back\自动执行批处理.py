import os
import time
import shutil
from datetime import datetime

def execute_batch(batch_file):
    # 记录开始执行时间
    start_time = datetime.now()
    print(f"开始执行批处理文件: {batch_file}")

    # 执行批处理文件
    os.system(batch_file)

    # 计算执行时间并打印
    end_time = datetime.now()
    execution_time = end_time - start_time
    print(f"批处理文件 {batch_file} 执行完毕，执行时间: {execution_time}")

def move_executed_files(batch_folder, executed_folder):
    # 获取批处理文件列表
    batch_files = [file for file in os.listdir(batch_folder) if file.endswith(".bat")]

    # 移动已执行的批处理文件到子文件夹
    for file in batch_files:
        source_path = os.path.join(batch_folder, file)
        target_path = os.path.join(executed_folder, file)
        shutil.move(source_path, target_path)

def main():
    # 设置批处理文件夹路径和已执行文件夹路径
    batch_folder = "d:/tdl_Windows_64bit"
    executed_folder = os.path.join(batch_folder, "executed")

    # 创建子文件夹
    os.makedirs(executed_folder, exist_ok=True)

    while True:
        # 获取批处理文件列表
        batch_files = [os.path.join(batch_folder, file) for file in os.listdir(batch_folder) if file.endswith(".bat")]

        if not batch_files:
            print("未找到新的批处理文件")
            # 等待5分钟
            for i in range(300, 0, -1):  # 300秒倒计时，即5分钟
                minutes = i // 60
                seconds = i % 60
                print(f"\r等待下一次查找: {minutes:02d}:{seconds:02d}", end="", flush=True)
                time.sleep(1)
                # 清除当前行
                print("\033[F\033[K", end="", flush=True)
            continue

        # 打印还有几个批处理要执行
        remaining_files = len(batch_files)
        print(f"还有 {remaining_files} 个批处理文件要执行")

        # 逐个执行批处理文件
        for batch_file in batch_files:
            execute_batch(batch_file)

            # 打印还有几个批处理要执行
            remaining_files -= 1
            print(f"还有 {remaining_files} 个批处理文件要执行")

            # 移动已执行的批处理文件到子文件夹
        move_executed_files(batch_folder, executed_folder)

if __name__ == "__main__":
    main()
